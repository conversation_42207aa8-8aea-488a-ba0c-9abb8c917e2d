/**
 * Root Layout Component
 * Main layout component for the entire application
 */
import type { Metadata } from "next";
import "./globals.css";
import "./styles/highlight-animation.css";
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import ClientBootstrap from './components/bootstrap/ClientBootstrap';
import ServerBootstrap from './components/bootstrap/ServerBootstrap';

// Load the Inter font with Latin character subset
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

/**
 * Metadata for the application
 * Includes title and description for SEO
 */
export const metadata: Metadata = {
  title: "Trend Tech Innovations - Inventory Management",
  description: "Inventory Management System for Trend Tech Innovations",
};

/**
 * RootLayout component that wraps the entire application
 * Sets up the HTML structure, language, font, and providers
 * @param children - The content to render inside the layout
 */
export default function RootLayout({
  children,
}: {
  /** The content to render inside the layout */
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        {/* Server-side bootstrap component */}
        <ServerBootstrap />
        
        {/* Providers wrapper for context providers */}
        <Providers>
          {/* Client-side bootstrap component */}
          <ClientBootstrap>
            {children}
          </ClientBootstrap>
        </Providers>
      </body>
    </html>
  );
}
