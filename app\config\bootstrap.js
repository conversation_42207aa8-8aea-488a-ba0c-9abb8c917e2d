import connectToDatabase, { checkDatabaseHealth } from '@/app/lib/mongodb';
import { logError } from '@/app/services/logging';
import { startDatabaseMonitoring } from '@/app/tasks/monitor';

/**
 * Bootstrap the application by initializing critical dependencies
 * This should be called early in the application lifecycle
 */
export async function bootstrapApplication() {
  console.log('[Bootstrap] Starting application initialization...');

  // Connect to database
  try {
    console.log('[Bootstrap] Connecting to database...');
    await connectToDatabase();
    
    // Check database health after connection
    const healthResult = await checkDatabaseHealth();
    
    if (healthResult.status === 'healthy') {
      console.log('[Bootstrap] Database connection established successfully');
    } else {
      console.warn('[Bootstrap] Database connection warning:', healthResult);
    }
  } catch (error) {
    console.error('[Bootstrap] Failed to connect to database:', 
      error instanceof Error ? error.message : String(error));
    
    // Log the error
    await logError(
      'bootstrap', 
      'Failed to connect to database during bootstrap', 
      error
    );
    
    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'production') {
      // In production, consider this a critical failure that should stop startup
      throw new Error('Failed to connect to database during application bootstrap');
    }
    
    // In development, show warning but continue
    console.warn('[Bootstrap] Continuing with application startup despite database error');
  }

  // Start database monitoring in development or if explicitly enabled
  if (typeof process !== 'undefined' && (process.env.NODE_ENV !== 'production' || process.env.ENABLE_DB_MONITORING === 'true')) {
    console.log('[Bootstrap] Starting database monitoring...');
    // Check every 30 seconds in development, adjust as needed
    startDatabaseMonitoring(30000);
  }

  // Initialize other critical services here as needed
  // ...

  console.log('[Bootstrap] Application initialization complete');
}

/**
 * Clean up application resources before shutdown
 * This should be called when the application is shutting down
 */
export async function shutdownApplication() {
  console.log('[Shutdown] Cleaning up application resources...');
  
  // Add cleanup logic here
  // ...
  
  console.log('[Shutdown] Application cleanup complete');
} 