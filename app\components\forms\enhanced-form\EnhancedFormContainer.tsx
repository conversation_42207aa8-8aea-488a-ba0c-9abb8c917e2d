"use client";

import { FormErrorDisplay } from "@/app/components/feedback";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/layout/cards/card";
import { AutosaveStatus } from "@/app/hooks/useAutosave";
import { cn } from "@/app/lib/utils";
import { AnimatePresence, motion } from "framer-motion";

import { LoadingInline, LoadingOverlay } from "@/app/components/data-display/loading";
import React from "react";
import { AutosaveIndicator } from "./AutosaveIndicator";

export interface EnhancedFormContainerProps {
  /**
   * Form title
   */
  title: string;
  
  /**
   * Optional form description
   */
  description?: string | undefined;
  
  /**
   * Whether the form is in a loading state
   */
  isLoading?: boolean;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Whether to animate the form container
   */
  animate?: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
  
  /**
   * Footer content
   */
  footer?: React.ReactNode;
  
  /**
   * Maximum width of the form container
   */
  maxWidth?: string;
  
  /**
   * Form content
   */
  children: React.ReactNode;
  
  /**
   * Autosave status
   */
  autosaveStatus?: AutosaveStatus;
  
  /**
   * Last saved timestamp
   */
  lastSaved?: Date | null;
  
  /**
   * Autosave error
   */
  autosaveError?: Error | null;
  
  /**
   * Whether to show the autosave indicator
   */
  showAutosaveIndicator?: boolean;
}

export const EnhancedFormContainer: React.FC<EnhancedFormContainerProps> = ({
  title,
  description,
  isLoading = false,
  error = null,
  animate = true,
  className = "",
  footer,
  maxWidth = "max-w-3xl",
  children,
  autosaveStatus = "idle",
  lastSaved = null,
  autosaveError = null,
  showAutosaveIndicator = false,
}) => {
  const Container = animate ? motion.div : React.Fragment;
  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : {};

  return (
    <Container {...animationProps}>
      <Card className={cn(
        "shadow-sm border border-input bg-background transition-all duration-300 hover:shadow-md",
        maxWidth,
        className
      )}>
        <CardHeader className="pb-4 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent dark:from-accent dark:to-accent/70">
              {title}
            </CardTitle>
            {isLoading && (
              <LoadingInline size="sm" variant="accent" />
            )}
          </div>
          
          <div className="flex items-center justify-between">
            {description && (
              <CardDescription className="text-muted-foreground">
                {description}
              </CardDescription>
            )}
            
            {showAutosaveIndicator && (
              <AutosaveIndicator
                status={autosaveStatus}
                lastSaved={lastSaved}
                error={autosaveError}
                className="ml-auto"
              />
            )}
          </div>
        </CardHeader>

        {error && (
          <div className="px-6">
            <FormErrorDisplay
              error={error}
              field="Enhanced Form"
              className="mb-4"
            />
          </div>
        )}

        <CardContent className="space-y-4">
          {/* Content with loading overlay */}
          <div className="relative">
            {/* Apply disabled styling when loading */}
            <div className={cn(
              "transition-opacity",
              isLoading && "opacity-70 pointer-events-none"
            )}>
              {children}
            </div>

            {/* Loading overlay using standardized component */}
            {isLoading && (
              <div className="absolute inset-0 z-10">
                <LoadingOverlay message="Loading..." />
              </div>
            )}
          </div>
        </CardContent>

        {footer && (
          <CardFooter className="flex justify-end gap-2 pt-2 border-t">
            {footer}
          </CardFooter>
        )}
      </Card>
    </Container>
  );
};
