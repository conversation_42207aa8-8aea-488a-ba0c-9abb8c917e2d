import { NextRequest, NextResponse } from 'next/server';
import { generateAssemblyReport } from '@/app/services/reports';
import { successResponse, errorResponse } from '@/app/lib/api-response';

/**
 * GET handler for generating assembly report
 * @param request - The incoming request with query parameters
 * @returns JSON response with assembly report data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/reports/assembly - Generating assembly report');
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const productFilter = searchParams.get('product') || null;
    
    // Generate the report with the specified options
    const reportData = await generateAssemblyReport({
      productFilter
    });
    
    const duration = Date.now() - startTime;
    console.log(`[API] Assembly report generated in ${duration}ms`);
    
    return successResponse(
      reportData,
      'Assembly report generated successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/reports/assembly (${duration}ms):`, error);
    
    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
