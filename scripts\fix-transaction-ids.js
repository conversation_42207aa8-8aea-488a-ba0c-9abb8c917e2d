const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.MONGODB_DB_NAME || 'Trend_IMS';

async function fixTransactionIds() {
  console.log('🔧 Starting transaction ID fix...');
  
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const transactionsCollection = db.collection('transactions');
    
    // First, let's see what we're dealing with
    console.log('\n📊 Analyzing transactions collection...');
    
    const totalTransactions = await transactionsCollection.countDocuments();
    console.log(`Total transactions: ${totalTransactions}`);
    
    // Find transactions with null or missing transactionId
    const invalidTransactions = await transactionsCollection.find({
      $or: [
        { transactionId: null },
        { transactionId: { $exists: false } },
        { transaction_id: null },
        { transaction_id: { $exists: false } }
      ]
    }).toArray();
    
    console.log(`Transactions with null/missing transactionId: ${invalidTransactions.length}`);
    
    if (invalidTransactions.length === 0) {
      console.log('✅ No invalid transaction IDs found.');
      return;
    }
    
    // Show some examples
    console.log('\n🔍 Examples of invalid transactions:');
    invalidTransactions.slice(0, 3).forEach((tx, i) => {
      console.log(`  ${i + 1}. Transaction ${tx._id}:`);
      console.log(`     transactionId: ${tx.transactionId}`);
      console.log(`     transaction_id: ${tx.transaction_id}`);
      console.log(`     partId: ${tx.partId}`);
      console.log(`     transactionDate: ${tx.transactionDate}`);
    });
    
    // Fix the transactions
    console.log('\n🔧 Fixing invalid transaction IDs...');
    let fixedCount = 0;
    
    for (const transaction of invalidTransactions) {
      // Generate a new transaction ID based on the date
      const date = new Date(transaction.transactionDate || transaction.createdAt || new Date());
      const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
      
      // Find the next available sequence number for this date
      const existingCount = await transactionsCollection.countDocuments({
        transactionId: { $regex: `^TXN-${dateStr}-\\d{3}$` }
      });
      
      const sequenceNumber = (existingCount + 1).toString().padStart(3, '0');
      const newTransactionId = `TXN-${dateStr}-${sequenceNumber}`;
      
      // Update the transaction
      const updateResult = await transactionsCollection.updateOne(
        { _id: transaction._id },
        { 
          $set: { 
            transactionId: newTransactionId 
          },
          $unset: {
            transaction_id: "" // Remove the old field if it exists
          }
        }
      );
      
      if (updateResult.modifiedCount > 0) {
        fixedCount++;
        console.log(`  ✅ Fixed transaction ${transaction._id}: → ${newTransactionId}`);
      } else {
        console.log(`  ❌ Failed to fix transaction ${transaction._id}`);
      }
    }
    
    console.log(`\n📈 Summary:`);
    console.log(`  ✅ Fixed: ${fixedCount} transactions`);
    
    // Verify the fix
    console.log('\n🔍 Verifying fix...');
    const remainingInvalid = await transactionsCollection.countDocuments({
      $or: [
        { transactionId: null },
        { transactionId: { $exists: false } }
      ]
    });
    
    if (remainingInvalid === 0) {
      console.log('✅ All transactions now have valid transaction IDs!');
    } else {
      console.log(`⚠️  Still ${remainingInvalid} transactions with invalid IDs`);
    }
    
    // Show final state
    console.log('\n📋 Final transaction ID samples:');
    const sampleTransactions = await transactionsCollection.find({}, {
      projection: { _id: 1, transactionId: 1, transactionDate: 1 }
    }).limit(5).toArray();
    
    sampleTransactions.forEach((tx, i) => {
      console.log(`  ${i + 1}. ${tx.transactionId} (${tx.transactionDate})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixTransactionIds().catch(console.error);
