import connectToDatabase, {
    forceReconnect,
    getConnectionState
} from '@/app/lib/mongodb';
import { logError } from '@/app/services/logging';
import { NextResponse } from 'next/server';

/**
 * @typedef {import('next/server').NextRequest} NextRequest
 * @typedef {import('next/server').NextResponse} NextResponse
 */

/**
 * ENHANCED DATABASE MIDDLEWARE WITH CONDITIONAL ENVIRONMENT CONFIGURATION
 *
 * This middleware ensures database connection is established before handling API requests
 * with environment-specific retry strategies, connection timeouts, and enhanced logging.
 *
 * Features:
 * - Environment-specific connection strategies (dev vs prod)
 * - Enhanced retry logic with exponential backoff
 * - Comprehensive logging and debugging
 * - Fallback connection strategies for DNS resolution issues
 * - Connection health monitoring
 *
 * @param {(request: NextRequest, ...args: any[]) => Promise<NextResponse>} handler - The API route handler function
 * @returns {(request: NextRequest, ...args: any[]) => Promise<NextResponse>} - The wrapped handler function with database connection assurance
 */
export default function withDatabase(handler) {
  // Get environment-specific configuration using direct process.env access to avoid circular dependencies
  const currentEnv = process.env.NODE_ENV || 'development';
  const enableDebugLogging = process.env.ENABLE_DB_CONNECTION_DEBUG === 'true';

  // Simple MongoDB configuration without circular dependencies
  const mongoConfig = {
    uri: currentEnv === 'production'
      ? (process.env.MONGODB_URI_PROD || process.env.MONGODB_URI || '')
      : (process.env.MONGODB_URI_DEV || process.env.MONGODB_URI || ''),
    dbName: process.env.MONGODB_DB_NAME || 'IMS',
  };

  const dbConnectionLogic = async () => {
    const connectionState = getConnectionState();

    if (enableDebugLogging) {
      console.log(`[Middleware][${currentEnv.toUpperCase()}] Connection state: ${connectionState.state}`);
      console.log(`[Middleware][${currentEnv.toUpperCase()}] MongoDB config: timeouts=${mongoConfig.connectTimeoutMS}ms, pool=${mongoConfig.minPoolSize}-${mongoConfig.maxPoolSize}`);
    }

    // Environment-specific retry strategy
    const maxRetries = currentEnv === 'production' ? 3 : 2;
    const baseDelay = currentEnv === 'production' ? 2000 : 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (connectionState.state === 'error') {
          if (enableDebugLogging) {
            console.log(`[Middleware][${currentEnv.toUpperCase()}] Database connection in error state, reconnecting... (attempt ${attempt}/${maxRetries})`);
          }
          await forceReconnect();
          break;
        } else if (connectionState.state !== 'connected') {
          if (enableDebugLogging) {
            console.log(`[Middleware][${currentEnv.toUpperCase()}] Establishing database connection... (attempt ${attempt}/${maxRetries})`);
          }
          await connectToDatabase();
          break;
        } else {
          if (enableDebugLogging) {
            console.log(`[Middleware][${currentEnv.toUpperCase()}] Database already connected`);
          }
          break;
        }
      } catch (error) {
        if (attempt === maxRetries) {
          throw error; // Re-throw on final attempt
        }

        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        if (enableDebugLogging) {
          console.warn(`[Middleware][${currentEnv.toUpperCase()}] Connection attempt ${attempt} failed, retrying in ${delay}ms:`, error.message);
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  };

  const errorHandler = async (error) => {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Enhanced error logging with environment context
    console.error(`[Middleware][${currentEnv.toUpperCase()}] Database connection error:`, errorMessage);

    // Log additional context for debugging
    if (enableDebugLogging) {
      console.error(`[Middleware][${currentEnv.toUpperCase()}] Error details:`, {
        name: error.name,
        message: errorMessage,
        stack: error.stack,
        mongoConfig: {
          connectTimeout: mongoConfig.connectTimeoutMS,
          serverSelectionTimeout: mongoConfig.serverSelectionTimeoutMS,
          socketTimeout: mongoConfig.socketTimeoutMS,
        }
      });
    }

    await logError(
      'database',
      `Database connection error in API middleware [${currentEnv}]`,
      error
    );

    // Environment-specific error responses
    const errorDetails = currentEnv === 'development' ? {
      message: errorMessage,
      environment: currentEnv,
      mongoConfig: enableDebugLogging ? {
        connectTimeout: mongoConfig.connectTimeoutMS,
        serverSelectionTimeout: mongoConfig.serverSelectionTimeoutMS,
        maxPoolSize: mongoConfig.maxPoolSize,
      } : undefined
    } : undefined;

    // CRITICAL: Always return JSON with proper headers to prevent HTML error pages
    const errorResponse = NextResponse.json(
      {
        success: false,
        error: 'Database connection error. Please try again later.',
        details: errorDetails,
        timestamp: new Date().toISOString(),
        environment: currentEnv
      },
      { status: 503 }
    );

    // Ensure Content-Type is always set to application/json
    errorResponse.headers.set('Content-Type', 'application/json');
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    errorResponse.headers.set('X-Environment', currentEnv);

    return errorResponse;
  };

  // Check handler argument length to determine if it's a static or dynamic route
  if (handler.length <= 1) {
    // Static handler: (request) => ...
    return async (request) => {
      try {
        await dbConnectionLogic();
        return handler(request);
      } catch (error) {
        return errorHandler(error);
      }
    };
  } else {
    // Dynamic handler: (request, context) => ...
    return async (request, ...args) => {
      try {
        await dbConnectionLogic();
        return handler(request, ...args);
      } catch (error) {
        return errorHandler(error);
      }
    };
  }
}