import { NextRequest, NextResponse } from 'next/server';

/**
 * Global API wrapper that ensures all API routes return JSON responses
 * This prevents HTML error pages from being returned in production
 */
export function withApiWrapper<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      const response = await handler(request, ...args);
      
      // Ensure all successful responses have proper JSON headers
      if (!response.headers.get('Content-Type')) {
        response.headers.set('Content-Type', 'application/json');
      }
      
      return response;
    } catch (error) {
      console.error('[API Wrapper] Unhandled error in API route:', error);
      
      // CRITICAL: Always return JSON, never let HTML error pages through
      const errorResponse = NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'An unknown error occurred',
          timestamp: new Date().toISOString(),
          // Only include stack trace in development
          ...(process.env.NODE_ENV === 'development' && {
            stack: error instanceof Error ? error.stack : undefined
          })
        },
        { status: 500 }
      );
      
      // Force JSON content type
      errorResponse.headers.set('Content-Type', 'application/json');
      errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      
      return errorResponse;
    }
  };
}

/**
 * Simplified wrapper for basic API routes
 */
export function safeApiRoute(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withApiWrapper(handler);
}

/**
 * Wrapper for dynamic API routes with parameters
 */
export function safeDynamicApiRoute(
  handler: (request: NextRequest, context: { params: any }) => Promise<NextResponse>
) {
  return withApiWrapper(handler);
}
