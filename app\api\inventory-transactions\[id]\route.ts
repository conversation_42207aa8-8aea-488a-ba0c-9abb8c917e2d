import connectToDatabase from '@/app/lib/mongodb';
import { ObjectId } from 'mongodb';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET handler for a specific inventory transaction
 * Fetches a single inventory transaction by ID
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    const transaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Fetch part details with complete information including businessName
    if (transaction.partId && ObjectId.isValid(transaction.partId)) {
      const part = await db.collection('parts').findOne({
        _id: new ObjectId(transaction.partId)
      }, {
        projection: {
          partNumber: 1,
          name: 1,
          businessName: 1,
          description: 1,
          assemblyCode: 1,
          productCode: 1
        }
      });

      if (part) {
        transaction.part = part;
        // Add convenience fields for easy access in the UI
        transaction.itemName = part.name || 'Unknown Part';
        transaction.businessName = part.businessName || part.name || 'Unknown Part';
        transaction.partNumber = part.partNumber || 'Unknown Part Number';
      }
    }

    // Fetch warehouse details if available
    if (transaction.warehouseId && ObjectId.isValid(transaction.warehouseId)) {
      const warehouse = await db.collection('warehouses').findOne({
        _id: new ObjectId(transaction.warehouseId)
      }, {
        projection: {
          name: 1,
          location_id: 1,
          location: 1
        }
      });

      if (warehouse) {
        transaction.warehouse = warehouse;
        transaction.warehouseName = warehouse.name || 'Unknown Warehouse';
      }
    }

    // Fetch location details for from/to movements (V4 schema)
    if (transaction.from?.locationId && ObjectId.isValid(transaction.from.locationId)) {
      const location = await db.collection('locations').findOne({
        _id: new ObjectId(transaction.from.locationId)
      }, {
        projection: {
          name: 1,
          description: 1,
          locationType: 1,
          warehouseId: 1
        }
      });

      if (location) {
        transaction.from.location = location;
        transaction.from.locationName = location.name || 'Unknown Location';

        // Also fetch the warehouse for this location
        if (location.warehouseId && ObjectId.isValid(location.warehouseId)) {
          const warehouse = await db.collection('warehouses').findOne({
            _id: new ObjectId(location.warehouseId)
          }, {
            projection: { name: 1 }
          });

          if (warehouse) {
            transaction.from.warehouse = warehouse;
            transaction.from.warehouseName = warehouse.name || 'Unknown Warehouse';
          }
        }
      }
    }

    if (transaction.to?.locationId && ObjectId.isValid(transaction.to.locationId)) {
      const location = await db.collection('locations').findOne({
        _id: new ObjectId(transaction.to.locationId)
      }, {
        projection: {
          name: 1,
          description: 1,
          locationType: 1,
          warehouseId: 1
        }
      });

      if (location) {
        transaction.to.location = location;
        transaction.to.locationName = location.name || 'Unknown Location';

        // Also fetch the warehouse for this location
        if (location.warehouseId && ObjectId.isValid(location.warehouseId)) {
          const warehouse = await db.collection('warehouses').findOne({
            _id: new ObjectId(location.warehouseId)
          }, {
            projection: { name: 1 }
          });

          if (warehouse) {
            transaction.to.warehouse = warehouse;
            transaction.to.warehouseName = warehouse.name || 'Unknown Warehouse';
          }
        }
      }
    }

    // Fetch user details
    if (transaction.userId && ObjectId.isValid(transaction.userId)) {
      const user = await db.collection('users').findOne({
        _id: new ObjectId(transaction.userId)
      }, {
        projection: {
          username: 1,
          first_name: 1,
          last_name: 1
        }
      });

      if (user) {
        transaction.user = user;
        transaction.userName = user.first_name && user.last_name
          ? `${user.first_name} ${user.last_name}`
          : user.username || 'Unknown User';
      }
    }

    return NextResponse.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error(`Error fetching transaction:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch transaction' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for a specific inventory transaction
 * Updates an existing inventory transaction
 */
export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const data = await request.json() as {
      notes?: string;
      referenceNumber?: string;
      [key: string]: any;
    };

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Find the existing transaction
    const existingTransaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!existingTransaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Only allow updating notes and reference number
    const updateData = {
      notes: data.notes,
      referenceNumber: data.referenceNumber
    };

    const result = await db.collection('transactions').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return NextResponse.json({
      success: true,
      data: { ...existingTransaction, ...updateData }
    });
  } catch (error) {
    console.error(`Error updating transaction:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to update transaction' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for a specific inventory transaction
 * DISABLED: Transactions are immutable in event-sourced inventory system
 */
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Log the deletion attempt for audit purposes
    console.warn(`[API] Attempted deletion of transaction ${id} - BLOCKED: Transactions are immutable`);

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Return 405 Method Not Allowed with clear explanation
    return NextResponse.json(
      {
        success: false,
        error: 'Transaction deletion is not allowed. Inventory transactions are immutable for audit integrity. To correct errors, create a reversing transaction instead.',
        code: 'TRANSACTION_IMMUTABLE',
        guidance: 'Use the stock adjustment feature to create a correcting transaction that reverses the effect of this transaction.'
      },
      {
        status: 405,
        headers: {
          'Allow': 'GET, PUT',
          'X-Deprecation-Notice': 'Transaction deletion disabled for data integrity'
        }
      }
    );
  } catch (error) {
    console.error(`Error processing transaction deletion request:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to process request' },
      { status: 500 }
    );
  }
}