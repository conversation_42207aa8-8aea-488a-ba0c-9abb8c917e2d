'use client';

import React from 'react';
import { MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/app/components/forms/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/app/components/navigation/DropdownMenu';

export interface ActionMenuAction {
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'destructive';
}

export interface ActionMenuProps {
  actions: ActionMenuAction[];
  triggerClassName?: string;
}

export const ActionMenu: React.FC<ActionMenuProps> = ({ 
  actions, 
  triggerClassName = '' 
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={`h-8 w-8 p-0 ${triggerClassName}`}
          aria-label="Open menu"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {actions.map((action, index) => (
          <DropdownMenuItem
            key={index}
            onClick={action.onClick}
            className={action.variant === 'destructive' ? 'text-destructive' : ''}
          >
            {action.icon}
            <span className="ml-2">{action.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Convenience function to create common actions
export const createViewAction = (onView: () => void): ActionMenuAction => ({
  label: 'View',
  icon: <Eye className="h-4 w-4" />,
  onClick: onView,
});

export const createEditAction = (onEdit: () => void): ActionMenuAction => ({
  label: 'Edit',
  icon: <Edit className="h-4 w-4" />,
  onClick: onEdit,
});

export const createDeleteAction = (onDelete: () => void): ActionMenuAction => ({
  label: 'Delete',
  icon: <Trash2 className="h-4 w-4" />,
  onClick: onDelete,
  variant: 'destructive' as const,
});
