import { Types } from 'mongoose';
import { Product, IProduct, IHierarchicalComponent } from '../models/product.model';
import Assembly from '../models/assembly.model';
import { connectToDatabase } from '../lib/mongodb';

/**
 * Interface for migration result tracking
 */
export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: Array<{
    productId: string;
    productCode: string;
    error: string;
  }>;
}

/**
 * Interface for rollback data
 */
export interface RollbackData {
  productId: string;
  productCode: string;
  originalAssemblyId?: Types.ObjectId | null | undefined;
  originalPartId?: Types.ObjectId | null | undefined;
  migratedComponents: IHierarchicalComponent[];
}

/**
 * Migrate a single product from flat assemblyId/partId structure to hierarchical components
 */
export async function migrateProductToHierarchical(
  product: IProduct
): Promise<{ success: boolean; components?: IHierarchicalComponent[]; error?: string }> {
  try {
    // If product already has components, skip migration
    if (product.components && product.components.length > 0) {
      return { success: true, components: product.components };
    }

    // If product has no assemblyId or partId, create empty components array
    if (!product.assemblyId && !product.partId) {
      return { success: true, components: [] };
    }

    const components: IHierarchicalComponent[] = [];

    // Migrate assemblyId to hierarchical component
    if (product.assemblyId) {
      // Verify the assembly exists
      await connectToDatabase();
      const assembly = await (Assembly.findById as any)(product.assemblyId).lean();
      
      if (!assembly) {
        return { 
          success: false, 
          error: `Referenced assembly ${product.assemblyId} not found` 
        };
      }

      components.push({
        assemblyId: product.assemblyId,
        quantityRequired: 1, // Default quantity for migrated assemblies
        children: [] // Start with empty children - can be populated later if needed
      });
    }

    // Note: partId is not directly migrated as the new structure focuses on assemblies
    // Parts should be referenced through assemblies in the hierarchical structure

    return { success: true, components };
  } catch (error) {
    return { 
      success: false, 
      error: `Migration error: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Migrate all products from flat structure to hierarchical components
 */
export async function migrateAllProductsToHierarchical(
  dryRun: boolean = true
): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: true,
    migratedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: []
  };

  const rollbackData: RollbackData[] = [];

  try {
    await connectToDatabase();

    // Get all products that need migration
    const products = await (Product.find as any)({
      $or: [
        { components: { $exists: false } },
        { components: { $size: 0 } },
        { components: null }
      ]
    }).lean() as IProduct[];

    console.log(`Found ${products.length} products to migrate`);

    for (const product of products) {
      try {
        const migrationResult = await migrateProductToHierarchical(product);

        if (!migrationResult.success) {
          result.errorCount++;
          result.errors.push({
            productId: product._id.toString(),
            productCode: product.productCode,
            error: migrationResult.error || 'Unknown migration error'
          });
          continue;
        }

        // Skip if no changes needed
        if (!migrationResult.components || migrationResult.components.length === 0) {
          if (!product.assemblyId && !product.partId) {
            result.skippedCount++;
            continue;
          }
        }

        // Store rollback data
        rollbackData.push({
          productId: product._id.toString(),
          productCode: product.productCode,
          originalAssemblyId: product.assemblyId,
          originalPartId: product.partId,
          migratedComponents: migrationResult.components || []
        });

        // Perform the actual migration if not a dry run
        if (!dryRun) {
          await (Product.findByIdAndUpdate as any)(
            product._id,
            {
              $set: {
                components: migrationResult.components || []
              }
            },
            { new: true }
          );
        }

        result.migratedCount++;
        
        if (result.migratedCount % 100 === 0) {
          console.log(`Migrated ${result.migratedCount} products...`);
        }

      } catch (error) {
        result.errorCount++;
        result.errors.push({
          productId: product._id.toString(),
          productCode: product.productCode,
          error: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    // Save rollback data if not a dry run
    if (!dryRun && rollbackData.length > 0) {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const rollbackFile = path.join(process.cwd(), 'migration-rollback-data.json');
      await fs.writeFile(rollbackFile, JSON.stringify(rollbackData, null, 2));
      console.log(`Rollback data saved to: ${rollbackFile}`);
    }

    result.success = result.errorCount === 0;
    
    console.log(`Migration ${dryRun ? 'dry run ' : ''}completed:`);
    console.log(`- Migrated: ${result.migratedCount}`);
    console.log(`- Skipped: ${result.skippedCount}`);
    console.log(`- Errors: ${result.errorCount}`);

    return result;

  } catch (error) {
    result.success = false;
    result.errors.push({
      productId: 'SYSTEM',
      productCode: 'SYSTEM',
      error: `System error: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
    
    return result;
  }
}

/**
 * Rollback migration using saved rollback data
 */
export async function rollbackMigration(rollbackFilePath?: string): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: true,
    migratedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: []
  };

  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const rollbackFile = rollbackFilePath || path.join(process.cwd(), 'migration-rollback-data.json');
    
    // Check if rollback file exists
    try {
      await fs.access(rollbackFile);
    } catch {
      throw new Error(`Rollback file not found: ${rollbackFile}`);
    }

    // Read rollback data
    const rollbackDataRaw = await fs.readFile(rollbackFile, 'utf-8');
    const rollbackData: RollbackData[] = JSON.parse(rollbackDataRaw);

    await connectToDatabase();

    console.log(`Rolling back ${rollbackData.length} products...`);

    for (const data of rollbackData) {
      try {
        await (Product.findByIdAndUpdate as any)(
          data.productId,
          {
            $set: {
              assemblyId: data.originalAssemblyId,
              partId: data.originalPartId
            },
            $unset: {
              components: 1
            }
          }
        );

        result.migratedCount++;

        if (result.migratedCount % 100 === 0) {
          console.log(`Rolled back ${result.migratedCount} products...`);
        }

      } catch (error) {
        result.errorCount++;
        result.errors.push({
          productId: data.productId,
          productCode: data.productCode,
          error: `Rollback error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    result.success = result.errorCount === 0;
    
    console.log(`Rollback completed:`);
    console.log(`- Rolled back: ${result.migratedCount}`);
    console.log(`- Errors: ${result.errorCount}`);

    return result;

  } catch (error) {
    result.success = false;
    result.errors.push({
      productId: 'SYSTEM',
      productCode: 'SYSTEM',
      error: `System error: ${error instanceof Error ? error.message : 'Unknown error'}`
    });
    
    return result;
  }
}

/**
 * Validate data integrity after migration
 */
export async function validateMigrationIntegrity(): Promise<{
  success: boolean;
  issues: Array<{
    productId: string;
    productCode: string;
    issue: string;
  }>;
}> {
  const issues: Array<{
    productId: string;
    productCode: string;
    issue: string;
  }> = [];

  try {
    await connectToDatabase();

    // Find products with components
    const products = await (Product.find as any)({
      components: { $exists: true, $ne: [] }
    }).lean() as IProduct[];

    for (const product of products) {
      if (!product.components) continue;

      // Check for circular references
      const hasCircularRef = !validateNoCircularReferences(product.components);
      if (hasCircularRef) {
        issues.push({
          productId: product._id.toString(),
          productCode: product.productCode,
          issue: 'Circular reference detected in component hierarchy'
        });
      }

      // Validate that all referenced assemblies exist
      const assemblyIds = extractAllAssemblyIds(product.components);
      for (const assemblyId of assemblyIds) {
        const assembly = await (Assembly.findById as any)(assemblyId).lean();
        if (!assembly) {
          issues.push({
            productId: product._id.toString(),
            productCode: product.productCode,
            issue: `Referenced assembly ${assemblyId} not found`
          });
        }
      }
    }

    return { success: issues.length === 0, issues };

  } catch (error) {
    return {
      success: false,
      issues: [{
        productId: 'SYSTEM',
        productCode: 'SYSTEM',
        issue: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }]
    };
  }
}

/**
 * Helper function to validate no circular references
 */
function validateNoCircularReferences(
  components: IHierarchicalComponent[], 
  visited: Set<string> = new Set()
): boolean {
  for (const component of components) {
    const assemblyIdStr = component.assemblyId.toString();
    
    if (visited.has(assemblyIdStr)) {
      return false;
    }
    
    const newVisited = new Set(visited);
    newVisited.add(assemblyIdStr);
    
    if (component.children && component.children.length > 0) {
      if (!validateNoCircularReferences(component.children, newVisited)) {
        return false;
      }
    }
  }
  
  return true;
}

/**
 * Helper function to extract all assembly IDs from hierarchical components
 */
function extractAllAssemblyIds(components: IHierarchicalComponent[]): Types.ObjectId[] {
  const assemblyIds: Types.ObjectId[] = [];
  
  for (const component of components) {
    assemblyIds.push(component.assemblyId);
    
    if (component.children && component.children.length > 0) {
      assemblyIds.push(...extractAllAssemblyIds(component.children));
    }
  }
  
  return assemblyIds;
}
