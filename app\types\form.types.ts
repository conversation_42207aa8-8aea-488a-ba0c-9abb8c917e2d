/**
 * Form Types for Trend IMS
 * 
 * This file contains type definitions for form request bodies and form data structures
 * used in API routes and form components.
 */

// ============================================================================
// Inventory Form Types
// ============================================================================

/**
 * Inventory request body for creating new inventory records
 */
export interface InventoryRequestBody {
  item_id: string;
  item_type: 'Part' | 'Assembly' | 'Product';
  warehouse_id: string;
  quantity_on_hand: number;
  quantity_allocated?: number;
  location_in_warehouse?: string | null;
  reorder_level?: number | null;
  safety_stock_level?: number | null;
  maximum_stock_level?: number | null;
  average_daily_usage?: number | null;
  abc_classification?: string | null;
  notes?: string | null;
}

/**
 * Inventory allocation request body for managing stock allocations
 */
export interface InventoryAllocationRequestBody {
  id?: string; // Inventory record ID
  allocation_quantity: number; // Positive to allocate, negative to deallocate
  reference_number?: string | null;
  notes?: string | null;
}

/**
 * Stock update request body for updating inventory levels
 */
export interface StockUpdateRequestBody {
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseId: string;
  quantityChange: number;
  transactionType?: string;
  notes?: string;
  userId: string;
  referenceId?: string;
  referenceModel?: string;
}

/**
 * Inventory transaction creation request
 */
export interface CreateTransactionRequestBody {
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseId: string;
  transactionType?: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity?: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;
}

// ============================================================================
// User Form Types
// ============================================================================

/**
 * User creation request body
 */
export interface CreateUserRequestBody {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  first_name?: string; // Database field name
  last_name?: string; // Database field name
  fullName?: string; // Legacy compatibility
  password_hash?: string; // Database field name
  roles: string[];
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  isActive?: boolean;
}

/**
 * User update request body
 */
export interface UpdateRequestBody {
  username?: string;
  email?: string;
  password?: string; // For password updates
  password_hash?: string; // Internal use - database field name
  passwordHash?: string; // Legacy compatibility
  firstName?: string;
  lastName?: string;
  first_name?: string; // Database field name
  last_name?: string; // Database field name
  roles?: string[];
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  isActive?: boolean;
}

// ============================================================================
// Supplier Form Types
// ============================================================================

/**
 * Supplier creation request body
 */
export interface CreateSupplierRequestBody {
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  specialty?: string[];
  rating?: number;
  paymentTerms?: string;
  deliveryTerms?: string;
  isActive?: boolean;
}

/**
 * Supplier update request body
 */
export interface UpdateSupplierRequestBody {
  name?: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  specialty?: string[];
  rating?: number;
  paymentTerms?: string;
  deliveryTerms?: string;
  isActive?: boolean;
}

// ============================================================================
// Warehouse Form Types
// ============================================================================

/**
 * Warehouse creation request body
 */
export interface CreateWarehouseRequestBody {
  warehouseCode: string;
  name: string;
  description?: string;
  address: string;
  location?: string; // Legacy compatibility
  managerId?: string | null;
  manager?: string; // Legacy compatibility
  contact?: string; // Legacy compatibility
  isActive?: boolean;
  capacity?: number;
  zones?: WarehouseZoneRequestBody[];
}

/**
 * Warehouse zone request body
 */
export interface WarehouseZoneRequestBody {
  zoneName: string;
  zoneType: 'receiving' | 'storage' | 'picking' | 'shipping' | 'quarantine';
  capacity?: number;
  temperatureControlled?: boolean;
  securityLevel?: 'low' | 'medium' | 'high';
}

// ============================================================================
// Part Form Types
// ============================================================================

/**
 * Part form data structure for hierarchical parts form
 */
export interface FormPartData {
  _id?: string;
  id?: string; // For React Hook Form key
  partId: string;
  name: string;
  description?: string;
  quantityRequired: number;
  isExpanded?: boolean;
  category?: string | null;
  currentStock?: number | null;
  reorderLevel?: number | null;
  minimumStockLevel?: number | null;
  supplier?: string | null;
  technicalSpecs?: string | null;
  unitOfMeasure?: string | null;
  costPrice?: number | null;
  cost?: number | null;
  location?: string | null;
  isManufactured?: boolean;
  isAssembly?: boolean | null;
  schemaVersion?: number;
  subParts?: FormPartData[];
  children?: FormPartData[];
  notes?: string;
  partNumber?: string | null;
  partDisplayIdentifier?: string;
  additionalAttributes?: Record<string, any> | null;
}

/**
 * Hierarchical parts form props
 */
export interface HierarchicalPartsFormProps {
  initialData?: {
    _id?: string;
    assemblyCode?: string;
    assemblyId?: string;
    name?: string;
    description?: string | null | undefined;
    status?: string;
    productId?: string | null | undefined;
    parentId?: string | null | undefined;
    isTopLevel?: boolean;
    version?: string | number;
    manufacturingInstructions?: string | null | undefined;
    estimatedBuildTime?: string | null | undefined;
    estimatedManufacturingTime?: string;
    partsRequired?: FormPartData[];
    parts?: FormPartData[];
    assembly_stage?: string;
    category?: string;
    notes?: string;
    assemblyYield?: number;
    assembly_id?: string;
  } | undefined;
  onSubmit?: (data: any) => void;
  onFormSubmit?: (data: any) => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
}

/**
 * Part form data for part creation/editing
 */
export interface PartFormData {
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string;
  technicalSpecs?: string;
  isManufactured: boolean;
  reorderLevel?: number;
  status: 'active' | 'inactive' | 'obsolete';

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  supplierId?: string;
  unitOfMeasure?: string;
  costPrice?: number;
  categoryId?: string;
  inventory: {
    currentStock: number;
    location?: string;
    lastStockUpdate?: Date | null;
    warehouseId?: string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: 'A' | 'B' | 'C';
  };
}

// ============================================================================
// Assembly Form Types
// ============================================================================

/**
 * Assembly creation request body
 */
export interface CreateAssemblyRequestBody {
  assemblyCode: string;
  name: string;
  description?: string;
  productId?: string;
  parentId?: string;
  isTopLevel: boolean;
  partsRequired: AssemblyPartRequirementRequestBody[];
  subAssemblies?: SubAssemblyRequirementRequestBody[];
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete' | 'archived';
  version: number;
  manufacturingInstructions?: string;
  estimatedBuildTime?: string;
  manufacturingLeadTime?: string;
  notes?: string;
  createdBy?: string;
}

/**
 * Assembly part requirement request body
 */
export interface AssemblyPartRequirementRequestBody {
  partId: string;
  quantityRequired: number;
  unitOfMeasure?: string;
  notes?: string;
  isOptional?: boolean;
  alternativePartIds?: string[];
}

/**
 * Sub-assembly requirement request body
 */
export interface SubAssemblyRequirementRequestBody {
  subAssemblyId: string;
  quantityRequired: number;
  notes?: string;
  isOptional?: boolean;
}

// ============================================================================
// Work Order Form Types
// ============================================================================

/**
 * Work order creation request body
 */
export interface CreateWorkOrderRequestBody {
  assemblyId?: string;
  partIdToManufacture?: string;
  productId?: string;
  quantity: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Date;
  assignedTo?: string;
  notes?: string;
  estimatedDuration?: number;
  costEstimate?: number;
}

/**
 * Work order update request body
 */
export interface UpdateWorkOrderRequestBody {
  assemblyId?: string;
  partIdToManufacture?: string;
  productId?: string;
  quantity?: number;
  status?: 'planned' | 'released' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled' | 'closed';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: Date;
  startDate?: Date;
  assignedTo?: string;
  notes?: string;
  completedAt?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  costEstimate?: number;
  actualCost?: number;
}

// ============================================================================
// Batch Form Types
// ============================================================================

/**
 * Batch form data structure
 */
export interface BatchFormData {
  batchCode?: string;
  partId?: string;
  assemblyId?: string;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string;
  endDate?: string;
  status: string;
  notes?: string;
  workOrderId: string;
}

// ============================================================================
// Search and Filter Form Types
// ============================================================================

/**
 * Generic search form data
 */
export interface SearchFormData {
  searchTerm?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Inventory search form data
 */
export interface InventorySearchFormData extends SearchFormData {
  filters?: {
    itemType?: ('Part' | 'Assembly' | 'Product')[];
    status?: string[];
    warehouseId?: string[];
    categoryId?: string[];
    supplierId?: string[];
    stockStatus?: ('in_stock' | 'low_stock' | 'out_of_stock')[];
    abcClassification?: ('A' | 'B' | 'C')[];
    dateRange?: {
      start: Date;
      end: Date;
    };
  };
}
