"use client";

/**
 * Standardized DataTable Column Definitions
 * 
 * This file contains reusable column definitions for all table types in the application.
 * Each column definition is optimized for mobile responsiveness, accessibility, and functionality.
 */

import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/app/components/navigation/DropdownMenu';
import { countHierarchicalParts } from '@/app/utils/assemblyDataTransform';
import { Progress } from '@/app/components/data-display/progress';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';
import { EditPartAction, DeletePartAction } from '@/app/components/actions/parts';
// UPDATED: Use generic actions and modals instead of specific ones
import { DeleteItemAction } from '@/app/components/actions/DeleteItemAction';
import { DuplicateItemAction } from '@/app/components/actions/DuplicateItemAction';
import { QuickEditItemAction } from '@/app/components/actions/QuickEditItemAction';
// REMOVED: Specific view buttons - use generic Button with callbacks instead
import { ViewPartButton } from '@/app/components/modals/ViewPartModal';
import { DataTableColumn } from './types';
import { Eye, MoreHorizontal, PencilIcon, Trash2, Edit, ArrowDownLeft, ArrowUpRight, RotateCcw, FileText } from 'lucide-react';
import { format } from 'date-fns';
import Link from 'next/link';
import { cn } from '@/app/lib/utils';

// ============================================================================
// PRODUCTS TABLE COLUMN DEFINITIONS
// ============================================================================

export interface ProductColumnData {
  _id: string;
  id: string;
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  assemblyId?: string | null;
  partId?: string | null;
  currentStock: number;
  reorderLevel: number;
  supplierManufacturer: string;
  createdAt: Date;
  updatedAt: Date;
  inventory: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    currentStock: number; // Required for backward compatibility
    warehouseId?: string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
}

export interface ProductsTableActions {
  onView?: (product: ProductColumnData) => void;
  onEdit?: (product: ProductColumnData) => void;
  onDelete?: (product: ProductColumnData) => void;
  onRefresh?: () => void;
}

/**
 * Products Table Columns - Simple Mode
 * Used for basic product listings with essential information
 */
const createProductsSimpleColumns = (
  actions: ProductsTableActions = {}
): DataTableColumn<ProductColumnData>[] => [
  {
    accessorKey: 'name',
    header: 'Name',
    mobilePriority: 1,
    searchable: true,
    cell: ({ row }) => (
      <div className="font-medium">
        {row.original.name}
      </div>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="name" className="font-semibold">{value}</div>
        <div key="code" className="text-sm text-muted-foreground">
          {row.id || row.productCode}
        </div>
      </div>
    ),
  },
  {
    id: 'productId',
    accessorFn: (row) => row.id || row.productCode,
    header: 'ID',
    mobilePriority: 2,
    cell: ({ row }) => (
      <Badge variant="outline" className="font-mono">
        {row.original.id || row.original.productCode}
      </Badge>
    ),
  },
  {
    id: 'stock',
    accessorFn: (row) => row.currentStock ?? row.inventory?.currentStock ?? 0,
    header: 'Stock',
    hideOnMobile: true,
    cell: ({ row }) => {
      const stock = row.original.currentStock ?? row.original.inventory?.currentStock ?? 0;
      return (
        <span className={stock > 0 ? 'text-success' : 'text-destructive'}>
          {stock}
        </span>
      );
    },
  },
  {
    accessorKey: 'supplierManufacturer',
    header: 'Supplier',
    hideOnMobile: true,
    cell: ({ row }) => row.original.supplierManufacturer || '—',
  },
  {
    accessorKey: 'reorderLevel',
    header: 'Reorder Level',
    hideOnMobile: true,
    cell: ({ row }) => row.original.reorderLevel ?? '—',
  },
  {
    id: 'actions',
    header: 'Actions',
    enableSorting: false,
    enableHiding: false,
    cell: ({ row }) => (
      <div className="flex items-center justify-end gap-1">
        <Button
          key={`view-${row.original._id || row.original.id}`}
          variant="ghost"
          size="sm"
          onClick={() => actions.onView?.(row.original)}
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          key={`edit-${row.original._id || row.original.id}`}
          variant="ghost"
          size="sm"
          onClick={() => actions.onEdit?.(row.original)}
        >
          <PencilIcon className="h-4 w-4" />
        </Button>
        <Button
          key={`delete-${row.original._id || row.original.id}`}
          variant="ghost"
          size="sm"
          onClick={() => actions.onDelete?.(row.original)}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

/**
 * Products Table Columns - Complex Mode
 * Used for detailed product management with assembly relationships
 */
const createProductsComplexColumns = (
  actions: ProductsTableActions = {}
): DataTableColumn<ProductColumnData>[] => [
  {
    accessorKey: 'name',
    header: 'Name',
    mobilePriority: 1,
    searchable: true,
    cell: ({ row }) => (
      <Button
        variant="ghost"
        size="sm"
        className="font-medium hover:underline p-0 h-auto"
        onClick={() => actions.onView?.(row.original)}
      >
        {row.original.name}
      </Button>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="name" className="font-semibold">{value}</div>
        <div key="code" className="text-sm text-muted-foreground">
          {row.id || row.productCode}
        </div>
      </div>
    ),
  },
  {
    id: 'productId',
    header: 'Product ID',
    accessorFn: (row) => row.id || row.productCode,
    mobilePriority: 2,
    cell: ({ row }) => (
      <Badge variant="outline" className="font-mono">
        {row.original.id || row.original.productCode}
      </Badge>
    ),
  },
  {
    accessorKey: 'assemblyId',
    header: 'Main Assembly',
    hideOnMobile: true,
    cell: ({ row }) => {
      if (row.original.assemblyId) {
        return (
          <Link
            href={`/assemblies/${row.original.assemblyId}`}
            className="text-primary hover:underline"
          >
            View Assembly
          </Link>
        );
      }
      return '—';
    },
  },
  {
    accessorKey: 'partId',
    header: 'Components',
    hideOnMobile: true,
    cell: ({ row }) => (
      <Badge>
        {/* TODO: Add components count when available */}
        0 parts
      </Badge>
    ),
  },
  {
    id: 'actions',
    header: 'Actions',
    enableSorting: false,
    enableHiding: false,
    cell: ({ row }) => (
      <div className="flex items-center gap-1">
        {/* View Action */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => actions.onView?.(row.original)}
        >
          <Eye className="h-4 w-4" />
        </Button>

        {/* Edit Action */}
        <Button
          variant="ghost"
          size="sm"
          asChild
        >
          <Link href={`/products/${row.original._id}/edit`}>
            <PencilIcon className="h-4 w-4" />
          </Link>
        </Button>

        {/* Delete Action */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => actions.onDelete?.(row.original)}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
];

// ============================================================================
// ASSEMBLIES TABLE COLUMN DEFINITIONS
// ============================================================================

export interface AssemblyColumnData {
  _id: string;
  name: string;
  assemblyCode: string;
  status: 'active' | 'pending_review' | 'obsolete' | 'in_production';
  // Support hierarchical parts structure for consistent counting
  partsRequired?: Array<{
    partId: string | { _id: string; [key: string]: any };
    partDetails?: {
      _id: string;
      name: string;
      inventory?: {
        stockLevels?: {
          raw: number;
          hardening: number;
          grinding: number;
          finished: number;
          rejected: number;
        };
        currentStock?: number;
      };
      [key: string]: any
    };
    quantityRequired: number;
    unitOfMeasure?: string;
    children?: Array<{
      partId: string | { _id: string; [key: string]: any };
      partDetails?: {
        _id: string;
        name: string;
        inventory?: {
          stockLevels?: {
            raw: number;
            hardening: number;
            grinding: number;
            finished: number;
            rejected: number;
          };
          currentStock?: number;
        };
        [key: string]: any
      };
      quantityRequired: number;
      unitOfMeasure?: string;
      children?: any[]; // Support deeper nesting
    }>;
  }>;
  // Additional assembly-specific fields
  description?: string;
  productId?: string | null | undefined;
  parentId?: string | null | undefined;
  isTopLevel?: boolean | undefined;
  version?: number | undefined;
  manufacturingInstructions?: string | null | undefined;
  estimatedBuildTime?: string | null | undefined;
  createdAt?: string | Date | undefined;
  updatedAt?: string | Date | undefined;
}

export interface AssembliesTableActions {
  onView?: (assembly: AssemblyColumnData) => void;
  onEdit?: (assembly: AssemblyColumnData) => void;
  onDelete?: (assembly: AssemblyColumnData) => void;
  onDuplicate?: (assembly: AssemblyColumnData) => void;
  onRefresh?: () => void;
}

/**
 * Assemblies Table Columns
 * Includes expandable row functionality and status indicators
 */
const createAssembliesColumns = (
  actions: AssembliesTableActions = {},
  simple: boolean = false
): DataTableColumn<AssemblyColumnData>[] => [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    mobilePriority: 1,
    searchable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-2 font-medium">
        {simple ? (
          row.original.name
        ) : (
          <Button
            variant="ghost"
            size="sm"
            className="hover:underline p-0 h-auto font-medium"
            onClick={() => actions.onView?.(row.original)}
          >
            {row.original.name}
          </Button>
        )}
      </div>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="name" className="font-semibold">{value}</div>
        <div key="code" className="text-sm text-muted-foreground">
          {row.assemblyCode}
        </div>
      </div>
    ),
  },
  {
    id: 'assemblyCode',
    header: 'Assembly Code',
    accessorKey: 'assemblyCode',
    mobilePriority: 2,
    cell: ({ row }) => (
      <Badge variant="outline" className="font-mono">
        {row.original.assemblyCode}
      </Badge>
    ),
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    mobilePriority: 3,
    cell: ({ row }) => (
      <AssemblyStatusBadge assembly={row.original as any} />
    ),
  },
  {
    id: 'parts',
    header: 'Parts',
    accessorFn: (row) => countHierarchicalParts(row.partsRequired || []),
    hideOnMobile: true,
    cell: ({ row }) => (
      <PartsCountBadge assembly={row.original as any} />
    ),
  },
  {
    id: 'inventoryStatus',
    header: 'Inventory Status',
    accessorFn: (row) => {
      // Calculate inventory status based on parts availability
      const parts = row.partsRequired || [];
      if (parts.length === 0) return 'No Parts';
      
      const availableParts = parts.filter(part =>
        (part.partDetails?.inventory?.currentStock || 0) >= part.quantityRequired
      ).length;
      
      return `${availableParts}/${parts.length}`;
    },
    hideOnMobile: true,
    cell: ({ row }) => {
      const parts = row.original.partsRequired || [];
      if (parts.length === 0) {
        return <Badge variant="secondary">No Parts</Badge>;
      }
      
      const availableParts = parts.filter(part =>
        (part.partDetails?.inventory?.currentStock || 0) >= part.quantityRequired
      ).length;
      
      const percentage = (availableParts / parts.length) * 100;
      
      return (
        <div className="flex items-center gap-2">
          <Progress value={percentage} className="w-16" />
          <span className="text-sm text-muted-foreground">
            {availableParts}/{parts.length}
          </span>
        </div>
      );
    },
  },
  ...(simple ? [] : [{
    id: 'actions',
    header: 'Actions',
    enableSorting: false,
    enableHiding: false,
    cell: ({ row }: { row: { original: AssemblyColumnData } }) => (
      <div className="flex items-center justify-end gap-1">
        <QuickEditItemAction
          key={`edit-${row.original._id}`}
          item={row.original as any}
          itemType="assembly"
          {...(actions.onRefresh && { onSuccess: actions.onRefresh })}
        />
        <DuplicateItemAction
          key={`duplicate-${row.original._id}`}
          item={row.original as any}
          itemType="assembly"
          {...(actions.onRefresh && { onSuccess: actions.onRefresh })}
        />
        <DeleteItemAction
          key={`delete-${row.original._id}`}
          item={row.original as any}
          itemType="assembly"
          {...(actions.onRefresh && { onSuccess: actions.onRefresh })}
        />
      </div>
    ),
  }] as DataTableColumn<AssemblyColumnData>[]),
];

// ============================================================================
// INVENTORY TABLE COLUMN DEFINITIONS
// ============================================================================

export interface InventoryColumnData {
  _id: string;
  id: string;
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string;
  unitOfMeasure?: string;
  currentStock?: number;
  reorderLevel?: number;

  // NEW: Part Master Data Planning Parameters (at top level)
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  supplier?: {
    _id: string;
    name: string;
    contactInfo?: string;
  };
  inventory?: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    currentStock?: number; // Backward compatibility
    warehouseId?: string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
  // Add stockLevels at top level for easier access
  stockLevels?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  location_in_warehouse?: string;
  quantity_on_hand?: number;
  quantity_allocated?: number;
  quantity_available?: number;
  safety_stock_level?: number;
  maximum_stock_level?: number;
  notes?: string;
  location?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InventoryTableActions {
  onView?: (item: InventoryColumnData) => void;
  onEdit?: (item: InventoryColumnData) => void;
  onDelete?: (item: InventoryColumnData) => void;
  onRefresh?: () => void; // Add refresh callback for EditPartAction
}

/**
 * Inventory Table Columns
 * Used for inventory management with stock levels and supplier information
 */
const createInventoryColumns = (
  actions: InventoryTableActions = {}
): DataTableColumn<InventoryColumnData>[] => [
  {
    id: 'partNumber',
    header: 'Part Number',
    accessorKey: 'partNumber',
    mobilePriority: 1,
    searchable: true,
    cell: ({ row }) => (
      <div className="font-mono text-xs">
        <div className="inline-block px-2 py-1 bg-primary/5 dark:bg-primary/10 rounded border border-primary/10 dark:border-primary/20">
          {row.original.partNumber}
        </div>
      </div>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="part-number" className="font-mono text-sm font-semibold">{value}</div>
        <div key="name" className="text-sm text-muted-foreground">{row.name}</div>
      </div>
    ),
  },
  {
    id: 'name',
    header: 'Part Name',
    accessorKey: 'name',
    mobilePriority: 2,
    searchable: true,
    cell: ({ row }) => (
      <div>
        <div className="font-medium text-foreground">
          {row.original.name || row.original.name}
        </div>
        {row.original.businessName &&
         row.original.businessName !== row.original.name && (
          <div className="text-xs text-muted-foreground">
            {row.original.businessName}
          </div>
        )}
        <div className="text-sm text-muted-foreground md:hidden">
          {row.original.description}
        </div>
      </div>
    ),
  },
  {
    id: 'currentStock',
    header: 'Stock Levels',
    accessorFn: (row) => row.inventory?.stockLevels?.finished ?? row.inventory?.currentStock ?? row.currentStock ?? 0,
    mobilePriority: 3,
    cell: ({ row }) => {
      const stockLevels = row.original.inventory?.stockLevels;
      const finishedStock = stockLevels?.finished ?? row.original.inventory?.currentStock ?? row.original.currentStock ?? 0;
      const unit = row.original.unitOfMeasure || 'pcs';

      // Calculate total stock across all levels
      const totalStock = stockLevels ?
        stockLevels.raw + stockLevels.hardening + stockLevels.grinding + stockLevels.finished + stockLevels.rejected :
        finishedStock;

      return (
        <div className="text-center">
          <div className={finishedStock > 0 ? 'text-success font-medium' : 'text-destructive font-medium'}>
            Finished: {finishedStock} {unit}
          </div>
          {stockLevels && (
            <div className="text-xs text-muted-foreground mt-1">
              <div title={`Raw: ${stockLevels.raw}, Hardening: ${stockLevels.hardening}, Grinding: ${stockLevels.grinding}, Rejected: ${stockLevels.rejected}`}>
                Total: {totalStock} {unit}
              </div>
            </div>
          )}
        </div>
      );
    },
    mobileRender: (value, row) => (
      <div>
        <div key="finished-stock" className="font-semibold">
          {row.inventory?.stockLevels?.finished ?? row.inventory?.currentStock ?? row.currentStock ?? 0} {row.unitOfMeasure || 'pcs'}
        </div>
        <div key="stock-breakdown" className="text-xs text-muted-foreground">
          Finished Stock Available
        </div>
      </div>
    ),
  },
  {
    id: 'supplier',
    header: 'Supplier',
    accessorFn: (row) => row.supplier?.name ?? 'N/A',
    hideOnMobile: true,
    enableHiding: true,
    meta: {
      defaultHidden: true, // Hide by default since most entries show "N/A"
    },
    cell: ({ row }) => (
      <div className="max-w-[150px] truncate">
        {row.original.supplier?.name ?? 'N/A'}
      </div>
    ),
  },
  {
    id: 'reorderLevel',
    header: 'Reorder Level',
    accessorKey: 'reorderLevel',
    hideOnMobile: true,
    cell: ({ row }) => {
      const reorderLevel = row.original.reorderLevel ?? row.original.safety_stock_level ?? 0;
      const currentStock = row.original.inventory?.currentStock ?? row.original.currentStock ?? 0;
      const needsReorder = currentStock <= reorderLevel;

      return (
        <div className={needsReorder ? 'text-destructive font-medium' : ''}>
          {reorderLevel}
        </div>
      );
    },
  },
  {
    id: 'status',
    header: 'Status',
    accessorFn: (row) => {
      const currentStock = row.inventory?.currentStock ?? row.currentStock ?? 0;
      const reorderLevel = row.reorderLevel ?? row.safety_stock_level ?? 0;

      if (currentStock === 0) return 'Out of Stock';
      if (currentStock <= reorderLevel) return 'Low Stock';
      return 'In Stock';
    },
    hideOnMobile: true,
    cell: ({ row }) => {
      // V4 Schema: Use inventory.stockLevels.finished instead of currentStock
      const currentStock = row.original.inventory?.stockLevels?.finished ??
                          row.original.inventory?.currentStock ??
                          row.original.currentStock ?? 0;
      const reorderLevel = row.original.reorderLevel ?? row.original.safety_stock_level ?? 0;

      let status = 'In Stock';
      let variant: 'default' | 'secondary' | 'destructive' = 'default';

      if (currentStock === 0) {
        status = 'Out of Stock';
        variant = 'destructive';
      } else if (currentStock <= reorderLevel) {
        status = 'Low Stock';
        variant = 'secondary';
      }

      return <Badge variant={variant}>{status}</Badge>;
    },
  },
  // NEW: Planning Parameters Columns
  {
    id: 'planningMethod',
    header: 'Planning Method',
    accessorKey: 'planningMethod',
    hideOnMobile: true,
    cell: ({ row }) => {
      const method = row.original.planningMethod;
      if (!method) return <span className="text-muted-foreground">-</span>;

      const methodLabels: Record<string, string> = {
        'MRP': 'MRP',
        'EOQ': 'EOQ',
        'JIT': 'JIT',
        'MANUAL': 'Manual',
        'KANBAN': 'Kanban'
      };

      return (
        <Badge variant="outline" className="text-xs">
          {methodLabels[method] || method}
        </Badge>
      );
    },
  },
  // {
  //   id: 'safetyStockLevel',
  //   header: 'Safety Stock',
  //   accessorKey: 'safetyStockLevel',
  //   hideOnMobile: true,
  //   cell: ({ row }) => {
  //     const safetyStock = row.original.safetyStockLevel;
  //     const unit = row.original.unitOfMeasure || 'pcs';

  //     if (safetyStock === null || safetyStock === undefined) {
  //       return <span className="text-muted-foreground">-</span>;
  //     }

  //     return (
  //       <div className="text-right">
  //         <span className="font-medium">{safetyStock}</span>
  //         <span className="text-xs text-muted-foreground ml-1">{unit}</span>
  //       </div>
  //     );
  //   },
  // },
  // {
  //   id: 'maximumStockLevel',
  //   header: 'Max Stock',
  //   accessorKey: 'maximumStockLevel',
  //   hideOnMobile: true,
  //   cell: ({ row }) => {
  //     const maxStock = row.original.maximumStockLevel;
  //     const unit = row.original.unitOfMeasure || 'pcs';

  //     if (maxStock === null || maxStock === undefined) {
  //       return <span className="text-muted-foreground">-</span>;
  //     }

  //     return (
  //       <div className="text-right">
  //         <span className="font-medium">{maxStock}</span>
  //         <span className="text-xs text-muted-foreground ml-1">{unit}</span>
  //       </div>
  //     );
  //   },
  // },
  // {
  //   id: 'leadTimeDays',
  //   header: 'Lead Time',
  //   accessorKey: 'leadTimeDays',
  //   hideOnMobile: true,
  //   cell: ({ row }) => {
  //     const leadTime = row.original.leadTimeDays;

  //     if (leadTime === null || leadTime === undefined) {
  //       return <span className="text-muted-foreground">-</span>;
  //     }

  //     return (
  //       <div className="text-right">
  //         <span className="font-medium">{leadTime}</span>
  //         <span className="text-xs text-muted-foreground ml-1">days</span>
  //       </div>
  //     );
  //   },
  // },
  // {
  //   id: 'averageDailyUsage',
  //   header: 'Daily Usage',
  //   accessorKey: 'averageDailyUsage',
  //   hideOnMobile: true,
  //   cell: ({ row }) => {
  //     const usage = row.original.averageDailyUsage;
  //     const unit = row.original.unitOfMeasure || 'pcs';

  //     if (usage === null || usage === undefined) {
  //       return <span className="text-muted-foreground">-</span>;
  //     }

  //     return (
  //       <div className="text-right">
  //         <span className="font-medium">{usage.toFixed(2)}</span>
  //         <span className="text-xs text-muted-foreground ml-1">{unit}/day</span>
  //       </div>
  //     );
  //   },
  // },
  {
    id: 'actions',
    header: 'Actions',
    enableSorting: false,
    enableHiding: false,
    cell: ({ row }) => (
      <div className="flex items-center gap-1">
        {/* View Action */}
        <ViewPartButton
          part={row.original}
          variant="ghost"
          size="sm"
        >
          <Eye className="h-4 w-4" />
        </ViewPartButton>

        {/* Edit Action with refresh callback */}
        <EditPartAction
          partId={row.original._id}
          variant="icon"
          size="sm"
          onSuccess={actions.onRefresh || (() => {})}
        />

        {/* Delete Action */}
        <DeletePartAction
          partId={row.original._id}
          partName={row.original.name || row.original.partNumber}
          variant="icon"
          size="sm"
          onSuccess={actions.onRefresh || (() => {})}
        />
      </div>
    ),
  },
];

// ============================================================================
// FEATURE PRODUCT TABLE COLUMN DEFINITIONS
// ============================================================================

export interface FeatureProductColumnData {
  id: string;
  name: string;
  supplierManufacturer?: string;
  description?: string;
  currentStock?: number;
  reorderLevel?: number;
  // Additional fields from the Product type
  [key: string]: any;
}

export interface FeatureProductTableActions {
  onEdit?: (product: FeatureProductColumnData) => void;
  onEditProduct?: (product: FeatureProductColumnData) => void;
  onDelete?: (id: string) => void;
  onDeleteProduct?: (id: string) => void;
  onView?: (product: FeatureProductColumnData) => void;
}

/**
 * Feature Product Table Columns
 * Used in app/components/features/ProductTable.tsx with selection and action menus
 */
export const createFeatureProductColumns = (
  actions: FeatureProductTableActions = {},
  selectedProducts: string[] = [],
  onToggleSelect?: (id: string) => void,
  onToggleSelectAll?: () => void
): DataTableColumn<FeatureProductColumnData>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <input
        type="checkbox"
        className="h-4 w-4 text-yellow-500 rounded border-gray-300 dark:border-gray-600 focus:ring-yellow-400 dark:focus:ring-yellow-500"
        checked={selectedProducts.length > 0 && selectedProducts.length === table.getRowModel().rows.length}
        onChange={onToggleSelectAll}
      />
    ),
    cell: ({ row }) => (
      <input
        type="checkbox"
        className="h-4 w-4 text-yellow-500 rounded border-gray-300 dark:border-gray-600 focus:ring-yellow-400 dark:focus:ring-yellow-500"
        checked={selectedProducts.includes(row.original.id)}
        onChange={() => onToggleSelect?.(row.original.id)}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'id',
    header: 'ID',
    accessorKey: 'id',
    mobilePriority: 1,
    cell: ({ row }) => (
      <div className="text-sm font-medium">{row.original.id}</div>
    ),
  },
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    mobilePriority: 2,
    searchable: true,
    cell: ({ row }) => (
      <div className="text-sm font-medium">{row.original.name}</div>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="name" className="font-semibold">{value}</div>
        <div key="id" className="text-sm text-muted-foreground">{row.id}</div>
      </div>
    ),
  },
  {
    id: 'supplierManufacturer',
    header: 'Supplier/Manufacturer',
    accessorKey: 'supplierManufacturer',
    hideOnMobile: true,
    cell: ({ row }) => (
      <div className="text-sm">{row.original.supplierManufacturer || '-'}</div>
    ),
  },
  {
    id: 'description',
    header: 'Description',
    accessorKey: 'description',
    hideOnMobile: true,
    cell: ({ row }) => (
      <div className="text-sm max-w-[200px] truncate">
        {row.original.description || '-'}
      </div>
    ),
  },
  {
    id: 'currentStock',
    header: 'Current Stock',
    accessorKey: 'currentStock',
    hideOnMobile: true,
    cell: ({ row }) => {
      const stock = row.original.currentStock || 0;
      return (
        <div className={`text-sm ${stock > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
          {stock}
        </div>
      );
    },
  },
  {
    id: 'reorderLevel',
    header: 'Reorder Level',
    accessorKey: 'reorderLevel',
    hideOnMobile: true,
    cell: ({ row }) => (
      <div className="text-sm">{row.original.reorderLevel || '-'}</div>
    ),
  },
  {
    id: 'actions',
    header: 'Action',
    enableSorting: false,
    enableHiding: false,
    cell: ({ row }) => (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => actions.onView?.(row.original)}>
            <Eye className="mr-2 h-4 w-4" />
            View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => {
            actions.onEdit?.(row.original);
            actions.onEditProduct?.(row.original);
          }}>
            <PencilIcon className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-destructive"
            onClick={() => {
              actions.onDelete?.(row.original.id);
              actions.onDeleteProduct?.(row.original.id);
            }}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
  },
];

// ============================================================================
// BATCHES TABLE COLUMN DEFINITIONS
// ============================================================================

/**
 * Interface for Batch data in DataTable
 * Represents a production batch with all necessary fields for table display
 */
export interface BatchColumnData {
  _id: string;
  batchCode: string;
  partId?: string | { _id: string; name: string; description?: string | null } | undefined;
  assemblyId?: string | { _id: string; assemblyCode: string; name: string } | undefined;
  quantityPlanned: number;
  quantityProduced?: number | undefined;
  startDate: string | Date;
  endDate?: string | Date | undefined;
  status: string;
  notes?: string | undefined;
  workOrderId: string | { _id: string; woNumber: string; status: string };
  createdAt: string | Date;
  updatedAt: string | Date;
}

/**
 * Actions interface for BatchesTable
 */
export interface BatchesTableActions {
  onView?: ((batch: BatchColumnData) => void) | undefined;
  onEdit?: ((batch: BatchColumnData) => void) | undefined;
  onDelete?: ((batch: BatchColumnData) => void) | undefined;
  onCreate?: (() => void) | undefined;
}

/**
 * Helper function to get item name from batch data
 */
const getBatchItemName = (batch: BatchColumnData): string => {
  if (batch.partId && typeof batch.partId === 'object') {
    return batch.partId.name;
  }
  if (batch.assemblyId && typeof batch.assemblyId === 'object') {
    return batch.assemblyId.name;
  }
  return 'Unknown Item';
};

/**
 * Status badge component for batches
 */
const BatchStatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-warning/10 text-warning';
      case 'in_progress':
      case 'in-progress':
        return 'bg-info/10 text-info';
      case 'completed':
        return 'bg-success/10 text-success';
      case 'cancelled':
        return 'bg-destructive/10 text-destructive';
      case 'on_hold':
      case 'on-hold':
        return 'bg-orange/10 text-orange';
      default:
        return 'bg-muted/50 text-muted-foreground';
    }
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
      {status.replace('_', ' ').replace('-', ' ').toUpperCase()}
    </span>
  );
};

/**
 * Creates column definitions for BatchesTable
 */
export const createBatchesColumns = (actions?: BatchesTableActions): DataTableColumn<BatchColumnData>[] => {
  const columns: DataTableColumn<BatchColumnData>[] = [
    {
      accessorKey: "batchCode",
      header: "Batch Code",
      mobilePriority: 1,
      searchable: true,
      cell: ({ row }) => (
        <div className="font-medium">{row.original.batchCode}</div>
      ),
    },
    {
      id: "itemName",
      header: "Item",
      accessorFn: (row) => getBatchItemName(row),
      mobilePriority: 2,
      cell: ({ row }) => {
        const itemName = getBatchItemName(row.original);
        const itemType = row.original.partId ? 'Part' : row.original.assemblyId ? 'Assembly' : 'N/A';

        return (
          <div>
            <div className="font-medium">{itemName}</div>
            <div className="text-xs text-muted-foreground">{itemType}</div>
          </div>
        );
      },
      filterFn: (row: any, id: string, value: string) => {
        const itemName = getBatchItemName(row.original);
        return itemName.toLowerCase().includes(value.toLowerCase());
      },
      mobileRender: (value, row) => (
        <div>
          <div key="name" className="font-semibold">{value}</div>
          <div key="batch-code" className="text-sm text-muted-foreground">
            {row.batchCode}
          </div>
        </div>
      ),
    },
    {
      id: "quantity",
      header: "Quantity",
      mobilePriority: 3,
      cell: ({ row }) => {
        const planned = row.original.quantityPlanned;
        const produced = row.original.quantityProduced;
        const displayValue = produced !== undefined ? `${produced}/${planned}` : planned.toString();

        return <div className="font-mono">{displayValue}</div>;
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      mobilePriority: 4,
      cell: ({ row }) => (
        <BatchStatusBadge status={row.original.status} />
      ),
      filterFn: (row: any, id: string, value: string[]) => {
        return value.includes(row.original.status);
      },
    },
    {
      accessorKey: "startDate",
      header: "Start Date",
      hideOnMobile: true,
      cell: ({ row }) => {
        const date = row.original.startDate;
        return <div>{date ? format(new Date(date), "MMM d, yyyy") : "N/A"}</div>;
      },
    },
    {
      accessorKey: "endDate",
      header: "End Date",
      hideOnMobile: true,
      cell: ({ row }) => {
        const date = row.original.endDate;
        return <div>{date ? format(new Date(date), "MMM d, yyyy") : "N/A"}</div>;
      },
    },
  ];

  // Add actions column if actions are provided
  if (actions && (actions.onView || actions.onEdit || actions.onDelete)) {
    columns.push({
      id: "actions",
      header: "Actions",
      enableSorting: false,
      enableHiding: false,
      cell: ({ row }) => {
        const batch = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              {actions.onView && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    actions.onView!(batch);
                  }}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
              )}
              {actions.onEdit && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    actions.onEdit!(batch);
                  }}
                >
                  <PencilIcon className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              {(actions.onView || actions.onEdit) && actions.onDelete && (
                <DropdownMenuSeparator />
              )}
              {actions.onDelete && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    actions.onDelete!(batch);
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    });
  }

  return columns;
};

// ============================================================================
// Work Orders Table Columns
// ============================================================================

/**
 * Work Orders Table Actions
 */
export interface WorkOrdersTableActions {
  onView?: (workOrder: WorkOrderColumnData) => void;
  onEdit?: (workOrder: WorkOrderColumnData) => void;
  onDelete?: (workOrder: WorkOrderColumnData) => void;
  onRefresh?: () => void;
}

/**
 * Work Order Column Data Interface
 * Normalized data structure for work orders table display
 */
export interface WorkOrderColumnData {
  _id: string;
  woNumber: string;
  assemblyId?: string | null;
  partIdToManufacture?: string | null;
  productId?: string | null;
  quantity: number;
  status: 'planned' | 'released' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled' | 'closed' | 'pending';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Date | string;
  startDate?: Date | string | null;
  assignedTo?: string | {
    _id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  } | null;
  notes?: string | null;
  completedAt?: Date | string | null;
  estimatedDuration?: number | null;
  actualDuration?: number | null;
  costEstimate?: number | null;
  actualCost?: number | null;
  createdBy?: string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
  // Populated fields for display
  assembly?: {
    _id: string;
    name: string;
    assemblyCode?: string;
  } | null;
  partToManufacture?: {
    _id: string;
    name: string;
    partNumber?: string;
    description?: string;
  } | null;
  product?: {
    _id: string;
    name: string;
    productCode?: string;
  } | null;
}

/**
 * Status Badge Component for Work Orders
 */
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'planned':
        return { variant: 'secondary' as const, label: 'Planned' };
      case 'released':
        return { variant: 'outline' as const, label: 'Released' };
      case 'in_progress':
        return { variant: 'default' as const, label: 'In Progress' };
      case 'completed':
        return { variant: 'success' as const, label: 'Completed' };
      case 'on_hold':
        return { variant: 'warning' as const, label: 'On Hold' };
      case 'cancelled':
        return { variant: 'destructive' as const, label: 'Cancelled' };
      case 'closed':
        return { variant: 'secondary' as const, label: 'Closed' };
      case 'pending':
        return { variant: 'outline' as const, label: 'Pending' };
      default:
        return { variant: 'secondary' as const, label: status };
    }
  };

  const config = getStatusConfig(status);
  return <Badge variant={config.variant}>{config.label}</Badge>;
};

/**
 * Priority Badge Component for Work Orders
 */
const PriorityBadge = ({ priority }: { priority: string }) => {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'low':
        return { variant: 'secondary' as const, label: 'Low' };
      case 'medium':
        return { variant: 'outline' as const, label: 'Medium' };
      case 'high':
        return { variant: 'warning' as const, label: 'High' };
      case 'urgent':
        return { variant: 'destructive' as const, label: 'Urgent' };
      default:
        return { variant: 'secondary' as const, label: priority };
    }
  };

  const config = getPriorityConfig(priority);
  return <Badge variant={config.variant}>{config.label}</Badge>;
};

/**
 * Work Orders Table Columns - Simple Mode
 * Used for basic work order listings with essential information
 */
const createWorkOrdersSimpleColumns = (
  actions: WorkOrdersTableActions = {}
): DataTableColumn<WorkOrderColumnData>[] => [
  {
    id: 'woNumber',
    header: 'WO Number',
    accessorKey: 'woNumber',
    mobilePriority: 1,
    searchable: true,
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        <div className="inline-block px-2 py-1 bg-primary/5 dark:bg-primary/10 rounded border border-primary/10 dark:border-primary/20">
          {row.original.woNumber}
        </div>
      </div>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="wo-number" className="font-mono text-sm font-semibold">{value}</div>
        <div key="status" className="text-sm text-muted-foreground">
          {row.status && <StatusBadge status={row.status} />}
        </div>
      </div>
    ),
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    mobilePriority: 2,
    cell: ({ row }) => <StatusBadge status={row.original.status} />,
    filterFn: (row: any, columnId: string, filterValue: any) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.original.status);
    },
  },
  {
    id: 'priority',
    header: 'Priority',
    accessorKey: 'priority',
    mobilePriority: 3,
    cell: ({ row }) => <PriorityBadge priority={row.original.priority} />,
    filterFn: (row: any, columnId: string, filterValue: any) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.original.priority);
    },
  },
  {
    id: 'quantity',
    header: 'Quantity',
    accessorKey: 'quantity',
    hideOnMobile: true,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.original.quantity.toLocaleString()}
      </div>
    ),
  },
  {
    id: 'assignedTo',
    header: 'Assigned To',
    accessorFn: (row) => {
      if (row.assignedTo && typeof row.assignedTo === 'object') {
        return `${row.assignedTo.first_name || ''} ${row.assignedTo.last_name || ''}`.trim() || row.assignedTo.username || 'Unassigned';
      }
      return row.assignedTo || 'Unassigned';
    },
    hideOnMobile: true,
    searchable: true,
    cell: ({ row }) => {
      const assignedTo = row.original.assignedTo;

      if (assignedTo && typeof assignedTo === 'object') {
        const fullName = `${assignedTo.first_name || ''} ${assignedTo.last_name || ''}`.trim();
        return (
          <div className="text-sm">
            {fullName || assignedTo.username || (
              <span className="text-muted-foreground italic">Unassigned</span>
            )}
          </div>
        );
      }

      if (assignedTo && typeof assignedTo === 'string') {
        return <div className="text-sm">{assignedTo}</div>;
      }

      return <span className="text-muted-foreground italic">Unassigned</span>;
    },
  },
  {
    id: 'dueDate',
    header: 'Due Date',
    accessorKey: 'dueDate',
    hideOnMobile: true,
    cell: ({ row }) => {
      const dueDate = row.original.dueDate;
      if (!dueDate) return <span className="text-muted-foreground">—</span>;

      try {
        const date = new Date(dueDate);
        const now = new Date();
        const isOverdue = date < now && row.original.status !== 'completed' && row.original.status !== 'closed';

        return (
          <div className={cn(
            "text-sm",
            isOverdue && "text-destructive font-medium"
          )}>
            {format(date, 'MMM d, yyyy')}
            {isOverdue && (
              <div className="text-xs text-destructive">Overdue</div>
            )}
          </div>
        );
      } catch (err) {
        return <span className="text-muted-foreground">Invalid Date</span>;
      }
    },
  },
  // Actions column
  {
    id: 'actions',
    header: '',
    cell: ({ row }) => (
      <div className="flex justify-end space-x-1">
        {actions.onView && (
          <Button
            key={`view-${row.original._id}`}
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              actions.onView!(row.original);
            }}
            title="View Work Order"
          >
            <Eye className="h-4 w-4" />
          </Button>
        )}
        {actions.onEdit && (
          <Button
            key={`edit-${row.original._id}`}
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              actions.onEdit!(row.original);
            }}
            title="Edit Work Order"
          >
            <Edit className="h-4 w-4" />
          </Button>
        )}
        {actions.onDelete && (
          <Button
            key={`delete-${row.original._id}`}
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              actions.onDelete!(row.original);
            }}
            title="Delete Work Order"
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
];

/**
 * Work Orders Table Columns - Complex Mode
 * Used for detailed work order management with additional information
 */
const createWorkOrdersComplexColumns = (
  actions: WorkOrdersTableActions = {}
): DataTableColumn<WorkOrderColumnData>[] => [
  {
    id: 'woNumber',
    header: 'WO Number',
    accessorKey: 'woNumber',
    mobilePriority: 1,
    searchable: true,
    enableHiding: false, // Always show WO number
    cell: ({ row }) => (
      <Link
        href={`/work-orders/${row.original._id}`}
        className="font-mono text-sm hover:underline"
      >
        <div className="inline-block px-2 py-1 bg-primary/5 dark:bg-primary/10 rounded border border-primary/10 dark:border-primary/20">
          {row.original.woNumber}
        </div>
      </Link>
    ),
    mobileRender: (value, row) => (
      <div>
        <div key="wo-number" className="font-mono text-sm font-semibold">{value}</div>
        <div key="status" className="text-sm text-muted-foreground">
          {row.status && <StatusBadge status={row.status} />}
        </div>
      </div>
    ),
  },
  {
    id: 'item',
    header: 'Item',
    accessorFn: (row) => {
      if (row.assembly?.name) return row.assembly.name;
      if (row.partToManufacture?.name) return row.partToManufacture.name;
      if (row.product?.name) return row.product.name;
      return 'Unknown Item';
    },
    mobilePriority: 2,
    searchable: true,
    enableHiding: false, // Always show item information
    cell: ({ row }) => {
      const { assembly, partToManufacture, product } = row.original;

      if (assembly) {
        return (
          <div>
            <div className="font-medium">{assembly.name}</div>
            <div className="text-xs text-muted-foreground">
              Assembly • {assembly.assemblyCode || assembly._id}
            </div>
          </div>
        );
      }

      if (partToManufacture) {
        return (
          <div>
            <div className="font-medium">{partToManufacture.name}</div>
            <div className="text-xs text-muted-foreground">
              Part • {partToManufacture.partNumber || partToManufacture._id}
            </div>
          </div>
        );
      }

      if (product) {
        return (
          <div>
            <div className="font-medium">{product.name}</div>
            <div className="text-xs text-muted-foreground">
              Product • {product.productCode || product._id}
            </div>
          </div>
        );
      }

      return <span className="text-muted-foreground italic">Unknown Item</span>;
    },
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    mobilePriority: 3,
    cell: ({ row }) => <StatusBadge status={row.original.status} />,
    filterFn: (row: any, columnId: string, filterValue: any) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.original.status);
    },
  },
  {
    id: 'priority',
    header: 'Priority',
    accessorKey: 'priority',
    mobilePriority: 4,
    cell: ({ row }) => <PriorityBadge priority={row.original.priority} />,
    filterFn: (row: any, columnId: string, filterValue: any) => {
      if (!filterValue || filterValue.length === 0) return true;
      return filterValue.includes(row.original.priority);
    },
  },
  {
    id: 'quantity',
    header: 'Quantity',
    accessorKey: 'quantity',
    hideOnMobile: true,
    enableHiding: true, // Allow hiding quantity column
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.original.quantity.toLocaleString()}
      </div>
    ),
  },
  {
    id: 'assignedTo',
    header: 'Assigned To',
    accessorFn: (row) => {
      if (row.assignedTo && typeof row.assignedTo === 'object') {
        return `${row.assignedTo.first_name || ''} ${row.assignedTo.last_name || ''}`.trim() || row.assignedTo.username || 'Unassigned';
      }
      return row.assignedTo || 'Unassigned';
    },
    hideOnMobile: true,
    searchable: true,
    enableHiding: true, // Allow hiding assigned to column
    cell: ({ row }) => {
      const assignedTo = row.original.assignedTo;

      if (assignedTo && typeof assignedTo === 'object') {
        const fullName = `${assignedTo.first_name || ''} ${assignedTo.last_name || ''}`.trim();
        return (
          <div className="text-sm">
            <div className="font-medium">{fullName || assignedTo.username}</div>
            {assignedTo.email && (
              <div className="text-xs text-muted-foreground">{assignedTo.email}</div>
            )}
          </div>
        );
      }

      if (assignedTo && typeof assignedTo === 'string') {
        return <div className="text-sm">{assignedTo}</div>;
      }

      return <span className="text-muted-foreground italic">Unassigned</span>;
    },
  },
  {
    id: 'dates',
    header: 'Dates',
    accessorFn: (row) => row.dueDate,
    hideOnMobile: true,
    cell: ({ row }) => {
      const { dueDate, startDate, completedAt } = row.original;
      const now = new Date();

      return (
        <div className="text-sm space-y-1">
          {dueDate && (
            <div className={cn(
              "flex items-center gap-1",
              new Date(dueDate) < now && row.original.status !== 'completed' && row.original.status !== 'closed' && "text-destructive"
            )}>
              <span className="text-xs text-muted-foreground">Due:</span>
              {format(new Date(dueDate), 'MMM d, yyyy')}
            </div>
          )}
          {startDate && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Started:</span>
              {format(new Date(startDate), 'MMM d, yyyy')}
            </div>
          )}
          {completedAt && (
            <div className="flex items-center gap-1 text-success">
              <span className="text-xs text-muted-foreground">Completed:</span>
              {format(new Date(completedAt), 'MMM d, yyyy')}
            </div>
          )}
        </div>
      );
    },
  },
  {
    id: 'duration',
    header: 'Duration',
    accessorFn: (row) => row.estimatedDuration || row.actualDuration,
    hideOnMobile: true,
    cell: ({ row }) => {
      const { estimatedDuration, actualDuration } = row.original;

      if (!estimatedDuration && !actualDuration) {
        return <span className="text-muted-foreground">—</span>;
      }

      return (
        <div className="text-sm space-y-1">
          {estimatedDuration && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Est:</span>
              {estimatedDuration}h
            </div>
          )}
          {actualDuration && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Actual:</span>
              {actualDuration}h
            </div>
          )}
        </div>
      );
    },
  },
  // Actions column
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        {actions.onView && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => actions.onView!(row.original)}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View work order</span>
          </Button>
        )}
        {actions.onEdit && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => actions.onEdit!(row.original)}
            className="h-8 w-8 p-0"
          >
            <Edit className="h-4 w-4" />
            <span className="sr-only">Edit work order</span>
          </Button>
        )}
        {actions.onDelete && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => actions.onDelete!(row.original)}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Delete work order</span>
          </Button>
        )}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
];

// ===== INVENTORY TRANSACTIONS TABLE DEFINITIONS =====

export interface InventoryTransactionColumnData {
  _id: string;
  transactionId?: string; // Human-readable transaction ID (e.g., "MOV-************")
  transactionType: string;
  itemName: string;
  itemId: string; // MongoDB ObjectID (kept for compatibility)
  partNumber?: string; // Human-readable part number
  businessName?: string; // Business name for the part
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseName: string;
  warehouseId: string; // MongoDB ObjectID (kept for compatibility)
  warehouseLocationId?: string; // Human-readable warehouse location ID
  quantity: number;
  previousStock: number | string | null; // Legacy field - may be null for new transactions
  newStock: number | string | null; // Legacy field - may be null for new transactions
  transactionDate: string | Date;
  referenceNumber?: string;
  referenceType?: string;
  reference?: string;
  userName: string;
  userId: string;
  notes?: string;
  // UPDATED: Enhanced location-based movement fields
  fromLocation?: string; // Human-readable from location (e.g., "Main Warehouse - A1-R2-S3 (finished)")
  toLocation?: string; // Human-readable to location (e.g., "Shipping Dock - DOCK-1 (finished)")
  fromLocationName?: string; // Just the location name (e.g., "A1-R2-S3")
  toLocationName?: string; // Just the location name (e.g., "DOCK-1")
  fromWarehouseName?: string; // Source warehouse name
  toWarehouseName?: string; // Destination warehouse name
  movementDisplay?: string; // Complete movement display (e.g., "Main Warehouse (A1-R2-S3) → Shipping Dock (DOCK-1)")
  from?: {
    locationId: string;
    stockType: string;
    // Populated fields from API enhancement
    location?: {
      _id: string;
      name: string;
      description?: string;
      locationType: string;
      warehouseId: string;
    };
    warehouse?: {
      _id: string;
      location_id: string;
      name: string;
      location: string;
    };
    // Convenience fields
    locationName?: string;
    warehouseName?: string;
  } | null;
  to?: {
    locationId: string;
    stockType: string;
    // Populated fields from API enhancement
    location?: {
      _id: string;
      name: string;
      description?: string;
      locationType: string;
      warehouseId: string;
    };
    warehouse?: {
      _id: string;
      location_id: string;
      name: string;
      location: string;
    };
    // Convenience fields
    locationName?: string;
    warehouseName?: string;
  } | null;
}

export interface InventoryTransactionsTableActions {
  onView?: (transaction: InventoryTransactionColumnData) => void;
  onEdit?: (transaction: InventoryTransactionColumnData) => void;
  onDelete?: (transaction: InventoryTransactionColumnData) => void;
}

// Transaction type icons mapping
const getTransactionTypeIcon = (type: string) => {
  switch (type) {
    case 'stock_in_purchase':
      return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
    case 'stock_out_production':
      return <ArrowUpRight className="w-4 h-4 text-red-600" />;
    case 'adjustment_cycle_count':
      return <RotateCcw className="w-4 h-4 text-blue-600" />;
    case 'stock_in_production':
      return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
    case 'transfer_out':
      return <ArrowUpRight className="w-4 h-4 text-orange-600" />;
    case 'transfer_in':
      return <ArrowDownLeft className="w-4 h-4 text-orange-600" />;
    case 'sales_shipment':
      return <ArrowUpRight className="w-4 h-4 text-purple-600" />;
    default:
      return <FileText className="w-4 h-4 text-gray-600" />;
  }
};

// Transaction type badge component - Simplified for consistent spacing
const TransactionTypeBadge = ({ type }: { type: string }) => {
  const safeType = type || 'adjustment_cycle_count';
  const displayType = safeType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return (
    <div className="flex items-center gap-2">
      {getTransactionTypeIcon(safeType)}
      <span className="font-medium text-sm">{displayType}</span>
    </div>
  );
};

// Quantity change badge component - Consistent with table styling
const QuantityChangeBadge = ({ quantity }: { quantity: number }) => {
  const safeQuantity = quantity || 0;
  const isPositive = safeQuantity > 0;
  return (
    <div className="text-center">
      <span className={`font-medium ${isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
        {isPositive ? '+' : ''}{safeQuantity}
      </span>
    </div>
  );
};

function createInventoryTransactionsSimpleColumns(
  actions?: InventoryTransactionsTableActions
): DataTableColumn<InventoryTransactionColumnData>[] {
  const columns: DataTableColumn<InventoryTransactionColumnData>[] = [
    {
      id: "transactionType",
      header: "Type",
      accessorKey: "transactionType",
      mobilePriority: 1,
      cell: ({ row }) => <TransactionTypeBadge type={row.original.transactionType} />,
      mobileRender: (value) => <TransactionTypeBadge type={value} />,
    },
    {
      id: "itemName",
      header: "Item",
      accessorKey: "itemName",
      mobilePriority: 2,
      searchable: true,
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span className="font-medium text-sm">{row.original.itemName || 'Unknown Item'}</span>
          <span className="text-xs text-gray-500 font-mono">{row.original.partNumber || row.original.itemId}</span>
        </div>
      ),
      mobileRender: (value, row) => (
        <div className="flex flex-col">
          <span key="item-name" className="font-medium text-sm">{value || 'Unknown Item'}</span>
          <span key="part-number" className="text-xs text-gray-500 font-mono">{row.partNumber || row.itemId}</span>
        </div>
      ),
    },
    {
      id: "quantity",
      header: "Qty Change",
      accessorKey: "quantity",
      mobilePriority: 3,
      cell: ({ row }) => {
        return <QuantityChangeBadge quantity={row.original.quantity} />;
      },
      mobileRender: (value) => <QuantityChangeBadge quantity={value} />,
    },
    {
      id: "transactionDate",
      header: "Date",
      accessorKey: "transactionDate",
      mobilePriority: 4,
      cell: ({ row }) => {
        const date = row.original.transactionDate as string | Date;
        if (!date) return <span className="text-gray-400">N/A</span>;

        try {
          const dateObj = date instanceof Date ? date : new Date(date);
          if (isNaN(dateObj.getTime())) {
            return <span className="text-gray-400">Invalid Date</span>;
          }
          return (
            <span className="text-gray-600 dark:text-gray-300">
              {format(dateObj, 'yyyy-MM-dd HH:mm')}
            </span>
          );
        } catch (error) {
          return <span className="text-gray-400">Invalid Date</span>;
        }
      },
      mobileRender: (value) => {
        const date = value as string | Date;
        if (!date) return <span className="text-gray-400">N/A</span>;

        try {
          const dateObj = date instanceof Date ? date : new Date(date);
          if (isNaN(dateObj.getTime())) {
            return <span className="text-gray-400">Invalid Date</span>;
          }
          return (
            <span className="text-gray-600 dark:text-gray-300">
              {format(dateObj, 'yyyy-MM-dd HH:mm')}
            </span>
          );
        } catch (error) {
          return <span className="text-gray-400">Invalid Date</span>;
        }
      },
    },
  ];

  // Add actions column if actions are provided
  if (actions && (actions.onView || actions.onEdit || actions.onDelete)) {
    columns.push({
      id: "actions",
      header: "",
      cell: ({ row }) => (
        <div className="flex items-center justify-end gap-1">
          {/* View Action */}
          {actions.onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onView!(row.original)}
              title="View Transaction"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}

          {/* Edit Action */}
          {actions.onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onEdit!(row.original)}
              title="Edit Transaction"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}

          {/* Delete Action */}
          {actions.onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onDelete!(row.original)}
              className="text-destructive hover:text-destructive"
              title="Delete Transaction"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    });
  }

  return columns;
}

function createInventoryTransactionsComplexColumns(
  actions?: InventoryTransactionsTableActions
): DataTableColumn<InventoryTransactionColumnData>[] {
  const columns: DataTableColumn<InventoryTransactionColumnData>[] = [
    {
      id: "transactionType",
      header: "Transaction Type",
      accessorKey: "transactionType",
      mobilePriority: 1,
      cell: ({ row }) => <TransactionTypeBadge type={row.original.transactionType} />,
      mobileRender: (value) => <TransactionTypeBadge type={value} />,
    },
    {
      id: "itemName",
      header: "Item",
      accessorKey: "itemName",
      mobilePriority: 2,
      searchable: true,
      cell: ({ row }) => {
        const itemName = row.original.itemName || 'Unknown Item';
        const partNumber = row.original.partNumber || row.original.itemId;
        return (
          <div>
            <div className="font-medium">{itemName}</div>
            <div className="text-sm text-muted-foreground font-mono">{partNumber}</div>
          </div>
        );
      },
      mobileRender: (value, row) => {
        const partNumber = row.partNumber || row.itemId;
        return (
          <div>
            <div key="item-name" className="font-medium">{value || 'Unknown Item'}</div>
            <div key="part-number" className="text-sm text-muted-foreground font-mono">{partNumber}</div>
          </div>
        );
      },
    },
    {
      id: "fromLocation",
      header: "From Location",
      accessorFn: (row) => row.fromLocation || row.fromWarehouseName || 'External',
      hideOnMobile: true,
      searchable: true,
      cell: ({ row }) => {
        const fromLocation = row.original.fromLocation;
        const fromWarehouse = row.original.fromWarehouseName;
        const fromLocationName = row.original.fromLocationName;

        if (fromLocation) {
          // New location-based format
          return (
            <div className="max-w-[150px]">
              <div className="font-medium text-sm">{fromWarehouse || 'External'}</div>
              {fromLocationName && (
                <div className="text-xs text-muted-foreground font-mono">{fromLocationName}</div>
              )}
            </div>
          );
        } else {
          // Legacy or external source
          return (
            <div className="text-sm text-muted-foreground">
              {fromWarehouse || 'External'}
            </div>
          );
        }
      },
      mobileRender: (value, row) => (
        <div>
          <div key="from-warehouse" className="font-medium text-sm">{row.fromWarehouseName || 'External'}</div>
          {row.fromLocationName && (
            <div key="from-location" className="text-xs text-muted-foreground font-mono">{row.fromLocationName}</div>
          )}
        </div>
      ),
    },
    {
      id: "toLocation",
      header: "To Location",
      accessorFn: (row) => row.toLocation || row.toWarehouseName || 'External',
      hideOnMobile: true,
      searchable: true,
      cell: ({ row }) => {
        const toLocation = row.original.toLocation;
        const toWarehouse = row.original.toWarehouseName;
        const toLocationName = row.original.toLocationName;

        if (toLocation) {
          // New location-based format
          return (
            <div className="max-w-[150px]">
              <div className="font-medium text-sm">{toWarehouse || 'External'}</div>
              {toLocationName && (
                <div className="text-xs text-muted-foreground font-mono">{toLocationName}</div>
              )}
            </div>
          );
        } else {
          // Legacy or external destination
          return (
            <div className="text-sm text-muted-foreground">
              {toWarehouse || 'External'}
            </div>
          );
        }
      },
      mobileRender: (value, row) => (
        <div>
          <div key="to-warehouse" className="font-medium text-sm">{row.toWarehouseName || 'External'}</div>
          {row.toLocationName && (
            <div key="to-location" className="text-xs text-muted-foreground font-mono">{row.toLocationName}</div>
          )}
        </div>
      ),
    },
    {
      id: "quantity",
      header: "Qty Change",
      accessorKey: "quantity",
      mobilePriority: 3,
      cell: ({ row }) => <QuantityChangeBadge quantity={row.original.quantity} />,
      mobileRender: (value) => <QuantityChangeBadge quantity={value} />,
    },
    {
      id: "previousStock",
      header: "Before",
      accessorKey: "previousStock",
      hideOnMobile: true,
      cell: ({ row }) => {
        const previousStock = row.original.previousStock;
        const transaction = row.original;

        // Handle string values (directional information) or numeric values
        if (typeof previousStock === 'string') {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                {previousStock}
              </span>
            </div>
          );
        }

        // If we have a numeric value, show it
        if (previousStock !== null && previousStock !== undefined) {
          return (
            <div className="text-center">
              <span className="font-medium">
                {previousStock}
              </span>
            </div>
          );
        }

        // For event-sourced transactions without absolute stock levels,
        // show the affected stock type and movement direction
        if (transaction.from && transaction.from.stockType) {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                {transaction.from.stockType}
              </span>
            </div>
          );
        } else if (transaction.to && transaction.to.stockType) {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                -
              </span>
            </div>
          );
        }

        return (
          <div className="text-center">
            <span className="text-xs text-muted-foreground">
              -
            </span>
          </div>
        );
      },
      mobileRender: (value) => (
        <span className="font-medium">{value || '-'}</span>
      ),
    },
    {
      id: "newStock",
      header: "After",
      accessorKey: "newStock",
      hideOnMobile: true,
      cell: ({ row }) => {
        const newStock = row.original.newStock;
        const transaction = row.original;

        // Handle string values (directional information) or numeric values
        if (typeof newStock === 'string') {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                {newStock}
              </span>
            </div>
          );
        }

        // If we have a numeric value, show it
        if (newStock !== null && newStock !== undefined) {
          return (
            <div className="text-center">
              <span className="font-medium">
                {newStock}
              </span>
            </div>
          );
        }

        // For event-sourced transactions without absolute stock levels,
        // show the affected stock type and movement direction
        if (transaction.to && transaction.to.stockType) {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                {transaction.to.stockType}
              </span>
            </div>
          );
        } else if (transaction.from && transaction.from.stockType) {
          return (
            <div className="text-center">
              <span className="text-xs text-muted-foreground">
                -
              </span>
            </div>
          );
        }

        return (
          <div className="text-center">
            <span className="text-xs text-muted-foreground">
              -
            </span>
          </div>
        );
      },
      mobileRender: (value) => (
        <span className="font-medium">{value || '-'}</span>
      ),
    },
    {
      id: "transactionDate",
      header: "Date",
      accessorKey: "transactionDate",
      mobilePriority: 4,
      cell: ({ row }) => {
        const date = row.original.transactionDate as string | Date;
        if (!date) return <span className="text-muted-foreground">N/A</span>;

        try {
          const dateObj = date instanceof Date ? date : new Date(date);
          if (isNaN(dateObj.getTime())) {
            return <span className="text-muted-foreground">Invalid Date</span>;
          }
          return (
            <div className="text-sm">
              <div>{format(dateObj, 'yyyy-MM-dd')}</div>
              <div className="text-muted-foreground">{format(dateObj, 'HH:mm')}</div>
            </div>
          );
        } catch (error) {
          return <span className="text-muted-foreground">Invalid Date</span>;
        }
      },
      mobileRender: (value) => {
        const date = value as string | Date;
        if (!date) return <span className="text-muted-foreground">N/A</span>;

        try {
          const dateObj = date instanceof Date ? date : new Date(date);
          if (isNaN(dateObj.getTime())) {
            return <span className="text-muted-foreground">Invalid Date</span>;
          }
          return (
            <span className="text-sm">
              {format(dateObj, 'yyyy-MM-dd HH:mm')}
            </span>
          );
        } catch (error) {
          return <span className="text-muted-foreground">Invalid Date</span>;
        }
      },
    },
    {
      id: "reference",
      header: "Reference",
      accessorKey: "reference",
      hideOnMobile: true,
      cell: ({ row }) => {
        const reference = row.original.reference || '-';
        return (
          <span className="text-muted-foreground">
            {reference}
          </span>
        );
      },
      mobileRender: (value, row) => {
        const reference = row.reference || value || '-';
        return (
          <span className="text-muted-foreground">
            {reference}
          </span>
        );
      },
    },
    {
      id: "userName",
      header: "User",
      accessorKey: "userName",
      hideOnMobile: true,
      searchable: true,
      cell: ({ row }) => {
        const userName = row.original.userName;
        const userId = row.original.userId;
        const fallbackValue = userId
          ? (typeof userId === 'string' ? userId.substring(0, 8) : (userId as any)?._id?.substring(0, 8) || 'Unknown')
          : 'Unknown';
        return (
          <span className="text-gray-600 dark:text-gray-300">
            {userName || fallbackValue}
          </span>
        );
      },
      mobileRender: (value, row) => {
        const userId = row.userId;
        const fallbackValue = userId
          ? (typeof userId === 'string' ? userId.substring(0, 8) : (userId as any)?._id?.substring(0, 8) || 'Unknown')
          : 'Unknown';
        return (
          <span className="text-gray-600 dark:text-gray-300">
            {value || fallbackValue}
          </span>
        );
      },
    },
    {
      id: "notes",
      header: "Notes",
      accessorKey: "notes",
      hideOnMobile: true,
      cell: ({ row }) => (
        <span className="max-w-[200px] truncate text-gray-600 dark:text-gray-300">
          {row.original.notes || '-'}
        </span>
      ),
      mobileRender: (value) => (
        <span className="max-w-[200px] truncate text-gray-600 dark:text-gray-300">
          {value || '-'}
        </span>
      ),
    },
  ];

  // Add actions column if actions are provided
  if (actions && (actions.onView || actions.onEdit || actions.onDelete)) {
    columns.push({
      id: "actions",
      header: "",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {actions.onView && (
              <DropdownMenuItem onClick={() => actions.onView!(row.original)}>
                <Eye className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
            )}
            {actions.onEdit && (
              <DropdownMenuItem onClick={() => actions.onEdit!(row.original)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            )}
            {actions.onDelete && (
              <DropdownMenuItem
                onClick={() => actions.onDelete!(row.original)}
                className="text-red-600 dark:text-red-400"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableSorting: false,
      enableHiding: false,
    });
  }

  return columns;
}

/**
 * Purchase Order Column Data Interface
 * Normalized data structure for purchase orders table display
 */
export interface PurchaseOrderColumnData {
  _id: string;
  poNumber: string;
  supplierId?: string | null;
  orderDate: Date | string;
  expectedDeliveryDate?: Date | string | null;
  items: Array<{
    partId: string;
    description: string;
    quantity: number;
    unitPrice: number;
    lineTotal: number;
    receivedQuantity: number;
  }>;
  totalAmount: number;
  status: 'draft' | 'pending_approval' | 'ordered' | 'partially_received' | 'fully_received' | 'cancelled';
  notes?: string | null;
  shippingAddress?: string | null;
  billingAddress?: string | null;
  termsAndConditions?: string | null;
  createdBy?: string | null;
  approvedBy?: string | null;
  approvalDate?: Date | string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
  // Populated fields for display
  supplier?: {
    _id: string;
    name: string;
    supplier_id?: string;
    email?: string;
  } | null;
  createdByUser?: {
    _id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  } | null;
  approvedByUser?: {
    _id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  } | null;
}

/**
 * Purchase Orders Table Actions Interface
 */
export interface PurchaseOrdersTableActions {
  onView?: (purchaseOrder: PurchaseOrderColumnData) => void;
  onEdit?: (purchaseOrder: PurchaseOrderColumnData) => void;
  onDelete?: (purchaseOrder: PurchaseOrderColumnData) => void;
  onRefresh?: () => void;
}

/**
 * Purchase Orders Table Columns - Complex Mode
 * Used for detailed purchase order management with additional information
 */
const createPurchaseOrdersComplexColumns = (
  actions: PurchaseOrdersTableActions = {}
): DataTableColumn<PurchaseOrderColumnData>[] => {
  const columns: DataTableColumn<PurchaseOrderColumnData>[] = [
    {
      id: 'poNumber',
      header: 'PO Number',
      accessorKey: 'poNumber',
      mobilePriority: 1,
      searchable: true,
      enableHiding: false, // Always show PO number
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          <div className="inline-block px-2 py-1 bg-primary/5 dark:bg-primary/10 rounded border border-primary/10 dark:border-primary/20">
            {row.original.poNumber}
          </div>
        </div>
      ),
      mobileRender: (value, row) => (
        <div>
          <div key="po-number" className="font-mono text-sm font-semibold">{value}</div>
          <div key="status" className="text-sm text-muted-foreground">
            {row.status && <StatusBadge status={row.status} />}
          </div>
        </div>
      ),
    },
    {
      id: 'supplier',
      header: 'Supplier',
      accessorFn: (row) => row.supplier?.name || 'Unknown Supplier',
      mobilePriority: 2,
      searchable: true,
      cell: ({ row }) => {
        const supplier = row.original.supplier;

        if (supplier) {
          return (
            <div>
              <div className="font-medium">{supplier.name}</div>
              {supplier.supplier_id && (
                <div className="text-xs text-muted-foreground">
                  ID: {supplier.supplier_id}
                </div>
              )}
            </div>
          );
        }

        return <span className="text-muted-foreground italic">Unknown Supplier</span>;
      },
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      mobilePriority: 3,
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
      filterFn: (row: any, columnId: string, filterValue: any) => {
        if (!filterValue || filterValue.length === 0) return true;
        return filterValue.includes(row.original.status);
      },
    },
    {
      id: 'totalAmount',
      header: 'Total Amount',
      accessorKey: 'totalAmount',
      hideOnMobile: true,
      enableHiding: true, // Allow hiding total amount
      cell: ({ row }) => (
        <div className="text-right font-medium">
          ${row.original.totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
        </div>
      ),
    },
    {
      id: 'dates',
      header: 'Dates',
      accessorFn: (row) => row.orderDate,
      hideOnMobile: true,
      enableHiding: true, // Allow hiding dates
      cell: ({ row }) => {
        const { orderDate, expectedDeliveryDate, approvalDate } = row.original;

        return (
          <div className="text-sm space-y-1">
            {orderDate && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-muted-foreground">Ordered:</span>
                {format(new Date(orderDate), 'MMM d, yyyy')}
              </div>
            )}
            {expectedDeliveryDate && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-muted-foreground">Expected:</span>
                {format(new Date(expectedDeliveryDate), 'MMM d, yyyy')}
              </div>
            )}
            {approvalDate && (
              <div className="flex items-center gap-1 text-success">
                <span className="text-xs text-muted-foreground">Approved:</span>
                {format(new Date(approvalDate), 'MMM d, yyyy')}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'items',
      header: 'Items',
      accessorFn: (row) => row.items?.length || 0,
      hideOnMobile: true,
      cell: ({ row }) => {
        const items = row.original.items || [];
        const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

        return (
          <div className="text-sm">
            <div className="font-medium">{items.length} line{items.length !== 1 ? 's' : ''}</div>
            <div className="text-xs text-muted-foreground">
              {totalQuantity} total qty
            </div>
          </div>
        );
      },
    },
  ];

  // Add actions column if any actions are provided
  if (actions.onView || actions.onEdit || actions.onDelete) {
    columns.push({
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {actions.onView && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onView!(row.original)}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View purchase order</span>
            </Button>
          )}
          {actions.onEdit && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onEdit!(row.original)}
              className="h-8 w-8 p-0"
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit purchase order</span>
            </Button>
          )}
          {actions.onDelete && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => actions.onDelete!(row.original)}
              className="h-8 w-8 p-0 text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete purchase order</span>
            </Button>
          )}
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    });
  }

  return columns;
};

// Export all column definitions
export {
  createProductsSimpleColumns,
  createProductsComplexColumns,
  createAssembliesColumns,
  createInventoryColumns,
  createWorkOrdersSimpleColumns,
  createWorkOrdersComplexColumns,
  createInventoryTransactionsSimpleColumns,
  createInventoryTransactionsComplexColumns,
  createPurchaseOrdersComplexColumns,
};



