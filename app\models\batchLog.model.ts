import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for BatchLog document based on updated schema
export interface IBatchLog extends Document {
  _id: Types.ObjectId;
  batchId: Types.ObjectId; // Reference to the Batch this log belongs to
  timestamp: Date;
  event: string; // Description of the event
  userId: Types.ObjectId; // Reference to the User who triggered the event (Required)
  details?: string; // Optional additional details as a string
  createdAt: Date;
  updatedAt: Date;
}

// Schema for BatchLog model based on updated schema
const BatchLogSchema: Schema = new Schema(
  {
    batchId: { type: Schema.Types.ObjectId, ref: 'Batch', required: true, index: true },
    timestamp: { type: Date, default: Date.now, required: true, index: true },
    event: { type: String, required: true, index: true },
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true }, // Now required
    details: { type: String } // Changed from Mixed to String, optional
  },
  { timestamps: true } // Automatically add createdAt and updatedAt fields
);

// Create and export BatchLog model with proper TypeScript typing
const BatchLog = mongoose.models?.BatchLog as mongoose.Model<IBatchLog> || mongoose.model<IBatchLog>('BatchLog', BatchLogSchema);

// Export as both named export and default export for compatibility
export { BatchLog };
export default BatchLog;
