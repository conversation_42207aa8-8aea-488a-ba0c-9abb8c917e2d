'use client';

import { Badge } from '@/app/components/data-display/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Assembly, Part } from '@/app/components/tables/AssembliesTable/types';
import { cn } from '@/app/lib/utils';
import { AlertCircle, CheckCircle2, Clock, HelpCircle } from 'lucide-react';

interface AssemblyStatusBadgeProps {
  assembly: Assembly;
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

/**
 * Component to display assembly status with appropriate visual indicators
 */
export function AssemblyStatusBadge({
  assembly,
  showLabel = true,
  size = 'default',
  className,
}: AssemblyStatusBadgeProps) {
  // Check if assembly has valid parts
  const hasValidParts = assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0;
  const hasMissingRefs = hasValidParts && assembly.partsRequired?.some((p: {partId: string | Part}) => !p.partId);

  // Determine status based on assembly properties
  const getStatus = () => {
    if (!hasValidParts) {
      return {
        label: 'Empty',
        variant: 'outline' as const,
        icon: <HelpCircle className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-secondary'
        )} />,
        description: 'This assembly has no parts',
      };
    }

    if (hasMissingRefs) {
      return {
        label: 'Needs Review',
        variant: 'destructive' as const,
        icon: <AlertCircle className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-error'
        )} />,
        description: 'This assembly has parts with missing references',
      };
    }

    // Check if assembly is marked as active (ready for use)
    if (assembly.status === 'active') {
      return {
        label: 'Active',
        variant: 'success' as const,
        icon: <CheckCircle2 className={cn(
          size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
          'text-theme-success'
        )} />,
        description: 'This assembly is active and ready for use',
      };
    }

    // Default status
    return {
      label: 'In Progress',
      variant: 'secondary' as const,
      icon: <Clock className={cn(
        size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
        'text-theme-info'
      )} />,
      description: 'This assembly is in progress',
    };
  };

  const status = getStatus();

  // Custom variant styles using theme variables
  const getVariantStyles = () => {
    switch (status.variant) {
      case 'success':
        return 'bg-theme-success-light text-theme-success hover:bg-theme-success/10 border border-theme-success/20';
      case 'destructive':
        return 'bg-theme-error-light text-theme-error hover:bg-theme-error/10 border border-theme-error/20';
      case 'secondary':
        return 'bg-theme-info-light text-theme-info hover:bg-theme-info/10 border border-theme-info/20';
      case 'outline':
      default:
        return 'bg-theme-tertiary text-theme-secondary hover:bg-theme-hover border border-theme-secondary/20';
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              'flex items-center gap-1 font-normal',
              getVariantStyles(),
              size === 'sm' ? 'text-xs py-0 px-2' : size === 'lg' ? 'text-sm py-1 px-3' : 'text-xs py-0.5 px-2.5',
              className
            )}
          >
            {status.icon}
            {showLabel && <span>{status.label}</span>}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{status.description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
