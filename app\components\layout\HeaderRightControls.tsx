"use client";

import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/app/components/navigation/DropdownMenu';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/app/components/navigation/popover';
import { EnhancedThemeToggle } from '@/app/components/theme/EnhancedThemeToggle';
import {
    AlertCircle,
    Bell,
    Database,
    Grid,
    List,
    LogOut,
    Plus,
    Search,
    User
} from 'lucide-react';
import React, { useRef, useState } from 'react';

interface Notification {
  id: string;
  type: 'alert' | 'info' | 'success';
  message: string;
  time: string;
  read: boolean;
}

interface SystemStatus {
  connected: boolean;
  lastSync: Date;
  dbStatus: string;
  performance: string;
}

const HeaderRightControls: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentView, setCurrentView] = useState<'list' | 'grid'>('list');
  const [isMobile, setIsMobile] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'alert',
      message: 'Low inventory alert: Tamping frame stock is running low',
      time: '2 minutes ago',
      read: false
    }
  ]);

  const [systemStatus] = useState<SystemStatus>({
    connected: true,
    lastSync: new Date(),
    dbStatus: 'Operational',
    performance: 'Good'
  });

  const searchInputRef = useRef<HTMLInputElement>(null);

  // Check for mobile screen size
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  return (
    <div className="flex items-center space-x-1 md:space-x-2" data-testid="header-controls">
      {/* Global Search */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            aria-label="Open global search"
          >
            <Search size={18} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0">
          <div className="p-3">
            <div className="flex items-center border rounded-md px-3 py-2">
              <Search size={16} className="text-muted-foreground" />
              <Input
                ref={searchInputRef}
                type="text"
                placeholder="Search for products, orders..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="ml-2 border-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Quick Actions */}
      <Button variant="default" size="icon" className="rounded-full">
        <Plus size={18} />
      </Button>

      {/* System Status */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            aria-label={`System status: ${systemStatus.connected ? 'Connected' : 'Disconnected'}`}
          >
            <Database size={18} className={systemStatus.connected ? 'text-success' : 'text-destructive'} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3">
          <div className="text-sm font-medium text-foreground mb-2">System Status</div>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Database:</span>
              <span className={`font-medium ${systemStatus.connected ? 'text-success' : 'text-destructive'}`}>
                {systemStatus.connected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* View Options */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="rounded-full">
            {currentView === 'list' ? <List size={18} /> : <Grid size={18} />}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          <DropdownMenuLabel>View Options</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setCurrentView('list')}>
            <List size={16} className="mr-3" />
            <span>List View</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setCurrentView('grid')}>
            <Grid size={16} className="mr-3" />
            <span>Grid View</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Notifications */}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full relative"
            aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
          >
            <Bell size={18} className={unreadCount > 0 ? "text-foreground" : "text-muted-foreground"} />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-medium text-foreground">Notifications</div>
            {unreadCount > 0 && (
              <Button variant="link" size="sm" className="h-auto p-0 text-xs" onClick={markAllAsRead}>
                Mark all as read
              </Button>
            )}
          </div>
          <div className="space-y-2">
            {notifications.map(notification => (
              <div
                key={notification.id}
                className={`p-2 rounded-lg flex items-start ${notification.read ? 'bg-background' : 'bg-accent'}`}
                onClick={() => markAsRead(notification.id)}
              >
                <AlertCircle size={18} className="mt-0.5 mr-3 text-destructive" />
                <div className="flex-1">
                  <div className="text-sm text-foreground">{notification.message}</div>
                  <div className="text-xs text-muted-foreground mt-1">{notification.time}</div>
                </div>
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>

      {/* Enhanced Theme Toggle */}
      <div className="flex items-center">
        <EnhancedThemeToggle
          isMinimal={isMobile}
          showDropdown={!isMobile}
          className={isMobile ? "" : "ml-1"}
        />
      </div>

      {/* User Profile */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full bg-primary/10"
            aria-label="User profile menu"
          >
            <span className="text-sm font-medium text-primary">JD</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64">
          <div className="flex items-center space-x-3 p-3 border-b border-border">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-lg font-medium text-primary">JD</span>
            </div>
            <div>
              <div className="text-sm font-medium text-foreground">John Doe</div>
              <div className="text-xs text-muted-foreground">Administrator</div>
            </div>
          </div>
          <DropdownMenuItem>
            <User size={16} className="mr-3" />
            <span>My Profile</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-destructive">
            <LogOut size={16} className="mr-3" />
            <span>Sign Out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default HeaderRightControls;
