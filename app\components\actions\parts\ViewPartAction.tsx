'use client';

import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON>Content, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { Eye } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React from 'react';

interface ViewPartActionProps {
  partId: string;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  id?: string;
}

/**
 * View part action component
 * Navigates to the part detail page
 */
export function ViewPartAction({
  partId,
  variant = 'icon',
  size = 'sm',
  className,
  id,
}: ViewPartActionProps) {
  const router = useRouter();

  // Handle view action
  const handleView = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // URL-encode the part ID to handle special characters like slashes
    const encodedPartId = encodeURIComponent(partId);
    console.log('[ViewPartAction] Navigating to part with encodedPartId:', encodedPartId);

    router.push(`/parts/${encodedPartId}`);
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={handleView}
            className={className}
          >
            <Eye size={16} className="mr-2" />
            View
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={handleView}
            className={cn("h-8 px-2 hover:bg-muted/50", className)}
          >
            <Eye size={15} className="mr-1" />
            View
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleView}
                  className={cn("h-8 w-8 p-0", className)}
                  id={id || `view-part-${partId}`}
                >
                  <Eye size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>View Part</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return renderButton();
}
