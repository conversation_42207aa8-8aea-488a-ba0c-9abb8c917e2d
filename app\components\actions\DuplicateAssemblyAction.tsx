'use client';

import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Button } from '@/app/components/forms/Button';
import { LazyUnifiedAssemblyForm } from '@/app/components/forms/LazyUnifiedAssemblyForm';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { Copy } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';

interface DuplicateAssemblyActionProps {
  assembly: Assembly;
  onSuccess?: () => void;
  className?: string;
  id?: string;
  variant?: 'icon' | 'button';
  size?: 'sm' | 'md' | 'lg';
}

export function DuplicateAssemblyAction({
  assembly,
  onSuccess,
  className,
  id,
  variant = 'icon',
}: DuplicateAssemblyActionProps) {
  const router = useRouter();
  const { duplicateAssembly } = useAssemblies();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [newAssemblyId, setNewAssemblyId] = useState<string | undefined>();

  const handleDuplicate = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!assembly._id) {
      toast.error('Assembly ID is missing. Cannot duplicate.');
      setIsDialogOpen(false);
      return;
    }

    setIsDuplicating(true);
    try {
      const newAssembly = await duplicateAssembly(assembly._id);
      if (newAssembly) {
        toast.success('Assembly duplicated successfully!');
        setIsDialogOpen(false);
        if (onSuccess) onSuccess();

        if (newAssembly._id) {
          setNewAssemblyId(newAssembly._id);
          setIsEditModalOpen(true); // Open the modal instead of navigating
        }
      }
    } finally {
      setIsDuplicating(false);
    }
  };

  const handleModalSuccess = () => {
    setIsEditModalOpen(false);
    router.refresh(); // Refresh data after edit
  };

  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            onClick={() => setIsDialogOpen(true)}
            className={className}
            id={id}
            size="sm"
          >
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setIsDialogOpen(true);
                  }}
                  className={className}
                  id={id}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Duplicate Assembly</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDuplicate}
        title="Duplicate Assembly"
        description={`Are you sure you want to duplicate the assembly "${assembly.name}"? A new copy will be created.`}
        confirmLabel="Duplicate Assembly"
        isLoading={isDuplicating}
        variant="info"
      />

      {isEditModalOpen && newAssemblyId && (
        <LazyUnifiedAssemblyForm
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          assemblyId={newAssemblyId}
          mode="edit"
          onSuccess={handleModalSuccess}
        />
      )}
    </>
  );
}