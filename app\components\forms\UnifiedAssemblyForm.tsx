'use client';

import { cn } from '@/app/lib/utils';
import { getApiUrl } from '@/app/utils/apiUtils';
import { AnimatePresence, motion } from 'framer-motion';
import { Save, X } from 'lucide-react';
import { LoadingCard, LoadingInline } from '@/app/components/data-display/loading';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { showErrorToast, showValidationErrorToast, showSuccessToast, showInfoToast } from '@/app/components/feedback';

import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { <PERSON><PERSON>onte<PERSON>, CardFooter, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Button } from '@/app/components/forms/Button';
import { Card } from '@/app/components/layout/cards/card';
import { useTheme } from '@/app/contexts/ThemeContext';
import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { useAssemblyForm } from '@/app/contexts/AssemblyFormContext';
import { AssemblyFormContainer } from '@/app/components/forms/enhanced-form/AssemblyFormContainer';
import { AssemblyFormSchema } from '@/app/components/forms/enhanced-form/AssemblyFormSchema';
import { FormErrorDisplay } from '@/app/components/feedback/FormErrorDisplay';
import { transformHierarchicalPartsForApi, debugLogHierarchicalParts } from '@/app/utils/assemblyDataTransform';

interface UnifiedAssemblyFormProps {
  isOpen: boolean;
  onClose: () => void;
  assemblyId?: string | undefined;
  mode?: 'create' | 'edit';
  onSuccess?: () => void;
}

/**
 * Unified Assembly Form component - appears as a popup modal
 * Used for both creating and editing assemblies
 */
export function UnifiedAssemblyForm({ isOpen, onClose, assemblyId, mode = 'create', onSuccess }: UnifiedAssemblyFormProps) {
  const hierarchicalPartsFormRef = useRef<{ triggerSubmit: () => Promise<void> }>(null);
  const router = useRouter();
  const { theme } = useTheme();
  const { themeClasses } = useEnhancedTheme();
  const { refreshAssemblies } = useAssemblies();
  const {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    saveAssembly,
    saveAssemblyWithFreshData,
    resetForm,
    loadAssembly,
    setFormData,
    updateFormField
  } = useAssemblyForm();

  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [showPartsTable, setShowPartsTable] = useState(mode === 'edit');
  const [selectedPart, setSelectedPart] = useState<any>(null);
  const [showPartDetails, setShowPartDetails] = useState(false);
  const [showPartSearch, setShowPartSearch] = useState(false);
  const [editPartQuantity, setEditPartQuantity] = useState<{index: number, quantity: number} | null>(null);
  const [saveProgress, setSaveProgress] = useState<'idle' | 'validating' | 'saving' | 'success' | 'error'>('idle');
  const [saveError, setSaveError] = useState<string | null>(null);

  // Create a ref for the portal container
  const portalRef = useRef<HTMLDivElement | null>(null);

  // Initialize portal container on mount
  useEffect(() => {
    setIsMounted(true);

    // Create portal container if it doesn't exist
    let portalContainer = document.getElementById('assembly-form-portal');
    if (!portalContainer) {
      portalContainer = document.createElement('div');
      portalContainer.id = 'assembly-form-portal';
      document.body.appendChild(portalContainer);
    }
    portalRef.current = portalContainer as HTMLDivElement;

    return () => {
      // Safer cleanup - only remove if it exists and we're the last component using it
      const container = document.getElementById('assembly-form-portal');
      if (container && container.childNodes.length === 0 && container.parentNode === document.body) {
        document.body.removeChild(container);
      }
    };
  }, []);

  const prevAssemblyIdRef = useRef<string | undefined>(undefined);
  const prevIsOpenRef = useRef<boolean>(false);

  // Load assembly data if in edit mode
  useEffect(() => {
    const assemblyIdActuallyChanged = assemblyId !== prevAssemblyIdRef.current;
    const formJustOpened = isOpen && !prevIsOpenRef.current;

    if (isOpen && assemblyId && mode === 'edit') {
      // Only load if assemblyId actually changed, or if the form just opened with a valid assemblyId.
      // This helps prevent re-loading if other dependencies cause re-renders 
      // but these key conditions haven't meaningfully changed for a reload.
      if (assemblyIdActuallyChanged || formJustOpened) {
        console.log('UnifiedAssemblyForm: Conditions met (ID changed or form opened), loading assembly:', assemblyId);
        loadAssembly(assemblyId);
      } else {
        // console.log('UnifiedAssemblyForm: Conditions for load not met or already loaded for current state.');
      }
    }

    // Update refs for the next render to track changes accurately
    prevAssemblyIdRef.current = assemblyId;
    prevIsOpenRef.current = isOpen;
  }, [isOpen, assemblyId, mode, loadAssembly]); // Keep original dependencies for correctness

  // Create a stable default object to prevent infinite re-renders
  const defaultAssemblyData = useMemo(() => ({
    assemblyCode: '',
    name: '',
    description: '',
    status: 'active' as const,
    productId: '',
    parentId: '',
    isTopLevel: true,
    version: 1,
    manufacturingInstructions: null,
    estimatedBuildTime: null,
    partsRequired: []
  }), []);

  // Memoize formatted assembly data for the form to prevent unnecessary re-renders
  const initialAssemblyData = useMemo(() => {
    const transformStartTime = performance.now();

    // Use stable default object when formData is null/undefined (create mode)
    if (!formData) {
      console.log('[UnifiedAssemblyForm] [PERFORMANCE] No formData, returning stable default object');
      return defaultAssemblyData;
    }

    console.log('[UnifiedAssemblyForm] [PERFORMANCE] Starting data transformation...');

    // Recursive function to transform parts and maintain hierarchy
    const mapPartsRecursively = (parts: any[]): any[] => {
      return parts.map((part: any) => {
        // The 'part' object here comes from formData.partsRequired, which should already be processed by AssemblyFormContext
        // to include partDisplayIdentifier, name, description, etc., directly or via a populated partId object.

        // Get quantity from either quantityRequired (new schema) or quantity (old schema)
        const quantity = part?.quantityRequired || part?.quantity || 0;
        const unitOfMeasure = part?.unitOfMeasure || part?.unit_of_measure || 'ea';

        // Create the formatted part with all required fields for HierarchicalPartsForm
        const formattedPart: {
          id: any; // This is the BOM item's _id or a generated UUID for new items
          partId: string; // This is the actual Part's _id
          partDisplayIdentifier: string; // This is the human-readable identifier (partNumber, assemblyCode)
          name: string;
          description: string;
          quantity: number; // Keep as quantity for UI consistency in HierarchicalPartsForm
          quantityRequired?: number; // Store for DB compatibility and internal logic
          isExpanded: boolean;
          category?: string;
          currentStock?: number;
          reorderLevel?: number;
          technicalSpecs?: string;
          unitOfMeasure?: string; // Canonical field name
          children?: any[];
        } = {
          // Ensure stable ID: Use existing _id (from DB) or id (from form state).
          // Random ID generation here caused instability and re-render loops.
          // If an ID is missing, it's an upstream data issue to be addressed.
          id: part?._id || part?.id,
          partId: part?.partId, // This should be the actual Part's _id
          partDisplayIdentifier: part?.partDisplayIdentifier || part?.partId, // Use the pre-processed display identifier, fallback to partId
          name: part?.name || 'Unknown Part',
          description: part?.description || '',
          quantity: Number(quantity),
          quantityRequired: Number(quantity), // Store for DB compatibility
          isExpanded: part?.isExpanded !== undefined ? part.isExpanded : true,
          category: part?.category || '',

          currentStock: part?.currentStock || part?.inventory?.currentStock || 0,
          reorderLevel: part?.reorderLevel || 0,
          technicalSpecs: part?.technicalSpecs || '',
          unitOfMeasure: unitOfMeasure
        };

        // If this part has children, recursively map them too
        if (part.children && Array.isArray(part.children) && part.children.length > 0) {
          formattedPart.children = mapPartsRecursively(part.children);
        }

        return formattedPart;
      });
    };

    // Transform assembly parts data to match HierarchicalPartsForm expected format
    const formattedParts = formData.partsRequired && Array.isArray(formData.partsRequired)
      ? mapPartsRecursively(formData.partsRequired)
      : [];

    console.log('Formatted parts with hierarchy:', formattedParts);

    // Map legacy status values to canonical status values if needed
    let statusValue = formData?.status || 'active';
    // Remove 'in_production' and map to 'active'
    if (statusValue === 'in_production') statusValue = 'active';
    // If using legacy status values, map them to canonical values
    if ((statusValue as any) === 'design_phase' || (statusValue as any) === 'design_complete') {
      statusValue = statusValue;
    }

    // For backward compatibility, map assembly_stage to status if needed
    // This is a legacy field that should be mapped to the canonical status field
    // Cast to any to access potential legacy fields
    const legacyStage = (formData as any)?.assembly_stage as string | undefined;
    if (legacyStage && !formData?.status) {
      switch (legacyStage) {
        case 'Final Assembly':
        case 'FINAL ASSEMBLY':
          statusValue = 'active';
          break;
        case 'Sub-Assembly':
        case 'SUB ASSEMBLY':
          statusValue = 'pending_review';
          break;
      }
    }

    const transformDuration = performance.now() - transformStartTime;
    console.log(`[UnifiedAssemblyForm] [PERFORMANCE] Data transformation completed in ${transformDuration.toFixed(2)}ms`);

    // Return data in the format expected by HierarchicalPartsForm
    return {
      assemblyCode: formData?.assemblyCode || '',
      name: formData?.name || '',
      description: formData?.description || '',
      status: statusValue, // Only allowed values
      productId: formData?.productId || '', // Always a string for Select
      parentId: formData?.parentId || '', // Always a string for Select
      isTopLevel: formData?.isTopLevel === undefined ? true : formData.isTopLevel,
      version: formData?.version || 1,
      manufacturingInstructions: formData?.manufacturingInstructions || null,
      estimatedBuildTime: formData?.estimatedBuildTime || null,
      partsRequired: formattedParts
    };
  }, [
    // Only include formData itself as dependency to prevent infinite loops
    // The useMemo will only re-run when formData reference changes
    formData
  ]);

  // Log initialAssemblyData before passing to HierarchicalPartsForm
  useEffect(() => {
    if (isOpen) {
      console.log('[UnifiedAssemblyForm] initialAssemblyData for HierarchicalPartsForm:', JSON.stringify(initialAssemblyData, null, 2));
    }
  }, [initialAssemblyData, isOpen]);

  // Handle save button click with enhanced progress tracking and error handling
  const handleSave = async () => {
    console.log('[UnifiedAssemblyForm] handleSave called');
    const saveStartTime = performance.now();

    if (!hierarchicalPartsFormRef.current) {
      console.error("HierarchicalPartsForm ref not available, cannot save.");
      showValidationErrorToast("Cannot save assembly. Form component is missing or not ready.");
      return;
    }

    try {
      console.log('[UnifiedAssemblyForm] Starting save process...');
      console.log(`[UnifiedAssemblyForm] [PERFORMANCE] Save process started at ${saveStartTime.toFixed(2)}ms`);

      setSaveProgress('validating');
      setSaveError(null);

      // Remove premature validation - let HierarchicalPartsForm handle all validation
      // The onFormSubmit callback has proper validation logic that checks both
      // the fresh form data and context data
      console.log('[UnifiedAssemblyForm] Triggering form submit...');
      const submitStartTime = performance.now();

      // Add a timeout for the save operation (reduced from 30s to 15s for better UX)
      const saveTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Save operation timed out after 15 seconds')), 15000)
      );

      const saveOperation = hierarchicalPartsFormRef.current.triggerSubmit();

      // Race between save operation and timeout
      await Promise.race([saveOperation, saveTimeout]);

      const submitDuration = performance.now() - submitStartTime;
      console.log(`[UnifiedAssemblyForm] [PERFORMANCE] Form submit completed in ${submitDuration.toFixed(2)}ms`);
      console.log('[UnifiedAssemblyForm] Save operation completed successfully');
      // Note: setSaveProgress('success') is handled in onFormSubmit callback

    } catch (error) {
      const saveDuration = performance.now() - saveStartTime;
      console.error(`[UnifiedAssemblyForm] [PERFORMANCE] Save failed after ${saveDuration.toFixed(2)}ms`);
      console.error("Error during form submission trigger in UnifiedAssemblyForm:", error);
      setSaveProgress('error');

      let errorMessage = 'An unexpected error occurred during submission.';

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('timed out')) {
          errorMessage = 'Save operation timed out. Please check your connection and try again.';
        } else if (error.message.includes('validation') || error.message.includes('required')) {
          errorMessage = `Validation Error: ${error.message}`;
        } else if (error.message.includes('duplicate') || error.message.includes('already exists')) {
          errorMessage = 'Assembly code already exists. Please use a unique assembly code.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('Assembly') || error.message.includes('part')) {
          errorMessage = error.message;
        } else {
          errorMessage = error.message || errorMessage;
        }
      }

      setSaveError(errorMessage);
      showErrorToast({ error: errorMessage });

      // Reset progress after showing error
      setTimeout(() => {
        setSaveProgress('idle');
        setSaveError(null);
      }, 5000);
    }
  };

  // Handle close with confirmation if form is dirty
  const handleClose = () => {
    if (isDirty) {
      setShowConfirmClose(true);
    } else {
      resetForm();
      onClose();
    }
  };

  // Confirm close and discard changes
  const confirmClose = () => {
    resetForm();
    setShowConfirmClose(false);
    onClose();
  };

  // Cancel close
  const cancelClose = () => {
    setShowConfirmClose(false);
  };

  // Log modal state for debugging
  useEffect(() => {
    console.log('Modal state changed:', { isOpen, assemblyId, mode });
  }, [isOpen, assemblyId, mode]);

  // Add a function to view part details
  const handleViewPart = (part: any) => {
    setSelectedPart(part);
    setShowPartDetails(true);
  };

  // Add a function to edit part
  const handleEditPart = (part: any, index: number) => {
    // Set up quantity editing
    setEditPartQuantity({
      index,
      quantity: part.quantity
    });
    showInfoToast(`Editing quantity for part ${typeof part.partId === 'object' ? part.partId.name : 'Unknown Part'}`);
  };

  // Save the updated part quantity
  const savePartQuantity = () => {
    if (editPartQuantity) {
      const { index, quantity } = editPartQuantity;

      // Create a copy of the parts array
      const updatedParts = [...(formData.partsRequired || [])];

      // Check if the part exists at the given index
      const existingPart = updatedParts[index];
      if (!existingPart) {
        showValidationErrorToast('Part not found at the specified index');
        setEditPartQuantity(null);
        return;
      }

      // Update the quantity for the specific part - ensure we follow DB schema
      updatedParts[index] = {
        ...existingPart,
        // Only include valid fields as per our schema
        partId: existingPart.partId,
        quantityRequired: quantity,
        unitOfMeasure: existingPart.unitOfMeasure || 'ea'
      };

      // Update form data with the updated parts
      setFormData({
        ...formData,
        partsRequired: updatedParts
      });

      showSuccessToast('Quantity updated');
      setEditPartQuantity(null);
    }
  };

  // Add a function to delete part
  const handleDeletePart = (part: any, index: number) => {
    if (confirm(`Are you sure you want to delete part ${part.name || part.partId}?`)) {
      // Create a copy of form data to modify
      const updatedParts = [...(formData.partsRequired || [])];
      updatedParts.splice(index, 1);

      // Update form data with the part removed
      setFormData({
        ...formData,
        partsRequired: updatedParts
      });

      showSuccessToast(`Part removed from assembly`);
    }
  };

  // Add state for products and assemblies
  const [products, setProducts] = useState<any[]>([]);
  const [assemblies, setAssemblies] = useState<any[]>([]);

  useEffect(() => {
    // Fetch products
    fetch(getApiUrl('/api/products?page=1&limit=100'))
      .then(res => res.json())
      .then(data => setProducts(data.data || []));
    // Fetch assemblies (exclude current in edit mode)
    fetch(getApiUrl('/api/assemblies?page=1&limit=100'))
      .then(res => res.json())
      .then(data => setAssemblies(data.data || []));
  }, [assemblyId]);

  // Handle form submission - extracted to useCallback to prevent infinite re-renders
  // FIXED: Removed formData dependency to prevent infinite recursion (similar to AssemblyFormContent.tsx)
  const handleFormSubmit = useCallback(async (data: any) => {
    const submitStartTime = performance.now();
    console.log('[UnifiedAssemblyForm] [PERFORMANCE] onFormSubmit started');

    try {
      // Prevent concurrent submissions
      if (isSaving) {
        console.log('[UnifiedAssemblyForm] Save already in progress, ignoring duplicate submission');
        return;
      }

      console.log('[UnifiedAssemblyForm] HierarchicalPartsForm submitted fresh data:', data);

      // Get current formData from context (avoid stale closure)
      const currentFormData = formData;

      // Validate required fields from the form data
      if (!data.assemblyCode?.trim() && !currentFormData?.assemblyCode?.trim()) {
        throw new Error('Assembly code is required');
      }
      if (!data.name?.trim() && !currentFormData?.name?.trim()) {
        throw new Error('Assembly name is required');
      }

      setSaveProgress('saving');

      // FIXED: Now properly use the fresh data from HierarchicalPartsForm
      // Transform the parts data to match the database schema (quantityRequired) - HIERARCHICAL SUPPORT
      console.log('[UnifiedAssemblyForm] Raw hierarchical form data before transformation:', data.partsRequired);
      debugLogHierarchicalParts(data.partsRequired || [], 'Before Transformation');

      const transformedPartsRequired = transformHierarchicalPartsForApi(data.partsRequired || []);

      console.log('[UnifiedAssemblyForm] Transformed hierarchical parts data:', transformedPartsRequired);
      debugLogHierarchicalParts(transformedPartsRequired, 'After Transformation');

      // Map status values to match Assembly type
      const rawStatus = data.status || currentFormData?.status || 'active';
      let mappedStatus: 'active' | 'pending_review' | 'in_production' | 'obsolete' = 'active';

      if (rawStatus === 'active') {
        mappedStatus = 'active';
      } else if (rawStatus === 'pending_review') {
        mappedStatus = 'pending_review';
      } else if (rawStatus === 'obsolete') {
        mappedStatus = 'obsolete';
      } else if (rawStatus === 'design_phase' || rawStatus === 'design_complete') {
        mappedStatus = 'pending_review'; // Map design phases to pending_review
      } else {
        mappedStatus = 'active'; // Default fallback
      }

      // Merge the fresh form data from HierarchicalPartsForm with current context data
      const freshDataToSave = {
        ...currentFormData, // Start with current context data (preserves _id, etc.)
        // Use the fresh data from HierarchicalPartsForm for all assembly fields
        assemblyCode: data.assemblyCode || currentFormData?.assemblyCode || '',
        name: data.name || currentFormData?.name || '',
        description: data.description || currentFormData?.description || '',
        status: mappedStatus,
        productId: data.productId || currentFormData?.productId || null,
        parentId: data.parentId || currentFormData?.parentId || null,
        isTopLevel: data.isTopLevel !== undefined ? data.isTopLevel : (currentFormData?.isTopLevel !== undefined ? currentFormData.isTopLevel : true),
        version: data.version || currentFormData?.version || 1,
        manufacturingInstructions: data.manufacturingInstructions || currentFormData?.manufacturingInstructions || null,
        estimatedBuildTime: data.estimatedBuildTime || currentFormData?.estimatedBuildTime || null,
        // Use transformed parts data from HierarchicalPartsForm
        partsRequired: transformedPartsRequired
      };

      console.log('[UnifiedAssemblyForm] Prepared fresh data for saving:', freshDataToSave);

      const apiCallStartTime = performance.now();
      console.log('[UnifiedAssemblyForm] [PERFORMANCE] Starting API call...');

      // Use the new saveAssemblyWithFreshData method to save directly with success callback
      const success = await saveAssemblyWithFreshData(freshDataToSave, async () => {
        // Success callback - refresh assemblies list and close modal
        try {
          console.log('[UnifiedAssemblyForm] Save successful, refreshing assemblies list...');
          await refreshAssemblies({ includeParts: true });

          // Call external success callback if provided
          if (onSuccess) {
            onSuccess();
          }
        } catch (refreshError) {
          console.error('[UnifiedAssemblyForm] Error refreshing assemblies:', refreshError);
          // Don't fail the save operation if refresh fails
        }
      });

      const apiCallDuration = performance.now() - apiCallStartTime;
      const totalSubmitDuration = performance.now() - submitStartTime;
      console.log(`[UnifiedAssemblyForm] [PERFORMANCE] API call completed in ${apiCallDuration.toFixed(2)}ms`);
      console.log(`[UnifiedAssemblyForm] [PERFORMANCE] Total submit process completed in ${totalSubmitDuration.toFixed(2)}ms`);

      if (success) {
        setSaveProgress('success');
        showSuccessToast(`Assembly ${mode === 'edit' ? 'updated' : 'created'} successfully`);

        // Auto-close after success with a small delay for user feedback
        setTimeout(() => {
          resetForm();
          onClose();
        }, 1500);
      } else {
        setSaveProgress('error');
        const errorMsg = 'Failed to save assembly. Please check the console for details.';
        setSaveError(errorMsg);
        console.error('[UnifiedAssemblyForm] Failed to save assembly with fresh data');
        showErrorToast({ error: errorMsg });
        throw new Error(errorMsg);
      }
    } catch (error) {
      setSaveProgress('error');
      console.error('[UnifiedAssemblyForm] Error in onFormSubmit:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred while saving';
      setSaveError(errorMessage);
      showErrorToast({ error: errorMessage });
      throw error; // Re-throw to be caught by handleSave
    }
  }, [
    // FIXED: Only include stable dependencies, not formData to prevent infinite recursion
    isSaving,
    setSaveProgress,
    setSaveError,
    saveAssemblyWithFreshData,
    refreshAssemblies,
    onSuccess,
    mode,
    resetForm,
    onClose,
    formData?._id // Only include formData._id for metadata, not full formData
  ]);

  if (!isOpen || !isMounted) return null;

  // Modal content to be rendered in the portal
  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center pointer-events-auto">
          {/* Backdrop with blur effect */}
          <motion.div
            className="absolute inset-0 backdrop-blur-sm bg-background/80"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            className="relative z-[9999] w-full max-w-4xl max-h-[90vh] rounded-xl shadow-2xl pointer-events-auto flex flex-col"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 10 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            <Card className="p-0 border border-border flex flex-col h-full shadow-lg" style={{ overflow: 'hidden' }}>
              {/* Fixed Header */}
              <CardHeader className={cn(
                "border-b border-border flex flex-row items-center justify-between flex-shrink-0",
                themeClasses.card,
                "shadow-sm"
              )}>
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <CardTitle className="text-xl font-bold">
                    {mode === 'edit' ? 'Edit Assembly' : 'Create New Assembly'}
                  </CardTitle>
                </motion.div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="rounded-full h-8 w-8 p-0 flex items-center justify-center hover:bg-muted"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardHeader>

              {isLoading ? (
                /* Loading State */
                <CardContent className="p-8 flex-1">
                  <LoadingCard message="Loading assembly data..." />
                </CardContent>
              ) : (
                <>
                  {/* Scrollable Content Area */}
                  <CardContent className={cn(
                    "flex-1 overflow-y-auto p-6",
                    themeClasses.card,
                    "scrollbar-thin scrollbar-track-muted/50 scrollbar-thumb-muted-foreground/50 hover:scrollbar-thumb-muted-foreground/70"
                  )}>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="space-y-6"
                    >
                      {/* Basic Assembly Information */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Basic Information</h3>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <label className="mb-2 block text-sm font-medium">Assembly Code</label>
                            <Input
                              placeholder="Enter assembly code"
                              value={formData?.assemblyCode || ''}
                              onChange={(e) => updateFormField('assemblyCode', e.target.value)}
                              className="w-full"
                              disabled={isLoading || mode === 'edit'} // Can't edit assembly code in edit mode
                            />
                          </div>

                          <div>
                            <label className="mb-2 block text-sm font-medium">Status</label>
                            <Select
                              value={formData?.status || 'active'}
                              onValueChange={(value) => updateFormField('status', value)}
                              disabled={isLoading}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="pending_review">Pending Review</SelectItem>
                                <SelectItem value="design_phase">Design Phase</SelectItem>
                                <SelectItem value="design_complete">Design Complete</SelectItem>
                                <SelectItem value="obsolete">Obsolete</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <label htmlFor="assemblyName" className="mb-2 block text-sm font-medium">Assembly Name</label>
                            <Input
                              id="assemblyName"
                              placeholder="Enter assembly name"
                              value={formData?.name || ''}
                              onChange={(e) => updateFormField('name', e.target.value)}
                              disabled={isSaving}
                            />
                          </div>
                          <div>
                            <label htmlFor="assemblyDescription" className="mb-2 block text-sm font-medium">Description</label>
                            <Input
                              id="assemblyDescription"
                              placeholder="Enter assembly description"
                              value={formData?.description || ''}
                              onChange={(e) => updateFormField('description', e.target.value)}
                              className="w-full"
                              disabled={isSaving}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Additional Assembly Details */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Assembly Details</h3>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <label htmlFor="assemblyVersion" className="mb-2 block text-sm font-medium">Version</label>
                            <Input
                              id="assemblyVersion"
                              type="number"
                              placeholder="Enter assembly version"
                              value={formData?.version || 1}
                              onChange={(e) => updateFormField('version', parseInt(e.target.value, 10) || 1)}
                              disabled={isSaving}
                            />
                          </div>
                          <div>
                            <label htmlFor="isTopLevel" className="mb-2 block text-sm font-medium">Is Top Level Assembly?</label>
                            <Select
                              value={formData?.isTopLevel === true ? 'true' : 'false'}
                              onValueChange={(value) => updateFormField('isTopLevel', value === 'true')}
                              disabled={isSaving}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select if top level" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="true">Yes</SelectItem>
                                <SelectItem value="false">No (Sub-assembly)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <label htmlFor="productId" className="mb-2 block text-sm font-medium">Product (Optional)</label>
                            <Select
                              value={formData?.productId ? formData.productId : 'none'}
                              onValueChange={val => updateFormField('productId', val === 'none' ? null : val)}
                              disabled={isSaving}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a product" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                {products.map((product) => (
                                  <SelectItem key={product._id} value={product._id}>
                                    {product.name} ({product.productCode || product._id})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <label htmlFor="parentId" className="mb-2 block text-sm font-medium">Parent Assembly (Optional)</label>
                            <Select
                              value={formData?.parentId ? formData.parentId : 'none'}
                              onValueChange={val => updateFormField('parentId', val === 'none' ? null : val)}
                              disabled={isSaving || formData?.isTopLevel === true}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select a parent assembly" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">None</SelectItem>
                                {assemblies
                                  .filter(a => !assemblyId || a._id !== assemblyId)
                                  .map((assembly) => (
                                    <SelectItem key={assembly._id} value={assembly._id}>
                                      {assembly.name} ({assembly.assemblyCode || assembly._id})
                                    </SelectItem>
                                  ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid gap-4 md:grid-cols-2">
                          <div>
                            <label htmlFor="manufacturingInstructions" className="mb-2 block text-sm font-medium">Manufacturing Instructions (Optional)</label>
                            <Input
                              id="manufacturingInstructions"
                              placeholder="Enter manufacturing instructions or link to SOP"
                              value={formData?.manufacturingInstructions || ''}
                              onChange={(e) => updateFormField('manufacturingInstructions', e.target.value)}
                              disabled={isSaving}
                            />
                          </div>
                          <div>
                            <label htmlFor="estimatedBuildTime" className="mb-2 block text-sm font-medium">Estimated Build Time (Optional)</label>
                            <Input
                              id="estimatedBuildTime"
                              placeholder="e.g., 1.5 hours, 30 minutes"
                              value={formData?.estimatedBuildTime || ''}
                              onChange={(e) => updateFormField('estimatedBuildTime', e.target.value)}
                              disabled={isSaving}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Parts Section - Now inside scrollable area */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Parts Required</h3>
                        <HierarchicalPartsForm
                          ref={hierarchicalPartsFormRef}
                          initialData={initialAssemblyData} // Pass the memoized data here
                          mode={mode}
                          onFormSubmit={handleFormSubmit} // Use the memoized callback to prevent infinite re-renders
                        />
                      </div>
                    </motion.div>
                  </CardContent>

                  {/* Fixed Footer */}
                  <CardFooter className={cn(
                    "flex justify-end gap-3 p-4 border-t border-border flex-shrink-0",
                    themeClasses.card,
                    "bg-muted/30"
                  )}>
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      className="px-4 py-2"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSave}
                      disabled={isSaving || isLoading || saveProgress === 'saving' || saveProgress === 'validating'}
                      className={cn(
                        "px-6 py-2 font-medium transition-all duration-300",
                        {
                          'bg-success hover:bg-success/90 text-success-foreground': saveProgress === 'success',
                          'bg-destructive hover:bg-destructive/90 text-destructive-foreground': saveProgress === 'error',
                          'bg-primary hover:bg-primary/90 text-primary-foreground': saveProgress !== 'success' && saveProgress !== 'error'
                        }
                      )}
                    >
                      {saveProgress === 'validating' ? (
                        <>
                          <LoadingInline size="sm" />
                          Validating...
                        </>
                      ) : saveProgress === 'saving' || isSaving ? (
                        <>
                          <LoadingInline size="sm" />
                          Saving...
                        </>
                      ) : saveProgress === 'success' ? (
                        <>
                          <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Saved!
                        </>
                      ) : saveProgress === 'error' ? (
                        <>
                          <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          Try Again
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Assembly
                        </>
                      )}
                    </Button>
                  </CardFooter>

                  {/* Error Display */}
                  {saveError && saveProgress === 'error' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className={cn(
                        "px-4 py-3 border-t border-destructive/30",
                        "bg-destructive/10 text-destructive"
                      )}
                    >
                      <div className="flex items-start gap-2">
                        <svg className="h-5 w-5 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div className="flex-1">
                          <p className="text-sm font-medium">Save Failed</p>
                          <p className="text-sm opacity-90 mt-1">{saveError}</p>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Visual separator between form sections */}
                  <div className="relative">
                    <div className="absolute inset-x-0 h-px bg-gradient-to-r from-transparent via-gray-400/50 dark:via-gray-600/50 to-transparent"></div>
                  </div>

                  {/* Part Search Modal (existing code needed by HierarchicalPartsForm potentially - keep for now, but maybe move logic) */}
                  {showPartSearch && (
                    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center">
                      {/* ... PartSearch Modal Content ... */}
                    </div>
                  )}

                  {/* Part Details Modal (existing code - keep for now) */}
                  {showPartDetails && selectedPart && (
                    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center">
                      {/* ... Part Details Modal Content ... */}
                    </div>
                  )}
                </>
              )}
            </Card>
          </motion.div>


          {/* Confirmation Dialog */}
          <AnimatePresence>
            {showConfirmClose && (
              <motion.div
                className="fixed inset-0 z-[10000] flex items-center justify-center pointer-events-auto"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <motion.div
                  className="absolute inset-0 backdrop-blur-sm bg-background/90"
                  onClick={cancelClose}
                />
                <motion.div
                  className="relative z-[10000] max-w-md w-full mx-4 pointer-events-auto"
                  initial={{ scale: 0.9, opacity: 0, y: 20 }}
                  animate={{ scale: 1, opacity: 1, y: 0 }}
                  exit={{ scale: 0.9, opacity: 0, y: 10 }}
                  transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                >
                  <Card className={cn("rounded-xl shadow-xl border-2 border-destructive/20 bg-card/95 backdrop-blur-sm", themeClasses.card)}>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-2">Discard changes?</h3>
                      <p className="text-muted-foreground mb-4">
                        You have unsaved changes that will be lost if you close this form.
                      </p>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={cancelClose}
                          className="px-4 py-2"
                        >
                          Continue Editing
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={confirmClose}
                          className="px-4 py-2"
                        >
                          Discard Changes
                        </Button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
    </AnimatePresence>
  );

  // Render the modal content in the portal
  // Only create portal if component is mounted and portal container exists
  return (isMounted && portalRef.current) ? createPortal(modalContent, portalRef.current) : null;
}