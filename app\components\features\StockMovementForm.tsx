'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Label } from '@/app/components/forms/label';
import { Textarea } from '@/app/components/forms/Textarea/Textarea';
import { PartSearch, PartSearchResult } from '@/app/components/search/PartSearch';
import { toast } from 'sonner';
import { StockMovementRequest } from '@/app/services/stockmovement.service';

interface Part {
  _id: string;
  partNumber: string;
  name: string;
  businessName?: string;
  inventory?: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    warehouseId?: string;
  };
}

interface Warehouse {
  _id: string;
  warehouseCode: string;
  name: string;
}

interface Location {
  _id: string;
  name: string;
  warehouseId: string;
  locationType: string;
  isActive: boolean;
}

interface StockMovementFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Interface for CreateTransactionRequest (expected by /api/inventory-transactions)
// V4 Schema: Updated to use locationId instead of warehouseId
interface CreateTransactionRequest {
  partId: string;
  itemType?: 'Part' | 'Assembly' | 'Product';
  warehouseId: string; // Legacy field for backward compatibility
  transactionType: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment' | 'internal_transfer';
  quantity: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;
  // V4 Schema: Primary location fields
  fromLocationId?: string;
  toLocationId?: string;
  fromStockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  toStockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
}

/**
 * Transform StockMovementRequest to CreateTransactionRequest format
 */
function transformToCreateTransactionRequest(formData: Partial<StockMovementRequest> & {
  fromWarehouseId?: string;
  toWarehouseId?: string;
}): CreateTransactionRequest {
  // Map movement types to legacy transaction types
  const movementTypeToTransactionType: Record<string, string> = {
    'purchase_receipt': 'stock_in_purchase',
    'sales_shipment': 'sales_shipment',
    'internal_transfer': 'internal_transfer',
    'adjustment': 'adjustment_cycle_count',
    'process_move': 'internal_transfer',
    'scrap_disposal': 'sales_shipment'
  };

  const transactionType = movementTypeToTransactionType[formData.movementType || 'purchase_receipt'] || 'adjustment_cycle_count';

  // V4 Schema: Determine warehouse and location IDs from form data
  let warehouseId = '';
  let fromLocationId: string | undefined;
  let toLocationId: string | undefined;
  let fromStockType: string | undefined;
  let toStockType: string | undefined;

  // V4 Schema: Use explicit warehouse selection for backward compatibility
  // In V4, we need to map warehouse to a default location
  // FIXED: Handle both fromWarehouseId and toWarehouseId for internal transfers
  if (formData.fromWarehouseId) {
    fromLocationId = formData.fromWarehouseId; // Temporary: use warehouse as location
    warehouseId = formData.fromWarehouseId; // Set primary warehouse to source
  }
  if (formData.toWarehouseId) {
    toLocationId = formData.toWarehouseId; // Temporary: use warehouse as location
    // For internal transfers, keep the source warehouse as primary
    if (!warehouseId) {
      warehouseId = formData.toWarehouseId;
    }
  }

  // V4 Schema: Set location and stock type information using locationId
  if (formData.from) {
    fromLocationId = formData.from.locationId;
    fromStockType = formData.from.stockType;
  }
  if (formData.to) {
    toLocationId = formData.to.locationId;
    toStockType = formData.to.stockType;
  }

  // Validation: Ensure warehouseId is provided
  if (!warehouseId) {
    throw new Error('Warehouse ID is required for inventory transactions');
  }

  // FIXED: Additional validation for internal transfers
  if (transactionType === 'internal_transfer') {
    if (!fromLocationId || fromLocationId === '') {
      throw new Error('Source location is required for internal transfers');
    }
    if (!toLocationId || toLocationId === '') {
      throw new Error('Destination location is required for internal transfers');
    }
  }

  const request: CreateTransactionRequest = {
    partId: formData.partId || '',
    itemType: 'Part',
    warehouseId,
    transactionType: transactionType as any,
    quantity: formData.quantity || 1,
    transactionDate: formData.transactionDate || new Date(),
    referenceNumber: formData.referenceNumber || '',
    referenceType: (formData.referenceType === 'ProcessOrder' || !formData.referenceType) ? 'StockAdjustment' : formData.referenceType,
    userId: formData.userId || '',
    notes: formData.notes || '',
    fromLocationId: fromLocationId || '',
    toLocationId: toLocationId || '',
    fromStockType: fromStockType as any,
    toStockType: toStockType as any
  };

  return request;
}

const MOVEMENT_TYPES = {
  purchase_receipt: {
    label: 'Purchase Receipt',
    description: 'Receive goods from supplier',
    fromRequired: false,
    toRequired: true,
    defaultToStockType: 'raw'
  },
  sales_shipment: {
    label: 'Sales Shipment',
    description: 'Ship finished goods to customer',
    fromRequired: true,
    toRequired: false,
    defaultFromStockType: 'finished'
  },
  internal_transfer: {
    label: 'Internal Transfer',
    description: 'Move between warehouses or stock types',
    fromRequired: true,
    toRequired: true
  },
  scrap_disposal: {
    label: 'Scrap Disposal',
    description: 'Dispose rejected parts',
    fromRequired: true,
    toRequired: true,
    defaultFromStockType: 'rejected',
    defaultToStockType: 'scrap'
  },
  process_move: {
    label: 'Process Move',
    description: 'Move between manufacturing stages',
    fromRequired: true,
    toRequired: true
  },
  adjustment: {
    label: 'Stock Adjustment',
    description: 'Manual stock correction',
    fromRequired: false,
    toRequired: false
  }
};

const STOCK_TYPES = [
  { value: 'raw', label: 'Raw Materials' },
  { value: 'hardening', label: 'In Hardening' },
  { value: 'grinding', label: 'In Grinding' },
  { value: 'finished', label: 'Finished Goods' },
  { value: 'rejected', label: 'Rejected Parts' },
  { value: 'scrap', label: 'Scrap' }
];

export function StockMovementForm({ isOpen, onClose, onSuccess }: StockMovementFormProps) {
  const [parts, setParts] = useState<Part[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [fromLocations, setFromLocations] = useState<Location[]>([]);
  const [toLocations, setToLocations] = useState<Location[]>([]);
  const [selectedPart, setSelectedPart] = useState<PartSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  // Enhanced form data structure to handle warehouse selection separately from location selection
  const [formData, setFormData] = useState<Partial<StockMovementRequest> & {
    fromWarehouseId?: string;
    toWarehouseId?: string;
  }>({
    movementType: 'purchase_receipt',
    quantity: 1,
    transactionDate: new Date(),
    partId: '', // Initialize partId field
    userId: '6751b8b8e5b8b8b8b8b8b8b8', // Default user ID - should be replaced with actual user session
    fromWarehouseId: '',
    toWarehouseId: ''
  });

  // Fetch parts and warehouses on component mount and reset form
  useEffect(() => {
    if (isOpen) {
      fetchParts();
      fetchWarehouses();
      // Reset form when opening
      setFormData({
        movementType: 'purchase_receipt',
        quantity: 1,
        transactionDate: new Date(),
        partId: '',
        userId: '6751b8b8e5b8b8b8b8b8b8b8', // Default user ID
        fromWarehouseId: '',
        toWarehouseId: ''
      });

    }
  }, [isOpen]);

  const fetchParts = async () => {
    try {
      const response = await fetch('/api/parts?limit=100');
      const result = await response.json();
      if (result.data) {
        setParts(result.data);
      }
    } catch (error) {
      console.error('Error fetching parts:', error);
      toast.error('Failed to load parts');
    }
  };

  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses');
      const result = await response.json();
      if (result.data) {
        setWarehouses(result.data);
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error);
      toast.error('Failed to load warehouses');
    }
  };

  const loadLocationsForWarehouse = async (warehouseId: string): Promise<Location[]> => {
    try {
      const response = await fetch(`/api/warehouses/${warehouseId}/locations`);
      const data = await response.json();
      if (data.success) {
        return data.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error loading locations for warehouse:', error);
      return [];
    }
  };

  const handleFromWarehouseChange = async (warehouseId: string) => {
    const locations = await loadLocationsForWarehouse(warehouseId);
    setFromLocations(locations);
    setFormData(prev => ({
      ...prev,
      fromWarehouseId: warehouseId,
      from: {
        // FIXED: Don't auto-select a location - let user choose explicitly
        // Using warehouseId as locationId causes "Insufficient stock" errors
        locationId: locations.length > 0 && locations[0] ? locations[0]._id : '', // Auto-select first location if available
        stockType: prev.from?.stockType || (movementConfig as any)?.defaultFromStockType || 'finished'
      }
    }));
  };

  const handleToWarehouseChange = async (warehouseId: string) => {
    const locations = await loadLocationsForWarehouse(warehouseId);
    setToLocations(locations);
    setFormData(prev => ({
      ...prev,
      toWarehouseId: warehouseId,
      to: {
        locationId: locations.length > 0 && locations[0] ? locations[0]._id : warehouseId, // Use first location or warehouse as fallback
        stockType: prev.to?.stockType || (movementConfig as any)?.defaultToStockType || 'raw'
      }
    }));
  };

  const movementConfig = MOVEMENT_TYPES[formData.movementType as keyof typeof MOVEMENT_TYPES];

  // Handle part selection from PartSearch component
  const handlePartSelect = (part: PartSearchResult) => {
    setSelectedPart(part);
    setFormData(prev => ({ ...prev, partId: part._id }));
  };

  // V4 Schema: Auto-populate location based on part's existing inventory
  useEffect(() => {
    // TODO: Implement location auto-population from inventories collection
    // For now, skip auto-population as parts no longer have embedded inventory
    if (false) { // Disabled for V4 schema
      if (movementConfig?.fromRequired) {
        setFormData(prev => ({
          ...prev,
          from: {
            locationId: '', // V4 Schema: Use locationId instead of warehouseId
            stockType: (movementConfig as any).defaultFromStockType || 'finished'
          }
        }));
      }

      if (movementConfig?.toRequired) {
        setFormData(prev => ({
          ...prev,
          to: {
            locationId: '', // V4 Schema: Use locationId instead of warehouseId
            stockType: (movementConfig as any).defaultToStockType || 'raw'
          }
        }));
      }
    }
  }, [selectedPart, formData.movementType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!formData.partId) {
        toast.error('Please select a part');
        setLoading(false);
        return;
      }
      if (!formData.movementType) {
        toast.error('Please select a movement type');
        setLoading(false);
        return;
      }
      if (!formData.quantity || formData.quantity <= 0) {
        toast.error('Please enter a valid quantity');
        setLoading(false);
        return;
      }
      if (!formData.userId) {
        toast.error('User ID is required');
        setLoading(false);
        return;
      }

      // Validate warehouse selection based on movement type
      const movementConfig = MOVEMENT_TYPES[formData.movementType as keyof typeof MOVEMENT_TYPES];
      if (movementConfig?.fromRequired && !formData.fromWarehouseId) {
        toast.error('Please select a source warehouse');
        setLoading(false);
        return;
      }
      if (movementConfig?.toRequired && !formData.toWarehouseId) {
        toast.error('Please select a destination warehouse');
        setLoading(false);
        return;
      }

      // FIXED: Validate that a specific location is selected for debit operations
      // This prevents "Insufficient stock" errors caused by missing locationId
      if (movementConfig?.fromRequired && (!formData.from?.locationId || formData.from.locationId === '')) {
        toast.error('Please select a specific location within the warehouse');
        setLoading(false);
        return;
      }

      // Transform StockMovementRequest to CreateTransactionRequest format
      let createTransactionRequest;
      try {
        createTransactionRequest = transformToCreateTransactionRequest(formData);
      } catch (transformError: any) {
        toast.error(transformError.message || 'Failed to prepare transaction request');
        setLoading(false);
        return;
      }

      console.log('Submitting inventory transaction request:', createTransactionRequest);

      const response = await fetch('/api/inventory-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createTransactionRequest),
      });

      const result = await response.json();
      console.log('Inventory transaction response:', result);

      if (result.success) {
        toast.success(result.data.message || 'Stock movement completed successfully');
        onSuccess();
        onClose();
        // Reset form
        setFormData({
          movementType: 'purchase_receipt',
          quantity: 1,
          transactionDate: new Date(),
          partId: '',
          userId: '6751b8b8e5b8b8b8b8b8b8b8',
          fromWarehouseId: '',
          toWarehouseId: ''
        });
        setSelectedPart(null);
      } else {
        toast.error(result.error || 'Failed to execute stock movement');
      }
    } catch (error) {
      console.error('Error executing stock movement:', error);
      toast.error('Failed to execute stock movement');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>Stock Movement</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Movement Type */}
            <div>
              <Label htmlFor="movementType">Movement Type</Label>
              <Select
                value={formData.movementType || 'purchase_receipt'}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  movementType: value as any,
                  from: null,
                  to: null
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select movement type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(MOVEMENT_TYPES).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div>
                        <div className="font-medium">{config.label}</div>
                        <div className="text-sm text-muted-foreground">{config.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Part Selection using PartSearch component */}
            <PartSearch
              selectedPart={selectedPart}
              onPartSelect={handlePartSelect}
              placeholder="Search parts by name, number, or description..."
              label="Part"
              showStockLevels={true}
              minQueryLength={3}
              debounceTime={300}
              maxResults={10}
              disabled={loading}
            />

            {/* Show current stock levels for selected part */}
            {selectedPart?.inventory?.stockLevels && (
              <div className="bg-muted p-3 rounded-md">
                <div className="text-sm font-medium mb-2">Current Stock Levels:</div>
                <div className="grid grid-cols-5 gap-2 text-xs">
                  <div>Raw: {selectedPart.inventory.stockLevels.raw}</div>
                  <div>Hardening: {selectedPart.inventory.stockLevels.hardening}</div>
                  <div>Grinding: {selectedPart.inventory.stockLevels.grinding}</div>
                  <div>Finished: {selectedPart.inventory.stockLevels.finished}</div>
                  <div>Rejected: {selectedPart.inventory.stockLevels.rejected}</div>
                </div>
              </div>
            )}

            {/* Quantity */}
            <div>
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                type="number"
                min="1"
                value={formData.quantity}
                onChange={(e) => setFormData(prev => ({ ...prev, quantity: parseInt(e.target.value) }))}
                required
              />
            </div>

            {/* From Location (if required) */}
            {movementConfig?.fromRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>From Warehouse</Label>
                    <Select
                      value={formData.fromWarehouseId || ''}
                      onValueChange={handleFromWarehouseChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse._id} value={warehouse._id}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>From Stock Type</Label>
                    <Select
                      value={formData.from?.stockType || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        from: { ...prev.from!, stockType: value as any }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select stock type" />
                      </SelectTrigger>
                      <SelectContent>
                        {STOCK_TYPES.filter(st => st.value !== 'scrap').map((stockType) => (
                          <SelectItem key={stockType.value} value={stockType.value}>
                            {stockType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* From Location Dropdown */}
                {formData.fromWarehouseId && fromLocations.length > 0 && (
                  <div>
                    <Label>From Location</Label>
                    <Select
                      value={formData.from?.locationId || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        from: { ...prev.from!, locationId: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific location" />
                      </SelectTrigger>
                      <SelectContent>
                        {fromLocations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name} ({location.locationType})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* To Location (if required) */}
            {movementConfig?.toRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>To Warehouse</Label>
                    <Select
                      value={formData.toWarehouseId || ''}
                      onValueChange={handleToWarehouseChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse._id} value={warehouse._id}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>To Stock Type</Label>
                    <Select
                      value={formData.to?.stockType || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        to: { ...prev.to!, stockType: value as any }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select stock type" />
                      </SelectTrigger>
                      <SelectContent>
                        {STOCK_TYPES.map((stockType) => (
                          <SelectItem key={stockType.value} value={stockType.value}>
                            {stockType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* To Location Dropdown */}
                {formData.toWarehouseId && toLocations.length > 0 && (
                  <div>
                    <Label>To Location</Label>
                    <Select
                      value={formData.to?.locationId || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        to: { ...prev.to!, locationId: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific location" />
                      </SelectTrigger>
                      <SelectContent>
                        {toLocations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name} ({location.locationType})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* Reference Information */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="referenceNumber">Reference Number</Label>
                <Input
                  value={formData.referenceNumber || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, referenceNumber: e.target.value }))}
                  placeholder="PO-001, WO-123, etc."
                />
              </div>
              <div>
                <Label htmlFor="referenceType">Reference Type</Label>
                <Select
                  value={formData.referenceType || ''}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, referenceType: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select reference type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PurchaseOrder">Purchase Order</SelectItem>
                    <SelectItem value="WorkOrder">Work Order</SelectItem>
                    <SelectItem value="SalesOrder">Sales Order</SelectItem>
                    <SelectItem value="StockAdjustment">Stock Adjustment</SelectItem>
                    <SelectItem value="ProcessOrder">Process Order</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Transaction Date */}
            <div>
              <Label htmlFor="transactionDate">Transaction Date</Label>
              <Input
                type="date"
                value={formData.transactionDate ? formData.transactionDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}
                onChange={(e) => setFormData(prev => ({ ...prev, transactionDate: new Date(e.target.value) }))}
                required
              />
            </div>

            {/* Notes */}
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                value={formData.notes || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes about this movement..."
                rows={3}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Processing...' : 'Execute Movement'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
