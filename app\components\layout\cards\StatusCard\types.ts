import { ReactNode } from 'react';

/**
 * Props for the StatusCard component
 */
export interface StatusCardProps {
  /** Title displayed at the top of the card */
  title: string;
  /** Data to display as percentages */
  data: Record<string, number>;
  /** Main statistic to display prominently */
  mainStat: {
    value: number;
    label: string;
  };
  /** Color theme for the card */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Icon displayed in the top-right corner */
  icon?: ReactNode;
  /** Click handler for the card */
  onClick?: () => void;
}

/**
 * Color class mapping for bar charts using semantic color variables
 */
export const colorClasses = {
  blue: {
    bar: 'bg-info'
  },
  green: {
    bar: 'bg-success'
  },
  red: {
    bar: 'bg-destructive'
  },
  yellow: {
    bar: 'bg-warning'
  },
  purple: {
    bar: 'bg-purple'
  },
  orange: {
    bar: 'bg-orange'
  },
  gray: {
    bar: 'bg-muted-foreground'
  }
};