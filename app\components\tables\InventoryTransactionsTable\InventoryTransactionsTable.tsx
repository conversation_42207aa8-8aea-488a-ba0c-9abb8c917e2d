import React from 'react';
import InventoryTransactionsTableClient from './InventoryTransactionsTableClient';
import { type InventoryTransactionsTableActions } from '@/app/components/data-display/data-table';

interface InventoryTransactionsTableProps {
  transactions: any[];
  isLoading?: boolean;
  error?: string | null;
  actions?: InventoryTransactionsTableActions;
}

export default function InventoryTransactionsTable({
  transactions,
  isLoading = false,
  error = null,
  actions = {}
}: InventoryTransactionsTableProps) {
  return (
    <InventoryTransactionsTableClient
      transactions={transactions}
      isLoading={isLoading}
      error={error}
      actions={actions}
    />
  );
}

export { InventoryTransactionsTableClient };
export type { InventoryTransactionsTableProps };
