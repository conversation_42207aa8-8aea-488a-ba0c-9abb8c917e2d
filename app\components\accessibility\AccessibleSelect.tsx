'use client';

import { Label } from '@/app/components/forms/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from '@/app/components/forms/Select';
import { cn } from '@/app/lib/utils';
import React, { useState } from 'react';
import { LiveRegion } from './LiveRegion';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectGroup {
  label: string;
  options: SelectOption[];
}

export interface AccessibleSelectProps {
  /**
   * Label for the select field (required for accessibility)
   */
  label: string;
  
  /**
   * Options for the select field
   */
  options: SelectOption[] | SelectGroup[];
  
  /**
   * Optional description for the select field
   */
  description?: string;
  
  /**
   * Error message to display when validation fails
   */
  error?: string;
  
  /**
   * Whether the select is required
   */
  isRequired?: boolean;
  
  /**
   * ID for the select field (will be generated if not provided)
   */
  id?: string;
  
  /**
   * Additional class name for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class name for the description
   */
  descriptionClassName?: string;
  
  /**
   * Additional class name for the error message
   */
  errorClassName?: string;
  
  /**
   * Whether to announce errors to screen readers
   */
  announceErrors?: boolean;
  
  /**
   * Placeholder text for the select
   */
  placeholder?: string;
  
  /**
   * Current value of the select
   */
  value?: string;
  
  /**
   * Default value of the select
   */
  defaultValue?: string;
  
  /**
   * Callback when the value changes
   */
  onValueChange?: (value: string) => void;
  
  /**
   * Whether the select is disabled
   */
  disabled?: boolean;
  
  /**
   * Additional class name for the select trigger
   */
  triggerClassName?: string;
  
  /**
   * Additional class name for the select content
   */
  contentClassName?: string;
}

/**
 * AccessibleSelect component that enhances the standard Select with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleSelect: React.FC<AccessibleSelectProps> = ({
  label,
  options,
  description,
  error,
  isRequired = false,
  id: propId,
  containerClassName,
  labelClassName,
  descriptionClassName,
  errorClassName,
  announceErrors = true,
  placeholder,
  value,
  defaultValue,
  onValueChange,
  disabled,
  triggerClassName,
  contentClassName,
}) => {
  const [open, setOpen] = useState(false);
  const [wasOpened, setWasOpened] = useState(false);
  const id = propId || React.useId();
  const descriptionId = `${id}-description`;
  const errorId = `${id}-error`;
  const labelId = `${id}-label`;
  
  // Handle open state change
  const handleOpenChange = (open: boolean) => {
    setOpen(open);
    if (open) setWasOpened(true);
  };
  
  // Determine if we should show the error
  const showError = !!error && wasOpened;
  
  // Check if options are grouped
  const isGrouped = options.length > 0 && options[0] && 'options' in options[0];
  
  // Determine the aria-describedby attribute
  const ariaDescribedBy = [
    description ? descriptionId : null,
    showError ? errorId : null
  ].filter(Boolean).join(' ') || undefined;
  
  return (
    <div className={cn("space-y-2", containerClassName)}>
      <Label 
        id={labelId}
        htmlFor={id}
        className={cn(
          showError && "text-destructive",
          labelClassName
        )}
      >
        {label}
        {isRequired && (
          <span className="text-destructive ml-1" aria-hidden="true">*</span>
        )}
      </Label>
      
      <Select
        {...(value !== undefined && { value })}
        {...(defaultValue !== undefined && { defaultValue })}
        {...(onValueChange && { onValueChange })}
        open={open}
        onOpenChange={handleOpenChange}
        {...(disabled !== undefined && { disabled })}
      >
        <SelectTrigger 
          id={id}
          className={cn(
            showError && "border-destructive focus-visible:ring-destructive",
            triggerClassName
          )}
          aria-describedby={ariaDescribedBy}
          aria-invalid={showError}
          aria-required={isRequired}
          aria-labelledby={labelId}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        
        <SelectContent className={contentClassName} position="popper">
          {isGrouped ? (
            // Render grouped options
            (options as SelectGroup[]).map((group, groupIndex) => (
              <SelectGroup key={groupIndex}>
                <SelectLabel>{group.label}</SelectLabel>
                {group.options.map((option, optionIndex) => (
                  <SelectItem
                    key={optionIndex}
                    value={option.value}
                    {...(option.disabled !== undefined && { disabled: option.disabled })}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            ))
          ) : (
            // Render flat options
            (options as SelectOption[]).map((option, index) => (
              <SelectItem
                key={index}
                value={option.value}
                {...(option.disabled !== undefined && { disabled: option.disabled })}
              >
                {option.label}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      
      {description && (
        <p 
          id={descriptionId}
          className={cn(
            "text-sm text-muted-foreground dark:text-text-secondary",
            descriptionClassName
          )}
        >
          {description}
        </p>
      )}
      
      {showError && (
        <p 
          id={errorId}
          className={cn(
            "text-sm font-medium text-destructive dark:text-destructive",
            errorClassName
          )}
          role="alert"
        >
          {error}
        </p>
      )}
      
      {/* Announce errors to screen readers */}
      {announceErrors && showError && (
        <LiveRegion politeness="assertive">
          {`Error for ${label}: ${error}`}
        </LiveRegion>
      )}
    </div>
  );
};

AccessibleSelect.displayName = 'AccessibleSelect';

export default AccessibleSelect;
