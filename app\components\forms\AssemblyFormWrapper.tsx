'use client';

import { getApiUrl } from '@/app/utils/apiUtils';
import { useRouter } from 'next/navigation';
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { checkStockStatus, defaultValues, PartialAssemblyFormValues, PartRequirement } from './schema';

// Define the context interface
interface AssemblyFormContextType {
  // Form state
  formData: PartialAssemblyFormValues & { _id?: string };
  originalData: PartialAssemblyFormValues & { _id?: string };
  isLoading: boolean;
  isSaving: boolean;
  isEditing: boolean;
  isDirty: boolean;
  
  // Form actions
  updateFormField: <K extends keyof PartialAssemblyFormValues>(
    field: K, 
    value: PartialAssemblyFormValues[K]
  ) => void;
  resetForm: () => void;
  saveAssembly: () => Promise<boolean>;
  
  // Parts management
  addPart: (part: PartRequirement) => void;
  updatePart: (index: number, updatedPart: Partial<PartRequirement>) => void;
  removePart: (index: number) => void;
  
  // Part stock status
  getStockStatus: (partId: string) => "sufficient" | "insufficient" | "unknown";
  refreshStockData: () => Promise<void>;
}

// Create the context
const AssemblyFormContext = createContext<AssemblyFormContextType | undefined>(undefined);

// Hook for using the context
export function useAssemblyForm() {
  const context = useContext(AssemblyFormContext);
  if (!context) {
    throw new Error('useAssemblyForm must be used within an AssemblyFormProvider');
  }
  return context;
}

// Props for the provider component
interface AssemblyFormProviderProps {
  children: React.ReactNode;
  assemblyId?: string | undefined;
}

// Helper function to process children, moved outside loadAssembly
function processPartChildren(children: any[], _stockData: Record<string, number>): any[] {
  return (children || []).map((child: any) => {
    const childIdStr = typeof child.partId === 'object' && child.partId !== null ? child.partId._id : child.partId;
    const childPartData = typeof child.partId === 'object' && child.partId !== null ? child.partId : (child.item_id && typeof child.item_id === 'object' ? child.item_id : {});

    return {
      id: child.id || child._id || uuidv4(),
      partId: childIdStr,
      // Preserve the populated Part object for stock access
      partData: childPartData, // Keep the full populated Part object
      name: childPartData.name || child.name || 'Unknown Child Part',
      description: childPartData.description || child.description || '',
      quantityRequired: Number(child.quantityRequired || child.quantity || 1),
      unitOfMeasure: child.unitOfMeasure || childPartData.unitOfMeasure || 'ea',
      isAssembly: childPartData.isAssembly || child.isAssembly || false,
      // Remove currentStock synthesis - use partData.inventory.currentStock directly in components
      children: child.children ? processPartChildren(child.children, {}) : [], // Recursive call without stockData
      category: childPartData.category || child.category,
      reorderLevel: childPartData.reorderLevel || child.reorderLevel,
      supplier: childPartData.supplier || child.supplier,
      technicalSpecs: childPartData.technicalSpecs || child.technicalSpecs,
      partDisplayIdentifier: childPartData.partNumber || childPartData.name || child.partDisplayIdentifier || childIdStr || '',
      isExpanded: child.isExpanded !== undefined ? child.isExpanded : true,
    };
  });
}

// Provider component
export function AssemblyFormProvider({ children, assemblyId }: AssemblyFormProviderProps) {
  // Safer log for initialization
  console.log("[AssemblyFormProvider] Initializing. Provided assemblyId type: " + typeof assemblyId + ", value: " + String(assemblyId));

  const router = useRouter();
  
  // State
  const [formData, setFormData] = useState<PartialAssemblyFormValues & { _id?: string }>(defaultValues as PartialAssemblyFormValues & { _id?: string });
  const [originalData, setOriginalData] = useState<PartialAssemblyFormValues & { _id?: string }>(defaultValues as PartialAssemblyFormValues & { _id?: string });
  const [isLoading, setIsLoading] = useState(!!assemblyId);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing] = useState(!!assemblyId);
  const [stockData, setStockData] = useState<Record<string, number>>({});
  
  // Computed values
  const isDirty = useMemo(() => {
    return JSON.stringify(formData) !== JSON.stringify(originalData);
  }, [formData, originalData]);
  
  // Load stock data for parts
  const refreshStockDataForParts = async (parts: PartRequirement[]) => {
    try {
      // Get unique part IDs
      const partIdSet = new Set(parts.map(p => p.partId));
      const partIds = Array.from(partIdSet).filter(Boolean);
      
      if (partIds.length === 0) return {}; // Return empty object if no parts
      
      // Fetch stock data
      const response = await fetch(getApiUrl('/api/inventory/batch-stock'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ partIds }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch stock data');
      }
      
      const data = await response.json();
      
      // Prepare stock data to return
      const newStockData = (data as any).items.reduce((acc: Record<string, number>, item: any) => {
        acc[item.itemId] = item.currentStock || 0;
        return acc;
      }, {});
      
      // Update stock data state
      setStockData(prev => ({
        ...prev,
        ...newStockData,
      }));
      return newStockData; // Return the fetched stock data
    } catch (error) {
      console.error('Error fetching stock data:', error);
      toast.error('Failed to load stock information');
      return {}; // Return empty object on error
    }
  };
  
  // Load assembly data if editing
  const loadAssembly = useCallback(async (id: string) => {
    console.log("[AssemblyFormWrapper] loadAssembly CALLED with id: " + String(id)); // Log at the very start of loadAssembly
    setIsLoading(true);
    try {
      // Fetch assembly data - attempt to include fully populated parts
      const assemblyUrl = new URL(`${getApiUrl()}/api/assemblies/${id}`, window.location.origin);
      assemblyUrl.searchParams.append('includeParts', 'true'); // <--- ADDED: Request populated parts

      const response = await fetch(assemblyUrl.toString());
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to load assembly data' }));
        throw new Error((errorData as any).message || 'Failed to load assembly');
      }
      const assemblyData = await response.json();
      console.log('[AssemblyFormWrapper] Raw API response for assembly:', JSON.stringify(assemblyData, null, 2));

      if ((assemblyData as any).success && (assemblyData as any).data) {
        const fetchedAssembly = (assemblyData as any).data;
        console.log('[AssemblyFormWrapper] Fetched assembly data from API:', JSON.stringify(fetchedAssembly, null, 2));

        // Log raw partsRequired from API before processing
        if (fetchedAssembly.partsRequired && Array.isArray(fetchedAssembly.partsRequired)) {
          console.log('[AssemblyFormWrapper] Raw partsRequired from API before processing:', JSON.stringify(fetchedAssembly.partsRequired, null, 2));
        }

        // Parts should be populated from the API with inventory data
        console.log('[AssemblyFormWrapper] loadAssembly - Using populated part data from API');

        const processedParts = (fetchedAssembly.partsRequired || fetchedAssembly.parts || []).map((part: any) => {
          const partIdStr = typeof part.partId === 'object' && part.partId !== null ? part.partId._id : part.partId;
          const partData = typeof part.partId === 'object' && part.partId !== null ? part.partId : (part.item_id && typeof part.item_id === 'object' ? part.item_id : {});

          // DETAILED LOGS FOR partData and inventory
          console.log(`[AssemblyFormWrapper] Processing part: ${partIdStr || 'N/A'}`);
          console.log(`[AssemblyFormWrapper]   partData object:`, JSON.stringify(partData, null, 2));
          if (partData) {
            console.log(`[AssemblyFormWrapper]   partData.inventory object:`, JSON.stringify(partData.inventory, null, 2));
            console.log(`[AssemblyFormWrapper]   partData.inventory.currentStock:`, partData.inventory?.currentStock);
          }

          return {
            // Ensure all necessary fields from FormPartData are mapped
            id: part.id || part._id || uuidv4(), // Existing ID or generate one
            partId: partIdStr,
            // Preserve the populated Part object for stock access
            partData: partData, // Keep the full populated Part object
            name: partData.name || part.name || 'Unknown Part',
            description: partData.description || part.description || '',
            quantityRequired: Number(part.quantityRequired || part.quantity || 1),
            unitOfMeasure: part.unitOfMeasure || partData.unitOfMeasure || 'ea',
            isAssembly: partData.isAssembly || part.isAssembly || false,
            // Remove currentStock synthesis - use partData.inventory.currentStock directly in components
            // Ensure children are processed recursively if they exist and are not already fully formed
            children: part.children ? processPartChildren(part.children, {}) : [], // Remove stockData dependency
            // Add other fields from FormPartData schema if they exist on partData or part
            category: partData.category || part.category,
            reorderLevel: partData.reorderLevel || part.reorderLevel,
            supplier: partData.supplier || part.supplier,
            technicalSpecs: partData.technicalSpecs || part.technicalSpecs,
            partDisplayIdentifier: partData.partNumber || partData.name || part.partDisplayIdentifier || partIdStr || '',
            isExpanded: part.isExpanded !== undefined ? part.isExpanded : true,
          };
        });
        
        // Prepare the form data
        const formattedData: PartialAssemblyFormValues & { _id?: string } = {
          _id: fetchedAssembly._id,
          assemblyCode: fetchedAssembly.assemblyCode || fetchedAssembly.assembly_id || '',
          name: fetchedAssembly.name || '',
          description: fetchedAssembly.description || '',
          status: fetchedAssembly.status || 'active',
          version: fetchedAssembly.version || 1,
          isTopLevel: fetchedAssembly.isTopLevel !== undefined ? fetchedAssembly.isTopLevel : true,
          manufacturingInstructions: fetchedAssembly.manufacturingInstructions || null,
          estimatedBuildTime: fetchedAssembly.estimatedBuildTime || null,
          productId: fetchedAssembly.productId || null,
          parentId: fetchedAssembly.parentId || null,
          partsRequired: processedParts,
        };
        
        setFormData(formattedData);
        setOriginalData(formattedData);

        // No need to fetch additional stock data - use populated data from API
      }
    } catch (error) {
      console.error('Error loading assembly:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to load assembly');
    } finally {
      setIsLoading(false);
    }
  }, []);
  
  // Refresh stock data for current parts
  const refreshStockData = useCallback(async () => {
    if (formData.partsRequired && formData.partsRequired.length > 0) {
      await refreshStockDataForParts(formData.partsRequired);
    }
  }, [formData.partsRequired, refreshStockDataForParts]);
  
  // Get stock status for a part
  const getStockStatus = useCallback((partId: string): "sufficient" | "insufficient" | "unknown" => {
    const part = formData.partsRequired?.find(p => p.partId === partId);
    if (!part) {
      return "unknown";
    }

    // Use stock from populated Part object - partData contains the populated Part
    const stock = (part as any).partData?.inventory?.currentStock;

    if (stock === undefined || stock === null) {
      return "unknown";
    }

    return checkStockStatus({ ...part, currentStock: stock });
  }, [formData.partsRequired]);
  
  // Update a form field
  const updateFormField = useCallback(<K extends keyof PartialAssemblyFormValues>(
    field: K, 
    value: PartialAssemblyFormValues[K]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);
  
  // Reset form to original data
  const resetForm = useCallback(() => {
    setFormData(isEditing ? originalData : defaultValues);
  }, [isEditing, originalData]);
  
  // Add a part to the assembly
  const addPart = useCallback((part: PartRequirement) => {
    // Check if part already exists
    setFormData(prev => {
      const existingParts = prev.partsRequired || [];
      const exists = existingParts.some(p => p.partId === part.partId);

      if (exists) {
        toast.error('This part is already in the assembly');
        return prev;
      }

      // Add the part without synthesizing currentStock
      const newPart = {
        ...part,
        // Stock status will be calculated in components using partData.inventory.currentStock
      };

      return {
        ...prev,
        partsRequired: [...existingParts, newPart],
      };
    });
  }, []);
  
  // Update a part in the assembly
  const updatePart = useCallback((index: number, updatedPart: Partial<PartRequirement>) => {
    setFormData(prev => {
      const parts = [...(prev.partsRequired || [])];
      if (index < 0 || index >= parts.length) return prev;

      const currentPart = parts[index];
      if (!currentPart) return prev;

      parts[index] = {
        ...currentPart,
        ...updatedPart,
        // Ensure required fields are present
        partId: updatedPart.partId || currentPart.partId,
        quantityRequired: updatedPart.quantityRequired || currentPart.quantityRequired,
        unitOfMeasure: updatedPart.unitOfMeasure || currentPart.unitOfMeasure,
        // Stock status will be calculated in components using partData.inventory.currentStock
      };

      return { ...prev, partsRequired: parts };
    });
  }, []);
  
  // Remove a part from the assembly
  const removePart = useCallback((index: number) => {
    setFormData(prev => {
      const parts = [...(prev.partsRequired || [])];
      if (index < 0 || index >= parts.length) return prev;
      
      parts.splice(index, 1);
      return { ...prev, partsRequired: parts };
    });
  }, []);
  
  // Save the assembly
  const saveAssembly = useCallback(async (): Promise<boolean> => {
    setIsSaving(true);
    try {
      // Filter out any parts that don't have a valid partId
      const validPartsRequired = formData.partsRequired?.filter(p => p.partId && p.partId.trim() !== '');

      if (!validPartsRequired || validPartsRequired.length === 0) {
        // Allow saving if it's a new assembly with no parts (template/placeholder)
        if (!isEditing) {
          // console.warn("[AssemblyFormWrapper] Attempting to save a new assembly with no parts. This might be intentional for a template.");
        } else {
          // If editing, and all parts were somehow removed, this might be an issue or intentional.
          // For now, we'll allow it but ideally, there should be UI feedback or rules against empty assemblies if not desired.
          // toast.warn("Assembly has no parts. Saving as is.");
        }
      }

      const payload = {
        ...formData,
        partsRequired: validPartsRequired || [],
      };

      // Remove the direct call to refreshStockDataForParts here as it's not correctly parameterized
      // and its primary role is now in loadAssembly.
      // If a pre-save stock check is needed, it must be implemented with correct part data.
      // await refreshStockDataForParts(); 

      const method = isEditing ? 'PUT' : 'POST';
      const url = isEditing ? `${getApiUrl()}/api/assemblies/${assemblyId}` : `${getApiUrl()}/api/assemblies`;
      
      // Send API request
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${isEditing ? 'update' : 'create'} assembly`);
      }
      
      const result = await response.json();
      
      toast.success(`Assembly ${isEditing ? 'updated' : 'created'} successfully`);
      
      // Update form data with the saved data
      if (result.data) {
        setFormData(result.data);
        setOriginalData(result.data);
      }
      
      return true;
    } catch (error) {
      console.error('Error saving assembly:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save assembly');
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [formData, isEditing, assemblyId]);
  
  // Load assembly on initial render if editing
  useEffect(() => {
    console.log("[AssemblyFormProvider] useEffect for loadAssembly triggered. assemblyId type: " + typeof assemblyId + ", value: " + String(assemblyId));
    if (assemblyId) {
      console.log("[AssemblyFormProvider] assemblyId is truthy, calling loadAssembly.");
      loadAssembly(assemblyId);
    } else {
      console.log("[AssemblyFormProvider] assemblyId is FALSშY, NOT calling loadAssembly."); // Corrected FALSშY
    }
  }, [assemblyId, loadAssembly]);
  
  // Context value
  const contextValue = useMemo(() => ({
    formData,
    originalData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    updateFormField,
    resetForm,
    saveAssembly,
    addPart,
    updatePart,
    removePart,
    getStockStatus,
    refreshStockData,
  }), [
    formData, originalData, isLoading, isSaving, isEditing, isDirty,
    updateFormField, resetForm, saveAssembly, addPart, updatePart, removePart,
    getStockStatus, refreshStockData
  ]);
  
  return (
    <AssemblyFormContext.Provider value={contextValue}>
      {children}
    </AssemblyFormContext.Provider>
  );
}

export default function AssemblyFormWrapper({ children, assemblyId }: AssemblyFormProviderProps) {
  return (
    <AssemblyFormProvider assemblyId={assemblyId}>
      {children}
    </AssemblyFormProvider>
  );
} 