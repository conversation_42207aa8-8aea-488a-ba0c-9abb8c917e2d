'use client';

import { Badge } from '@/app/components/data-display/badge';
import { ScrollArea } from '@/app/components/feedback/scroll-area';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { cn } from '@/app/lib/utils';
import { ChevronRight, Clock, Eye } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

// Define recent assembly type
interface RecentAssembly {
  id: string;
  name: string;
  timestamp: number;
  assemblyCode?: string;
}

/**
 * Component to display and track recently viewed assemblies
 */
export function RecentAssemblies() {
  const [recentAssemblies, setRecentAssemblies] = useState<RecentAssembly[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const pathname = usePathname();
  
  // Load recent assemblies from localStorage on mount - only once
  useEffect(() => {
    if (isInitialized) return;
    
    try {
      const stored = localStorage.getItem('recentAssemblies');
      if (stored) {
        setRecentAssemblies(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Failed to load recent assemblies:', error);
    } finally {
      setIsInitialized(true);
    }
  }, [isInitialized]);
  
  // Save recent assemblies to localStorage whenever they change - but only if initialized
  useEffect(() => {
    if (!isInitialized) return;
    
    try {
      localStorage.setItem('recentAssemblies', JSON.stringify(recentAssemblies));
    } catch (error) {
      console.error('Failed to save recent assemblies:', error);
    }
  }, [recentAssemblies, isInitialized]);
  
  // Get assembly data from window safely
  const getAssemblyData = useCallback(() => {
    try {
      // Safely access window and extract data
      if (typeof window === 'undefined') return null;
      if (!window.__NEXT_DATA__?.props?.pageProps?.assembly) return null;
      
      return window.__NEXT_DATA__.props.pageProps.assembly;
    } catch (error) {
      console.error('Failed to get assembly data from window:', error);
      return null;
    }
  }, []);
  
  // Add current assembly to recent list if on an assembly detail page
  useEffect(() => {
    // Only run if initialized
    if (!isInitialized) return;
    
    // Check if we're on an assembly detail page
    const match = pathname.match(/\/assemblies\/([^\/]+)$/);
    if (!match) return;
    
    const id = match[1];
    
    // Get assembly data with the safe accessor
    const assemblyData = getAssemblyData();
    if (!assemblyData) return;
    
    const { name, assemblyCode } = assemblyData;
    
    // Only update state if we have valid data
    if (!name || !id) return;
    
    // Add to recent list - use functional update to avoid dependency on recentAssemblies
    setRecentAssemblies(prev => {
      // Check if already exists at the top of the list - prevent unnecessary updates
      if (prev.length > 0 && prev[0]?.id === id) return prev;
      
      // Remove if already exists
      const filtered = prev.filter(item => item.id !== id);
      
      // Add to start of list
      return [
        { id, name, timestamp: Date.now(), assemblyCode },
        ...filtered
      ].slice(0, 5); // Keep only 5 most recent
    });
  }, [pathname, isInitialized, getAssemblyData]);
  
  // Don't render anything until we're initialized
  if (!isInitialized) return null;
  
  // Don't render if no recent assemblies
  if (recentAssemblies.length === 0) {
    return null;
  }
  
  return (
    <Card className="mb-6">
      <CardHeader className="py-3">
        <CardTitle className="text-sm font-medium flex items-center">
          <Clock size={16} className="mr-2 text-gray-500" />
          Recently Viewed
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 pb-2">
        <ScrollArea className="h-auto max-h-[180px]">
          <ul className="space-y-1 pr-3">
            {recentAssemblies.map(assembly => (
              <li key={assembly.id}>
                <Link 
                  href={`/assemblies/${assembly.id}`}
                  className={cn(
                    "flex items-center justify-between p-2 rounded-md",
                    "text-sm hover:bg-gray-100 dark:hover:bg-gray-800",
                    "transition-colors duration-200"
                  )}
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{assembly.name}</div>
                    {assembly.assemblyCode && (
                      <Badge variant="outline" className="font-mono mt-1 text-xs">
                        {assembly.assemblyCode}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center ml-2">
                    <Eye size={14} className="mr-1 text-gray-500" />
                    <ChevronRight size={14} className="text-gray-400" />
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </ScrollArea>
      </CardContent>
    </Card>
  );
} 