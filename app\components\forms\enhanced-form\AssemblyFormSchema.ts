import { z } from "zod";

// SCHEMA ALIGNMENT: Based on database_schema_updated.md assembly structure
// Matches the canonical assembly fields and validation requirements

/**
 * Assembly status enum - matches database schema
 */
export const AssemblyStatusEnum = z.enum([
  'active',
  'pending_review', 
  'design_phase',
  'design_complete',
  'obsolete'
]);

/**
 * Part requirement schema for assembly parts with hierarchical support
 */
export const PartRequirementSchema: z.ZodSchema = z.lazy(() => z.object({
  partId: z.string()
    .min(1, "Part ID is required")
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid part ID format"),

  quantityRequired: z.number()
    .min(0.001, "Quantity must be greater than 0")
    .max(999999, "Quantity cannot exceed 999,999"),

  unitOfMeasure: z.string()
    .min(1, "Unit of measure is required")
    .max(20, "Unit of measure cannot exceed 20 characters"),

  children: z.array(PartRequirementSchema).optional().default([]),
}));

/**
 * Base assembly form schema object without refinements
 */
const BaseAssemblyFormSchema = z.object({
  assemblyCode: z.string()
    .min(1, "Assembly code is required")
    .max(50, "Assembly code cannot exceed 50 characters")
    .regex(/^[A-Z0-9-_]+$/, "Assembly code must contain only uppercase letters, numbers, hyphens, and underscores")
    .refine((code) => !code.startsWith('-') && !code.endsWith('-'), {
      message: "Assembly code cannot start or end with a hyphen"
    }),
  
  name: z.string()
    .min(1, "Assembly name is required")
    .max(200, "Assembly name cannot exceed 200 characters")
    .trim(),
  
  description: z.string()
    .max(1000, "Description cannot exceed 1000 characters")
    .optional()
    .nullable(),
  
  status: AssemblyStatusEnum
    .default('active'),
  
  version: z.number()
    .int("Version must be a whole number")
    .min(1, "Version must be at least 1")
    .max(999, "Version cannot exceed 999")
    .default(1),
  
  isTopLevel: z.boolean()
    .default(true),
  
  manufacturingInstructions: z.string()
    .max(5000, "Manufacturing instructions cannot exceed 5000 characters")
    .optional()
    .nullable(),
  
  estimatedBuildTime: z.string()
    .max(100, "Estimated build time cannot exceed 100 characters")
    .optional()
    .nullable(),
  
  productId: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid product ID format")
    .optional()
    .nullable(),
  
  parentId: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid parent assembly ID format")
    .optional()
    .nullable(),
  
  partsRequired: z.array(PartRequirementSchema)
    .default([])
    .refine((parts) => {
      // Check for duplicate part IDs
      const partIds = parts.map(p => p.partId);
      return new Set(partIds).size === partIds.length;
    }, {
      message: "Duplicate parts are not allowed in the same assembly"
    })
    .refine((parts) => {
      // Validate total parts count
      return parts.length <= 1000;
    }, {
      message: "Assembly cannot have more than 1000 parts"
    }),
});

/**
 * Assembly form schema with business rule validations
 */
export const AssemblyFormSchema = BaseAssemblyFormSchema
.refine((data) => {
  // Business rule: Top-level assemblies cannot have a parent
  if (data.isTopLevel && data.parentId) {
    return false;
  }
  return true;
}, {
  message: "Top-level assemblies cannot have a parent assembly",
  path: ["parentId"]
})
.refine((data) => {
  // Business rule: Non-top-level assemblies should have a parent (optional but recommended)
  // This is a warning-level validation, not blocking
  return true;
}, {
  message: "Sub-assemblies typically should have a parent assembly",
  path: ["parentId"]
});

/**
 * Partial assembly form schema for create/edit operations
 * Some fields may be optional during creation
 */
export const PartialAssemblyFormSchema = BaseAssemblyFormSchema.partial({
  version: true,
  isTopLevel: true,
  status: true,
}).extend({
  // Override required fields that must always be present
  assemblyCode: BaseAssemblyFormSchema.shape.assemblyCode,
  name: BaseAssemblyFormSchema.shape.name,
});

/**
 * Assembly form schema for editing existing assemblies
 * Includes ID field and makes certain fields optional
 */
export const EditAssemblyFormSchema = BaseAssemblyFormSchema.extend({
  _id: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid assembly ID format"),
}).partial({
  // Allow partial updates during editing
  assemblyCode: true,
  name: true,
});

/**
 * Quick assembly creation schema with minimal required fields
 */
export const QuickAssemblyFormSchema = z.object({
  assemblyCode: BaseAssemblyFormSchema.shape.assemblyCode,
  name: BaseAssemblyFormSchema.shape.name,
  description: BaseAssemblyFormSchema.shape.description,
  status: BaseAssemblyFormSchema.shape.status,
});

/**
 * Assembly parts update schema for managing parts separately
 */
export const AssemblyPartsUpdateSchema = z.object({
  assemblyId: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid assembly ID format"),
  
  partsRequired: z.array(PartRequirementSchema)
    .min(1, "At least one part is required")
    .refine((parts) => {
      const partIds = parts.map(p => p.partId);
      return new Set(partIds).size === partIds.length;
    }, {
      message: "Duplicate parts are not allowed"
    }),
});

// Type exports for TypeScript integration
export type AssemblyFormData = z.infer<typeof AssemblyFormSchema>;
export type PartialAssemblyFormData = z.infer<typeof PartialAssemblyFormSchema>;
export type EditAssemblyFormData = z.infer<typeof EditAssemblyFormSchema>;
export type QuickAssemblyFormData = z.infer<typeof QuickAssemblyFormSchema>;
export type AssemblyPartsUpdateData = z.infer<typeof AssemblyPartsUpdateSchema>;
export type PartRequirementData = z.infer<typeof PartRequirementSchema>;
export type AssemblyStatus = z.infer<typeof AssemblyStatusEnum>;

// Validation helper functions
export const validateAssemblyCode = (code: string): boolean => {
  try {
    BaseAssemblyFormSchema.shape.assemblyCode.parse(code);
    return true;
  } catch {
    return false;
  }
};

export const validatePartRequirement = (part: any): boolean => {
  try {
    PartRequirementSchema.parse(part);
    return true;
  } catch {
    return false;
  }
};

// Default values for form initialization
export const defaultAssemblyFormValues: Partial<AssemblyFormData> = {
  assemblyCode: '',
  name: '',
  description: '',
  status: 'active',
  version: 1,
  isTopLevel: true,
  manufacturingInstructions: null,
  estimatedBuildTime: null,
  productId: null,
  parentId: null,
  partsRequired: [],
};

// Form field validation messages
export const assemblyFormMessages = {
  assemblyCode: {
    required: "Assembly code is required",
    format: "Use only uppercase letters, numbers, hyphens, and underscores",
    length: "Assembly code must be 1-50 characters",
  },
  name: {
    required: "Assembly name is required",
    length: "Assembly name must be 1-200 characters",
  },
  description: {
    length: "Description cannot exceed 1000 characters",
  },
  partsRequired: {
    duplicates: "Each part can only be added once per assembly",
    limit: "Assembly cannot have more than 1000 parts",
  },
  businessRules: {
    topLevelParent: "Top-level assemblies cannot have a parent",
    subAssemblyParent: "Sub-assemblies should typically have a parent",
  },
};
