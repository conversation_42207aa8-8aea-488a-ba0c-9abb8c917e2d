'use client';

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, Toolt<PERSON>Provider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { UnifiedAssemblyForm } from '@/app/components/forms/UnifiedAssemblyForm';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import { cn } from '@/app/lib/utils';
import { PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface QuickEditAssemblyActionProps {
  assembly: Assembly;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Quick edit assembly action component
 * Opens the UnifiedAssemblyForm in a modal with the assembly data loaded
 */
export function QuickEditAssemblyAction({
  assembly,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: QuickEditAssemblyActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Open the modal for editing
  const handleOpenModal = () => {
    console.log('Opening quick edit modal for assembly:', assembly._id);
    setIsModalOpen(true);
  };

  // Close the modal
  const handleCloseModal = () => {
    console.log('Closing quick edit modal for assembly:', assembly._id);
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
    router.refresh();
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={handleOpenModal}
            className={className}
            id={id || `quick-edit-button-${assembly._id}`}
          >
            <PencilIcon size={16} className="mr-2" />
            Quick Edit
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={handleOpenModal}
            className={cn("h-8 px-2 hover:bg-muted/50", className)}
            id={id || `quick-edit-ghost-${assembly._id}`}
          >
            <PencilIcon size={15} className="mr-1" />
            Edit
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    handleOpenModal();
                  }}
                  className={cn("h-8 w-8 p-0", className)}
                  id={id || `quick-edit-${assembly._id}`}
                  style={{ position: 'relative', zIndex: 30 }}
                >
                  <span className="sr-only">Edit Assembly</span>
                  <PencilIcon size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit Assembly</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {isModalOpen && (
        <AssemblyFormProvider assemblyId={assembly._id}>
          <UnifiedAssemblyForm
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            assemblyId={assembly._id}
            mode="edit"
          />
        </AssemblyFormProvider>
      )}
    </>
  );
}
