'use client';

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { PartForm, PartFormData } from '@/app/components/forms/PartForm';
import { cn } from '@/app/lib/utils';
import { PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { loadPartInventory, createOrUpdatePartInventory } from '@/app/api-client/part.api';

interface EditPartActionProps {
  partId: string;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Edit part action component
 * Opens the PartForm in a modal with the part data loaded
 */
export function EditPartAction({
  partId,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: EditPartActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialData, setInitialData] = useState<PartFormData | null>(null);
  
  const defaultPartData: PartFormData = {
    _id: '',
    name: '',
    businessName: null,
    description: '',
    technicalSpecs: '',
    isManufactured: false,
    reorderLevel: 0,
    status: 'active',
    // V4 Schema: Removed inventory object - use inventory.service.ts for stock management
    planningMethod: null,
    safetyStockLevel: null,
    maximumStockLevel: null,
    leadTimeDays: null,
    averageDailyUsage: null,
    isAssembly: false,
    schemaVersion: 1,
    supplierId: ''
  };
  const [isLoading, setIsLoading] = useState(false);

  // Fetch part data when modal is opened
  const handleOpenModal = async () => {
    setIsLoading(true);
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);

      // Fetch part data and inventory data in parallel
      const [partResponse, inventoryResponse] = await Promise.all([
        fetch(`/api/parts/${encodedPartId}`),
        fetch(`/api/inventories/part/${encodedPartId}?format=v2`)
      ]);

      if (!partResponse.ok) {
        throw new Error('Failed to fetch part data');
      }

      const partData = await partResponse.json();
      let inventoryData = null;

      // Fetch inventory data using V4 schema
      try {
        inventoryData = await loadPartInventory(partId);
        console.log('[EditPartAction] Loaded inventory data:', inventoryData);
      } catch (error) {
        console.warn('[EditPartAction] Failed to load inventory data:', error);
      }

      if ((partData as any).data) {

        // Ensure _id is always a string
        const partId = (partData as any).data._id;
        if (!partId) {
          throw new Error('Part ID is required');
        }

        // Transform API data to match PartFormData structure
        const apiData = (partData as any).data;

        // Helper function to extract ID from populated field or return the value as-is
        const extractId = (field: any): string => {
          if (!field) return '';
          if (typeof field === 'string') return field;
          if (typeof field === 'object' && field._id) return field._id;
          if (typeof field === 'object' && field.id) return field.id;
          return '';
        };

        // V4 Schema: Use inventory data directly from loadPartInventory
        const transformInventoryData = () => {
          if (!inventoryData) {
            return {
              warehouseId: '',
              locationId: '',
              stockLevels: {
                raw: 0,
                hardening: 0,
                grinding: 0,
                finished: 0,
                rejected: 0
              },
              adjustmentReason: undefined,
              adjustmentNotes: ''
            };
          }

          // V4 schema already returns data in the correct format, but ensure types match
          return {
            warehouseId: inventoryData.warehouseId || '',
            locationId: inventoryData.locationId || '',
            stockLevels: inventoryData.stockLevels,
            adjustmentReason: inventoryData.adjustmentReason as 'INITIAL_STOCK' | 'PHYSICAL_COUNT' | 'DAMAGE' | 'FOUND' | 'CORRECTION' | 'OTHER' | undefined,
            adjustmentNotes: inventoryData.adjustmentNotes || ''
          };
        };

        const formPartData: PartFormData = {
          _id: partId,
          name: apiData.name,
          businessName: apiData.businessName || null, // NEW FIELD: Human-readable business name
          partNumber: apiData.partNumber || '', // Add missing partNumber mapping
          description: apiData.description || '',
          technicalSpecs: apiData.technicalSpecs || '', // Use correct field name
          isManufactured: apiData.isManufactured || false, // Use correct field name
          reorderLevel: apiData.reorderLevel || null, // Use correct field name and allow null
          status: (apiData.status || 'active') as 'active' | 'inactive' | 'obsolete',
          // V4 Schema: Simplified inventory management
          inventory: transformInventoryData(),
          // Planning parameters are now part of part master data
          planningMethod: apiData.planningMethod || null,
          safetyStockLevel: apiData.safetyStockLevel || null,
          maximumStockLevel: apiData.maximumStockLevel || null,
          leadTimeDays: apiData.leadTimeDays || null,
          averageDailyUsage: apiData.averageDailyUsage || null,
          isAssembly: apiData.isAssembly || false,
          subParts: apiData.subParts || [], // Use correct field name
          schemaVersion: apiData.schemaVersion || 1,
          supplierId: extractId(apiData.supplierId || apiData.supplier), // Handle both nested and top-level supplier
          unitOfMeasure: apiData.unitOfMeasure || 'pcs', // Add missing field
          standardCost: apiData.standardCost || apiData.costPrice || undefined, // Changed from costPrice to standardCost
          categoryId: extractId(apiData.categoryId || apiData.category), // Handle both nested and top-level category
        };

        // DEBUG: Log the transformed data before setting it
        console.log('[EditPartAction] Transformed part data:', {
          supplierId: formPartData.supplierId,
          partNumber: formPartData.partNumber,
          businessName: formPartData.businessName,
          name: formPartData.name
        });

        // DEBUG: Log the inventory data mapping
        console.log('[EditPartAction] Inventory data mapping:', {
          rawInventoryData: inventoryData,
          transformedInventory: formPartData.inventory
        });

        // Set initial data first, then open modal in the next tick to ensure state is updated
        setInitialData(formPartData);

        // Use setTimeout to ensure initialData is set before opening modal
        setTimeout(() => {
          setIsModalOpen(true);
        }, 0);
      }
    } catch (error) {
      console.error('Error fetching part data:', error);
      toast.error('Failed to load part data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: PartFormData) => {
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);

      // Remove immutable and system-managed fields from the request body
      // These fields should not be included in update requests:
      // - _id: should be in URL path, not body
      // - partNumber: immutable identifier
      // - isAssembly: system-managed field
      // - subParts: managed separately
      // - schemaVersion: system-managed field
      // - inventory: handled separately through inventory API
      // - inventoryDetails: handled separately through bulk inventory API
      const {
        _id,
        partNumber,
        isAssembly,
        subParts,
        schemaVersion,
        inventory,

        ...updateData
      } = data;

      // Handle ObjectId fields - convert empty strings to undefined for optional fields
      if (updateData.supplierId === '') {
        delete updateData.supplierId;
      }
      if (updateData.categoryId === '') {
        delete updateData.categoryId;
      }

      // Update part data first
      const response = await fetch(`/api/parts/${encodedPartId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error((errorData as any).error || 'Failed to update part');
      }

      // V4 Schema: Handle inventory updates if inventory data is provided
      if (inventory && inventory.locationId && hasInventoryChanges(inventory)) {
        console.log('[EditPartAction] Updating inventory for part:', partId);
        try {
          // Ensure required fields are present
          const validInventoryData = {
            ...inventory,
            warehouseId: inventory.warehouseId || '',
            locationId: inventory.locationId || ''
          };
          await createOrUpdatePartInventory(partId, validInventoryData);
          console.log('[EditPartAction] Inventory updated successfully');
        } catch (inventoryError) {
          console.error('[EditPartAction] Error updating inventory:', inventoryError);
          toast.error('Part updated but inventory update failed. Please check inventory manually.');
        }
      }

      // Close modal
      setIsModalOpen(false);

      // Clear cached initial data to ensure fresh data is fetched next time
      setInitialData(null);

      // Show success message
      toast.success(`Part "${data.name}" updated successfully`);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();
    } catch (error) {
      console.error('Error updating part:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred while updating the part');
    }
  };

  // Helper function to check if there are inventory changes
  const hasInventoryChanges = (inventory: any): boolean => {
    if (!initialData?.inventory) return true; // If no initial inventory, any inventory data is a change

    const initial = initialData.inventory;
    const current = inventory;

    // Check if stock levels have changed
    if (initial.stockLevels && current.stockLevels) {
      const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'] as const;
      for (const stockType of stockTypes) {
        if ((initial.stockLevels[stockType] || 0) !== (current.stockLevels[stockType] || 0)) {
          return true;
        }
      }
    }

    // Check if warehouse or location changed
    if (initial.warehouseId !== current.warehouseId || initial.locationId !== current.locationId) {
      return true;
    }

    return false;
  };





  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={handleOpenModal}
            className={className}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={16} className="mr-2" />
                Edit
              </>
            )}
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={handleOpenModal}
            className={cn("h-8 px-2 hover:bg-muted/50", className)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={15} className="mr-1" />
                Edit
              </>
            )}
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    handleOpenModal();
                  }}
                  className={cn("h-8 w-8 p-0", className)}
                  id={id || `edit-part-${partId}`}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>Loading...</>
                  ) : (
                    <PencilIcon size={15} />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit Part</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {isModalOpen && initialData && (
        <PartForm
          onSubmit={(data) => Promise.resolve(handleSubmit(data))}
          onClose={() => setIsModalOpen(false)}
          initialData={initialData}
          isEdit={true}
          title={`Edit Part: ${initialData.name}`}
        />
      )}
    </>
  );
}
