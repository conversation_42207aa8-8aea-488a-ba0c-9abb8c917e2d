import { useState, useEffect, useCallback } from 'react';

interface StockLevels {
  raw: number;
  hardening: number;
  grinding: number;
  finished: number;
  rejected: number;
}

interface InventoryData {
  totalStock: number;
  stockLevels: StockLevels;
  lastUpdated: Date | null;
}

interface BulkInventoryResult {
  [partId: string]: InventoryData;
}

interface UseBulkInventoryDetailsReturn {
  inventoryData: BulkInventoryResult;
  isLoading: boolean;
  error: string | null;
  fetchInventoryData: (partIds: string[]) => Promise<void>;
  getPartInventory: (partId: string) => InventoryData | null;
}

/**
 * Custom hook for fetching inventory data for multiple parts in bulk
 * Prevents N+1 queries by fetching all inventory data in a single request
 */
export function useBulkInventoryDetails(): UseBulkInventoryDetailsReturn {
  const [inventoryData, setInventoryData] = useState<BulkInventoryResult>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchInventoryData = useCallback(async (partIds: string[]) => {
    if (!partIds || partIds.length === 0) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/inventories/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ partIds }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch inventory data');
      }

      setInventoryData(result.data);
    } catch (err) {
      console.error('Error fetching bulk inventory data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getPartInventory = useCallback((partId: string): InventoryData | null => {
    return inventoryData[partId] || null;
  }, [inventoryData]);

  return {
    inventoryData,
    isLoading,
    error,
    fetchInventoryData,
    getPartInventory,
  };
}

/**
 * Hook for fetching inventory data for a specific set of parts
 * Automatically fetches data when partIds change
 */
export function useBulkInventoryDetailsWithAutoFetch(partIds: string[]): UseBulkInventoryDetailsReturn {
  const bulkInventory = useBulkInventoryDetails();

  useEffect(() => {
    if (partIds && partIds.length > 0) {
      bulkInventory.fetchInventoryData(partIds);
    }
  }, [partIds, bulkInventory.fetchInventoryData]);

  return bulkInventory;
}

/**
 * Utility function to extract part IDs from various data structures
 */
export function extractPartIds(items: any[]): string[] {
  const partIds = new Set<string>();
  
  items.forEach(item => {
    // Handle different data structures
    if (item.partId) {
      partIds.add(typeof item.partId === 'string' ? item.partId : item.partId.toString());
    } else if (item._id) {
      partIds.add(typeof item._id === 'string' ? item._id : item._id.toString());
    }
    
    // Handle nested structures like assembly parts
    if (item.partsRequired && Array.isArray(item.partsRequired)) {
      item.partsRequired.forEach((part: any) => {
        if (part.partId) {
          partIds.add(typeof part.partId === 'string' ? part.partId : part.partId.toString());
        }
      });
    }
  });
  
  return Array.from(partIds);
}
