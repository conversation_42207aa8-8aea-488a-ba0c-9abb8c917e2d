import * as z from 'zod';
// import { PartDocument } from '@/app/types/inventory/part'; // Commented out if not directly used for PartFormData

/**
 * Interface for PartForm props
 */
export interface PartFormProps {
  /** Submit handler function */
  onSubmit: (data: PartFormData) => void;
  /** Close handler function */
  onClose: () => void;
  /** Initial data for the form (for editing) */
  initialData?: PartFormData;
  /** Whether the form is for editing or adding a part */
  isEdit?: boolean;
  /** Title to display on the form */
  title?: string;
}

/**
 * Schema for validating part form data
 * Uses Zod to define the validation rules
 */
export const partFormSchema = z.object({
  _id: z.string(),
  name: z.string().min(1, 'Name is required'),
  businessName: z.string().nullable().optional(), // NEW FIELD: Human-readable business name
  partNumber: z.string().optional(),
  description: z.string().optional(),
  technicalSpecs: z.string().optional(),
  isManufactured: z.boolean(),
  reorderLevel: z.number().nullable(),
  status: z.enum(['active', 'inactive', 'obsolete']),

  // NEW: Part Master Data Planning Parameters
  planningMethod: z.string().nullable().optional(),                    // Planning method (MRP, EOQ, etc.)
  safetyStockLevel: z.number().min(0).nullable().optional(),           // Safety stock level for this part
  maximumStockLevel: z.number().min(0).nullable().optional(),          // Maximum stock level for this part
  leadTimeDays: z.number().int().min(0).nullable().optional(),         // Lead time in days
  averageDailyUsage: z.number().min(0).nullable().optional(),          // Average daily usage

  // V4 Schema: Simplified Inventory Management (Single Warehouse/Location)
  inventory: z.object({
    warehouseId: z.string().optional(),                                 // Selected warehouse for inventory
    locationId: z.string().optional(),                                  // Selected location within warehouse
    stockLevels: z.object({                                            // Stock levels by type
      raw: z.number().min(0).default(0),
      hardening: z.number().min(0).default(0),
      grinding: z.number().min(0).default(0),
      finished: z.number().min(0).default(0),
      rejected: z.number().min(0).default(0)
    }),
    adjustmentReason: z.enum([
      'INITIAL_STOCK',
      'PHYSICAL_COUNT',
      'DAMAGE',
      'FOUND',
      'CORRECTION',
      'OTHER'
    ]).optional(),                                                     // Reason for manual adjustment
    adjustmentNotes: z.string().optional()                             // Additional notes for adjustment
  }).optional(),

  isAssembly: z.boolean(),
  schemaVersion: z.number().optional(),
  subParts: z.array(
    z.object({
      partId: z.string(),
      quantity: z.number().min(1)
    })
  ).optional(),
  supplierId: z.string().optional(),
  unitOfMeasure: z.string().optional(),
  standardCost: z.number().optional(), // Changed from costPrice to match target schema
  abcClassification: z.string().optional(), // NEW FIELD: ABC classification (A, B, C)
  categoryId: z.string().optional()
});

/**
 * Interface for the internal form data structure, derived from the Zod schema
 */
export type PartFormData = z.infer<typeof partFormSchema>;

// Explicitly define SubPartData to match Zod schema and usage
export interface SubPartData {
  partId: string;
  quantity: number;
}

/**
 * Interface for warehouse data used in inventory management
 */
export interface WarehouseData {
  _id: string;
  warehouseCode: string;
  name: string;
  location: string;
  isActive?: boolean;
}

/**
 * Interface for location data used in inventory management
 */
export interface LocationData {
  _id: string;
  name: string;
  warehouseId: string;
  locationType: 'Bin' | 'Shelf' | 'Floor Area' | 'Staging' | 'Production Area' | 'Quality Control' | 'Shipping' | 'Receiving';
  isActive: boolean;
  description?: string;
}

/**
 * Interface for current inventory data display
 */
export interface CurrentInventoryData {
  partId: string;
  warehouseId: string;
  locationId: string;
  stockLevels: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  totalStock: number;
}

/**
 * Interface for multi-warehouse inventory details used in the edit form
 */
export interface InventoryDetailsData {
  warehouses: WarehouseInventoryData[];
  adjustmentReason?: string;
  adjustmentNotes?: string;
}

/**
 * Interface for warehouse inventory data in the edit form
 */
export interface WarehouseInventoryData {
  warehouseId: string;
  warehouseName: string;
  locations: LocationInventoryData[];
}

/**
 * Interface for location inventory data in the edit form
 */
export interface LocationInventoryData {
  locationId: string;
  locationName: string;
  stockLevels: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
}