'use client';

import { getApiUrl } from '@/app/utils/env';
import { safeFetch } from '@/app/utils/safeFetch';
import { PartFormData } from '@/app/components/forms/PartForm/types';

/**
 * Client-side API wrapper for part service
 * This avoids importing server-side mongoose models in client components
 */

/**
 * Get part by ID
 * @param partId - The ID of the part to retrieve
 * @returns The part data or null if not found
 */
export async function getPartById(partId: string) {
  try {
    const response = await safeFetch(`${getApiUrl('/api/parts')}/${partId}`);
    
    if (!response.success || !response.data) {
      console.error('Failed to fetch part:', response.error);
      return null;
    }
    
    return response.data;
  } catch (error) {
    console.error('Error fetching part:', error);
    return null;
  }
}

/**
 * Search parts with optional filters
 * @param query - Search query
 * @param options - Search options
 * @returns Search results
 */
export async function searchParts(query: string, options: any = {}) {
  try {
    const queryParams = new URLSearchParams({
      q: query,
      ...options
    });
    
    const response = await safeFetch(`${getApiUrl('/api/parts/search')}?${queryParams}`);
    
    if (!response.success) {
      console.error('Failed to search parts:', response.error);
      return { parts: [], total: 0 };
    }
    
    return response.data;
  } catch (error) {
    console.error('Error searching parts:', error);
    return { parts: [], total: 0 };
  }
}

/**
 * Interface for inventory stock levels from form
 */
interface InventoryStockLevels {
  raw: number;
  hardening: number;
  grinding: number;
  finished: number;
  rejected: number;
}

/**
 * Interface for inventory data from form
 */
interface InventoryFormData {
  warehouseId?: string;
  locationId?: string;
  stockLevels: InventoryStockLevels;
  adjustmentReason?: 'INITIAL_STOCK' | 'PHYSICAL_COUNT' | 'DAMAGE' | 'FOUND' | 'CORRECTION' | 'OTHER' | undefined;
  adjustmentNotes?: string | undefined;
}

/**
 * Create or update inventory records for a part
 * Transforms form inventory data into V4 schema inventory records
 * @param partId - The ID of the part
 * @param inventoryData - Inventory data from the form
 * @returns Promise with success/error result
 */
export async function createOrUpdatePartInventory(partId: string, inventoryData: InventoryFormData) {
  try {
    console.log('[INVENTORY API] Creating/updating inventory for part:', partId, inventoryData);

    if (!inventoryData.locationId) {
      throw new Error('Location ID is required for inventory operations');
    }

    const results = [];
    const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'] as const;

    // Create/update inventory record for each stock type
    for (const stockType of stockTypes) {
      const quantity = inventoryData.stockLevels[stockType];

      // Only create records for non-zero quantities or if updating existing records
      if (quantity > 0) {
        const inventoryRecord = {
          partId,
          locationId: inventoryData.locationId,
          stockType,
          quantity,
          // Include adjustment metadata if provided
          ...(inventoryData.adjustmentReason && { adjustmentReason: inventoryData.adjustmentReason }),
          ...(inventoryData.adjustmentNotes && { adjustmentNotes: inventoryData.adjustmentNotes })
        };

        console.log('[INVENTORY API] Creating inventory record:', inventoryRecord);

        const response = await safeFetch(`${getApiUrl('/api/inventories')}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(inventoryRecord),
        });

        if (!response.success) {
          console.error(`[INVENTORY API] Failed to create inventory record for ${stockType}:`, response.error);
          throw new Error(`Failed to create inventory record for ${stockType}: ${response.error}`);
        }

        results.push({ stockType, success: true, data: response.data });
      }
    }

    console.log('[INVENTORY API] Successfully created/updated inventory records:', results);
    return { success: true, results };

  } catch (error) {
    console.error('[INVENTORY API] Error creating/updating inventory:', error);
    throw error;
  }
}

/**
 * Load existing inventory data for a part
 * Transforms V4 inventory records back to form structure
 * @param partId - The ID of the part
 * @returns Promise with inventory data in form format
 */
export async function loadPartInventory(partId: string): Promise<InventoryFormData | null> {
  try {
    console.log('[INVENTORY API] Loading inventory for part:', partId);

    const response = await safeFetch(`${getApiUrl('/api/inventories')}?partId=${partId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.success) {
      console.error('[INVENTORY API] Failed to load inventory:', response.error);
      return null;
    }

    const inventoryRecords = response.data?.inventories || [];
    console.log('[INVENTORY API] Loaded inventory records:', inventoryRecords);

    if (inventoryRecords.length === 0) {
      return null;
    }

    // Transform V4 records back to form structure
    const stockLevels: InventoryStockLevels = {
      raw: 0,
      hardening: 0,
      grinding: 0,
      finished: 0,
      rejected: 0
    };

    let locationId = '';
    let warehouseId = '';

    // Aggregate quantities by stock type
    for (const record of inventoryRecords) {
      if (record.stockType && stockLevels.hasOwnProperty(record.stockType)) {
        stockLevels[record.stockType as keyof InventoryStockLevels] += record.quantity || 0;
      }

      // Use the first record's location info (assuming all records are for the same location)
      if (!locationId && record.locationId) {
        locationId = record.locationId;
      }
      if (!warehouseId && record.location?.warehouseId) {
        warehouseId = record.location.warehouseId;
      }
    }

    const inventoryData: InventoryFormData = {
      warehouseId,
      locationId,
      stockLevels
    };

    console.log('[INVENTORY API] Transformed inventory data for form:', inventoryData);
    return inventoryData;

  } catch (error) {
    console.error('[INVENTORY API] Error loading inventory:', error);
    return null;
  }
}
