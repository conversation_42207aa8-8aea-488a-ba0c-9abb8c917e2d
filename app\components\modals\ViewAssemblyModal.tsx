'use client';

import { Layers } from 'lucide-react';

import { BaseViewModal } from './BaseViewModal';
import { BaseViewButton } from './BaseViewButton';
import { AssemblyViewContent } from './content/AssemblyViewContent';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { getBomTabConfig, type BomTabConfig } from '@/app/components/bom/bomIntegrationHelpers';

interface ViewAssemblyModalProps {
  assembly: Assembly;
  isOpen: boolean;
  onClose: () => void;
  trigger?: React.ReactNode;
}

/**
 * Modal component for viewing assembly details
 * Now uses the standardized BaseViewModal with automatic BOM integration
 */
export function ViewAssemblyModal({ assembly, isOpen, onClose, trigger }: ViewAssemblyModalProps) {
  // Get BOM configuration for this assembly
  const bomConfig: BomTabConfig | null = getBomTabConfig(assembly, { type: 'assembly' });

  // Determine if content will be in tab mode (when BOM data exists)
  const isInTab = bomConfig?.bomData ? true : false;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={assembly.name}
      subtitle="Assembly Details"
      icon={<Layers className="h-5 w-5 text-primary" />}
      {...(bomConfig || {})}
    >
      <AssemblyViewContent assembly={assembly} isInTab={isInTab} />
    </BaseViewModal>
  );
}

interface ViewAssemblyButtonProps {
  assembly: Assembly;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  /**
   * Custom button content (overrides default content)
   */
  buttonContent?: React.ReactNode;
  /**
   * Children to render inside the button
   */
  children?: React.ReactNode;
}

/**
 * Button component that opens the ViewAssemblyModal
 * Now uses the standardized BaseViewButton with automatic BOM integration
 */
export function ViewAssemblyButton({
  assembly,
  variant = 'default',
  size = 'sm',
  className,
  buttonContent,
  children
}: ViewAssemblyButtonProps) {
  // Get BOM configuration for this assembly
  const bomConfig: BomTabConfig | null = getBomTabConfig(assembly, { type: 'assembly' });

  return (
    <BaseViewButton
      title={assembly.name}
      subtitle="Assembly Details"
      icon={<Layers className="h-5 w-5 text-primary" />}
      variant={variant}
      size={size}
      className={className || ''}
      buttonContent={buttonContent || children}
      {...(bomConfig || {})}
    >
      <AssemblyViewContent assembly={assembly} />
    </BaseViewButton>
  );
}
