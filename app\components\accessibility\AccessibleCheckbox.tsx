'use client';

import { Checkbox } from '@/app/components/forms/checkbox';
import { cn } from '@/app/lib/utils';
import React, { useState } from 'react';
import { LiveRegion } from './LiveRegion';

export interface AccessibleCheckboxProps {
  /**
   * Label for the checkbox (required for accessibility)
   */
  label: string;
  
  /**
   * Optional description for the checkbox
   */
  description?: string;
  
  /**
   * Error message to display when validation fails
   */
  error?: string;
  
  /**
   * Whether the checkbox is required
   */
  isRequired?: boolean;
  
  /**
   * ID for the checkbox (will be generated if not provided)
   */
  id?: string;
  
  /**
   * Additional class name for the container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the label
   */
  labelClassName?: string;
  
  /**
   * Additional class name for the description
   */
  descriptionClassName?: string;
  
  /**
   * Additional class name for the error message
   */
  errorClassName?: string;
  
  /**
   * Whether to announce errors to screen readers
   */
  announceErrors?: boolean;
  
  /**
   * Whether the checkbox is checked
   */
  checked?: boolean;
  
  /**
   * Default checked state
   */
  defaultChecked?: boolean;
  
  /**
   * Callback when the checked state changes
   */
  onCheckedChange?: (checked: boolean) => void;
  
  /**
   * Whether the checkbox is disabled
   */
  disabled?: boolean;
  
  /**
   * Additional class name for the checkbox
   */
  checkboxClassName?: string;
}

/**
 * AccessibleCheckbox component that enhances the standard Checkbox with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleCheckbox: React.FC<AccessibleCheckboxProps> = ({
  label,
  description,
  error,
  isRequired = false,
  id: propId,
  containerClassName,
  labelClassName,
  descriptionClassName,
  errorClassName,
  announceErrors = true,
  checked,
  defaultChecked,
  onCheckedChange,
  disabled,
  checkboxClassName,
}) => {
  const [wasInteracted, setWasInteracted] = useState(false);
  const id = propId || React.useId();
  const descriptionId = `${id}-description`;
  const errorId = `${id}-error`;
  
  // Handle checked state change
  const handleCheckedChange = (checked: boolean) => {
    setWasInteracted(true);
    if (onCheckedChange) onCheckedChange(checked);
  };
  
  // Determine if we should show the error
  const showError = !!error && wasInteracted;
  
  // Determine the aria-describedby attribute
  const ariaDescribedBy = [
    description ? descriptionId : null,
    showError ? errorId : null
  ].filter(Boolean).join(' ') || undefined;
  
  return (
    <div className={cn("space-y-2", containerClassName)}>
      <div className="flex items-start space-x-2">
        <Checkbox
          id={id}
          {...(checked !== undefined && { checked })}
          {...(defaultChecked !== undefined && { defaultChecked })}
          onCheckedChange={handleCheckedChange}
          disabled={disabled}
          aria-describedby={ariaDescribedBy}
          aria-invalid={showError}
          aria-required={isRequired}
          required={isRequired}
          className={cn(
            showError && "border-destructive focus-visible:ring-destructive",
            checkboxClassName
          )}
        />
        
        <div className="grid gap-1.5 leading-none">
          <label
            htmlFor={id}
            className={cn(
              "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
              showError && "text-destructive",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-destructive ml-1" aria-hidden="true">*</span>
            )}
          </label>
          
          {description && (
            <p 
              id={descriptionId}
              className={cn(
                "text-sm text-muted-foreground dark:text-text-secondary",
                descriptionClassName
              )}
            >
              {description}
            </p>
          )}
        </div>
      </div>
      
      {showError && (
        <p 
          id={errorId}
          className={cn(
            "text-sm font-medium text-destructive dark:text-destructive pl-6",
            errorClassName
          )}
          role="alert"
        >
          {error}
        </p>
      )}
      
      {/* Announce errors to screen readers */}
      {announceErrors && showError && (
        <LiveRegion politeness="assertive">
          {`Error for ${label}: ${error}`}
        </LiveRegion>
      )}
    </div>
  );
};

AccessibleCheckbox.displayName = 'AccessibleCheckbox';

export default AccessibleCheckbox;
