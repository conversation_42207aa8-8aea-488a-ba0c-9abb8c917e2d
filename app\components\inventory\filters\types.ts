/**
 * Types and utilities for inventory filtering
 */

export interface RangeFilter {
  min: number;
  max: number;
  enabled: boolean;
}

export interface ReorderLevelFilter {
  value: number;
  belowOnly: boolean;
  enabled: boolean;
}

export interface MultiSelectFilter<T extends string = string> {
  values: T[];
  enabled: boolean;
}

export interface FilterState {
  stockQuantity: RangeFilter;
  reorderLevel: ReorderLevelFilter;
  supplier: MultiSelectFilter;
  category: MultiSelectFilter;
  location: MultiSelectFilter;

  // NEW: Planning Parameter Filters
  planningMethod: MultiSelectFilter;
  safetyStockLevel: RangeFilter;
  maximumStockLevel: RangeFilter;
  leadTimeDays: RangeFilter;
  averageDailyUsage: RangeFilter;
}

export const DEFAULT_FILTER_STATE: FilterState = {
  stockQuantity: {
    min: 0,
    max: 1000,
    enabled: false
  },
  reorderLevel: {
    value: 10,
    belowOnly: true,
    enabled: false
  },
  supplier: {
    values: [],
    enabled: false
  },
  category: {
    values: [],
    enabled: false
  },
  location: {
    values: [],
    enabled: false
  },

  // NEW: Planning Parameter Filters
  planningMethod: {
    values: [],
    enabled: false
  },
  safetyStockLevel: {
    min: 0,
    max: 1000,
    enabled: false
  },
  maximumStockLevel: {
    min: 0,
    max: 10000,
    enabled: false
  },
  leadTimeDays: {
    min: 0,
    max: 365,
    enabled: false
  },
  averageDailyUsage: {
    min: 0,
    max: 100,
    enabled: false
  }
};

/**
 * Count the number of active filters - UPDATED: Include planning parameter filters
 */
export const countActiveFilters = (filters: FilterState): number => {
  if (!filters) return 0;

  let count = 0;

  if (filters.stockQuantity?.enabled) count++;
  if (filters.reorderLevel?.enabled) count++;
  if (filters.supplier?.enabled && filters.supplier.values.length > 0) count++;
  if (filters.category?.enabled && filters.category.values.length > 0) count++;
  if (filters.location?.enabled && filters.location.values.length > 0) count++;

  // NEW: Planning Parameter Filters
  if (filters.planningMethod?.enabled && filters.planningMethod.values.length > 0) count++;
  if (filters.safetyStockLevel?.enabled) count++;
  if (filters.maximumStockLevel?.enabled) count++;
  if (filters.leadTimeDays?.enabled) count++;
  if (filters.averageDailyUsage?.enabled) count++;

  return count;
};

/**
 * Check if any filters are active - UPDATED: Include planning parameter filters
 */
export const hasActiveFilters = (filters: FilterState): boolean => {
  return countActiveFilters(filters) > 0;
};

/**
 * Reset all filters to their default state
 */
export const resetFilters = (): FilterState => {
  return { ...DEFAULT_FILTER_STATE };
};

/**
 * Get the localStorage key for filters
 */
export const getFilterStorageKey = (userId: string = 'default'): string => {
  return `inventory_filters_${userId}`;
};

/**
 * Save filters to localStorage
 */
export const saveFiltersToStorage = (filters: FilterState, userId: string = 'default'): void => {
  try {
    localStorage.setItem(getFilterStorageKey(userId), JSON.stringify(filters));
  } catch (error) {
    console.error('Failed to save filters to localStorage:', error);
  }
};

/**
 * Load filters from localStorage - FIXED: Merge with defaults to ensure all fields are present
 */
export const loadFiltersFromStorage = (userId: string = 'default'): FilterState | null => {
  try {
    const storedFilters = localStorage.getItem(getFilterStorageKey(userId));
    if (storedFilters) {
      const parsedFilters = JSON.parse(storedFilters);
      // FIXED: Merge with DEFAULT_FILTER_STATE to ensure all new fields are present
      // This prevents TypeError when new filter fields are added
      return {
        ...DEFAULT_FILTER_STATE,
        ...parsedFilters,
        // Ensure nested objects are properly merged
        stockQuantity: { ...DEFAULT_FILTER_STATE.stockQuantity, ...parsedFilters.stockQuantity },
        reorderLevel: { ...DEFAULT_FILTER_STATE.reorderLevel, ...parsedFilters.reorderLevel },
        supplier: { ...DEFAULT_FILTER_STATE.supplier, ...parsedFilters.supplier },
        category: { ...DEFAULT_FILTER_STATE.category, ...parsedFilters.category },
        location: { ...DEFAULT_FILTER_STATE.location, ...parsedFilters.location },
        planningMethod: { ...DEFAULT_FILTER_STATE.planningMethod, ...parsedFilters.planningMethod },
        safetyStockLevel: { ...DEFAULT_FILTER_STATE.safetyStockLevel, ...parsedFilters.safetyStockLevel },
        maximumStockLevel: { ...DEFAULT_FILTER_STATE.maximumStockLevel, ...parsedFilters.maximumStockLevel },
        leadTimeDays: { ...DEFAULT_FILTER_STATE.leadTimeDays, ...parsedFilters.leadTimeDays },
        averageDailyUsage: { ...DEFAULT_FILTER_STATE.averageDailyUsage, ...parsedFilters.averageDailyUsage },
      };
    }
  } catch (error) {
    console.error('Failed to load filters from localStorage:', error);
  }
  return null;
};
