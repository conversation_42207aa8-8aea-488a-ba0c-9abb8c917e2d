'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

// Define types for UI preferences
export interface ProductUIPreferences {
  viewMode: 'grid' | 'table';
  sortBy: string;
  filterStatus: string;
  searchQuery: string;
}

// Default preferences
const defaultPreferences: ProductUIPreferences = {
  viewMode: 'grid',
  sortBy: 'name',
  filterStatus: 'all',
  searchQuery: '',
};

/**
 * Hook for managing product UI preferences with localStorage persistence
 */
export function useProductUIPreferences() {
  // Initialize with default preferences
  const [preferences, setPreferences] = useState<ProductUIPreferences>(defaultPreferences);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Load preferences from localStorage on mount
  useEffect(() => {
    if (isLoaded) return; // Only load once
    
    try {
      const storedPreferences = localStorage.getItem('productUIPreferences');
      if (storedPreferences) {
        const parsedPreferences = JSON.parse(storedPreferences);
        setPreferences(prev => ({
          ...prev,
          ...parsedPreferences,
        }));
      }
    } catch (error) {
      console.error('Failed to load product UI preferences:', error);
    } finally {
      setIsLoaded(true);
    }
  }, [isLoaded]);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    if (!isLoaded || isUpdating) return; // Don't save during initial load or updates
    
    try {
      localStorage.setItem('productUIPreferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save product UI preferences:', error);
    }
  }, [preferences, isLoaded, isUpdating]);

  // Function to update a single preference
  const updatePreference = useCallback(<K extends keyof ProductUIPreferences>(
    key: K,
    value: ProductUIPreferences[K]
  ) => {
    setIsUpdating(true);
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
    setTimeout(() => setIsUpdating(false), 0);
  }, []);

  // Function to update multiple preferences at once
  const updatePreferences = useCallback((updates: Partial<ProductUIPreferences>) => {
    setIsUpdating(true);
    setPreferences(prev => ({
      ...prev,
      ...updates,
    }));
    setTimeout(() => setIsUpdating(false), 0);
  }, []);
  
  // Function to reset preferences to defaults
  const resetPreferences = useCallback(() => {
    setIsUpdating(true);
    setPreferences(defaultPreferences);
    setTimeout(() => setIsUpdating(false), 0);
  }, []);
  
  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(() => ({
    preferences,
    updatePreference,
    updatePreferences,
    resetPreferences,
    isLoaded
  }), [preferences, updatePreference, updatePreferences, resetPreferences, isLoaded]);
}
