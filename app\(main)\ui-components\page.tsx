"use client";

import { DebounceDemo } from "@/app/components/demos/DebounceDemo";
import { DataTableVariantsDemo } from "@/app/components/demos/EnhancedTableDemo";
import { DataTableDemo } from "@/app/components/demos/DataTableDemo";
import UnifiedCardDemo from "@/app/components/demos/UnifiedCardDemo";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/navigation/tabs";

export default function UIComponentsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">UI Components</h1>

      <Tabs defaultValue="data-table" className="w-full mb-12">
        <TabsList className="mb-6">
          <TabsTrigger value="data-table">New DataTable</TabsTrigger>
          <TabsTrigger value="cards">Unified Cards</TabsTrigger>
          <TabsTrigger value="table">DataTable Variants</TabsTrigger>
          <TabsTrigger value="utils">Utility Functions</TabsTrigger>
        </TabsList>

        <TabsContent value="data-table" className="space-y-12">
          <DataTableDemo
            onView={(item) => console.log('View item:', item)}
            onEdit={(item) => console.log('Edit item:', item)}
            onDelete={(item) => console.log('Delete item:', item)}
          />
        </TabsContent>

        <TabsContent value="cards" className="space-y-12">
          <UnifiedCardDemo />
        </TabsContent>

        <TabsContent value="table" className="space-y-12">
          <DataTableVariantsDemo />
        </TabsContent>

        <TabsContent value="utils" className="space-y-12">
          <DebounceDemo />
        </TabsContent>
      </Tabs>
    </div>
  );
}
