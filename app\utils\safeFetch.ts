/**
 * Safe fetch utility that prevents "Unexpected token '<'" errors
 * by validating responses before JSON parsing
 */

export interface SafeFetchOptions extends RequestInit {
  timeout?: number;
}

export interface SafeFetchResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

/**
 * Enhanced JSON parsing with detailed error handling for Vercel deployment
 */
export async function safeJsonParse<T = any>(response: Response): Promise<SafeFetchResult<T>> {
  try {
    // Check content type before parsing JSON
    const contentType = response.headers.get('content-type') || '';

    if (!contentType.includes('application/json')) {
      const responseText = await response.text();
      console.error('[SafeJsonParse] Expected JSON, got:', contentType);
      console.error('[SafeJsonParse] Response preview:', responseText.substring(0, 200));

      // Check if it's an HTML error page (common in Vercel deployment issues)
      if (responseText.includes('<!DOCTYPE html>') || responseText.includes('<html')) {
        return {
          success: false,
          error: 'Server returned HTML error page instead of JSON. This usually indicates a server error or misconfigured API route.',
          status: response.status
        };
      }

      return {
        success: false,
        error: `Expected JSON response, got ${contentType}. Response: ${responseText.substring(0, 100)}...`,
        status: response.status
      };
    }

    // Get response text first for better error handling
    const responseText = await response.text();

    if (!responseText.trim()) {
      return {
        success: false,
        error: 'Empty response body',
        status: response.status
      };
    }

    try {
      const data = JSON.parse(responseText);
      return {
        success: true,
        data,
        status: response.status
      };
    } catch (parseError) {
      console.error('[SafeJsonParse] JSON parse error:', parseError);
      console.error('[SafeJsonParse] Raw response:', responseText.substring(0, 500));

      return {
        success: false,
        error: `Failed to parse JSON: ${parseError instanceof Error ? parseError.message : 'Unknown parse error'}. Response preview: ${responseText.substring(0, 100)}...`,
        status: response.status
      };
    }

  } catch (error) {
    console.error('[SafeJsonParse] Error reading response:', error);
    return {
      success: false,
      error: `Failed to read response: ${error instanceof Error ? error.message : 'Unknown error'}`,
      status: response.status
    };
  }
}

/**
 * Safe fetch wrapper that handles HTML error responses gracefully
 */
export async function safeFetch<T = any>(
  url: string, 
  options: SafeFetchOptions = {}
): Promise<SafeFetchResult<T>> {
  const { timeout = 10000, ...fetchOptions } = options;
  
  try {
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Check if response is ok
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[SafeFetch] API error (${response.status}):`, errorText.substring(0, 200));
      
      return {
        success: false,
        error: `API request failed with status ${response.status}`,
        status: response.status
      };
    }
    
    // Check content type
    const contentType = response.headers.get('content-type') || '';
    if (!contentType.includes('application/json')) {
      const body = await response.text();
      console.error(`[SafeFetch] Expected JSON, got ${contentType}:`, body.substring(0, 200));
      
      return {
        success: false,
        error: `Expected JSON response, got ${contentType}. This usually means the API returned an HTML error page.`,
        status: response.status
      };
    }
    
    // Parse JSON safely
    try {
      const data = await response.json();
      return {
        success: true,
        data,
        status: response.status
      };
    } catch (parseError) {
      console.error('[SafeFetch] JSON parse error:', parseError);
      const responseText = await response.text();
      
      return {
        success: false,
        error: `Failed to parse JSON response: ${parseError instanceof Error ? parseError.message : 'Unknown parse error'}`,
        status: response.status
      };
    }
    
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: `Request timeout after ${timeout}ms`
      };
    }
    
    console.error('[SafeFetch] Network error:', error);
    return {
      success: false,
      error: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Safe fetch for API routes with automatic error handling
 */
export async function safeApiCall<T = any>(
  endpoint: string,
  options: SafeFetchOptions = {}
): Promise<SafeFetchResult<T>> {
  // Ensure we use relative paths for internal API calls
  const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  console.log(`[SafeApiCall] Making request to: ${url}`);
  
  const result = await safeFetch<T>(url, options);
  
  if (!result.success) {
    console.error(`[SafeApiCall] Failed request to ${url}:`, result.error);
  }
  
  return result;
}

/**
 * Legacy fetch wrapper for backward compatibility
 * Gradually replace direct fetch calls with this
 */
export async function legacyFetchWrapper(url: string, options?: RequestInit) {
  const result = await safeFetch(url, options);
  
  if (!result.success) {
    throw new Error(result.error || 'API request failed');
  }
  
  // Return a Response-like object for compatibility
  return {
    ok: true,
    status: result.status || 200,
    json: async () => result.data,
    text: async () => JSON.stringify(result.data)
  };
}
