import mongoose, { ClientSession, Types } from 'mongoose';
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import { Inventories, IInventories } from '../models/inventories.model';
import { Location } from '../models/location.model';
import { Part } from '../models/part.model';
import Warehouse from '../models/warehouse.model';
// V4 Schema: Removed migration feature flags - no longer needed

/**
 * UNIFIED INVENTORY SERVICE V4 - PRODUCTION READY
 *
 * This service provides comprehensive inventory management functionality:
 * - Atomic stock increment/decrement operations
 * - Multi-warehouse and multi-location inventory management
 * - Transaction-safe operations with MongoDB sessions
 * - Comprehensive error handling and logging
 * - Performance-optimized queries with proper indexing
 * - Location-based inventory tracking
 * - Aggregated inventory data with warehouse/location breakdown
 * - V4 Schema: Uses locationId for all operations
 */

// DTOs for inventory operations with location support
export interface CreateInventoryDto {
  partId: string;
  locationId: string; // V4 Schema - location-based tracking (REQUIRED)
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  quantity: number;
  safetyStockLevel?: number;
  maximumStockLevel?: number;
  averageDailyUsage?: number;
  abcClassification?: string;
  notes?: string;
  // REMOVED: warehouseId - use locationId instead (V4 Schema)
}

export interface UpdateInventoryDto extends Partial<CreateInventoryDto> {}

export interface InventoryQueryOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
  partId?: string;
  locationId?: string;
  stockType?: string;
  // REMOVED: warehouseId - use locationId instead (V4 Schema)
  // To filter by warehouse, query locations collection first to get locationIds
}

// Interface for aggregated inventory data
export interface AggregatedInventoryData {
  partId: string;
  warehouses: {
    warehouseId: string;
    warehouseName: string;
    totalStock: number;
    locations: {
      locationId: string;
      locationName: string;
      stockTypes: {
        stockType: string;
        quantity: number;
        safetyStockLevel?: number;
        maximumStockLevel?: number;
      }[];
    }[];
  }[];
  totalStock: number;
  totalFinishedStock: number;
}

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoriesService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 */
export const handleMongoDBError = (error: any) => {
  console.error('[InventoriesService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'inventories');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate inventory record: This part/location/stockType combination already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else if (error.name === 'CastError') {
    errorType = 'cast';
    errorStatus = 400;
    errorMessage = `Invalid data format: ${error.message}`;
    setTag('error.subtype', 'cast_error');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

/**
 * Create a new inventory record
 */
export const createInventory = async (inventoryData: CreateInventoryDto): Promise<IInventories> => {
  try {
    await connectToDatabase();
    logOperation('CREATE', 'Inventory', { partId: inventoryData.partId, locationId: inventoryData.locationId });

    // Validate that the part exists
    const part = await Part.findById(inventoryData.partId);
    if (!part) {
      throw new Error(`Part with ID ${inventoryData.partId} not found`);
    }

    // Validate that the location exists
    const location = await Location.findById(inventoryData.locationId);
    if (!location) {
      throw new Error(`Location with ID ${inventoryData.locationId} not found`);
    }

    // Check for existing inventory record for this part/location/stockType combination
    const existingInventory = await Inventories.findOne({
      partId: inventoryData.partId,
      locationId: inventoryData.locationId,
      stockType: inventoryData.stockType
    });

    if (existingInventory) {
      throw new Error(`Inventory record already exists for part ${inventoryData.partId} at location ${inventoryData.locationId} with stock type ${inventoryData.stockType}`);
    }

    const inventory = new Inventories(inventoryData);
    const savedInventory = await inventory.save();

    logOperation('CREATED', 'Inventory', { id: savedInventory._id });
    return savedInventory;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

/**
 * Interface for stock operation parameters - V4 Schema
 */
export interface StockOperationParams {
  partId: string | Types.ObjectId;
  locationId: string | Types.ObjectId; // V4 Schema - use locationId instead of warehouseId
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  quantity: number;
  session?: ClientSession;
}

/**
 * Interface for inventory query parameters
 */
export interface InventoryQueryParams {
  partId?: string | Types.ObjectId;
  locationId?: string | Types.ObjectId; // V4 Schema - use locationId instead of warehouseId
  stockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  minQuantity?: number;
  maxQuantity?: number;
}

// V4 Schema: Removed migration helper functions - no longer needed

/**
 * InventoriesService class - handles all inventory operations
 */
export class InventoriesService {
  
  /**
   * Increment stock for a specific part/location/stockType combination
   * Uses atomic $inc operation with upsert for safety
   */
  static async incrementStock(params: StockOperationParams): Promise<IInventories> {
    try {
      await connectToDatabase();

      const { partId, locationId, stockType, quantity, session } = params;

      logOperation('incrementStock', 'service', {
        partId: partId.toString(),
        locationId: locationId.toString(),
        stockType,
        quantity
      });

      if (quantity <= 0) {
        throw new Error('Quantity must be positive for increment operation');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      };

      const update = {
        $inc: { quantity: quantity },
        $set: { lastUpdated: new Date() },
        $setOnInsert: {
          partId: new Types.ObjectId(partId),
          locationId: new Types.ObjectId(locationId),
          stockType,
          createdAt: new Date()
        }
      };

      const options = {
        upsert: true,
        new: true,
        ...(session && { session })
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        throw new Error('Failed to increment stock - no result returned');
      }

      logOperation('incrementStock completed', 'service', { 
        inventoryId: result._id,
        newQuantity: result.quantity 
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to increment stock: ${errorDetails.message}`);
    }
  }

  /**
   * Decrement stock for a specific part/location/stockType combination
   * Ensures stock doesn't go below zero
   */
  static async decrementStock(params: StockOperationParams): Promise<IInventories> {
    try {
      await connectToDatabase();

      const { partId, locationId, stockType, quantity, session } = params;

      logOperation('decrementStock', 'service', {
        partId: partId.toString(),
        locationId: locationId.toString(),
        stockType,
        quantity
      });

      if (quantity <= 0) {
        throw new Error('Quantity must be positive for decrement operation');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType,
        quantity: { $gte: quantity } // Ensure sufficient stock
      };

      const update = {
        $inc: { quantity: -quantity },
        $set: { lastUpdated: new Date() }
      };

      const options = {
        new: true,
        ...(session && { session })
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        // Check if the inventory record exists but has insufficient stock
        const existingRecord = await Inventories.findOne({
          partId: new Types.ObjectId(partId),
          locationId: new Types.ObjectId(locationId),
          stockType
        }, null, { ...(session && { session }) });

        if (existingRecord) {
          throw new Error(`Insufficient stock: Available ${existingRecord.quantity}, requested ${quantity}`);
        } else {
          throw new Error('Inventory record not found for decrement operation');
        }
      }

      logOperation('decrementStock completed', 'service', { 
        inventoryId: result._id,
        newQuantity: result.quantity 
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to decrement stock: ${errorDetails.message}`);
    }
  }

  /**
   * Get current stock for a specific part/location/stockType combination
   */
  static async getStock(params: Omit<StockOperationParams, 'quantity'>): Promise<number> {
    try {
      await connectToDatabase();

      const { partId, locationId, stockType } = params;

      const inventory = await Inventories.findOne({
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      });

      return inventory?.quantity || 0;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get stock: ${errorDetails.message}`);
    }
  }

  /**
   * Get all inventory records for a specific part
   */
  static async getPartInventory(partId: string | Types.ObjectId): Promise<IInventories[]> {
    try {
      await connectToDatabase();

      logOperation('getPartInventory', 'service', { partId: partId.toString() });

      const inventories = await Inventories.find({
        partId: new Types.ObjectId(partId)
      })
      .populate('locationId', 'name warehouseId')
      .sort({ stockType: 1, locationId: 1 });

      return inventories;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Get detailed inventory breakdown for a part with warehouse grouping
   * This provides the multi-warehouse view needed for the part details modal
   */
  static async getPartInventoryWithWarehouseBreakdown(partId: string | Types.ObjectId) {
    try {
      await connectToDatabase();

      logOperation('getPartInventoryWithWarehouseBreakdown', 'service', { partId: partId.toString() });

      const breakdown = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },

        // V4 Schema: Lookup location details first
        {
          $lookup: {
            from: 'locations',
            localField: 'locationId',
            foreignField: '_id',
            as: 'location',
            pipeline: [
              { $project: { name: 1, warehouseId: 1 } }
            ]
          }
        },

        // Unwind location (should always be one)
        { $unwind: '$location' },

        // Lookup warehouse details through location
        {
          $lookup: {
            from: 'warehouses',
            localField: 'location.warehouseId',
            foreignField: '_id',
            as: 'warehouse',
            pipeline: [
              { $project: { name: 1, location: 1 } }
            ]
          }
        },

        // Unwind warehouse (should always be one)
        { $unwind: '$warehouse' },

        // Group by warehouse to get stock levels per warehouse
        {
          $group: {
            _id: '$location.warehouseId',
            warehouseName: { $first: '$warehouse.name' },
            warehouseLocation: { $first: '$warehouse.location' },
            stockLevels: {
              $push: {
                stockType: '$stockType',
                quantity: '$quantity',
                lastUpdated: '$lastUpdated',
                locationName: '$location.name'
              }
            },
            totalQuantity: { $sum: '$quantity' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },

        // Transform stock levels into a more usable format
        {
          $addFields: {
            stockLevelsByType: {
              $arrayToObject: {
                $map: {
                  input: '$stockLevels',
                  as: 'stock',
                  in: {
                    k: '$$stock.stockType',
                    v: '$$stock.quantity'
                  }
                }
              }
            }
          }
        },

        // Add default values for missing stock types
        {
          $addFields: {
            stockLevelsByType: {
              raw: { $ifNull: ['$stockLevelsByType.raw', 0] },
              hardening: { $ifNull: ['$stockLevelsByType.hardening', 0] },
              grinding: { $ifNull: ['$stockLevelsByType.grinding', 0] },
              finished: { $ifNull: ['$stockLevelsByType.finished', 0] },
              rejected: { $ifNull: ['$stockLevelsByType.rejected', 0] }
            }
          }
        },

        // Sort by warehouse name for consistent display
        { $sort: { warehouseName: 1 } },

        // Final projection
        {
          $project: {
            warehouseId: '$_id',
            warehouseName: 1,
            warehouseLocation: 1,
            stockLevels: '$stockLevelsByType',
            totalQuantity: 1,
            lastUpdated: 1,
            _id: 0
          }
        }
      ]);

      // Also get overall totals
      const totals = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },
        {
          $group: {
            _id: '$stockType',
            totalQuantity: { $sum: '$quantity' }
          }
        },
        {
          $group: {
            _id: null,
            stockTotals: {
              $push: {
                stockType: '$_id',
                quantity: '$totalQuantity'
              }
            },
            grandTotal: { $sum: '$totalQuantity' }
          }
        },
        {
          $addFields: {
            stockTotalsByType: {
              $arrayToObject: {
                $map: {
                  input: '$stockTotals',
                  as: 'stock',
                  in: {
                    k: '$$stock.stockType',
                    v: '$$stock.quantity'
                  }
                }
              }
            }
          }
        },
        {
          $project: {
            totals: {
              raw: { $ifNull: ['$stockTotalsByType.raw', 0] },
              hardening: { $ifNull: ['$stockTotalsByType.hardening', 0] },
              grinding: { $ifNull: ['$stockTotalsByType.grinding', 0] },
              finished: { $ifNull: ['$stockTotalsByType.finished', 0] },
              rejected: { $ifNull: ['$stockTotalsByType.rejected', 0] }
            },
            grandTotal: 1,
            _id: 0
          }
        }
      ]);

      const result = {
        warehouseBreakdown: breakdown,
        totals: totals.length > 0 ? totals[0].totals : {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: 0,
          rejected: 0
        },
        grandTotal: totals.length > 0 ? totals[0].grandTotal : 0,
        warehouseCount: breakdown.length
      };

      logOperation('getPartInventoryWithWarehouseBreakdown completed', 'service', {
        partId: partId.toString(),
        warehouseCount: result.warehouseCount,
        grandTotal: result.grandTotal
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part inventory breakdown: ${errorDetails.message}`);
    }
  }

  /**
   * V4 Schema: Get inventory data for multiple parts efficiently
   * This replaces the legacy embedded inventory approach with proper aggregation
   */
  static async getMultiplePartsInventory(partIds: (string | Types.ObjectId)[]): Promise<Map<string, any>> {
    try {
      await connectToDatabase();

      logOperation('getMultiplePartsInventory', 'service', { partCount: partIds.length });

      const objectIds = partIds.map(id => new Types.ObjectId(id));

      const inventoryData = await Inventories.aggregate([
        // Match all inventory records for the requested parts
        { $match: { partId: { $in: objectIds } } },

        // Lookup location information
        {
          $lookup: {
            from: 'locations',
            localField: 'locationId',
            foreignField: '_id',
            as: 'location',
            pipeline: [{ $project: { name: 1, warehouseId: 1 } }]
          }
        },

        // Group by partId to aggregate all inventory for each part
        {
          $group: {
            _id: '$partId',
            totalStock: { $sum: '$quantity' },
            stockLevels: {
              $push: {
                stockType: '$stockType',
                quantity: '$quantity',
                locationId: '$locationId',
                locationName: { $arrayElemAt: ['$location.name', 0] },
                warehouseId: { $arrayElemAt: ['$location.warehouseId', 0] }
              }
            },
            primaryWarehouseId: { $first: { $arrayElemAt: ['$location.warehouseId', 0] } },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },

        // Transform stockLevels array into object structure for backward compatibility
        {
          $addFields: {
            stockLevelsObject: {
              $arrayToObject: {
                $map: {
                  input: [
                    { k: 'raw', v: { $sum: { $map: { input: { $filter: { input: '$stockLevels', cond: { $eq: ['$$this.stockType', 'raw'] } } }, as: 'item', in: '$$item.quantity' } } } },
                    { k: 'hardening', v: { $sum: { $map: { input: { $filter: { input: '$stockLevels', cond: { $eq: ['$$this.stockType', 'hardening'] } } }, as: 'item', in: '$$item.quantity' } } } },
                    { k: 'grinding', v: { $sum: { $map: { input: { $filter: { input: '$stockLevels', cond: { $eq: ['$$this.stockType', 'grinding'] } } }, as: 'item', in: '$$item.quantity' } } } },
                    { k: 'finished', v: { $sum: { $map: { input: { $filter: { input: '$stockLevels', cond: { $eq: ['$$this.stockType', 'finished'] } } }, as: 'item', in: '$$item.quantity' } } } },
                    { k: 'rejected', v: { $sum: { $map: { input: { $filter: { input: '$stockLevels', cond: { $eq: ['$$this.stockType', 'rejected'] } } }, as: 'item', in: '$$item.quantity' } } } }
                  ],
                  as: 'level',
                  in: '$$level'
                }
              }
            }
          }
        },

        // Final projection for backward compatibility
        {
          $project: {
            partId: '$_id',
            inventory: {
              currentStock: '$stockLevelsObject.finished',
              stockLevels: '$stockLevelsObject',
              warehouseId: { $toString: '$primaryWarehouseId' },
              safetyStockLevel: 0, // These are now in part master data
              maximumStockLevel: 0,
              averageDailyUsage: 0,
              abcClassification: 'C',
              lastStockUpdate: '$lastUpdated'
            },
            totalStock: 1,
            currentStock: '$stockLevelsObject.finished',
            _id: 0
          }
        }
      ]);

      // Convert to Map for efficient lookup
      const inventoryMap = new Map<string, any>();
      inventoryData.forEach(item => {
        inventoryMap.set(item.partId.toString(), item);
      });

      // Add empty inventory for parts with no inventory records
      partIds.forEach(partId => {
        const partIdStr = partId.toString();
        if (!inventoryMap.has(partIdStr)) {
          inventoryMap.set(partIdStr, {
            partId: partIdStr,
            inventory: {
              currentStock: 0,
              stockLevels: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              warehouseId: '',
              safetyStockLevel: 0,
              maximumStockLevel: 0,
              averageDailyUsage: 0,
              abcClassification: 'C',
              lastStockUpdate: null
            },
            totalStock: 0,
            currentStock: 0
          });
        }
      });

      return inventoryMap;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get multiple parts inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Get aggregated stock totals for a part across all warehouses and stock types
   */
  static async getPartStockSummary(partId: string | Types.ObjectId) {
    try {
      await connectToDatabase();
      
      logOperation('getPartStockSummary', 'service', { partId: partId.toString() });

      const summary = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },
        {
          $group: {
            _id: null,
            totalStock: { $sum: '$quantity' },
            stockByType: {
              $push: {
                stockType: '$stockType',
                quantity: '$quantity',
                warehouseId: '$warehouseId'
              }
            },
            warehouseCount: { $addToSet: '$warehouseId' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },
        {
          $project: {
            _id: 0,
            totalStock: 1,
            stockByType: 1,
            warehouseCount: { $size: '$warehouseCount' },
            lastUpdated: 1
          }
        }
      ]);

      return summary.length > 0 ? summary[0] : {
        totalStock: 0,
        stockByType: [],
        warehouseCount: 0,
        lastUpdated: null
      };
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part stock summary: ${errorDetails.message}`);
    }
  }

  /**
   * Get inventory records with optional filtering
   */
  static async queryInventory(params: InventoryQueryParams = {}): Promise<IInventories[]> {
    try {
      await connectToDatabase();
      
      logOperation('queryInventory', 'inventories', params);

      const filter: any = {};
      
      if (params.partId) {
        filter.partId = new Types.ObjectId(params.partId);
      }
      
      if (params.locationId) {
        filter.locationId = new Types.ObjectId(params.locationId);
      }
      
      if (params.stockType) {
        filter.stockType = params.stockType;
      }
      
      if (params.minQuantity !== undefined || params.maxQuantity !== undefined) {
        filter.quantity = {};
        if (params.minQuantity !== undefined) {
          filter.quantity.$gte = params.minQuantity;
        }
        if (params.maxQuantity !== undefined) {
          filter.quantity.$lte = params.maxQuantity;
        }
      }

      const inventories = await Inventories.find(filter)
        .populate('partId', 'partNumber name')
        .populate('locationId', 'name warehouseId')
        .sort({ partId: 1, stockType: 1, locationId: 1 });

      return inventories;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to query inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Create or update an inventory record
   */
  static async upsertInventory(inventoryData: Partial<IInventories>): Promise<IInventories> {
    try {
      await connectToDatabase();
      
      logOperation('upsertInventory', 'inventories', inventoryData);

      const { partId, locationId, stockType, ...updateData } = inventoryData;

      if (!partId || !locationId || !stockType) {
        throw new Error('partId, locationId, and stockType are required');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      };

      const update = {
        $set: {
          ...updateData,
          lastUpdated: new Date()
        },
        $setOnInsert: {
          partId: new Types.ObjectId(partId),
          locationId: new Types.ObjectId(locationId),
          stockType,
          createdAt: new Date()
        }
      };

      const options = {
        upsert: true,
        new: true
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        throw new Error('Failed to upsert inventory record');
      }

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to upsert inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Delete an inventory record - V4 Schema
   */
  static async deleteInventory(partId: string | Types.ObjectId, locationId: string | Types.ObjectId, stockType: string): Promise<boolean> {
    try {
      await connectToDatabase();

      logOperation('deleteInventory', 'service', { partId: partId.toString(), locationId: locationId.toString(), stockType });

      const result = await Inventories.deleteOne({
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      });

      return result.deletedCount > 0;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to delete inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Get warehouse inventory summary - V4 Schema
   * Gets summary by querying locations that belong to the warehouse
   */
  static async getWarehouseInventorySummary(warehouseId: string | Types.ObjectId) {
    try {
      await connectToDatabase();

      logOperation('getWarehouseInventorySummary', 'service', { warehouseId: warehouseId.toString() });

      // V4 Schema: First get all locations for this warehouse
      const locations = await Location.find({ warehouseId: new Types.ObjectId(warehouseId) }).select('_id');
      const locationIds = locations.map(loc => loc._id);

      if (locationIds.length === 0) {
        return []; // No locations found for this warehouse
      }

      const summary = await Inventories.aggregate([
        { $match: { locationId: { $in: locationIds } } },
        {
          $group: {
            _id: '$stockType',
            totalQuantity: { $sum: '$quantity' },
            partCount: { $addToSet: '$partId' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },
        {
          $project: {
            stockType: '$_id',
            totalQuantity: 1,
            partCount: { $size: '$partCount' },
            lastUpdated: 1,
            _id: 0
          }
        },
        { $sort: { stockType: 1 } }
      ]);

      return summary;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get warehouse inventory summary: ${errorDetails.message}`);
    }
  }
}

/**
 * Execute atomic stock transfer between two inventory locations
 * This is the core function for internal transfers and process moves
 */
export async function executeAtomicStockTransfer(
  fromParams: StockOperationParams,
  toParams: StockOperationParams
): Promise<{ from: IInventories; to: IInventories }> {
  const session = await mongoose.startSession();

  try {
    logOperation('executeAtomicStockTransfer', 'service', {
      from: {
        partId: fromParams.partId.toString(),
        locationId: fromParams.locationId.toString(),
        stockType: fromParams.stockType,
        quantity: fromParams.quantity
      },
      to: {
        partId: toParams.partId.toString(),
        locationId: toParams.locationId.toString(),
        stockType: toParams.stockType,
        quantity: toParams.quantity
      }
    });

    const result = await session.withTransaction(async () => {
      // Decrement from source
      const fromResult = await InventoriesService.decrementStock({
        ...fromParams,
        session
      });

      // Increment to destination
      const toResult = await InventoriesService.incrementStock({
        ...toParams,
        session
      });

      return { from: fromResult, to: toResult };
    });

    logOperation('executeAtomicStockTransfer completed', 'service', {
      fromQuantity: result.from.quantity,
      toQuantity: result.to.quantity
    });

    return result;
  } catch (error: any) {
    logOperation('executeAtomicStockTransfer failed', 'service', { error: error.message });
    throw error;
  } finally {
    await session.endSession();
  }
}

export default InventoriesService;

// Legacy function exports for backward compatibility
export const getInventoryById = async (id: string): Promise<IInventories | null> => {
  try {
    await connectToDatabase();
    logOperation('GET_BY_ID', 'Inventory', { id });

    if (!Types.ObjectId.isValid(id)) {
      throw new Error('Invalid inventory ID format');
    }

    const inventory = await Inventories.findById(id)
      .populate('partId', 'name partNumber businessName')
      .populate('locationId', 'name warehouseId')
      .lean();

    if (!inventory) {
      logOperation('NOT_FOUND', 'Inventory', { id });
      return null;
    }

    logOperation('RETRIEVED', 'Inventory', { id });
    return inventory;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const updateInventoryById = async (id: string, updateData: UpdateInventoryDto): Promise<IInventories | null> => {
  try {
    await connectToDatabase();
    logOperation('UPDATE_BY_ID', 'Inventory', { id, updateData });

    if (!Types.ObjectId.isValid(id)) {
      throw new Error('Invalid inventory ID format');
    }

    const updatedInventory = await Inventories.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    )
      .populate('partId', 'name partNumber businessName')
      .populate('locationId', 'name warehouseId')
      .lean();

    if (!updatedInventory) {
      logOperation('NOT_FOUND', 'Inventory', { id });
      return null;
    }

    logOperation('UPDATED', 'Inventory', { id });
    return updatedInventory;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const deleteInventoryById = async (id: string): Promise<boolean> => {
  try {
    await connectToDatabase();
    logOperation('DELETE_BY_ID', 'Inventory', { id });

    if (!Types.ObjectId.isValid(id)) {
      throw new Error('Invalid inventory ID format');
    }

    const deletedInventory = await Inventories.findByIdAndDelete(id);

    if (!deletedInventory) {
      logOperation('NOT_FOUND', 'Inventory', { id });
      return false;
    }

    logOperation('DELETED', 'Inventory', { id });
    return true;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const getInventoryByItem = async (itemType: string, itemId: string) => {
  try {
    await connectToDatabase();
    logOperation('GET_BY_ITEM', 'Inventory', { itemType, itemId });

    if (!Types.ObjectId.isValid(itemId)) {
      throw new Error('Invalid item ID format');
    }

    const query: any = {};
    if (itemType === 'part') {
      query.partId = itemId;
    } else {
      throw new Error(`Unsupported item type: ${itemType}`);
    }

    const inventories = await Inventories.find(query)
      .populate('partId', 'name partNumber businessName')
      .populate('locationId', 'name warehouseId')
      .lean();

    logOperation('RETRIEVED_BY_ITEM', 'Inventory', { itemType, itemId, count: inventories.length });
    return inventories;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const getLowStockInventory = async (options: InventoryQueryOptions = {}) => {
  try {
    await connectToDatabase();
    logOperation('GET_LOW_STOCK', 'Inventory', options);

    const {
      page = 1,
      limit = 20,
      sort = { quantity: 1 }
    } = options;

    // Build query for low stock (quantity <= safetyStockLevel)
    const query: any = {
      $expr: {
        $lte: ['$quantity', '$safetyStockLevel']
      }
    };

    // V4 Schema: Use locationId directly for filtering (warehouse filtering removed)

    const skip = (page - 1) * limit;

    const [inventories, totalCount] = await Promise.all([
      Inventories.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('partId', 'name partNumber businessName')
        .populate('locationId', 'name warehouseId')
        .lean(),
      Inventories.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logOperation('RETRIEVED_LOW_STOCK', 'Inventory', { count: inventories.length, totalCount });

    return {
      data: inventories,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const searchInventory = async (searchTerm: string, options: InventoryQueryOptions = {}) => {
  try {
    await connectToDatabase();
    logOperation('SEARCH_INVENTORY', 'Inventory', { searchTerm, options });

    const {
      page = 1,
      limit = 20,
      sort = { createdAt: -1 }
    } = options;

    // Build search query
    const searchQuery: any = {
      $or: [
        { 'partId.name': { $regex: searchTerm, $options: 'i' } },
        { 'partId.partNumber': { $regex: searchTerm, $options: 'i' } },
        { 'partId.businessName': { $regex: searchTerm, $options: 'i' } }
      ]
    };

    // V4 Schema: Use locationId directly for filtering (warehouse filtering removed)

    const skip = (page - 1) * limit;

    const [inventories, totalCount] = await Promise.all([
      Inventories.find(searchQuery)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('partId', 'name partNumber businessName')
        .populate('locationId', 'name warehouseId')
        .lean(),
      Inventories.countDocuments(searchQuery)
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logOperation('SEARCHED_INVENTORY', 'Inventory', { searchTerm, count: inventories.length, totalCount });

    return {
      data: inventories,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

/**
 * Get inventories with pagination and filtering
 */
export const getInventories = async (options: InventoryQueryOptions = {}) => {
  try {
    await connectToDatabase();
    logOperation('GET_INVENTORIES', 'Inventory', options);

    const {
      page = 1,
      limit = 20,
      sort = { createdAt: -1 },
      filter
    } = options;

    // Build query
    const query: any = {};

    // V4 Schema: Use locationId directly for filtering (warehouse filtering removed)

    // Apply additional filters
    if (filter) {
      Object.assign(query, filter);
    }

    const skip = (page - 1) * limit;

    const [inventories, totalCount] = await Promise.all([
      Inventories.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('partId', 'name partNumber businessName')
        .populate('locationId', 'name warehouseId')
        .lean(),
      Inventories.countDocuments(query)
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logOperation('RETRIEVED_INVENTORIES', 'Inventory', { count: inventories.length, totalCount });

    return {
      data: inventories,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const getAllInventory = async (options: InventoryQueryOptions = {}) => {
  return await getInventories(options);
};

// V4 Schema: Removed deprecated getInventoryByWarehouse function

export const getPartInventoryBreakdown = async (partId: string) => {
  return InventoriesService.getPartInventoryWithWarehouseBreakdown(partId);
};

export const adjustInventoryQuantity = async (
  id: string,
  adjustment: number,
  reason?: string,
  session?: ClientSession
) => {
  try {
    await connectToDatabase();
    logOperation('ADJUST_QUANTITY', 'Inventory', { id, adjustment, reason });

    if (!Types.ObjectId.isValid(id)) {
      throw new Error('Invalid inventory ID format');
    }

    // Use atomic findOneAndUpdate with $inc to prevent race conditions
    const filter = { _id: new Types.ObjectId(id) };
    const update = {
      $inc: { quantity: adjustment },
      $set: { lastUpdated: new Date() },
      ...(reason && {
        $set: {
          notes: reason
        }
      })
    };

    const options = {
      new: true,
      runValidators: true,
      ...(session && { session })
    };

    const updatedInventory = await Inventories.findOneAndUpdate(filter, update, options)
      .populate('partId', 'name partNumber businessName')
      .populate('locationId', 'name warehouseId')
      .lean();

    if (!updatedInventory) {
      throw new Error('Inventory record not found');
    }

    // Validate that quantity doesn't go negative after atomic update
    if (updatedInventory.quantity < 0) {
      throw new Error('Adjustment would result in negative inventory');
    }

    logOperation('ADJUSTED_QUANTITY', 'Inventory', {
      id,
      adjustment,
      newQuantity: updatedInventory.quantity
    });

    return updatedInventory;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const manageInventoryAllocation = async (
  id: string,
  allocatedQuantity: number,
  session?: ClientSession
) => {
  try {
    await connectToDatabase();
    logOperation('MANAGE_ALLOCATION', 'Inventory', { id, allocatedQuantity });

    if (!Types.ObjectId.isValid(id)) {
      throw new Error('Invalid inventory ID format');
    }

    if (allocatedQuantity < 0) {
      throw new Error('Allocated quantity cannot be negative');
    }

    // Use atomic update with validation to prevent race conditions
    const filter = {
      _id: new Types.ObjectId(id),
      quantity: { $gte: allocatedQuantity } // Ensure sufficient quantity available
    };

    const update = {
      $set: {
        allocatedQuantity,
        lastUpdated: new Date()
      }
    };

    const options = {
      new: true,
      runValidators: true,
      ...(session && { session })
    };

    const updatedInventory = await Inventories.findOneAndUpdate(filter, update, options)
      .populate('partId', 'name partNumber businessName')
      .populate('locationId', 'name warehouseId')
      .lean();

    if (!updatedInventory) {
      throw new Error('Inventory record not found or insufficient quantity available for allocation');
    }

    logOperation('MANAGED_ALLOCATION', 'Inventory', { id, allocatedQuantity });
    return updatedInventory;
  } catch (error) {
    const errorDetails = handleMongoDBError(error);
    throw new Error(errorDetails.message);
  }
};

export const updateStockLevel = async (params: any) => {
  return InventoriesService.upsertInventory(params);
};

export const updateInventoryQuantity = async (params: any) => {
  return InventoriesService.upsertInventory(params);
};

/**
 * ATOMIC TRANSACTION WRAPPER
 * Executes multiple inventory operations within a single MongoDB transaction
 * to ensure data consistency and prevent race conditions
 */
export const executeAtomicInventoryTransaction = async <T>(
  operations: (session: ClientSession) => Promise<T>
): Promise<T> => {
  const session = await mongoose.startSession();

  try {
    logOperation('executeAtomicInventoryTransaction', 'service', {
      sessionId: session.id
    });

    const result = await session.withTransaction(async () => {
      return await operations(session);
    });

    logOperation('executeAtomicInventoryTransaction completed', 'service', {
      sessionId: session.id
    });

    return result;
  } catch (error) {
    logOperation('executeAtomicInventoryTransaction failed', 'service', {
      sessionId: session.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    const errorDetails = handleMongoDBError(error);
    throw new Error(`Atomic inventory transaction failed: ${errorDetails.message}`);
  } finally {
    await session.endSession();
  }
};

// Interface for bulk stock update operations
export interface StockUpdateOperation {
  locationId: string;
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  newQuantity: number;
}

export interface BulkStockUpdateParams {
  partId: string;
  userId: string;
  reason: string;
  updates: StockUpdateOperation[];
}

/**
 * Execute bulk stock updates for a single part across multiple locations and stock types
 * Uses MongoDB transactions to ensure atomicity - either all updates succeed or all fail
 *
 * @param params - Bulk update parameters including partId, userId, reason, and array of updates
 * @returns Promise<{ updatedRecords: number, transactionIds: string[] }>
 */
export const executeBulkStockUpdate = async (params: BulkStockUpdateParams): Promise<{
  updatedRecords: number;
  transactionIds: string[];
}> => {
  const { partId, userId, reason, updates } = params;

  // Validate input parameters
  if (!partId || !userId || !reason || !updates || updates.length === 0) {
    throw new Error('Missing required parameters: partId, userId, reason, and updates array are required');
  }

  if (!Types.ObjectId.isValid(partId)) {
    throw new Error('Invalid partId format');
  }

  if (!Types.ObjectId.isValid(userId)) {
    throw new Error('Invalid userId format');
  }

  // Validate each update operation
  for (const update of updates) {
    if (!update.locationId || !update.stockType || update.newQuantity < 0) {
      throw new Error('Each update must have locationId, stockType, and non-negative newQuantity');
    }
    if (!Types.ObjectId.isValid(update.locationId)) {
      throw new Error(`Invalid locationId format: ${update.locationId}`);
    }
  }

  logOperation('executeBulkStockUpdate', 'service', {
    partId,
    userId,
    reason,
    updateCount: updates.length
  });

  return await executeAtomicInventoryTransaction(async (session) => {
    const updatedRecords: any[] = [];
    const transactionIds: string[] = [];

    // Process each update within the transaction
    for (const update of updates) {
      const { locationId, stockType, newQuantity } = update;

      // Get current inventory record to calculate the change
      const currentInventory = await Inventories.findOne({
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      }).session(session);

      const previousQuantity = currentInventory?.quantity || 0;
      const quantityChange = newQuantity - previousQuantity;

      // Skip if no change needed
      if (quantityChange === 0) {
        continue;
      }

      // Update or create inventory record
      const filter = {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      };

      const updateDoc = {
        $set: {
          quantity: newQuantity,
          lastUpdated: new Date()
        },
        $setOnInsert: {
          partId: new Types.ObjectId(partId),
          locationId: new Types.ObjectId(locationId),
          stockType,
          createdAt: new Date()
        }
      };

      const options = {
        upsert: true,
        new: true,
        session
      };

      const updatedInventory = await Inventories.findOneAndUpdate(filter, updateDoc, options);

      if (!updatedInventory) {
        throw new Error(`Failed to update inventory for location ${locationId}, stock type ${stockType}`);
      }

      updatedRecords.push(updatedInventory);

      // Create transaction record for audit trail
      const transactionData = {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        userId: new Types.ObjectId(userId),
        transactionType: quantityChange > 0 ? 'IN' : quantityChange < 0 ? 'OUT' : 'ADJUSTMENT',
        stockType,
        quantity: Math.abs(quantityChange),
        previousQuantity,
        newQuantity,
        reason,
        referenceNumber: `BULK-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        transactionDate: new Date()
      };

      // Import and use the transaction model
      const { default: InventoryTransaction } = await import('@/app/models/inventorytransaction.model');
      const transaction = new InventoryTransaction(transactionData);
      const savedTransaction = await transaction.save({ session });

      transactionIds.push(savedTransaction._id.toString());

      logOperation('bulkStockUpdate - record updated', 'service', {
        inventoryId: updatedInventory._id,
        locationId,
        stockType,
        previousQuantity,
        newQuantity,
        quantityChange,
        transactionId: savedTransaction._id
      });
    }

    logOperation('executeBulkStockUpdate completed', 'service', {
      partId,
      updatedRecords: updatedRecords.length,
      transactionIds: transactionIds.length
    });

    return {
      updatedRecords: updatedRecords.length,
      transactionIds
    };
  });
};
