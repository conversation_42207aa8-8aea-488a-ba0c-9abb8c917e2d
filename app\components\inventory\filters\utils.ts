import { Product } from '@/app/types';
import { FilterState } from './types';

/**
 * Apply filters to a list of products
 * This is a memoizable function that should be used with useMemo
 */
export const applyFilters = (products: Product[], filters: FilterState): Product[] => {
  return products.filter((product) => {
    // Stock Quantity Filter
    if (filters.stockQuantity.enabled) {
      const stock = product.inventory?.currentStock ?? product.currentStock ?? 0;
      if (stock < filters.stockQuantity.min || stock > filters.stockQuantity.max) {
        return false;
      }
    }

    // Reorder Level Filter
    if (filters.reorderLevel.enabled) {
      const reorderLevel = product.reorderLevel ?? 0;
      const currentStock = product.inventory?.currentStock ?? product.currentStock ?? 0;
      
      if (filters.reorderLevel.belowOnly) {
        // "Below only" mode: check if stock is below reorder level
        if (currentStock >= reorderLevel) {
          return false;
        }
      } else {
        // Exact match mode: check if reorder level matches the filter value
        if (reorderLevel !== filters.reorderLevel.value) {
          return false;
        }
      }
    }

    // Supplier Filter
    if (filters.supplier.enabled && filters.supplier.values.length > 0) {
      const supplier = product.supplierManufacturer ?? '';
      if (!filters.supplier.values.includes(supplier)) {
        return false;
      }
    }

    // Category Filter
    if (filters.category.enabled && filters.category.values.length > 0) {
      const categoryId = product.categoryId ?? ''; // Already using categoryId
      if (!filters.category.values.includes(categoryId)) {
        return false;
      }
    }

    // Location Filter
    if (filters.location.enabled && filters.location.values.length > 0) {
      const location = product.inventory?.warehouseId ?? '';
      if (!filters.location.values.includes(location)) {
        return false;
      }
    }

    // NEW: Planning Method Filter
    if (filters.planningMethod.enabled && filters.planningMethod.values.length > 0) {
      const planningMethod = product.planningMethod ?? '';
      if (!filters.planningMethod.values.includes(planningMethod)) {
        return false;
      }
    }

    // NEW: Safety Stock Level Filter
    if (filters.safetyStockLevel.enabled) {
      const safetyStock = product.safetyStockLevel ?? 0;
      if (safetyStock < filters.safetyStockLevel.min || safetyStock > filters.safetyStockLevel.max) {
        return false;
      }
    }

    // NEW: Maximum Stock Level Filter
    if (filters.maximumStockLevel.enabled) {
      const maxStock = product.maximumStockLevel ?? 0;
      if (maxStock < filters.maximumStockLevel.min || maxStock > filters.maximumStockLevel.max) {
        return false;
      }
    }

    // NEW: Lead Time Days Filter
    if (filters.leadTimeDays.enabled) {
      const leadTime = product.leadTimeDays ?? 0;
      if (leadTime < filters.leadTimeDays.min || leadTime > filters.leadTimeDays.max) {
        return false;
      }
    }

    // NEW: Average Daily Usage Filter
    if (filters.averageDailyUsage.enabled) {
      const usage = product.averageDailyUsage ?? 0;
      if (usage < filters.averageDailyUsage.min || usage > filters.averageDailyUsage.max) {
        return false;
      }
    }

    // All filters passed
    return true;
  });
};

/**
 * Extract unique values from a list of products for a specific field
 */
export const extractUniqueValues = <T>(
  products: Product[],
  field: keyof Product
): string[] => {
  const values = products
    .map((product) => product[field])
    .filter((value): value is string => Boolean(value));
  
  return Array.from(new Set(values)).sort();
};
