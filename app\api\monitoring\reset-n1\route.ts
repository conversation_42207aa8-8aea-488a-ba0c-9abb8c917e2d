/**
 * N+1 Detection Reset API Endpoint
 * 
 * Provides ability to reset N+1 detection statistics for testing
 * and monitoring purposes.
 */

import { NextRequest, NextResponse } from 'next/server';
import { resetN1Detection, getN1DetectionStats } from '@/app/lib/n1-detection';

export async function POST(request: NextRequest) {
  try {
    // Get stats before reset
    const statsBefore = getN1DetectionStats();
    
    // Reset the N+1 detection system
    resetN1Detection();
    
    // Get stats after reset
    const statsAfter = getN1DetectionStats();
    
    console.log('[N+1 Reset] Detection system reset successfully', {
      before: statsBefore,
      after: statsAfter,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: 'N+1 detection system reset successfully',
      data: {
        before: statsBefore,
        after: statsAfter
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('[N+1 Reset API] Error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to reset N+1 detection system',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to reset N+1 detection.' },
    { status: 405 }
  );
}
