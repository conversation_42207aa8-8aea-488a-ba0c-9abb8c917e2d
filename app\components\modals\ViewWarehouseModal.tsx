'use client';

import { Building2 } from 'lucide-react';

import { BaseViewModal } from './BaseViewModal';
import { WarehouseViewContent } from './content/WarehouseViewContent';
import { WarehouseDisplayData } from '@/app/components/forms/WarehouseForm';

interface ViewWarehouseModalProps {
  warehouse: WarehouseDisplayData;
  isOpen: boolean;
  onClose: () => void;
  trigger?: React.ReactNode;
}

/**
 * Modal component for viewing warehouse details
 * Uses the standardized BaseViewModal pattern
 */
export function ViewWarehouseModal({ warehouse, isOpen, onClose, trigger }: ViewWarehouseModalProps) {
  // Determine if content will be in tab mode (warehouses don't have BOM data)
  const isInTab = false;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={warehouse.name}
      subtitle="Warehouse Details"
      icon={<Building2 className="h-5 w-5 text-primary" />}
    >
      <WarehouseViewContent warehouse={warehouse} isInTab={isInTab} />
    </BaseViewModal>
  );
}
