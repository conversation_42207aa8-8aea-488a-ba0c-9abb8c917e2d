import { NextRequest, NextResponse } from 'next/server';
import { InventoriesService, getPartInventoryBreakdown } from '@/app/services/inventory.service';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { Types } from 'mongoose';

const ROUTE_PATH = '/api/inventories/part/[partId]';

/**
 * GET /api/inventories/part/[partId]
 *
 * Retrieves detailed inventory breakdown for a specific part across all warehouses and locations.
 * This endpoint provides the multi-location view needed for the part details modal.
 *
 * NEW FEATURES (v2):
 * - Location-level breakdown within each warehouse
 * - Stock type breakdown within each location
 * - Hierarchical structure: Part -> Warehouse -> Location -> Stock Type
 *
 * BACKWARD COMPATIBILITY:
 * - Still supports legacy warehouse-level aggregation
 * - Returns both old and new format for smooth transition
 */
async function handleGET(
  request: NextRequest,
  context: { params: Promise<{ partId: string }> }
) {
  const startTime = Date.now();
  const { partId } = await context.params;
  const { searchParams } = new URL(request.url);

  logApiRequest('GET', ROUTE_PATH, { partId });

  try {
    // Validate partId format
    if (!Types.ObjectId.isValid(partId)) {
      return errorResponse("API_ERROR", 'Invalid part ID format', [{ duration: Date.now() - startTime  }], 400);
    }

    // Check if client wants the new location-based format
    const useV2Format = searchParams.get('format') === 'v2' || searchParams.get('locations') === 'true';

    if (useV2Format) {
      // Use new location-based service
      const inventoryBreakdown = await getPartInventoryBreakdown(partId);
      const duration = Date.now() - startTime;

      return successResponse(
        {
          ...inventoryBreakdown,
          format: 'v2',
          description: 'Location-based inventory breakdown'
        },
        'Part inventory breakdown (v2) fetched successfully',
        {
          duration,
          partId,
          totalWarehouses: inventoryBreakdown.warehouseCount,
          totalStock: inventoryBreakdown.grandTotal
        }
      );
    } else {
      // Use legacy warehouse-based service for backward compatibility
      const inventoryBreakdown = await InventoriesService.getPartInventoryWithWarehouseBreakdown(partId);
      const duration = Date.now() - startTime;

      return successResponse(
        {
          ...inventoryBreakdown,
          format: 'v1',
          description: 'Legacy warehouse-based inventory breakdown'
        },
        'Part inventory breakdown (legacy) fetched successfully',
        { duration, partId }
      );
    }

  } catch (error: any) {
    const duration = Date.now() - startTime;

    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to fetch part inventory breakdown', [{ duration }], 500);
  }
}

// Apply error handling middleware
export const GET = withErrorHandling(
  (request: NextRequest, context: { params: Promise<{ partId: string }> }) => handleGET(request, context),
  ROUTE_PATH
);
