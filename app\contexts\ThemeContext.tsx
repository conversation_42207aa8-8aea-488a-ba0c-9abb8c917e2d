"use client";

/**
 * Enhanced Theme Context Module
 * Provides theme management functionality with support for multiple theme variants
 * Maintains backward compatibility with existing light/dark theme system
 */
import {
    DEFAULT_THEME_MODE,
    DEFAULT_THEME_VARIANT,
    getAvailableThemes
} from '@/app/config/themes.config';
import {
    CurrentTheme,
    ThemeContextValue,
    ThemeMode,
    ThemeVariant
} from '@/app/types/theme.types';
import {
    applyThemeToDocument,
    createCurrentTheme,
    initializeTheme,
    storeThemeMode,
    storeThemeVariant,
    toggleThemeMode
} from '@/app/utils/theme.utils';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';

/** Legacy theme type for backward compatibility */
type LegacyTheme = 'light' | 'dark';

/**
 * Default theme context used before provider is mounted
 */
const defaultThemeContext: ThemeContextValue = {
  currentTheme: {
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: {
      id: 'default-light',
      name: 'Default Light',
      variant: DEFAULT_THEME_VARIANT,
      mode: DEFAULT_THEME_MODE,
      description: 'Default theme',
      preview: { primary: '#3b82f6', secondary: '#f1f5f9', accent: '#f1f5f9', background: '#ffffff' },
      className: 'theme-default-light'
    }
  },
  availableThemes: [],
  setMode: () => console.warn('ThemeProvider not mounted'),
  setVariant: () => console.warn('ThemeProvider not mounted'),
  setTheme: () => console.warn('ThemeProvider not mounted'),
  toggleTheme: () => console.warn('ThemeProvider not mounted'),
  theme: {
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: {
      id: 'default-light',
      name: 'Default Light',
      variant: DEFAULT_THEME_VARIANT,
      mode: DEFAULT_THEME_MODE,
      description: 'Default theme',
      preview: { primary: '#3b82f6', secondary: '#f1f5f9', accent: '#f1f5f9', background: '#ffffff' },
      className: 'theme-default-light'
    }
  }, // Now returns CurrentTheme object for component compatibility
  mode: DEFAULT_THEME_MODE, // Legacy theme mode string
};

/** Theme context for providing theme information throughout the app */
const ThemeContext = createContext<ThemeContextValue>(defaultThemeContext);

/**
 * Enhanced Theme Provider component that manages theme variants and modes
 * Persists theme choice in localStorage and syncs with system preferences
 * Maintains backward compatibility with existing theme system
 * @param children - Child components that will have access to the theme context
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize with default theme for SSR
  const [currentTheme, setCurrentTheme] = useState<CurrentTheme>(() => ({
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: defaultThemeContext.currentTheme.config!
  }));
  const [mounted, setMounted] = useState(false);
  const [availableThemes] = useState(() => getAvailableThemes());

  // Initialize theme on mount
  useEffect(() => {
    const initialTheme = initializeTheme();
    setCurrentTheme(initialTheme);
    setMounted(true);
  }, []);

  // Apply theme to document when theme changes
  useEffect(() => {
    if (mounted) {
      applyThemeToDocument(currentTheme);
    }
  }, [currentTheme, mounted]);

  /**
   * Set theme mode (light/dark/system)
   */
  const setMode = useCallback((mode: ThemeMode) => {
    const newTheme = createCurrentTheme(currentTheme.variant, mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeMode(mode);
    }
  }, [currentTheme.variant]);

  /**
   * Set theme variant (default/blue/green/etc.)
   */
  const setVariant = useCallback((variant: ThemeVariant) => {
    const newTheme = createCurrentTheme(variant, currentTheme.mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeVariant(variant);
    }
  }, [currentTheme.mode]);

  /**
   * Set both mode and variant at once
   */
  const setTheme = useCallback((variant: ThemeVariant, mode: ThemeMode) => {
    const newTheme = createCurrentTheme(variant, mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeMode(mode);
      storeThemeVariant(variant);
    }
  }, []);

  /**
   * Toggle between light and dark modes (legacy compatibility)
   */
  const toggleTheme = useCallback(() => {
    const newTheme = toggleThemeMode(currentTheme);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeMode(newTheme.mode);
    }
  }, [currentTheme]);

  const contextValue: ThemeContextValue = {
    currentTheme,
    availableThemes,
    setMode,
    setVariant,
    setTheme,
    toggleTheme,
    // Legacy compatibility
    theme: currentTheme,
    mode: currentTheme.mode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to access theme context
 * Provides both new enhanced theme API and legacy compatibility
 * @returns Theme context value with current theme and control functions
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    console.warn('useTheme must be used within a ThemeProvider. Using default theme.');
    return defaultThemeContext;
  }
  
  return context;
};

/**
 * Legacy hook for backward compatibility
 * @deprecated Use useTheme instead for enhanced functionality
 */
export const useThemeMode = (): {
  theme: LegacyTheme;
  toggleTheme: () => void;
} => {
  const { currentTheme, toggleTheme } = useTheme();
  
  return {
    theme: currentTheme.resolvedMode as LegacyTheme,
    toggleTheme,
  };
};

export default ThemeProvider;
