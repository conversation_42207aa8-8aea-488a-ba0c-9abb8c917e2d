'use client';

import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { Copy } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

// Import types from DeleteItemAction for consistency
import type { SupportedItem, ItemType } from './DeleteItemAction';

export interface DuplicateItemActionProps {
  item: SupportedItem;
  itemType: ItemType;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
  customDuplicateFunction?: (itemId: string) => Promise<any>;
  onDuplicateSuccess?: (newItem: any) => void;
}

/**
 * Generic duplicate action component that works for assemblies, products, and parts
 */
export function DuplicateItemAction({
  item,
  itemType,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
  customDuplicateFunction,
  onDuplicateSuccess,
}: DuplicateItemActionProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);

  // Get the appropriate API endpoint based on item type
  const getApiEndpoint = (type: ItemType, itemId: string): string => {
    const encodedId = encodeURIComponent(itemId);
    switch (type) {
      case 'assembly':
        return `/api/assemblies/${encodedId}/duplicate`;
      case 'product':
        return `/api/products/${encodedId}/duplicate`;
      case 'part':
        return `/api/parts/${encodedId}/duplicate`;
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  };

  // Get display name for the item type
  const getItemTypeName = (type: ItemType): string => {
    switch (type) {
      case 'assembly':
        return 'Assembly';
      case 'product':
        return 'Product';
      case 'part':
        return 'Part';
      default:
        return 'Item';
    }
  };

  // Handle duplicate confirmation
  const handleDuplicate = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!item._id) {
      toast.error(`${getItemTypeName(itemType)} ID is missing. Cannot duplicate.`);
      setIsDialogOpen(false);
      return;
    }

    setIsDuplicating(true);
    try {
      let newItem;
      
      if (customDuplicateFunction) {
        // Use custom duplicate function if provided
        newItem = await customDuplicateFunction(item._id);
      } else {
        // Use default API call
        const endpoint = getApiEndpoint(itemType, item._id);
        const response = await fetch(endpoint, {
          method: 'POST',
        });

        if (!response.ok) {
          let errorMessage = `Failed to duplicate ${getItemTypeName(itemType).toLowerCase()}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch (e) {
            console.error('[DuplicateItemAction] Error parsing error response:', e);
            errorMessage = `Server error ${response.status}: ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        newItem = await response.json();
      }

      if (newItem) {
        toast.success(`${getItemTypeName(itemType)} duplicated successfully!`);
        setIsDialogOpen(false);
        
        if (onSuccess) onSuccess();
        if (onDuplicateSuccess) onDuplicateSuccess(newItem);
        
        // Refresh the page
        router.refresh();
      }
    } catch (error) {
      console.error(`[DuplicateItemAction] Error duplicating ${getItemTypeName(itemType)}:`, error);
      toast.error(error instanceof Error ? error.message : `Failed to duplicate ${getItemTypeName(itemType).toLowerCase()}`);
    } finally {
      setIsDuplicating(false);
    }
  };

  // Render button based on variant
  const renderButton = () => {
    const buttonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        setIsDialogOpen(true);
      },
      disabled: isDuplicating,
      id: id || `duplicate-${itemType}-${item._id}`,
    };

    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <Copy size={16} className="mr-2" />
            Duplicate {getItemTypeName(itemType)}
          </Button>
        );

      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <Copy size={16} className="mr-2" />
            Duplicate
          </Button>
        );

      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn("h-8 w-8 p-0", className)}
                  {...buttonProps}
                >
                  <span className="sr-only">Duplicate {getItemTypeName(itemType)}</span>
                  <Copy size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Duplicate {getItemTypeName(itemType)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  const itemTypeName = getItemTypeName(itemType);

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDuplicate}
        title={`Duplicate ${itemTypeName}`}
        description={`Are you sure you want to duplicate the ${itemTypeName.toLowerCase()} "${item.name}"? A new copy will be created.`}
        confirmLabel={`Duplicate ${itemTypeName}`}
        cancelLabel="Cancel"
        variant="info"
        isLoading={isDuplicating}
      />
    </>
  );
}
