'use client';

import { useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { X, Layers, Package } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/app/components/forms/Button';
import { useTheme } from '@/app/contexts/ThemeContext';
import { cn } from '@/app/lib/utils';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/app/components/navigation/tabs';
import { BomViewerTab } from '@/app/components/bom/BomViewerTab';
import { hasBomData, type ModalComponentItem } from '@/app/components/bom/EnhancedBomViewer';

/**
 * Tab configuration for BaseViewModal
 */
export interface BaseViewModalTab {
  id: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface BaseViewModalProps {
  /**
   * Whether the modal is open
   */
  isOpen: boolean;

  /**
   * Function to call when the modal should be closed
   */
  onClose: () => void;

  /**
   * The trigger element (button) that opens the modal
   */
  trigger?: React.ReactNode;

  /**
   * The title to display in the modal header
   */
  title: string;

  /**
   * The subtitle to display under the title
   */
  subtitle?: string;

  /**
   * The icon to display in the modal header
   */
  icon: React.ReactNode;

  /**
   * The content to display in the modal body (used when tabs are not provided)
   */
  children?: React.ReactNode;

  /**
   * Tab configuration for tabbed content
   */
  tabs?: BaseViewModalTab[];

  /**
   * Default active tab ID (only used when tabs are provided)
   */
  defaultTab?: string;

  /**
   * Additional CSS classes for the modal container
   */
  className?: string;

  /**
   * Maximum width of the modal (default: max-w-4xl)
   */
  maxWidth?: string;

  /**
   * BOM (Bill of Materials) data for automatic BOM tab integration
   */
  bomData?: ModalComponentItem[];

  /**
   * Callback for lazy loading assembly components in BOM viewer
   */
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;

  /**
   * Whether to show BOM summary statistics
   */
  showBomSummary?: boolean;

  /**
   * Custom height for BOM tree viewer
   */
  bomViewerHeight?: string;

  /**
   * Whether BOM search is enabled
   */
  enableBomSearch?: boolean;
}

/**
 * Base modal component for viewing entity details
 * Provides common functionality like portal rendering, keyboard handling, and modal structure
 *
 * Supports two modes:
 * 1. Simple content mode: Content is provided via children prop (backward compatible)
 * 2. Tabbed content mode: Content is organized in tabs via tabs prop
 *
 * The component automatically detects which mode to use based on the presence of tabs prop
 */
export function BaseViewModal({
  isOpen,
  onClose,
  trigger,
  title,
  subtitle,
  icon,
  children,
  tabs,
  defaultTab,
  className,
  maxWidth = 'max-w-4xl',
  bomData,
  onLoadAssemblyComponents,
  showBomSummary = true,
  bomViewerHeight = '500px',
  enableBomSearch = true
}: BaseViewModalProps) {
  const { theme } = useTheme();

  // Automatically create tabs structure when BOM data is available
  const enhancedTabs = useMemo(() => {
    const baseTabs = tabs || [];

    // Check if we have BOM data and should create unified tabbed interface
    if (hasBomData(bomData)) {
      const bomTab: BaseViewModalTab = {
        id: 'bom',
        label: 'Bill of Materials',
        icon: <Layers className="h-4 w-4" />,
        content: (
          <BomViewerTab
            components={bomData!}
            {...(onLoadAssemblyComponents && { onLoadAssemblyComponents })}
            showSummary={showBomSummary}
            height={bomViewerHeight}
            enableSearch={enableBomSearch}
          />
        )
      };

      // If no explicit tabs provided, create a unified tabbed interface
      if (baseTabs.length === 0 && children) {
        const detailsTab: BaseViewModalTab = {
          id: 'details',
          label: 'Details',
          icon: <Package className="h-4 w-4" />,
          content: children
        };

        // Return Details tab first, then BOM tab
        return [detailsTab, bomTab];
      }

      // If explicit tabs provided, add BOM tab at the end
      return [...baseTabs, bomTab];
    }

    return baseTabs;
  }, [tabs, bomData, children, onLoadAssemblyComponents, showBomSummary, bomViewerHeight, enableBomSearch]);

  // Determine if we're using tabs or simple content
  const useTabs = enhancedTabs && enhancedTabs.length > 0;
  const activeDefaultTab = defaultTab || (useTabs ? enhancedTabs[0]?.id : '') || '';

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  return (
    <>
      {trigger}

      {isOpen && createPortal(
        <AnimatePresence>
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
              onClick={onClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className={cn(
                "bg-background border border-border rounded-xl shadow-2xl w-full max-h-[90vh] overflow-hidden",
                maxWidth,
                className
              )}>
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "p-2 rounded-lg",
                      theme.isDark ? "bg-primary/10" : "bg-primary/5"
                    )}>
                      {icon}
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-foreground">
                        {title}
                      </h2>
                      {subtitle && (
                        <p className="text-sm text-muted-foreground">
                          {subtitle}
                        </p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
                  {useTabs ? (
                    <Tabs defaultValue={activeDefaultTab} className="w-full">
                      <div className="px-6 pt-4 pb-2 border-b border-border">
                        <TabsList className={cn(
                          "grid w-full",
                          enhancedTabs.length <= 2 ? "grid-cols-2" :
                          enhancedTabs.length <= 3 ? "grid-cols-3" :
                          enhancedTabs.length <= 4 ? "grid-cols-4" :
                          "grid-cols-5"
                        )}>
                          {enhancedTabs.map((tab) => (
                            <TabsTrigger
                              key={tab.id}
                              value={tab.id}
                              className="flex items-center gap-2"
                            >
                              {tab.icon && (
                                <span className="w-4 h-4">{tab.icon}</span>
                              )}
                              {tab.label}
                            </TabsTrigger>
                          ))}
                        </TabsList>
                      </div>
                      <div className="p-6">
                        {enhancedTabs.map((tab) => (
                          <TabsContent key={tab.id} value={tab.id} className="mt-0">
                            {tab.content}
                          </TabsContent>
                        ))}
                      </div>
                    </Tabs>
                  ) : (
                    <div className="p-6">
                      {children}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </>
        </AnimatePresence>,
        document.body
      )}
    </>
  );
}
