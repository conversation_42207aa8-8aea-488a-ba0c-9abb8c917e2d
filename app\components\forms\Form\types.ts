import * as React from "react";
import { FieldPath, FieldValues } from "react-hook-form";

/**
 * Form Field Context Value type
 */
export type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
  name: TName;
};

/**
 * Form Item Context Value type
 */
export type FormItemContextValue = {
  id: string;
};

/**
 * Context for form fields
 */
export const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue
);

/**
 * Context for form items
 */
export const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue
); 