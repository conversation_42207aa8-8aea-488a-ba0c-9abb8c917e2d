import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Delivery document based on updated schema
export interface IDelivery extends Document {
  _id: Types.ObjectId;
  deliveryId: string; // Unique delivery identifier
  referenceType: string; // Type of reference (e.g., 'PurchaseOrder', 'Transfer')
  referenceId: Types.ObjectId; // Reference to related document (e.g., PurchaseOrder _id)
  supplierId?: Types.ObjectId; // Optional reference to Supplier model
  status: string; // Delivery status (e.g., 'scheduled', 'in_transit', 'delivered', 'delayed')
  scheduledDate: Date;
  actualDate?: Date; // Optional, set upon actual delivery
  trackingNumber?: string; // Optional
  notes?: string; // Optional
  receivedBy?: Types.ObjectId; // Optional reference to User model
  createdAt: Date;
  updatedAt: Date;
}

// Schema for Delivery model based on updated schema
const DeliverySchema: Schema = new Schema(
  {
    deliveryId: { type: String, required: true, unique: true, index: true },
    referenceType: { type: String, required: true },
    referenceId: { type: Schema.Types.ObjectId, required: true, index: true }, // Index for lookups
    supplierId: { type: Schema.Types.ObjectId, ref: 'Supplier', index: true }, // Optional ref
    status: { type: String, required: true, index: true }, // Index for status queries
    scheduledDate: { type: Date, required: true },
    actualDate: { type: Date }, // Optional
    trackingNumber: { type: String }, // Optional
    notes: { type: String }, // Optional
    receivedBy: { type: Schema.Types.ObjectId, ref: 'User' }, // Optional ref
  },
  { timestamps: true } // Automatically add createdAt and updatedAt fields
);

// Create and export Delivery model with proper TypeScript typing
const Delivery = mongoose.models?.Delivery as mongoose.Model<IDelivery> || mongoose.model<IDelivery>('Delivery', DeliverySchema);

// Export as both named export and default export for compatibility
export { Delivery };
export default Delivery;
