import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface StockStalenessData {
  hasStaleData: boolean;
  staleParts: string[];
  recommendations: string[];
}

interface RefreshRecommendation {
  shouldRefresh: boolean;
  reasons: string[];
  priority: 'low' | 'medium' | 'high';
}

interface UseStockStalenessProps {
  partsRequired?: any[] | undefined;
  checkStockStaleness: () => StockStalenessData;
  getStockRefreshRecommendation: () => RefreshRecommendation;
  refreshStockData: () => Promise<void>;
  autoShowWarnings?: boolean;
}

interface UseStockStalenessReturn {
  stockStaleness: StockStalenessData;
  refreshRecommendation: RefreshRecommendation;
  handleStockRefresh: () => Promise<void>;
  isRefreshing: boolean;
}

/**
 * Custom hook for managing stock staleness detection and refresh logic
 * Extracts complex stock staleness logic from components for better separation of concerns
 */
export function useStockStaleness({
  partsRequired,
  checkStockStaleness,
  getStockRefreshRecommendation,
  refreshStockData,
  autoShowWarnings = true
}: UseStockStalenessProps): UseStockStalenessReturn {
  const [stockStaleness, setStockStaleness] = useState<StockStalenessData>({
    hasStaleData: false,
    staleParts: [],
    recommendations: []
  });

  const [refreshRecommendation, setRefreshRecommendation] = useState<RefreshRecommendation>({
    shouldRefresh: false,
    reasons: [],
    priority: 'low'
  });

  const [isRefreshing, setIsRefreshing] = useState(false);

  // Monitor stock staleness when partsRequired changes
  useEffect(() => {
    if (partsRequired && partsRequired.length > 0) {
      const staleness = checkStockStaleness();
      const recommendation = getStockRefreshRecommendation();

      setStockStaleness(staleness);
      setRefreshRecommendation(recommendation);

      // Show automatic warnings for high priority issues
      if (autoShowWarnings && recommendation.shouldRefresh && recommendation.priority === 'high') {
        toast.warning('Stock data may be outdated. Consider refreshing before making changes.', {
          action: {
            label: 'Refresh Now',
            onClick: () => handleStockRefresh()
          }
        });
      }
    }
  }, [partsRequired, checkStockStaleness, getStockRefreshRecommendation, autoShowWarnings]);

  // Enhanced stock refresh handler
  const handleStockRefresh = useCallback(async () => {
    if (isRefreshing) return; // Prevent concurrent refreshes

    setIsRefreshing(true);
    try {
      await refreshStockData();
      toast.success('Stock data refreshed successfully');

      // Update staleness indicators
      const newStaleness = checkStockStaleness();
      const newRecommendation = getStockRefreshRecommendation();
      setStockStaleness(newStaleness);
      setRefreshRecommendation(newRecommendation);
    } catch (error) {
      console.error('Error refreshing stock data:', error);
      toast.error('Failed to refresh stock data');
      throw error;
    } finally {
      setIsRefreshing(false);
    }
  }, [refreshStockData, checkStockStaleness, getStockRefreshRecommendation, isRefreshing]);

  return {
    stockStaleness,
    refreshRecommendation,
    handleStockRefresh,
    isRefreshing
  };
}

/**
 * Hook for checking if stock refresh is recommended before saving
 * Returns a promise that resolves to whether the operation should proceed
 */
export function useStockRefreshBeforeSave(
  refreshRecommendation: RefreshRecommendation,
  handleStockRefresh: () => Promise<void>
) {
  return useCallback(async (): Promise<boolean> => {
    // Check for stock staleness before saving
    if (refreshRecommendation.shouldRefresh && refreshRecommendation.priority === 'medium') {
      const shouldProceed = confirm(
        `Stock data may be outdated for some parts:\n${refreshRecommendation.reasons.join('\n')}\n\nDo you want to refresh stock data before saving?`
      );

      if (shouldProceed) {
        await handleStockRefresh();
      }
    }

    return true; // Always proceed after handling refresh
  }, [refreshRecommendation, handleStockRefresh]);
}

/**
 * Utility function to detect stale stock data
 * This would typically be imported from a service, but included here for completeness
 */
export function detectStaleStockData(partsRequired: any[]): StockStalenessData {
  const staleParts: string[] = [];
  const recommendations: string[] = [];
  
  const STALE_THRESHOLD_MINUTES = 30; // Consider data stale after 30 minutes
  const now = new Date();

  partsRequired.forEach((part) => {
    if (part.partId && part.lastStockUpdate) {
      const lastUpdate = new Date(part.lastStockUpdate);
      const minutesSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
      
      if (minutesSinceUpdate > STALE_THRESHOLD_MINUTES) {
        const partName = part.partData?.name || part.partId;
        staleParts.push(partName);
        recommendations.push(`${partName}: Last updated ${Math.round(minutesSinceUpdate)} minutes ago`);
      }
    }
  });

  return {
    hasStaleData: staleParts.length > 0,
    staleParts,
    recommendations
  };
}
