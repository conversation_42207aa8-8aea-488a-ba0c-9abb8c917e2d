/**
 * Type definitions for BatchesTable component
 */

/**
 * Interface for Part data
 */
export interface Part {
  _id: string;
  name: string;
  description?: string | null;
}

/**
 * Interface for Assembly data
 */
export interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
}

/**
 * Interface for WorkOrder data
 */
export interface WorkOrder {
  _id: string;
  woNumber: string;
  status: string;
}

/**
 * Interface for Batch data in the table
 */
export interface Batch {
  _id: string;
  batchCode: string;
  partId?: string | Part;
  assemblyId?: string | Assembly;
  quantityPlanned: number;
  quantityProduced?: number;
  startDate: string | Date;
  endDate?: string | Date;
  status: string;
  notes?: string;
  workOrderId: string | WorkOrder;
  createdAt: string | Date;
  updatedAt: string | Date;
}

/**
 * Props for the BatchesTable component
 */
export interface BatchesTableProps {
  /**
   * Whether to use a simplified version of the table
   */
  simple?: boolean;
  
  /**
   * Optional className for styling
   */
  className?: string;
  
  /**
   * Optional initial data for the table
   */
  initialData?: Batch[];
  
  /**
   * Whether to fetch data from the API
   */
  fetchData?: boolean;

  /**
   * The work order number to fetch batches for
   */
  woNumber?: string;
  
  /**
   * Optional callback for when a batch is clicked
   */
  onBatchClick?: (batch: Batch) => void;
  
  /**
   * Optional callback for when a batch is edited
   */
  onBatchEdit?: (batch: Batch) => void;
  
  /**
   * Optional callback for when a batch is deleted
   */
  onBatchDelete?: (batch: Batch) => void;
  
  /**
   * Optional callback for when a batch is created
   */
  onBatchCreate?: () => void;
}
