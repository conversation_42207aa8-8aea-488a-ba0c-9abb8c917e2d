import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for SystemLog document - Aligned with checklist
export interface ISystemLog extends Document {
  _id: Types.ObjectId;
  timestamp: Date;
  eventType: string; // Added as per checklist
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG' | 'FATAL'; // Updated enum to uppercase
  message: string;
  source: string; // Renamed from serviceContext, made required
  userId?: Types.ObjectId | null;
  correlationId?: string | null; // Added as per checklist
  // ipAddress and errorCode removed as they are not in checklist for SystemLog, can be in details
  details?: Schema.Types.Mixed | null;
  // tags removed as not in checklist for SystemLog
}

// Define schema for SystemLog model - Aligned with checklist
const SystemLogSchema: Schema = new Schema(
  {
    timestamp: {
      type: Date,
      required: [true, 'Timestamp is required'],
      default: Date.now,
      index: true
    },
    eventType: { // Added
      type: String,
      required: [true, 'Event type is required'],
      trim: true,
      index: true
    },
    level: {
      type: String,
      required: [true, 'Log level is required'],
      enum: ['INFO', 'WARN', 'ERROR', 'DEBUG', 'FATAL'], // Updated enum to uppercase
      index: true
    },
    message: {
      type: String,
      required: [true, 'Log message is required'],
      trim: true
    },
    source: { // Renamed from serviceContext and made required
      type: String,
      required: [true, 'Source is required'],
      trim: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
      index: true
    },
    correlationId: { // Added
        type: String,
        default: null,
        trim: true,
        index: true
    },
    // ipAddress and errorCode fields removed
    details: {
      type: Schema.Types.Mixed,
      default: null
    }
    // tags field removed
  },
  {
    timestamps: { createdAt: 'timestamp', updatedAt: false }, // Use 'timestamp' for createdAt, disable updatedAt
    // Capped collection can be kept if desired, checklist doesn't specify for/against
    capped: { size: 1024 * 1024 * 50, max: 50000 }
  }
);

// Indexes - ensure checklist indexes are covered
// SystemLogSchema.index({ level: 1, timestamp: -1 }); // Covered by individual indexes
// SystemLogSchema.index({ source: 1, timestamp: -1 }); // Covered by individual indexes
// SystemLogSchema.index({ userId: 1, timestamp: -1 }); // Covered by individual indexes
// errorCode is removed
// Ensure eventType is indexed (done above)

// Create the model with proper TypeScript typing
const SystemLog = mongoose.models?.SystemLog as mongoose.Model<ISystemLog> || mongoose.model<ISystemLog>('SystemLog', SystemLogSchema);

export { SystemLog };
export default SystemLog;
