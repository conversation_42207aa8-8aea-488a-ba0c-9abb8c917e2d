"use client";

import ThemeShowcase from '@/app/components/features/ThemeShowcase';
import { Button } from "@/app/components/forms/Button";
import Header from '@/app/components/layout/Header';
import { ThemeSelector } from '@/app/components/theme/ThemeSelector';
import { useTheme } from '@/app/contexts/ThemeContext';
import { LegacyApiResponse } from '@/app/types/api-responses';
import { getApiUrl } from '@/app/utils/apiUtils';
import {
    AlertTriangle,
    Bell,
    Check,
    Database,
    Globe,
    Loader2,
    Lock,
    RefreshCw,
    Save,
    User
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Define interfaces for settings data
interface SettingsApiResponse extends LegacyApiResponse<Setting[] | null> {
  // data property is inherited from LegacyApiResponse
}
interface Setting {
  _id: string;
  key: string;
  value: string;
  description?: string;
  dataType: string;
  group?: string;
  lastModifiedBy: {
    _id: string;
    username: string;
    fullName: string;
  };
  lastModifiedAt: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Settings page component
 * Allows users to configure application settings including theme, notifications, and other preferences
 */
const Settings: React.FC = () => {
  const { theme, toggleTheme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('appearance');
  const [settings, setSettings] = useState<Setting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saveStatus, setSaveStatus] = useState<null | 'saving' | 'saved' | 'error'>(null);

  // Form state for settings
  const [notifications, setNotifications] = useState(true);
  const [emailAlerts, setEmailAlerts] = useState(true);
  const [dataRetention, setDataRetention] = useState('30days');
  const [timeZone, setTimeZone] = useState('UTC');
  const [dateFormat, setDateFormat] = useState('MM/DD/YYYY');

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from API
  const fetchSettings = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(getApiUrl('/api/settings'));

      if (!response.ok) {
        throw new Error(`Error fetching settings: ${response.status}`);
      }

      const data = await response.json() as SettingsApiResponse;

      if (data.error) {
        throw new Error(data.error);
      }

      setSettings(data.data || []);

      // Update form state based on fetched settings
      updateFormState(data.data || []);
    } catch (err) {
      console.error('Failed to fetch settings:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Update form state based on fetched settings
  const updateFormState = (settingsData: Setting[]) => {
    // Find settings by key and update state
    const findSetting = (key: string) => settingsData.find(s => s.key === key);

    const notificationsSetting = findSetting('notifications.enabled');
    if (notificationsSetting) {
      setNotifications(notificationsSetting.value === 'true');
    }

    const emailAlertsSetting = findSetting('notifications.email.enabled');
    if (emailAlertsSetting) {
      setEmailAlerts(emailAlertsSetting.value === 'true');
    }

    const dataRetentionSetting = findSetting('data.retention.period');
    if (dataRetentionSetting) {
      setDataRetention(dataRetentionSetting.value);
    }

    const timeZoneSetting = findSetting('region.timezone');
    if (timeZoneSetting) {
      setTimeZone(timeZoneSetting.value);
    }

    const dateFormatSetting = findSetting('region.dateFormat');
    if (dateFormatSetting) {
      setDateFormat(dateFormatSetting.value);
    }
  };

  // Save settings to API
  const handleSaveSettings = async () => {
    setSaveStatus('saving');

    try {
      // Create an array of settings to update
      const settingsToUpdate = [
        {
          key: 'notifications.enabled',
          value: notifications.toString(),
          dataType: 'boolean',
          group: 'Notifications',
          lastModifiedBy: '65f1b2e3c4d5e6f7a8b9c0d1' // Mock user ID, would be replaced with actual user ID
        },
        {
          key: 'notifications.email.enabled',
          value: emailAlerts.toString(),
          dataType: 'boolean',
          group: 'Notifications',
          lastModifiedBy: '65f1b2e3c4d5e6f7a8b9c0d1'
        },
        {
          key: 'data.retention.period',
          value: dataRetention,
          dataType: 'string',
          group: 'Data Management',
          lastModifiedBy: '65f1b2e3c4d5e6f7a8b9c0d1'
        },
        {
          key: 'region.timezone',
          value: timeZone,
          dataType: 'string',
          group: 'Region',
          lastModifiedBy: '65f1b2e3c4d5e6f7a8b9c0d1'
        },
        {
          key: 'region.dateFormat',
          value: dateFormat,
          dataType: 'string',
          group: 'Region',
          lastModifiedBy: '65f1b2e3c4d5e6f7a8b9c0d1'
        }
      ];

      // Update each setting
      for (const setting of settingsToUpdate) {
        const existingSetting = settings.find(s => s.key === setting.key);

        if (existingSetting) {
          // Update existing setting
          await fetch(`/api/settings/${setting.key}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
          });
        } else {
          // Create new setting
          await fetch('/api/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(setting)
          });
        }
      }

      setSaveStatus('saved');
      toast.success('Settings saved successfully');

      // Refresh settings
      fetchSettings();

      // Reset save status after a delay
      setTimeout(() => {
        setSaveStatus(null);
      }, 2000);
    } catch (err) {
      console.error('Failed to save settings:', err);
      setSaveStatus('error');
      toast.error('Failed to save settings');
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Settings" />

      <div className="px-8 py-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading settings...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-400">
            <p>{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={fetchSettings}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-12 gap-6">
            {/* Settings Menu */}
            <div className="col-span-12 md:col-span-3">
              <div className="bg-card rounded-xl shadow-md p-4 border border-border">
                <h3 className="font-medium text-foreground px-4 py-2 mb-2">
                  Settings
                </h3>

                <div className="space-y-1">
                  <div
                    className={`flex items-center px-4 py-2 rounded-lg cursor-pointer ${activeTab === 'appearance' ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50 text-muted-foreground'}`}
                    onClick={() => setActiveTab('appearance')}
                  >
                    <User size={18} className="mr-3" />
                    <span className="text-sm">Appearance</span>
                  </div>
                  <div
                    className={`flex items-center px-4 py-2 rounded-lg cursor-pointer ${activeTab === 'notifications' ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50 text-muted-foreground'}`}
                    onClick={() => setActiveTab('notifications')}
                  >
                    <Bell size={18} className="mr-3" />
                    <span className="text-sm">Notifications</span>
                  </div>
                  <div
                    className={`flex items-center px-4 py-2 rounded-lg cursor-pointer ${activeTab === 'security' ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50 text-muted-foreground'}`}
                    onClick={() => setActiveTab('security')}
                  >
                    <Lock size={18} className="mr-3" />
                    <span className="text-sm">Privacy & Security</span>
                  </div>
                  <div
                    className={`flex items-center px-4 py-2 rounded-lg cursor-pointer ${activeTab === 'data' ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50 text-muted-foreground'}`}
                    onClick={() => setActiveTab('data')}
                  >
                    <Database size={18} className="mr-3" />
                    <span className="text-sm">Data Management</span>
                  </div>
                  <div
                    className={`flex items-center px-4 py-2 rounded-lg cursor-pointer ${activeTab === 'region' ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50 text-muted-foreground'}`}
                    onClick={() => setActiveTab('region')}
                  >
                    <Globe size={18} className="mr-3" />
                    <span className="text-sm">Language & Region</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Settings Content */}
            <div className="col-span-12 md:col-span-9">
              <div className="bg-card rounded-xl shadow-md p-6 border border-border">
                {activeTab === 'appearance' && (
                  <>
                    <h2 className="text-xl font-semibold text-foreground mb-6">
                      Appearance
                    </h2>

                    {/* Enhanced Theme Selection */}
                    <div className="mb-8">
                      <ThemeSelector />
                    </div>

                    {/* Theme Preview */}
                    <div className="mb-8">
                      <h3 className="text-md font-medium text-foreground mb-4">
                        Live Preview
                      </h3>
                      <ThemeShowcase />
                    </div>
                  </>
                )}

                {activeTab === 'notifications' && (
                  <>
                    <h2 className="text-xl font-semibold text-foreground mb-6">
                      Notifications
                    </h2>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-foreground">
                            Enable Notifications
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            Receive notifications about inventory updates
                          </p>
                        </div>
                        <div className="relative">
                          <input
                            type="checkbox"
                            id="notifications"
                            checked={notifications}
                            onChange={() => setNotifications(!notifications)}
                            className="sr-only"
                          />
                          <label
                            htmlFor="notifications"
                            className={`block w-14 h-7 rounded-full transition-colors duration-300 cursor-pointer ${notifications ? 'bg-primary' : 'bg-input'}`}
                          >
                            <span
                              className={`absolute block w-5 h-5 rounded-full bg-white transform transition-transform duration-300 ${notifications ? 'translate-x-8' : 'translate-x-1'} top-1`}
                            />
                          </label>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-foreground">
                            Email Alerts
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            Receive critical alerts via email
                          </p>
                        </div>
                        <div className="relative">
                          <input
                            type="checkbox"
                            id="email-alerts"
                            checked={emailAlerts}
                            onChange={() => setEmailAlerts(!emailAlerts)}
                            className="sr-only"
                          />
                          <label
                            htmlFor="email-alerts"
                            className={`block w-14 h-7 rounded-full transition-colors duration-300 cursor-pointer ${emailAlerts ? 'bg-primary' : 'bg-input'}`}
                          >
                            <span
                              className={`absolute block w-5 h-5 rounded-full bg-white transform transition-transform duration-300 ${emailAlerts ? 'translate-x-8' : 'translate-x-1'} top-1`}
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {activeTab === 'data' && (
                  <>
                    <h2 className="text-xl font-semibold text-foreground mb-6">
                      Data Management
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="data-retention" className="block text-sm font-medium text-foreground mb-1">
                          Data Retention Period
                        </label>
                        <select
                          id="data-retention"
                          value={dataRetention}
                          onChange={(e) => setDataRetention(e.target.value)}
                          className="w-full p-2 border border-input bg-background rounded-md"
                        >
                            <option value="7days">7 Days</option>
                            <option value="30days">30 Days</option>
                            <option value="90days">90 Days</option>
                            <option value="forever">Forever</option>
                        </select>
                      </div>
                    </div>
                  </>
                )}

                {activeTab === 'region' && (
                  <>
                    <h2 className="text-xl font-semibold text-foreground mb-6">
                      Language & Region
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="timezone" className="block text-sm font-medium text-foreground mb-1">
                          Time Zone
                        </label>
                        <select
                          id="timezone"
                          value={timeZone}
                          onChange={(e) => setTimeZone(e.target.value)}
                          className="w-full p-2 border border-input bg-background rounded-md"
                        >
                          <option value="UTC">UTC</option>
                          <option value="EST">EST</option>
                          <option value="PST">PST</option>
                        </select>
                      </div>
                      <div>
                        <label htmlFor="dateformat" className="block text-sm font-medium text-foreground mb-1">
                          Date Format
                        </label>
                        <select
                          id="dateformat"
                          value={dateFormat}
                          onChange={(e) => setDateFormat(e.target.value)}
                          className="w-full p-2 border border-input bg-background rounded-md"
                        >
                          <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                          <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                          <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                      </div>
                    </div>
                  </>
                )}

                {activeTab === 'security' && (
                  <>
                    <h2 className="text-xl font-semibold text-foreground mb-6">
                      Privacy & Security
                    </h2>

                    <p className="text-muted-foreground mb-4">
                      Security settings are managed by your system administrator.
                    </p>
                  </>
                )}

                {/* Save Settings Button */}
                <div className="flex justify-end mt-8">
                  <Button
                    onClick={handleSaveSettings}
                    disabled={saveStatus === 'saving'}
                    variant={saveStatus === 'error' ? 'destructive' : 'default'}
                    className={`${saveStatus === 'saved' ? 'bg-green-500 hover:bg-green-600' : ''}`}
                  >
                    {saveStatus === 'saving' ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        <span>Saving...</span>
                      </>
                    ) : saveStatus === 'saved' ? (
                      <>
                        <Check size={18} className="mr-2" />
                        <span>Saved</span>
                      </>
                    ) : saveStatus === 'error' ? (
                      <>
                        <AlertTriangle size={18} className="mr-2" />
                        <span>Error Saving</span>
                      </>
                    ) : (
                      <>
                        <Save size={18} className="mr-2" />
                        <span>Save Settings</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;