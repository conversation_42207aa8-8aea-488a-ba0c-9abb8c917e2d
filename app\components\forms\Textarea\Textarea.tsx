import { TextareaProps } from "./types";
import dynamic from "next/dynamic";

/**
 * Textarea component that lazy loads the client-side implementation
 * This server component acts as the entry point for the Textarea
 */
const TextareaClient = dynamic(() => import("./TextareaClient"), {
  ssr: false,
});

/**
 * Textarea component that provides a multi-line text input
 * @param props - Standard textarea attributes plus any custom props
 */
export function Textarea(props: TextareaProps) {
  return <TextareaClient {...props} />;
}

Textarea.displayName = "Textarea"; 