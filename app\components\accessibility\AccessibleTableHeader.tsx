'use client';

import { <PERSON><PERSON><PERSON>, TableHeader, TableRow } from '@/app/components/data-display/table';
import { cn } from '@/app/lib/utils';
import { ArrowUpDown, ChevronDown, ChevronUp } from 'lucide-react';
import React from 'react';
import { VisuallyHidden } from './VisuallyHidden';

export interface AccessibleTableColumn {
  /**
   * Unique identifier for the column
   */
  id: string;
  
  /**
   * Header text for the column
   */
  header: string;
  
  /**
   * Whether the column is sortable
   */
  sortable?: boolean;
  
  /**
   * Current sort direction for the column
   */
  sortDirection?: 'asc' | 'desc' | null;
  
  /**
   * Additional class name for the column
   */
  className?: string;
  
  /**
   * Whether to hide the column on mobile
   */
  hideOnMobile?: boolean;
  
  /**
   * Scope attribute for the column (default: 'col')
   */
  scope?: 'col' | 'row' | 'colgroup' | 'rowgroup';
}

export interface AccessibleTableHeaderProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  /**
   * Column definitions
   */
  columns: AccessibleTableColumn[];
  
  /**
   * Callback when a sortable column is clicked
   */
  onSort?: (columnId: string) => void;
  
  /**
   * Additional class name for the header
   */
  className?: string;
  
  /**
   * Additional class name for the header row
   */
  rowClassName?: string;
  
  /**
   * Additional class name for the header cells
   */
  cellClassName?: string;
}

/**
 * AccessibleTableHeader component that enhances the standard TableHeader with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleTableHeader: React.FC<AccessibleTableHeaderProps> = ({
  columns,
  onSort,
  className,
  rowClassName,
  cellClassName,
  ...props
}) => {
  // Handle sort click
  const handleSortClick = (columnId: string, sortable: boolean) => {
    if (!sortable || !onSort) return;
    onSort(columnId);
  };
  
  return (
    <TableHeader
      className={className}
      role="rowgroup"
      {...props}
    >
      <TableRow
        className={rowClassName}
        role="row"
      >
        {columns.map((column, index) => {
          const isSorted = column.sortDirection !== null && column.sortDirection !== undefined;
          const sortLabel = !isSorted
            ? 'Sort by column'
            : column.sortDirection === 'asc'
              ? 'Sorted ascending. Click to sort descending'
              : 'Sorted descending. Click to sort ascending';
          
          return (
            <TableHead
              key={column.id}
              className={cn(
                column.sortable ? 'cursor-pointer select-none' : '',
                column.hideOnMobile ? 'hidden md:table-cell' : '',
                cellClassName,
                column.className
              )}
              onClick={() => handleSortClick(column.id, !!column.sortable)}
              scope={column.scope || 'col'}
              aria-sort={
                !isSorted
                  ? 'none'
                  : column.sortDirection === 'asc'
                    ? 'ascending'
                    : 'descending'
              }
              role="columnheader"
              tabIndex={column.sortable ? 0 : undefined}
              aria-label={column.sortable ? `${column.header}, ${sortLabel}` : undefined}
            >
              <div className="flex items-center space-x-1">
                <span>{column.header}</span>
                {column.sortable && (
                  <span className="flex items-center">
                    {isSorted ? (
                      column.sortDirection === 'asc' ? (
                        <ChevronUp className="h-4 w-4" aria-hidden="true" />
                      ) : (
                        <ChevronDown className="h-4 w-4" aria-hidden="true" />
                      )
                    ) : (
                      <ArrowUpDown className="h-4 w-4 opacity-50" aria-hidden="true" />
                    )}
                    <VisuallyHidden>{sortLabel}</VisuallyHidden>
                  </span>
                )}
              </div>
            </TableHead>
          );
        })}
      </TableRow>
    </TableHeader>
  );
};

export default AccessibleTableHeader;
