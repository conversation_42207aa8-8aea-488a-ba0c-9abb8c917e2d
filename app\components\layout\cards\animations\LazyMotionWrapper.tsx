'use client';

import React, { Suspense, useState, useEffect, useSyncExternalStore } from 'react';
import dynamic from 'next/dynamic';
import { AnimationType } from './cardAnimationVariants';

// Lazy load Framer Motion only when animations are needed
const DynamicMotionDiv = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.div })),
  {
    ssr: false,
    loading: () => null, // No loading state needed for animation wrapper
  }
);

interface LazyMotionWrapperProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  animate?: boolean;
  animation?: AnimationType;
  [key: string]: any; // For other props
}

// Hydration-safe client detection using useSyncExternalStore
function useIsClient() {
  return useSyncExternalStore(
    () => () => {}, // subscribe function (no-op since we don't need updates)
    () => true, // client-side value
    () => false // server-side value
  );
}

/**
 * Animated motion component that loads animation variants dynamically
 */
const AnimatedMotionDiv = React.forwardRef<HTMLDivElement, LazyMotionWrapperProps>(
  ({ children, className, onClick, animation = 'subtle', ...props }, ref) => {
    const [animationVariants, setAnimationVariants] = React.useState({});

    React.useEffect(() => {
      // Load animation variants asynchronously
      import('./cardAnimationVariants').then(({ getAnimationVariants }) => {
        setAnimationVariants(getAnimationVariants(animation));
      });
    }, [animation]);

    return (
      <DynamicMotionDiv
        ref={ref}
        className={className}
        onClick={onClick}
        variants={animationVariants}
        whileHover="hover"
        whileTap="tap"
        {...props}
      >
        {children}
      </DynamicMotionDiv>
    );
  }
);

AnimatedMotionDiv.displayName = "AnimatedMotionDiv";

/**
 * Lazy-loaded motion wrapper that only loads Framer Motion when animations are enabled
 * This significantly reduces bundle size when animations are disabled
 *
 * HYDRATION FIX: Uses useSyncExternalStore for consistent server/client rendering
 */
export const LazyMotionWrapper = React.forwardRef<HTMLDivElement, LazyMotionWrapperProps>(
  ({ children, className, onClick, animate = true, animation = 'subtle', ...props }, ref) => {
    // Use hydration-safe client detection to prevent server/client mismatch
    const isClient = useIsClient();

    // HYDRATION FIX: Always render a regular div when animations are disabled
    // This ensures consistent rendering between server and client
    if (!animate) {
      return (
        <div
          ref={ref}
          className={className}
          onClick={onClick}
          {...props}
        >
          {children}
        </div>
      );
    }

    // HYDRATION FIX: For animated components, render a div on server and during hydration,
    // then upgrade to animated version only on client to prevent hydration mismatch
    if (!isClient) {
      return (
        <div
          ref={ref}
          className={className}
          onClick={onClick}
          {...props}
        >
          {children}
        </div>
      );
    }

    // For animated cards, use the animated motion component with Suspense
    // This only runs on client-side after hydration is complete
    return (
      <Suspense fallback={
        <div
          ref={ref}
          className={className}
          onClick={onClick}
          {...props}
        >
          {children}
        </div>
      }>
        <AnimatedMotionDiv
          ref={ref}
          className={className}
          onClick={onClick}
          animation={animation}
          {...props}
        >
          {children}
        </AnimatedMotionDiv>
      </Suspense>
    );
  }
);

LazyMotionWrapper.displayName = "LazyMotionWrapper";
