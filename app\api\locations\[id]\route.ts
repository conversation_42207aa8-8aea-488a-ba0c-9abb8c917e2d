import { NextRequest, NextResponse } from 'next/server';
import { 
  getLocationById, 
  updateLocation, 
  deactivateLocation,
  UpdateLocationDto
} from '@/app/services/location.service';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

interface RouteParams {
  id: string;
}

const ROUTE_PATH = '/api/locations/[id]';

/**
 * GET /api/locations/[id] - Get a specific location by ID
 */
async function handleGET(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params;

  logApiRequest('GET', ROUTE_PATH, { locationId });

  try {
    const location = await getLocationById(locationId);
    const duration = Date.now() - startTime;

    if (!location) {
      return errorResponse("API_ERROR", `Location with ID ${locationId} not found`, [{ duration }], 404);
    }

    return successResponse(
      location,
      'Location fetched successfully',
      { duration }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to fetch location', [{ duration }], 500);
  }
}

/**
 * PUT /api/locations/[id] - Update a specific location
 */
async function handlePUT(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params;

  logApiRequest('PUT', ROUTE_PATH, { locationId });

  try {
    const body = await request.json();

    // Validate locationType if provided
    if (body.locationType) {
      const validLocationTypes = [
        'Bin', 'Shelf', 'Floor Area', 'Staging', 
        'Production Area', 'Quality Control', 'Shipping', 'Receiving'
      ];
      if (!validLocationTypes.includes(body.locationType)) {
        return errorResponse(
          'INVALID_LOCATION_TYPE',
          `Invalid location type. Must be one of: ${validLocationTypes.join(', ')}`,
          [],
          400
        );
      }
    }

    const updateData: UpdateLocationDto = {
      name: body.name,
      description: body.description,
      locationType: body.locationType,
      capacity: body.capacity,
      isActive: body.isActive
    };

    // Remove undefined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateLocationDto] === undefined) {
        delete updateData[key as keyof UpdateLocationDto];
      }
    });

    const updatedLocation = await updateLocation(locationId, updateData);
    const duration = Date.now() - startTime;

    if (!updatedLocation) {
      return errorResponse("API_ERROR", `Location with ID ${locationId} not found`, [{ duration }], 404);
    }

    return successResponse(
      updatedLocation,
      'Location updated successfully',
      { duration }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    if (error.message.includes('already exists')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 409);
    }
    if (error.message.includes('not found')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 404);
    }
    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to update location', [{ duration }], 500);
  }
}

/**
 * DELETE /api/locations/[id] - Deactivate a location (soft delete)
 */
async function handleDELETE(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params;

  logApiRequest('DELETE', ROUTE_PATH, { locationId });

  try {
    const success = await deactivateLocation(locationId);
    const duration = Date.now() - startTime;

    if (!success) {
      return errorResponse("API_ERROR", `Location with ID ${locationId} not found`, [{ duration }], 404);
    }

    return successResponse(
      { locationId, deactivated: true },
      'Location deactivated successfully',
      { duration }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to deactivate location', [{ duration }], 500);
  }
}

// Apply error handling middleware
export const GET = withErrorHandling(
  (request: NextRequest, context: { params: Promise<RouteParams> }) => handleGET(request, context),
  ROUTE_PATH
);

export const PUT = withErrorHandling(
  (request: NextRequest, context: { params: Promise<RouteParams> }) => handlePUT(request, context),
  ROUTE_PATH
);

export const DELETE = withErrorHandling(
  (request: NextRequest, context: { params: Promise<RouteParams> }) => handleDELETE(request, context),
  ROUTE_PATH
);
