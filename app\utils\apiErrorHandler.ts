/**
 * API Error Handler Utility
 * 
 * Ensures all API routes return consistent JSON responses and never return HTML error pages
 * This prevents "Unexpected token '<'" JSON parsing errors in Vercel deployment
 */

import { NextResponse } from 'next/server';
import { env } from './env';

export interface ApiErrorResponse {
  success: false;
  error: string;
  message: string;
  timestamp: string;
  details?: any;
}

export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string | undefined;
  timestamp: string;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Create a standardized error response that's always valid JSON
 */
export function createErrorResponse(
  error: string | Error,
  status: number = 500,
  details?: any
): NextResponse<ApiErrorResponse> {
  const errorMessage = error instanceof Error ? error.message : error;
  
  const response: ApiErrorResponse = {
    success: false,
    error: errorMessage,
    message: errorMessage,
    timestamp: new Date().toISOString(),
    ...(env.NODE_ENV === 'development' && details && { details })
  };

  return NextResponse.json(response, { 
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    }
  });
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString()
  };

  return NextResponse.json(response, { 
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    }
  });
}

/**
 * Wrap API route handlers to ensure they always return JSON
 */
export function withApiErrorHandler<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse<any>>
) {
  return async (...args: T): Promise<NextResponse<any>> => {
    try {
      const result = await handler(...args);

      // Ensure the response has proper JSON headers
      if (result instanceof NextResponse) {
        result.headers.set('Content-Type', 'application/json');
        result.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      }

      return result;
    } catch (error) {
      console.error('[API Error Handler] Unhandled error:', error);

      // Always return JSON error response, never let HTML error pages through
      return createErrorResponse(
        error instanceof Error ? error.message : 'Internal server error',
        500,
        env.NODE_ENV === 'development' ? error : undefined
      );
    }
  };
}

/**
 * Validate request method and return error if not allowed
 */
export function validateMethod(
  request: Request,
  allowedMethods: string[]
): NextResponse<ApiErrorResponse> | null {
  if (!allowedMethods.includes(request.method)) {
    return createErrorResponse(
      `Method ${request.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
      405
    );
  }
  return null;
}

/**
 * Handle database connection errors specifically
 */
export function handleDatabaseError(error: any): NextResponse<ApiErrorResponse> {
  console.error('[Database Error]:', error);
  
  if (error.name === 'MongoNetworkError') {
    return createErrorResponse(
      'Database connection failed. Please check your MongoDB configuration.',
      503,
      env.NODE_ENV === 'development' ? error.message : undefined
    );
  }
  
  if (error.name === 'MongoTimeoutError') {
    return createErrorResponse(
      'Database operation timed out. Please try again.',
      504,
      env.NODE_ENV === 'development' ? error.message : undefined
    );
  }
  
  return createErrorResponse(
    'Database error occurred',
    500,
    env.NODE_ENV === 'development' ? error.message : undefined
  );
}

/**
 * Handle validation errors
 */
export function handleValidationError(error: any): NextResponse<ApiErrorResponse> {
  console.error('[Validation Error]:', error);
  
  return createErrorResponse(
    'Validation failed',
    400,
    env.NODE_ENV === 'development' ? error : undefined
  );
}

/**
 * Global error handler for unhandled promise rejections in API routes
 */
export function setupGlobalErrorHandling(): void {
  if (typeof process !== 'undefined') {
    process.on('unhandledRejection', (reason, promise) => {
      console.error('[Unhandled Rejection] at:', promise, 'reason:', reason);
    });
    
    process.on('uncaughtException', (error) => {
      console.error('[Uncaught Exception]:', error);
    });
  }
}
