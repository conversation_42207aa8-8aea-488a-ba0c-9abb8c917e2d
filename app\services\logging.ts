/**
 * Logging service for application-wide logging
 */

import { captureException, captureMessage, setExtra, setTag } from '../lib/logging-utils';
import SystemLog from '../models/systemLog.model';

/**
 * Log an operation to the console and optionally to the database
 */
// Overload for 3 parameters (backward compatibility)
export async function logOperation(operation: string, entity: string, details: any): Promise<void>;
// Overload for 4 parameters (new signature)
export async function logOperation(operation: string, entity: string, details: any, saveToDb: boolean): Promise<void>;
// Implementation
export async function logOperation(
  operation: string,
  entity: string,
  details?: any,
  saveToDb: boolean = false
): Promise<void> {
  const timestamp = new Date();

  // Log to console
  console.log(`[${timestamp.toISOString()}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);

  // Save to database if requested
  if (saveToDb) {
    try {
      // Add timeout to the database operation to prevent hanging
      const logPromise = (SystemLog.create as any)({
        timestamp,
        eventType: 'DB_OPERATION',
        level: 'INFO',
        message: `${operation} on ${entity}`,
        source: 'API',
        details
      });

      // Race the log operation against a timeout
      await Promise.race([
        logPromise,
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Log operation timed out after 5 seconds')), 5000);
        })
      ]);
    } catch (error) {
      console.error('Error saving log to database:', error);
    }
  }
};

/**
 * Log an API request
 * @param method - HTTP method
 * @param endpoint - API endpoint
 * @param params - Request parameters
 * @param saveToDb - Whether to save the log to the database (default: false)
 */
export const logApiRequest = async (
  method: string,
  endpoint: string,
  params?: any,
  saveToDb: boolean = false
) => {
  const timestamp = new Date();

  // Log to console
  console.log(`[API][${timestamp.toISOString()}] ${method} ${endpoint}${params ? ' - Params: ' + JSON.stringify(params) : ''}`);

  // Log to Sentry
  captureMessage(`${method} ${endpoint}`, 'info', {
    method,
    endpoint,
    params
  });

  // Set Sentry tags for better filtering
  setTag('api.method', method);
  setTag('api.endpoint', endpoint);

  // Save to database if requested
  if (saveToDb) {
    try {
      // Add timeout to the database operation to prevent hanging
      const logPromise = (SystemLog.create as any)({
        timestamp,
        eventType: 'API_REQUEST',
        level: 'INFO',
        message: `${method} ${endpoint}`,
        source: 'API',
        details: params
      });

      // Race the log operation against a timeout
      await Promise.race([
        logPromise,
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Log operation timed out after 5 seconds')), 5000);
        })
      ]);
    } catch (error) {
      console.error('Error saving API log to database:', error);
      // Don't capture this exception to Sentry to avoid infinite loops
      // captureException(error);
    }
  }
};

/**
 * Log an error
 * @param source - Source of the error
 * @param message - Error message
 * @param error - Error object
 * @param saveToDb - Whether to save the log to the database (default: true)
 */
export const logError = async (
  source: string,
  message: string,
  error: any,
  saveToDb: boolean = true
) => {
  const timestamp = new Date();

  // Log to console
  console.error(`[ERROR][${timestamp.toISOString()}] ${source}: ${message}`, error);

  // Log to Sentry
  setTag('error.source', source);
  setExtra('error.details', {
    message,
    timestamp: timestamp.toISOString(),
    source
  });

  // Capture the exception in Sentry
  captureException(error, {
    source,
    message
  });

  // Save to database if requested
  if (saveToDb) {
    try {
      // Add timeout to the database operation to prevent hanging
      const logPromise = (SystemLog.create as any)({
        timestamp,
        eventType: 'ERROR',
        level: 'ERROR',
        message,
        source,
        details: {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined
        }
      });

      // Race the log operation against a timeout
      await Promise.race([
        logPromise,
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Log operation timed out after 5 seconds')), 5000);
        })
      ]);
    } catch (logError) {
      console.error('Error saving error log to database:', logError);
      // Don't capture this in Sentry to avoid potential infinite loops
    }
  }
};

/**
 * Log a report generation
 * @param reportType - Type of report
 * @param filters - Filters applied to the report
 * @param saveToDb - Whether to save the log to the database (default: false)
 */
export const logReportGeneration = async (
  reportType: string,
  filters?: any,
  saveToDb: boolean = false
) => {
  const timestamp = new Date();

  // Log to console
  console.log(`[REPORT][${timestamp.toISOString()}] Generated ${reportType} report${filters ? ' with filters: ' + JSON.stringify(filters) : ''}`);

  // Save to database if requested
  if (saveToDb) {
    try {
      // Add timeout to the database operation to prevent hanging
      const logPromise = (SystemLog.create as any)({
        timestamp,
        eventType: 'REPORT_GENERATION',
        level: 'INFO',
        message: `Generated ${reportType} report`,
        source: 'API',
        details: filters
      });

      // Race the log operation against a timeout
      await Promise.race([
        logPromise,
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Log operation timed out after 5 seconds')), 5000);
        })
      ]);
    } catch (error) {
      console.error('Error saving report log to database:', error);
    }
  }
};

/**
 * Log API performance data
 * @param endpoint - API endpoint
 * @param duration - Request duration in milliseconds
 * @param category - Performance category (normal, slow, very-slow)
 * @param details - Additional details about the request
 * @param saveToDb - Whether to save the log to the database (default: true)
 */
export const logApiPerformance = async (
  endpoint: string,
  duration: number,
  category: string,
  details?: any,
  saveToDb: boolean = true
) => {
  const timestamp = new Date();

  // Log to console based on performance category
  if (category === 'very-slow') {
    console.warn(`[PERFORMANCE][${timestamp.toISOString()}] VERY SLOW API response: ${endpoint} - ${duration}ms`);
  } else if (category === 'slow') {
    console.warn(`[PERFORMANCE][${timestamp.toISOString()}] SLOW API response: ${endpoint} - ${duration}ms`);
  } else {
    console.log(`[PERFORMANCE][${timestamp.toISOString()}] API response: ${endpoint} - ${duration}ms`);
  }

  // Log to Sentry for slow and very slow responses
  if (category !== 'normal') {
    captureMessage(`Slow API response: ${endpoint} (${duration}ms)`, 'warning', {
      endpoint,
      duration,
      category,
      details
    });
    
    // Set Sentry tags for better filtering
    setTag('performance.endpoint', endpoint);
    setTag('performance.duration', duration.toString());
    setTag('performance.category', category);
  }

  // Save to database if requested
  if (saveToDb) {
    try {
      // Add timeout to the database operation to prevent hanging
      const logPromise = (SystemLog.create as any)({
        timestamp,
        eventType: 'API_PERFORMANCE',
        level: category === 'normal' ? 'INFO' : (category === 'slow' ? 'WARNING' : 'ERROR'),
        message: `API response time: ${endpoint} - ${duration}ms (${category})`,
        source: 'API',
        details: {
          endpoint,
          duration,
          category,
          ...details
        }
      });

      // Race the log operation against a timeout
      await Promise.race([
        logPromise,
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Log operation timed out after 5 seconds')), 5000);
        })
      ]);
    } catch (error) {
      console.error('Error saving performance log to database:', error);
      if (category !== 'normal') {
        // Don't capture this exception to Sentry to avoid infinite loops
        // captureException(error);
      }
    }
  }
};


// Note: logOperation is already exported above with the correct signature
