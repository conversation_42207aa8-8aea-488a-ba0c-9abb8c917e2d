"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { Button } from "@/app/components/forms/Button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { WorkOrderForm } from '@/app/components/forms/WorkOrderForm';
import { WorkOrderFormData } from '@/app/components/forms/WorkOrderForm/types';
import Header from '@/app/components/layout/Header';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/app/components/navigation/dialog";
import { StandardizedTable, type ViewMode } from '@/app/components/tables/StandardizedTable';
import { createWorkOrdersComplexColumns, type WorkOrderColumnData, type WorkOrdersTableActions } from "@/app/components/data-display/data-table";

import { asWorkOrdersResponse, extractApiError, hasApiError, LegacyApiResponse } from '@/app/types/api-responses';
import { WorkOrder } from '@/app/types/orders';
import { motion } from 'framer-motion';
import {
    AlertTriangle,
    Plus,
    X
} from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

// Define API response type for work order operations
interface WorkOrderResponse extends LegacyApiResponse<{ woNumber?: string } | null> {
  // data property is inherited from LegacyApiResponse
}

/**
 * Work Orders page component
 * Displays a list of work orders and allows users to create, edit, and delete work orders
 * Includes filtering and search functionality
 */
const WorkOrders: React.FC = () => {

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [currentWorkOrder, setCurrentWorkOrder] = useState<WorkOrder | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // StandardizedTable state
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('table');

  // Fetch work orders data
  const fetchWorkOrders = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/work-orders');
      if (!response.ok) {
        throw new Error(`Failed to fetch work orders: ${response.statusText}`);
      }

      const data = await response.json();
      if (hasApiError(data)) {
        const errorMsg = extractApiError(data);
        throw new Error(errorMsg || 'Unknown API error');
      }

      const workOrdersResponse = asWorkOrdersResponse(data);
      setWorkOrders(workOrdersResponse.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch work orders';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load data on component mount and refresh trigger
  useEffect(() => {
    fetchWorkOrders();
  }, [fetchWorkOrders, refreshTrigger]);



  // Transform WorkOrder to WorkOrderColumnData
  const transformWorkOrderData = useCallback((workOrder: WorkOrder): WorkOrderColumnData => ({
    _id: workOrder._id,
    woNumber: workOrder.woNumber,
    assemblyId: workOrder.assemblyId ?? null,
    partIdToManufacture: workOrder.partIdToManufacture ?? null,
    productId: workOrder.productId ?? null,
    quantity: workOrder.quantity,
    status: workOrder.status,
    priority: workOrder.priority,
    dueDate: workOrder.dueDate,
    startDate: workOrder.startDate ?? null,
    assignedTo: workOrder.assignedTo ?? null,
    notes: workOrder.notes ?? null,
    completedAt: workOrder.completedAt ?? null,
    estimatedDuration: workOrder.estimatedDuration ?? null,
    actualDuration: workOrder.actualDuration ?? null,
    costEstimate: workOrder.costEstimate ?? null,
    actualCost: workOrder.actualCost ?? null,
    createdBy: workOrder.createdBy ?? null,
    createdAt: workOrder.createdAt,
    updatedAt: workOrder.updatedAt,
  }), []);

  // Table actions
  const tableActions: WorkOrdersTableActions = useMemo(() => ({
    onView: (workOrder: WorkOrderColumnData) => {
      const originalWorkOrder = workOrders.find(wo => wo._id === workOrder._id);
      if (originalWorkOrder) handleViewWorkOrder(originalWorkOrder);
    },
    onEdit: (workOrder: WorkOrderColumnData) => {
      const originalWorkOrder = workOrders.find(wo => wo._id === workOrder._id);
      if (originalWorkOrder) handleEditWorkOrder(originalWorkOrder);
    },
    onDelete: (workOrder: WorkOrderColumnData) => {
      const originalWorkOrder = workOrders.find(wo => wo._id === workOrder._id);
      if (originalWorkOrder) handleDeleteWorkOrder(originalWorkOrder);
    },
  }), [workOrders]);

  // Table columns
  const columns = useMemo(() =>
    createWorkOrdersComplexColumns(tableActions),
    [tableActions]
  );

  // Filtered and transformed data
  const filteredWorkOrders = useMemo(() => {
    const filtered = workOrders.filter(workOrder => {
      const matchesSearch = !searchTerm ||
        workOrder.woNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workOrder.notes?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || workOrder.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    return filtered.map(transformWorkOrderData);
  }, [workOrders, searchTerm, statusFilter, transformWorkOrderData]);

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Open form modal for creating a new work order
  const openCreateModal = () => {
    setCurrentWorkOrder(null);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Open form modal for editing an existing work order
  const handleEditWorkOrder = (workOrder: WorkOrder) => {
    setCurrentWorkOrder(workOrder);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Close form modal
  const closeFormModal = () => {
    setIsFormModalOpen(false);
    setCurrentWorkOrder(null);
    setFormError(null);
  };

  // Handle work order form submission
  const handleWorkOrderSubmit = async (data: WorkOrderFormData) => {
    setIsSubmitting(true);
    setFormError(null);

    try {
      const isEditing = !!currentWorkOrder;
      const url = isEditing
        ? `/api/work-orders/${currentWorkOrder.woNumber}`
        : '/api/work-orders';

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || 'Failed to save work order');
      }

      const result = await response.json() as WorkOrderResponse;

      if (result.error) {
        throw new Error(result.error);
      }

      // Close modal and refresh table
      setIsFormModalOpen(false);
      setCurrentWorkOrder(null);
      setRefreshTrigger(prev => prev + 1);

      // Show success toast
      toast.success(isEditing
        ? `Work order ${result.data?.woNumber || 'Unknown'} updated successfully`
        : `Work order ${result.data?.woNumber || 'Unknown'} created successfully`
      );
    } catch (err: any) {
      setFormError(err.message || 'An error occurred while saving the work order');
      console.error('Error saving work order:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle work order deletion
  const handleDeleteWorkOrder = (workOrder: WorkOrder) => {
    // The actual delete operation is handled in the WorkOrdersTable component
    // Here we just refresh the table after deletion
    setRefreshTrigger(prev => prev + 1);
    toast.success(`Work order ${workOrder.woNumber} deleted successfully`);
  };

  // Handle work order view
  const handleViewWorkOrder = (workOrder: WorkOrder) => {
    setCurrentWorkOrder(workOrder);
    setIsFormModalOpen(true);
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Work Orders" />

      <div className="px-8 pb-8">


        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <StandardizedTable
          data={filteredWorkOrders}
          columns={columns}
          searchPlaceholder="Search work orders..."
          defaultViewMode={viewMode}
          enableSearch={true}
          enableViewToggle={true}
          onViewModeChange={setViewMode}
          onSearchChange={setSearchTerm}
          renderFilters={() => (
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          )}
          renderActions={() => (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="default" onClick={openCreateModal}>
                <Plus size={18} className="mr-1" />
                <span>New Work Order</span>
              </Button>
            </motion.div>
          )}
          // FIXED: Updated from legacy tableProps pattern to direct props pattern
          isLoading={isLoading}
          error={error ? new Error(error) : null}
          enableSorting={true}
          enableFiltering={true}
          enablePagination={true}
          enableGlobalSearch={false} // Using StandardizedTable's search instead
          enableColumnVisibility={false} // Disabled - StandardizedTable handles column visibility
          mobileDisplayMode="cards"
          density="normal"
          initialPagination={{ pageIndex: 0, pageSize: 20 }}
          pageSizeOptions={[10, 20, 50, 100]}
          key={`work-orders-table-${refreshTrigger}`}
        />
      </div>

      {/* Work Order Form Modal */}
      <Dialog open={isFormModalOpen} onOpenChange={setIsFormModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {currentWorkOrder ? `Edit Work Order: ${currentWorkOrder.woNumber}` : 'Create New Work Order'}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4"
              onClick={closeFormModal}
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <WorkOrderForm
            initialData={currentWorkOrder ? currentWorkOrder : undefined}
            onSubmit={handleWorkOrderSubmit}
            onCancel={closeFormModal}
            isLoading={isSubmitting}
            error={formError}
            isEditing={!!currentWorkOrder}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WorkOrders;