import { NextRequest, NextResponse } from 'next/server';
import { getLocationsByWarehouse } from '@/app/services/location.service';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

interface RouteParams {
  id: string; // warehouse ID
}

const ROUTE_PATH = '/api/warehouses/[id]/locations';

/**
 * GET /api/warehouses/[id]/locations - Get all locations for a specific warehouse
 */
async function handleGET(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: warehouseId } = await context.params;
  const { searchParams } = new URL(request.url);

  logApiRequest('GET', ROUTE_PATH, { warehouseId });

  try {
    // Parse query parameters
    const activeOnly = searchParams.get('activeOnly') !== 'false'; // Default to true
    const locationType = searchParams.get('locationType') || undefined;

    let locations = await getLocationsByWarehouse(warehouseId, activeOnly);

    // Filter by location type if specified
    if (locationType) {
      locations = locations.filter(location => location.locationType === locationType);
    }

    const duration = Date.now() - startTime;

    return successResponse(
      locations,
      `Locations for warehouse ${warehouseId} fetched successfully`,
      { 
        duration,
        count: locations.length,
        warehouseId,
        activeOnly,
        locationType
      }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to fetch warehouse locations', [{ duration }], 500);
  }
}

// Apply error handling middleware
export const GET = withErrorHandling(
  (request: NextRequest, context: { params: Promise<RouteParams> }) => handleGET(request, context),
  ROUTE_PATH
);
