import React from 'react';
import { ContainerProps } from './types';
import { cn } from '@/app/lib/utils';

/**
 * Container component that provides consistent layout and spacing
 * This is a server component that doesn't include animations for better performance
 */
const Container: React.FC<ContainerProps> = ({
  children,
  maxWidth = 'xl',
  padding = 'px-4 py-6 md:px-6 lg:px-8',
  className = '',
  centered = true
}) => {
  // Map maxWidth value to appropriate Tailwind class
  const maxWidthClass = maxWidth === 'none' 
    ? '' 
    : maxWidth === 'full'
      ? 'w-full'
      : `max-w-${maxWidth}`;

  return (
    <div
      className={cn(
        maxWidthClass,
        padding,
        centered && 'mx-auto',
        className
      )}
    >
      {children}
    </div>
  );
};

export default Container; 