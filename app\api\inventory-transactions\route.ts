import connectToDatabase, { handleMongoDBError } from '@/app/lib/mongodb';
import InventoryTransaction from '@/app/models/inventorytransaction.model';
import { IPart as IPartModel } from '@/app/models/part.model';
import Location from '@/app/models/location.model';
import Warehouse from '@/app/models/warehouse.model';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';
import { getPartById } from '../../services/part.service'; // V4 Schema: adjustStockLevelByDelta removed, use StockMovementService instead
import { generateTransactionId } from '../../services/transactionIdGenerator';
import { StockMovementService, StockMovementRequest, StockMovementType } from '../../services/stockmovement.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

// Define interface for the transaction creation request
interface CreateTransactionRequest {
  // Updated to match database schema
  partId: string; // Changed from itemId to match database schema
  itemType?: 'Part' | 'Assembly' | 'Product'; // Made optional for backward compatibility
  warehouseId: string;
  transactionType?: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment' | 'internal_transfer';
  quantity?: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;
  // V4 Schema: Additional fields for proper internal transfers
  fromLocationId?: string;
  toLocationId?: string;
  fromStockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  toStockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
}

/**
 * Transform legacy transaction type to modern stock movement type
 */
function mapLegacyTransactionTypeToMovementType(legacyType: string): StockMovementType {
  const mapping: Record<string, StockMovementType> = {
    'stock_in_purchase': 'purchase_receipt',
    'stock_out_production': 'sales_shipment',
    'adjustment_cycle_count': 'adjustment',
    'stock_in_production': 'purchase_receipt',
    'transfer_out': 'internal_transfer',
    'transfer_in': 'internal_transfer',
    'sales_shipment': 'sales_shipment',
    'internal_transfer': 'internal_transfer'
  };

  const movementType = mapping[legacyType];
  if (!movementType) {
    throw new Error(`Unsupported legacy transaction type: ${legacyType}`);
  }

  return movementType;
}

/**
 * Transform legacy CreateTransactionRequest to modern StockMovementRequest
 */
function transformLegacyRequestToStockMovement(legacyRequest: CreateTransactionRequest): StockMovementRequest {
  const movementType = mapLegacyTransactionTypeToMovementType(legacyRequest.transactionType!);

  // Determine from/to locations based on movement type and business rules
  let from: StockMovementRequest['from'] = null;
  let to: StockMovementRequest['to'] = null;

  switch (movementType) {
    case 'purchase_receipt':
      // External → Raw stock
      to = {
        locationId: legacyRequest.warehouseId, // V4 Schema: Use as locationId
        stockType: 'raw'
      };
      break;

    case 'sales_shipment':
      // Finished stock → External
      from = {
        locationId: legacyRequest.warehouseId, // V4 Schema: Use as locationId
        stockType: 'finished'
      };
      break;

    case 'adjustment':
      // Manual adjustment - determine if positive or negative
      if (legacyRequest.quantity! > 0) {
        to = {
          locationId: legacyRequest.warehouseId, // V4 Schema: Use as locationId
          stockType: 'finished' // Default to finished for adjustments
        };
      } else {
        from = {
          locationId: legacyRequest.warehouseId, // V4 Schema: Use as locationId
          stockType: 'finished'
        };
      }
      break;

    case 'internal_transfer':
      // For internal transfers, we need both source and destination locations
      // This should be provided in the request body for proper transfers
      if (!legacyRequest.fromLocationId || !legacyRequest.toLocationId) {
        throw new Error('Internal transfers require both fromLocationId and toLocationId');
      }
      from = {
        locationId: legacyRequest.fromLocationId,
        stockType: legacyRequest.fromStockType || 'finished'
      };
      to = {
        locationId: legacyRequest.toLocationId,
        stockType: legacyRequest.toStockType || 'finished'
      };
      break;
  }

  const stockMovementRequest: StockMovementRequest = {
    partId: legacyRequest.partId,
    movementType,
    quantity: Math.abs(legacyRequest.quantity!), // StockMovementService expects positive quantity
    from,
    to,
    userId: legacyRequest.userId
  };

  // Add optional properties only if they have values
  if (legacyRequest.referenceNumber) {
    stockMovementRequest.referenceNumber = legacyRequest.referenceNumber;
  }
  if (legacyRequest.referenceType) {
    stockMovementRequest.referenceType = legacyRequest.referenceType;
  }
  if (legacyRequest.notes) {
    stockMovementRequest.notes = legacyRequest.notes;
  }
  if (legacyRequest.transactionDate) {
    stockMovementRequest.transactionDate = legacyRequest.transactionDate;
  }

  return stockMovementRequest;
}

/**
 * GET handler for inventory transactions
 * Fetches a list of inventory transactions with pagination and filtering options
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/inventory-transactions - Fetching transactions');
    await connectToDatabase();

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10', 10), MAX_LIMIT);
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    // Filter by part ID if provided
    const partId = url.searchParams.get('partId');
    if (partId) {
      try {
        filter.partId = new mongoose.Types.ObjectId(partId);
      } catch (err) {
        console.error('Invalid partId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid partId format' },
          { status: 400 }
        );
      }
    }

    const itemType = url.searchParams.get('itemType');
    if (itemType) {
      if (!['Part', 'Assembly', 'Product'].includes(itemType)) {
        return NextResponse.json(
          { success: false, error: 'Invalid itemType format' },
          { status: 400 }
        );
      }
      filter.itemType = itemType;
    }

    // Filter by warehouse ID if provided
    const warehouseId = url.searchParams.get('warehouseId');
    if (warehouseId) {
      try {
        filter.warehouseId = new mongoose.Types.ObjectId(warehouseId);
      } catch (err) {
        console.error('Invalid warehouseId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid warehouseId format' },
          { status: 400 }
        );
      }
    }

    // Filter by transaction type if provided
    const transactionType = url.searchParams.get('transactionType');
    if (transactionType) {
      filter.transactionType = transactionType;
    }

    // Handle search parameter for text search across transactions
    const searchTerm = url.searchParams.get('search');
    let useAggregation = false;

    if (searchTerm && searchTerm.trim()) {
      useAggregation = true; // We need aggregation to search in populated fields
    }

    // Filter by date range if provided
    const startDateParam = url.searchParams.get('startDate');
    const endDateParam = url.searchParams.get('endDate');

    if (startDateParam && endDateParam) {
      filter.transactionDate = {
        $gte: new Date(startDateParam),
        $lte: new Date(endDateParam)
      };
    } else if (startDateParam) {
      filter.transactionDate = { $gte: new Date(startDateParam) };
    } else if (endDateParam) {
      filter.transactionDate = { $lte: new Date(endDateParam) };
    } else {
      // If no specific date range is provided, check for period filter
      const period = url.searchParams.get('period');
      let startDate: Date | null = null;
      const now = new Date();

      switch (period) {
        case 'day':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        filter.transactionDate = { $gte: startDate };
      }
    }

    let transactions, total;

    if (useAggregation) {
      // Use aggregation pipeline for search functionality
      const searchRegex = new RegExp(searchTerm!.trim(), 'i');

      const pipeline = [
        // First, do the lookups to get related data
        {
          $lookup: {
            from: 'parts',
            localField: 'partId',
            foreignField: '_id',
            as: 'partData'
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouseId',
            foreignField: '_id',
            as: 'warehouseData'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData'
          }
        },
        {
          $addFields: {
            partData: { $arrayElemAt: ['$partData', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseData', 0] },
            userData: { $arrayElemAt: ['$userData', 0] }
          }
        },
        // Apply base filters and search
        {
          $match: {
            ...filter,
            $or: [
              { notes: searchRegex },
              { referenceNumber: searchRegex },
              { transactionType: searchRegex },
              { 'partData.partNumber': searchRegex },
              { 'partData.name': searchRegex },
              { 'warehouseData.name': searchRegex },
              { 'warehouseData.location_id': searchRegex },
              { 'userData.username': searchRegex },
              { 'userData.first_name': searchRegex },
              { 'userData.last_name': searchRegex }
            ]
          }
        },
        // Transform the data to match the populate format
        {
          $addFields: {
            partId: {
              $cond: {
                if: { $ne: ['$partData', null] },
                then: {
                  _id: '$partData._id',
                  partNumber: '$partData.partNumber',
                  name: '$partData.name',
                  description: '$partData.description'
                },
                else: '$partId'
              }
            },
            warehouseId: {
              $cond: {
                if: { $ne: ['$warehouseData', null] },
                then: {
                  _id: '$warehouseData._id',
                  location_id: '$warehouseData.location_id',
                  name: '$warehouseData.name'
                },
                else: '$warehouseId'
              }
            },
            userId: {
              $cond: {
                if: { $ne: ['$userData', null] },
                then: {
                  _id: '$userData._id',
                  username: '$userData.username',
                  first_name: '$userData.first_name',
                  last_name: '$userData.last_name'
                },
                else: '$userId'
              }
            }
          }
        },
        // Remove the temporary fields
        {
          $unset: ['partData', 'warehouseData', 'userData']
        },
        { $sort: { transactionDate: -1 as -1 } },
        { $skip: skip },
        { $limit: limit }
      ];

      // Get transactions with search
      transactions = await InventoryTransaction.aggregate(pipeline);

      // Get total count for search results
      const countPipeline = [
        {
          $lookup: {
            from: 'parts',
            localField: 'partId',
            foreignField: '_id',
            as: 'partData'
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouseId',
            foreignField: '_id',
            as: 'warehouseData'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData'
          }
        },
        {
          $addFields: {
            partData: { $arrayElemAt: ['$partData', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseData', 0] },
            userData: { $arrayElemAt: ['$userData', 0] }
          }
        },
        {
          $match: {
            ...filter,
            $or: [
              { notes: searchRegex },
              { referenceNumber: searchRegex },
              { transactionType: searchRegex },
              { 'partData.partNumber': searchRegex },
              { 'partData.name': searchRegex },
              { 'warehouseData.name': searchRegex },
              { 'warehouseData.location_id': searchRegex },
              { 'userData.username': searchRegex },
              { 'userData.first_name': searchRegex },
              { 'userData.last_name': searchRegex }
            ]
          }
        },
        { $count: 'total' }
      ];

      const countResult = await InventoryTransaction.aggregate(countPipeline);
      total = countResult.length > 0 ? countResult[0].total : 0;

    } else {
      // Use regular query with population for non-search requests
      try {
        // First, get transactions without populate to avoid ObjectId cast errors
        const rawTransactions = await (InventoryTransaction as any).find(filter)
          .sort({ transactionDate: -1 })
          .skip(skip)
          .limit(limit)
          .lean();

        // Get total count for pagination
        total = await InventoryTransaction.countDocuments(filter);

        // Now manually populate each transaction, handling errors gracefully
        transactions = await Promise.all(rawTransactions.map(async (transaction: any) => {
          const populatedTransaction = { ...transaction };

          // Populate partId if it's a valid ObjectId
          if (transaction.partId && mongoose.Types.ObjectId.isValid(transaction.partId)) {
            try {
              const Part = mongoose.model('Part');
              const part = await Part.findById(transaction.partId)
                .select('partNumber name businessName description')
                .lean();
              if (part) {
                populatedTransaction.partId = part;
                // Add convenience fields for easy access in the UI
                populatedTransaction.itemName = (part as any).name || 'Unknown Part';
                populatedTransaction.businessName = (part as any).businessName || (part as any).name || 'Unknown Part';
                populatedTransaction.partNumber = (part as any).partNumber || 'Unknown Part Number';
              }
            } catch (partError) {
              console.warn(`Failed to populate partId ${transaction.partId}:`, partError);
              // Keep original partId value
            }
          }

          // Populate warehouseId if it's a valid ObjectId
          if (transaction.warehouseId && mongoose.Types.ObjectId.isValid(transaction.warehouseId)) {
            try {
              const Warehouse = mongoose.model('Warehouse');
              const warehouse = await Warehouse.findById(transaction.warehouseId)
                .select('location_id name')
                .lean();
              if (warehouse) {
                populatedTransaction.warehouseId = warehouse;
                // Add convenience field for easy access in the UI
                populatedTransaction.warehouseName = (warehouse as any).name || 'Unknown Warehouse';
              }
            } catch (warehouseError) {
              console.warn(`Failed to populate warehouseId ${transaction.warehouseId}:`, warehouseError);
              // Keep original warehouseId value
            }
          }

          // Populate location details for from/to movements (V4 schema)
          if (transaction.from?.locationId && mongoose.Types.ObjectId.isValid(transaction.from.locationId)) {
            try {
              const location = await Location.findById(transaction.from.locationId)
                .populate('warehouseId', 'name')
                .select('name description locationType warehouseId')
                .lean();

              if (location) {
                populatedTransaction.from = {
                  ...populatedTransaction.from,
                  location: location,
                  locationName: (location as any).name || 'Unknown Location',
                  warehouseName: (location as any).warehouseId?.name || 'Unknown Warehouse'
                };
              }
            } catch (locationError) {
              console.warn(`Failed to populate from.locationId ${transaction.from.locationId}:`, locationError);
            }
          }

          if (transaction.to?.locationId && mongoose.Types.ObjectId.isValid(transaction.to.locationId)) {
            try {
              const location = await Location.findById(transaction.to.locationId)
                .populate('warehouseId', 'name')
                .select('name description locationType warehouseId')
                .lean();

              if (location) {
                populatedTransaction.to = {
                  ...populatedTransaction.to,
                  location: location,
                  locationName: (location as any).name || 'Unknown Location',
                  warehouseName: (location as any).warehouseId?.name || 'Unknown Warehouse'
                };
              }
            } catch (locationError) {
              console.warn(`Failed to populate to.locationId ${transaction.to.locationId}:`, locationError);
            }
          }

          // Populate userId if it's a valid ObjectId
          if (transaction.userId && mongoose.Types.ObjectId.isValid(transaction.userId)) {
            try {
              const User = mongoose.model('User');
              const user = await User.findById(transaction.userId)
                .select('username first_name last_name')
                .lean();
              if (user) {
                populatedTransaction.userId = user;
                // Add convenience field for easy access
                populatedTransaction.userName = (user as any).first_name && (user as any).last_name
                  ? `${(user as any).first_name} ${(user as any).last_name}`
                  : (user as any).username || 'Unknown User';
              }
            } catch (userError) {
              console.warn(`Failed to populate userId ${transaction.userId}:`, userError);
              // Keep original userId value
            }
          }

          // Populate location and warehouse information for from/to objects (V4 Schema)
          if (transaction.from?.locationId && mongoose.Types.ObjectId.isValid(transaction.from.locationId)) {
            try {
              const location = await Location.findById(transaction.from.locationId)
                .select('name description locationType warehouseId')
                .lean();

              if (location) {
                // Also populate the warehouse information
                const warehouse = await Warehouse.findById((location as any).warehouseId)
                  .select('location_id name location')
                  .lean();

                // Enhanced location name with fallbacks for null/empty names
                let locationName = (location as any).name;
                if (!locationName) {
                  const description = (location as any).description || '';
                  const locationType = (location as any).locationType || '';

                  if (description.includes('Default legacy location')) {
                    locationName = '(Default Area)';
                  } else if (locationType) {
                    locationName = locationType;
                  } else {
                    locationName = 'Unknown Location';
                  }
                }

                populatedTransaction.from = {
                  ...transaction.from,
                  location: location,
                  warehouse: warehouse,
                  // Add convenience fields for easy access
                  locationName: locationName,
                  warehouseName: (warehouse as any)?.name || 'Unknown Warehouse'
                };
              }
            } catch (fromLocationError) {
              console.warn(`Failed to populate from.locationId ${transaction.from.locationId}:`, fromLocationError);
            }
          }

          if (transaction.to?.locationId && mongoose.Types.ObjectId.isValid(transaction.to.locationId)) {
            try {
              const location = await Location.findById(transaction.to.locationId)
                .select('name description locationType warehouseId')
                .lean();

              if (location) {
                // Also populate the warehouse information
                const warehouse = await Warehouse.findById((location as any).warehouseId)
                  .select('location_id name location')
                  .lean();

                // Enhanced location name with fallbacks for null/empty names
                let locationName = (location as any).name;
                if (!locationName) {
                  const description = (location as any).description || '';
                  const locationType = (location as any).locationType || '';

                  if (description.includes('Default legacy location')) {
                    locationName = '(Default Area)';
                  } else if (locationType) {
                    locationName = locationType;
                  } else {
                    locationName = 'Unknown Location';
                  }
                }

                populatedTransaction.to = {
                  ...transaction.to,
                  location: location,
                  warehouse: warehouse,
                  // Add convenience fields for easy access
                  locationName: locationName,
                  warehouseName: (warehouse as any)?.name || 'Unknown Warehouse'
                };
              }
            } catch (toLocationError) {
              console.warn(`Failed to populate to.locationId ${transaction.to.locationId}:`, toLocationError);
            }
          }

          return populatedTransaction;
        }));

      } catch (populateError: any) {
        console.error('Error during manual populate operation:', populateError);

        // Final fallback: return raw data without population
        transactions = await (InventoryTransaction as any).find(filter)
          .sort({ transactionDate: -1 })
          .skip(skip)
          .limit(limit)
          .lean();

        total = await InventoryTransaction.countDocuments(filter);
      }
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transactions query completed in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      meta: { duration }
    });
  } catch (error: any) {
    console.error('Error fetching inventory transactions:', error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to fetch inventory transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for inventory transactions
 * PHASE 2: Consolidated to use StockMovementService for unified transaction processing
 * Maintains backward compatibility with legacy request/response format
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log('[API] POST /api/inventory-transactions - Received request (Phase 2: Consolidated)');

  try {
    const data = (await request.json()) as CreateTransactionRequest;

    // Validate quantity - must be positive and non-zero
    if (!data.quantity || data.quantity <= 0) {
      return NextResponse.json(
        { success: false, error: 'Quantity must be a positive number greater than zero' },
        { status: 400 }
      );
    }
    console.log('[API] POST /api/inventory-transactions - Request body:', data);

    // Validate required fields
    if (!data.partId || !data.warehouseId || !data.userId || data.quantity === undefined || !data.transactionType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: partId, warehouseId, userId, quantity, transactionType' },
        { status: 400 }
      );
    }

    // itemType is optional for backward compatibility, default to 'Part'
    if (data.itemType && !['Part', 'Assembly', 'Product'].includes(data.itemType)) {
      return NextResponse.json({ success: false, error: 'Invalid itemType specified' }, { status: 400 });
    }

    // Validate ObjectId formats
    if (!mongoose.Types.ObjectId.isValid(data.partId)) {
      return NextResponse.json({ success: false, error: 'Invalid partId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.warehouseId)) {
      return NextResponse.json({ success: false, error: 'Invalid warehouseId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.userId)) {
      return NextResponse.json({ success: false, error: 'Invalid userId format' }, { status: 400 });
    }

    console.log('[API] Phase 2: Transforming legacy request to StockMovementRequest');

    // Transform legacy request to modern StockMovementRequest
    const stockMovementRequest = transformLegacyRequestToStockMovement(data);
    console.log('[API] Phase 2: Transformed request:', stockMovementRequest);

    // Execute the stock movement using the consolidated service
    const result = await StockMovementService.executeMovement(stockMovementRequest);

    const duration = Date.now() - startTime;
    console.log(`[API] Phase 2: Stock movement completed successfully in ${duration}ms`);

    // Transform response back to legacy format for backward compatibility
    return NextResponse.json({
      success: true,
      data: {
        transaction: result.transaction,
        updatedItem: result.updatedPart,
        message: result.message
      },
      meta: { duration }
    }, { status: 201 });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Phase 2: Error in POST /api/inventory-transactions (${duration}ms):`, error);

    // Handle specific error types
    if (error.message.includes('Insufficient stock') ||
        error.message.includes('Part') ||
        error.message.includes('Warehouse') ||
        error.message.includes('Invalid') ||
        error.message.includes('Unsupported')) {
      return NextResponse.json({ success: false, error: error.message }, { status: 400 });
    }

    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to process inventory transaction' },
      { status: 500 }
    );
  }
}

