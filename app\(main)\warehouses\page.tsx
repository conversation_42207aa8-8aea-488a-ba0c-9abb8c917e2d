"use client";

import { Alert, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { Button } from "@/app/components/forms/Button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { WarehouseStats, WarehouseStatsData } from '@/app/components/features/WarehouseStats';
import { WarehouseForm, WarehouseFormData, WarehouseDisplayData, transformApiDataToFormData, transformFormDataToApiRequest } from '@/app/components/forms/WarehouseForm';
import Header from '@/app/components/layout/Header';
import { LocationApiService, CreateLocationRequest } from '@/app/utils/locationApi';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/app/components/navigation/dialog";

import ExpandableWarehouseTable from '@/app/components/tables/WarehouseTable/ExpandableWarehouseTable';
import { ViewWarehouseModal } from '@/app/components/modals/ViewWarehouseModal';
import { StandardizedTable, ViewMode } from '@/app/components/tables/StandardizedTable';
import { createWarehouseComplexColumns } from '@/app/components/tables/WarehouseTable/columns';
import { WarehouseColumnData, WarehouseTableActionsForDataTable } from '@/app/components/tables/WarehouseTable/types';
import { useTheme } from '@/app/contexts/ThemeContext';
import { WarehouseApiService } from '@/app/utils/warehouseApi';
import { motion } from 'framer-motion';
import {
    AlertTriangle,
    Building2,
    Plus,
    Table,
    Grid3X3
} from 'lucide-react';
import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { toast } from 'sonner';



/**
 * Warehouses page component
 * Displays a list of warehouses and allows users to create, edit, and delete warehouses
 * Includes filtering and search functionality
 */
const Warehouses: React.FC = () => {
  const { theme } = useTheme();
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [currentWarehouse, setCurrentWarehouse] = useState<WarehouseDisplayData | null>(null);
  const [viewWarehouse, setViewWarehouse] = useState<WarehouseDisplayData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [warehouses, setWarehouses] = useState<WarehouseDisplayData[]>([]);
  const [isLoadingWarehouses, setIsLoadingWarehouses] = useState(true);
  const [warehouseError, setWarehouseError] = useState<string | null>(null);

  const [stats, setStats] = useState<WarehouseStatsData>({
    total: 0,
    active: 0,
    inactive: 0,
    totalCapacity: 0,
    binTrackedCount: 0
  });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Fetch warehouses data
  const fetchWarehouses = useCallback(async () => {
    try {
      setIsLoadingWarehouses(true);
      setWarehouseError(null);
      const response = await WarehouseApiService.getAll();

      if (response.data && Array.isArray(response.data)) {
        const warehouseData = response.data.map((warehouse: any) => ({
          ...warehouse,
          status: warehouse.isActive !== undefined ? (warehouse.isActive ? 'active' : 'inactive') : 'active',
          capacityFormatted: warehouse.capacity ? `${warehouse.capacity.toLocaleString()} units` : 'N/A',
          createdAtFormatted: warehouse.createdAt ? new Date(warehouse.createdAt).toLocaleDateString() : 'N/A',
          updatedAtFormatted: warehouse.updatedAt ? new Date(warehouse.updatedAt).toLocaleDateString() : 'N/A',
        }));
        setWarehouses(warehouseData);

        // Update stats based on fetched data
        const newStats: WarehouseStatsData = {
          total: warehouseData.length,
          active: warehouseData.length, // Assuming all are active for now
          inactive: 0,
          totalCapacity: warehouseData.reduce((sum, w) => sum + (w.capacity || 0), 0),
          binTrackedCount: warehouseData.filter(w => w.isBinTracked).length
        };
        setStats(newStats);
        setIsLoadingStats(false);
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load warehouses';
      setWarehouseError(errorMessage);
      setWarehouses([]);
    } finally {
      setIsLoadingWarehouses(false);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    fetchWarehouses();
  }, [fetchWarehouses, refreshTrigger]);

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Open form modal for creating a new warehouse
  const openCreateModal = () => {
    setCurrentWarehouse(null);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Open form modal for editing an existing warehouse
  const handleEditWarehouse = (warehouse: WarehouseDisplayData) => {
    setCurrentWarehouse(warehouse);
    setIsFormModalOpen(true);
    setFormError(null);
  };

  // Close form modal
  const closeFormModal = () => {
    setIsFormModalOpen(false);
    setCurrentWarehouse(null);
    setFormError(null);
  };

  // Handle warehouse view
  const handleViewWarehouse = (warehouse: WarehouseDisplayData) => {
    setViewWarehouse(warehouse);
    setIsViewModalOpen(true);
  };

  // Close view modal
  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setViewWarehouse(null);
  };

  // Helper function to create locations for a warehouse
  const createWarehouseLocations = async (warehouseId: string, locations: any[]) => {
    const createdLocations = [];
    const errors = [];

    for (const location of locations) {
      try {
        // Skip locations that already have a real ID (not temp)
        if (location._id && !location._id.startsWith('temp-')) {
          continue;
        }

        const locationData: CreateLocationRequest = {
          warehouseId: warehouseId,
          name: location.name,
          description: location.description || null,
          locationType: location.locationType,
          capacity: location.capacity || null,
          isActive: location.isActive ?? true
        };

        const response = await LocationApiService.create(locationData);
        if (response.error) {
          errors.push(`Failed to create location "${location.name}": ${response.error}`);
        } else {
          createdLocations.push(response.data);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Failed to create location "${location.name}": ${errorMessage}`);
      }
    }

    return { createdLocations, errors };
  };

  // Handle warehouse form submission
  const handleWarehouseSubmit = useCallback(async (formData: WarehouseFormData) => {
    try {
      setIsSubmitting(true);
      setFormError(null);

      const apiData = transformFormDataToApiRequest(formData);

      if (currentWarehouse) {
        // Update existing warehouse
        const response = await WarehouseApiService.update(currentWarehouse.location_id, apiData);
        if (response.error) {
          throw new Error(response.error);
        }

        // Handle location updates for existing warehouse
        if (formData.locations && formData.locations.length > 0) {
          const { createdLocations, errors } = await createWarehouseLocations(
            currentWarehouse._id,
            formData.locations
          );

          if (errors.length > 0) {
            console.warn('Some locations failed to create:', errors);
            toast.warning(`Warehouse updated, but some locations failed: ${errors.join(', ')}`);
          } else if (createdLocations.length > 0) {
            toast.success(`Warehouse "${formData.name}" updated successfully with ${createdLocations.length} new locations`);
          } else {
            toast.success(`Warehouse "${formData.name}" updated successfully`);
          }
        } else {
          toast.success(`Warehouse "${formData.name}" updated successfully`);
        }
      } else {
        // Create new warehouse
        const response = await WarehouseApiService.create(apiData);
        if (response.error) {
          throw new Error(response.error);
        }

        const createdWarehouse = response.data;

        // Handle location creation for new warehouse
        if (createdWarehouse && !Array.isArray(createdWarehouse) && formData.locations && formData.locations.length > 0) {
          const { createdLocations, errors } = await createWarehouseLocations(
            createdWarehouse._id,
            formData.locations
          );

          if (errors.length > 0) {
            console.warn('Some locations failed to create:', errors);
            toast.warning(`Warehouse created, but some locations failed: ${errors.join(', ')}`);
          } else {
            toast.success(`Warehouse "${formData.name}" created successfully with ${createdLocations.length} locations`);
          }
        } else {
          toast.success(`Warehouse "${formData.name}" created successfully`);
        }
      }

      // Refresh the data
      setRefreshTrigger(prev => prev + 1);
      closeFormModal();
    } catch (error) {
      console.error('Error submitting warehouse:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save warehouse';
      setFormError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [currentWarehouse]);

  // Handle warehouse deletion
  const handleDeleteWarehouse = useCallback(async (warehouse: WarehouseDisplayData) => {
    if (!confirm(`Are you sure you want to delete warehouse "${warehouse.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await WarehouseApiService.delete(warehouse.location_id);
      if (response.error) {
        throw new Error(response.error);
      }

      toast.success(`Warehouse "${warehouse.name}" deleted successfully`);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Error deleting warehouse:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete warehouse';
      toast.error(errorMessage);
    }
  }, []);

  // Transform warehouses data for table display
  const tableData: WarehouseColumnData[] = useMemo(() => {
    return warehouses.map(warehouse => ({
      _id: warehouse._id,
      location_id: warehouse.location_id,
      name: warehouse.name,
      location: warehouse.location,
      capacity: warehouse.capacity,
      capacityFormatted: warehouse.capacityFormatted,
      manager: warehouse.manager,
      contact: warehouse.contact,
      isBinTracked: warehouse.isBinTracked,
      status: warehouse.status,
      createdAt: warehouse.createdAt,
      updatedAt: warehouse.updatedAt,
      createdAtFormatted: warehouse.createdAtFormatted,
      updatedAtFormatted: warehouse.updatedAtFormatted,
    }));
  }, [warehouses]);

  // Create table actions
  const tableActions: WarehouseTableActionsForDataTable = useMemo(() => ({
    onView: (warehouse: WarehouseColumnData) => {
      const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
      if (originalWarehouse) {
        handleViewWarehouse(originalWarehouse);
      }
    },
    onEdit: (warehouse: WarehouseColumnData) => {
      const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
      if (originalWarehouse) {
        handleEditWarehouse(originalWarehouse);
      }
    },
    onDelete: (warehouse: WarehouseColumnData) => {
      const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
      if (originalWarehouse) {
        handleDeleteWarehouse(originalWarehouse);
      }
    },
  }), [warehouses]);

  // Create columns for table view
  const columns = useMemo(() => {
    return createWarehouseComplexColumns(tableActions) as any;
  }, [tableActions]);

  // Wrapper component for ExpandableWarehouseTable to match StandardizedTable interface
  const ExpandableWarehouseTableWrapper = useCallback(({ data, searchTerm }: { data: WarehouseColumnData[], searchTerm: string }) => {
    return (
      <ExpandableWarehouseTable
        warehouses={data}
        onWarehouseClick={handleViewWarehouse}
        onWarehouseEdit={handleEditWarehouse}
        onWarehouseDelete={handleDeleteWarehouse}
        searchTerm={searchTerm}
        refreshTrigger={refreshTrigger}
      />
    );
  }, [refreshTrigger]);

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Warehouses" />

      <div className="px-8 pb-8">
        {/* Warehouse Statistics */}
        <WarehouseStats
          stats={stats}
          isLoading={isLoadingStats}
          className="mb-6"
        />

        <StandardizedTable
          data={tableData}
          columns={columns}
          searchPlaceholder="Search warehouses..."
          viewOptions={[
            { id: 'table', label: 'Table', icon: <Table className="h-4 w-4" /> },
            { id: 'detailed', label: 'Detailed', icon: <Grid3X3 className="h-4 w-4" /> },
          ]}
          defaultViewMode="detailed"
          enableSearch={true}
          enableViewToggle={true}
          DetailedComponent={ExpandableWarehouseTableWrapper}
          renderFilters={() => (
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-36 h-9">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          )}
          renderActions={() => (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="default" onClick={openCreateModal}>
                <Plus size={18} className="mr-1" />
                <span>Add Warehouse</span>
              </Button>
            </motion.div>
          )}
          // FIXED: Updated from legacy tableProps pattern to direct props pattern
          isLoading={isLoadingWarehouses}
          error={warehouseError ? new Error(warehouseError) : null}
          enableSorting={true}
          enableFiltering={true}
          enablePagination={true}
          enableGlobalSearch={false} // Using StandardizedTable's search instead
          enableColumnVisibility={false}
          mobileDisplayMode="cards"
          density="normal"
          initialPagination={{ pageIndex: 0, pageSize: 20 }}
          pageSizeOptions={[10, 20, 50, 100]}
          onRowClick={(row: any) => {
            const warehouse = warehouses.find(w => w._id === row._id);
            if (warehouse) {
              handleViewWarehouse(warehouse);
            }
          }}
        />
      </div>

      {/* Warehouse Form Modal */}
      <Dialog open={isFormModalOpen} onOpenChange={setIsFormModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="warehouse-dialog-description">
          <DialogHeader>
            <DialogTitle>
              {currentWarehouse ? `Edit Warehouse: ${currentWarehouse.name}` : 'Create New Warehouse'}
            </DialogTitle>
            <div id="warehouse-dialog-description" className="sr-only">
              {currentWarehouse
                ? 'Edit warehouse information including basic details, operations contact, and manage warehouse locations.'
                : 'Create a new warehouse by providing basic information, operations details, and optionally adding warehouse locations for inventory management.'
              }
            </div>
          </DialogHeader>

          <WarehouseForm
            initialData={currentWarehouse ? transformApiDataToFormData(currentWarehouse) : undefined}
            onSubmit={handleWarehouseSubmit}
            onClose={closeFormModal}
            isLoading={isSubmitting}
            error={formError}
            isEdit={!!currentWarehouse}
          />
        </DialogContent>
      </Dialog>

      {/* Warehouse View Modal */}
      {viewWarehouse && (
        <ViewWarehouseModal
          warehouse={viewWarehouse}
          isOpen={isViewModalOpen}
          onClose={closeViewModal}
        />
      )}
    </div>
  );
};

export default Warehouses;