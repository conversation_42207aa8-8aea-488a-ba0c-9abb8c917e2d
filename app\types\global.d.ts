/**
 * Global Type Declarations for Trend IMS
 * 
 * This file contains global type declarations that extend built-in types
 * and provide type safety for global objects and properties.
 */

// ============================================================================
// Window Object Extensions
// ============================================================================

/**
 * Environment variables interface for window.ENV
 */
interface EnvironmentVariables {
  SUPABASE_URL?: string;
  SUPABASE_ANON_KEY?: string;
  NODE_ENV?: string;
  API_BASE_URL?: string;
  [key: string]: string | undefined;
}

/**
 * Extend the global Window interface to include ENV property
 */
declare global {
  interface Window {
    ENV?: EnvironmentVariables;
  }
}

// ============================================================================
// Process Environment Extensions
// ============================================================================

/**
 * Extend NodeJS ProcessEnv interface for better type safety
 */
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test';
    MONGODB_URI: string;
    NEXTAUTH_SECRET: string;
    NEXTAUTH_URL: string;
    SUPABASE_URL?: string;
    SUPABASE_ANON_KEY?: string;
    API_BASE_URL?: string;
    [key: string]: string | undefined;
  }
}

// ============================================================================
// Module Declarations
// ============================================================================

/**
 * Declare modules for assets that don't have type definitions
 */
declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.jpg' {
  const content: any;
  export default content;
}

declare module '*.jpeg' {
  const content: any;
  export default content;
}

declare module '*.gif' {
  const content: any;
  export default content;
}

declare module '*.webp' {
  const content: any;
  export default content;
}

declare module '*.ico' {
  const content: any;
  export default content;
}

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.sass' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.less' {
  const content: { [className: string]: string };
  export default content;
}

// ============================================================================
// Third-Party Library Extensions
// ============================================================================

/**
 * Extend console interface for better debugging
 */
interface Console {
  debug(...args: any[]): void;
  trace(...args: any[]): void;
}

/**
 * Extend JSON interface for better type safety
 */
interface JSON {
  parse(text: string, reviver?: (this: any, key: string, value: any) => any): any;
  stringify(
    value: any,
    replacer?: (this: any, key: string, value: any) => any,
    space?: string | number
  ): string;
  stringify(
    value: any,
    replacer?: (number | string)[] | null,
    space?: string | number
  ): string;
}

// ============================================================================
// Custom Global Types
// ============================================================================

/**
 * Global error handler type
 */
type GlobalErrorHandler = (error: Error, errorInfo?: any) => void;

/**
 * Global configuration type
 */
interface GlobalConfig {
  apiBaseUrl: string;
  environment: 'development' | 'production' | 'test';
  features: {
    [key: string]: boolean;
  };
  version: string;
}

/**
 * Global utility types
 */
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

type Nullable<T> = T | null;

type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// ============================================================================
// API Response Types
// ============================================================================

/**
 * Generic API response wrapper
 */
interface ApiResponse<T = any> {
  data: T | null;
  error: string | null;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
    };
    duration?: number;
    timestamp?: string;
  };
}

/**
 * API error response
 */
interface ApiError {
  code: string;
  message: string;
  details?: any[];
  timestamp: string;
}

// ============================================================================
// Event Types
// ============================================================================

/**
 * Custom event types for application events
 */
interface CustomEventMap {
  'theme-changed': CustomEvent<{ theme: string; mode: string }>;
  'user-logged-in': CustomEvent<{ userId: string; email: string }>;
  'user-logged-out': CustomEvent<{}>;
  'data-updated': CustomEvent<{ entity: string; id: string }>;
  'error-occurred': CustomEvent<{ error: Error; context: string }>;
}

/**
 * Extend EventTarget to include custom events
 */
declare global {
  interface EventTarget {
    addEventListener<K extends keyof CustomEventMap>(
      type: K,
      listener: (this: EventTarget, ev: CustomEventMap[K]) => any,
      options?: boolean | AddEventListenerOptions
    ): void;
    
    removeEventListener<K extends keyof CustomEventMap>(
      type: K,
      listener: (this: EventTarget, ev: CustomEventMap[K]) => any,
      options?: boolean | EventListenerOptions
    ): void;
    
    dispatchEvent<K extends keyof CustomEventMap>(ev: CustomEventMap[K]): boolean;
  }
}

// Export empty object to make this a module
export {};
