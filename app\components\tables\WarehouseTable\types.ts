import { WarehouseDisplayData, WarehouseTableActions } from '@/app/components/forms/WarehouseForm/types';

/**
 * Props for the WarehouseTable component
 */
export interface WarehouseTableProps {
  /**
   * Whether to use a simplified version of the table
   */
  simple?: boolean;
  
  /**
   * Optional className for styling
   */
  className?: string;
  
  /**
   * Optional initial data for the table
   */
  initialData?: WarehouseDisplayData[];
  
  /**
   * Whether to fetch data from the API
   */
  fetchData?: boolean;
  
  /**
   * Optional callback for when a warehouse is clicked
   */
  onWarehouseClick?: (warehouse: WarehouseDisplayData) => void;
  
  /**
   * Optional callback for when a warehouse is edited
   */
  onWarehouseEdit?: (warehouse: WarehouseDisplayData) => void;
  
  /**
   * Optional callback for when a warehouse is deleted
   */
  onWarehouseDelete?: (warehouse: WarehouseDisplayData) => void;
  
  /**
   * Optional callback for when a warehouse is created
   */
  onWarehouseCreate?: () => void;

  /**
   * Optional callback for when a warehouse is viewed
   */
  onWarehouseView?: (warehouse: WarehouseDisplayData) => void;

  /**
   * Whether the table is in loading state
   */
  isLoading?: boolean;

  /**
   * Error message to display
   */
  error?: string | null;

  /**
   * Search term for filtering
   */
  searchTerm?: string;

  /**
   * Additional table actions
   */
  actions?: WarehouseTableActions;
}

/**
 * Column data interface for warehouse table
 * This represents the data structure expected by the DataTable columns
 */
export interface WarehouseColumnData {
  _id: string;
  location_id: string;
  name: string;
  location: string;
  capacity: number;
  capacityFormatted: string;
  manager: string;
  contact: string;
  isBinTracked: boolean;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
  createdAtFormatted: string;
  updatedAtFormatted: string;
}

/**
 * Interface for warehouse table actions (for DataTable)
 */
export interface WarehouseTableActionsForDataTable {
  onView?: (warehouse: WarehouseColumnData) => void;
  onEdit?: (warehouse: WarehouseColumnData) => void;
  onDelete?: (warehouse: WarehouseColumnData) => void;
}
