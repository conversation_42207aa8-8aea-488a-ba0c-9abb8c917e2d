/**
 * Unified Error Handling Types
 * Standardized error types for consistent error handling across the application
 */

export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  USER_INPUT = 'user_input'
}

export interface StandardError {
  id: string;                    // Unique error correlation ID
  code: string;                  // Error code for categorization
  message: string;               // User-friendly message
  details?: string;              // Technical details (dev only)
  severity: ErrorSeverity;       // Error severity level
  category: ErrorCategory;       // Error category
  context?: Record<string, any>; // Additional context
  timestamp: Date;               // When error occurred
  recoverable: boolean;          // Whether error is recoverable
  retryable: boolean;           // Whether retry is possible
}

export interface ErrorDisplayProps {
  error: StandardError | Error | string;
  variant?: 'default' | 'compact' | 'inline';
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

export interface ErrorAlertProps {
  error: StandardError | string;
  variant?: 'destructive' | 'warning' | 'info';
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export interface ErrorBannerProps {
  error: StandardError | string;
  actions?: Array<{
    label: string;
    action: () => void;
    variant?: 'default' | 'outline';
  }>;
  persistent?: boolean;
  className?: string;
}

export interface FormErrorDisplayProps {
  error: StandardError | string | null;
  field?: string;
  className?: string;
}

export interface InlineErrorProps {
  message: string;
  icon?: boolean;
  className?: string;
}

export interface ErrorToastProps {
  error: StandardError | string;
  duration?: number;
  action?: {
    label: string;
    action: () => void;
  };
}

export interface ErrorRecoveryProps {
  onRetry?: () => void;
  onAlternative?: () => void;
  retryLabel?: string;
  alternativeLabel?: string;
  loading?: boolean;
  disabled?: boolean;
}

export interface UseErrorHandlerOptions {
  onError?: (error: StandardError) => void;
  showToast?: boolean;
  logError?: boolean;
  category?: ErrorCategory;
}

export interface UseErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  onRetrySuccess?: () => void;
  onRetryFailure?: (error: StandardError) => void;
}

export interface ErrorLogEntry {
  id: string;
  timestamp: Date;
  error: StandardError;
  context: {
    userId?: string;
    sessionId: string;
    route: string;
    userAgent: string;
    component?: string;
    action?: string;
  };
  stack?: string;
  resolved?: boolean;
  resolutionTime?: number;
}

// Error message templates
export const ERROR_MESSAGES = {
  NETWORK: {
    TIMEOUT: 'Request timed out. Please check your connection and try again.',
    OFFLINE: 'You appear to be offline. Please check your connection.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    CONNECTION_FAILED: 'Failed to connect to server. Please try again.',
  },
  VALIDATION: {
    REQUIRED: 'This field is required.',
    INVALID_FORMAT: 'Please enter a valid {field}.',
    OUT_OF_RANGE: 'Value must be between {min} and {max}.',
    TOO_SHORT: 'Must be at least {min} characters long.',
    TOO_LONG: 'Must be no more than {max} characters long.',
  },
  PERMISSION: {
    ACCESS_DENIED: 'You do not have permission to perform this action.',
    LOGIN_REQUIRED: 'Please log in to continue.',
    INSUFFICIENT_PRIVILEGES: 'Insufficient privileges to access this resource.',
  },
  BUSINESS_LOGIC: {
    DUPLICATE_ENTRY: 'This {entity} already exists.',
    INVALID_OPERATION: 'This operation is not allowed in the current state.',
    RESOURCE_NOT_FOUND: 'The requested {resource} was not found.',
    OPERATION_FAILED: 'The operation could not be completed.',
  },
  SYSTEM: {
    INTERNAL_ERROR: 'An internal error occurred. Please try again later.',
    SERVICE_UNAVAILABLE: 'Service is temporarily unavailable. Please try again later.',
    MAINTENANCE_MODE: 'System is under maintenance. Please try again later.',
  },
  USER_INPUT: {
    INVALID_INPUT: 'Please check your input and try again.',
    MISSING_REQUIRED_FIELDS: 'Please fill in all required fields.',
    INVALID_FILE_TYPE: 'Please select a valid file type.',
    FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  }
} as const;

// Utility type for error message template keys
export type ErrorMessageTemplate = typeof ERROR_MESSAGES;
export type ErrorMessageCategory = keyof ErrorMessageTemplate;
export type ErrorMessageKey<T extends ErrorMessageCategory> = keyof ErrorMessageTemplate[T];
