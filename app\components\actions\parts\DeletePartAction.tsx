'use client';

import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { asApiResponse, extractApiError } from '@/app/types/api-responses';
import { Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface DeletePartActionProps {
  partId: string;
  partName: string;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Delete part action component
 */
function DeletePartAction({
  partId,
  partName,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: DeletePartActionProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!partId) {
      console.log('[DeletePartAction] partId is null:', partId);
      setIsDialogOpen(false);
      return;
    }

    setIsDeleting(true);

    try {
      // Ensure we're using a valid MongoDB _id format
      // MongoDB _id is typically a UUID string with hyphens
      // If the partId doesn't look like a UUID, log a warning
      if (!partId.includes('-')) {
        console.warn('[DeletePartAction] partId does not look like a MongoDB _id (UUID format):', partId);
      }

      console.log('[DeletePartAction] partId before fetch:', partId, 'type:', typeof partId);

      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);
      console.log('[DeletePartAction] encodedPartId for fetch:', encodedPartId);

      const response = await fetch(`/api/parts/${encodedPartId}`, {
        method: 'DELETE',
      });
      console.log('[DeletePartAction] response:', response);

      if (!response.ok) {
        console.log('[DeletePartAction] response not ok:', response.status, response.statusText);
        let errorMessage = `Failed to delete part. Status: ${response.status}`;
        try {
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const errorData = asApiResponse<any>(await response.json());
            console.log('[DeletePartAction] errorData:', errorData);
            const apiError = extractApiError(errorData);
            errorMessage = apiError || `Failed to delete part: ${(errorData as any).message || response.statusText}`;
          } else {
            const textError = await response.text();
            console.log('[DeletePartAction] non-JSON error response snippet:', textError.substring(0, 200));
            errorMessage = `Server error ${response.status}: ${response.statusText}. The part might not exist or there was an issue processing the request.`;
          }
        } catch (e) {
          console.error('[DeletePartAction] Error parsing error response:', e);
          errorMessage = `Failed to delete part (status ${response.status}) and couldn't parse the error response.`;
        }
        throw new Error(errorMessage);
      }

      console.log('[DeletePartAction] part deleted successfully');

      // Close dialog
      setIsDialogOpen(false);

      // Show success message
      toast.success(`Part "${partName}" deleted successfully`);

      // Refresh the page immediately
      router.refresh();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.log('[DeletePartAction] error deleting part:', error);
      console.error('Error deleting part:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred while deleting the part');
    } finally {
      setIsDeleting(false);
    }
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="destructive"
            size={size}
            onClick={() => setIsDialogOpen(true)}
            className={className}
          >
            <Trash2 size={16} className="mr-2" />
            Delete
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={() => setIsDialogOpen(true)}
            className={cn("h-8 px-2 hover:bg-destructive/10 hover:text-destructive", className)}
          >
            <Trash2 size={15} className="mr-1" />
            Delete
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setIsDialogOpen(true);
                  }}
                  className={cn("h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive", className)}
                  id={id || `delete-part-${partId}`}
                >
                  <Trash2 size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Delete Part</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Part"
        description={
          <>
            <p>Are you sure you want to delete the part <strong>"{partName}"</strong>?</p>
            <p className="mt-2 text-sm">This action cannot be undone. This will permanently delete the part and remove it from any assemblies that use it.</p>
          </>
        }
        confirmLabel="Delete Part"
        cancelLabel="Cancel"
        variant="destructive"
        isLoading={isDeleting}
      />
    </>
  );
}

// Export the component
export { DeletePartAction };
