import { NextRequest, NextResponse } from 'next/server';
import { searchCategories, handleMongoDBError } from '@/app/services/category.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for searching categories with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with categories data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/categories/search - Searching categories');
    const url = new URL(request.url);

    // Get search query
    const query = url.searchParams.get('query') || '';

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Additional filters
    const filter: any = {};
    
    // Parent category filter
    const parentCategoryFilter = url.searchParams.get('parentCategory');
    if (parentCategoryFilter) {
      if (parentCategoryFilter === 'null') {
        // Filter for top-level categories (no parent)
        filter.parentCategory = null;
      } else {
        // Filter for categories with specific parent
        filter.parentCategory = parentCategoryFilter;
      }
    }

    // Prepare options for service function
    const options = {
      query,
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling searchCategories service with options: ${JSON.stringify(options)}`);

    // Call service function
    const result = await searchCategories(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service searchCategories completed in ${duration}ms with ${result.categories.length} results`);

    // Return response
    return NextResponse.json({
      data: result.categories,
      pagination: result.pagination,
      error: null,
      meta: { duration, query }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/categories/search (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
} 