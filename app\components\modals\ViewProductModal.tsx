'use client';

import { Package, Eye } from 'lucide-react';

import { BaseViewModal } from './BaseViewModal';
import { ProductViewContent } from './content/ProductViewContent';
import { Product } from '@/app/components/tables/ProductsTable/types';
import { getBomTabConfig, getBomTabConfigAsync, type BomTabConfig } from '@/app/components/bom/bomIntegrationHelpers';
import { useState, useEffect } from 'react';
import { useAppContext } from '@/app/contexts/AppContext';
import { Button } from '@/app/components/forms/Button';

interface ViewProductModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  trigger?: React.ReactNode;
}

/**
 * Enhanced modal component for viewing product details with real part data
 * Uses async BOM configuration to fetch actual part names and details
 */
export function ViewProductModal({ product, isOpen, onClose, trigger }: ViewProductModalProps) {
  const [bomConfig, setBomConfig] = useState<BomTabConfig | null>(null);
  const [isLoadingBom, setIsLoadingBom] = useState(false);
  const { products: partsData } = useAppContext();

  // Fetch enhanced BOM configuration with real part data
  useEffect(() => {
    const fetchBomConfig = async () => {
      if (!product || !isOpen) return;

      setIsLoadingBom(true);
      try {
        console.log('[ViewProductModal] Fetching enhanced BOM configuration for product:', product.name);
        console.log('[ViewProductModal] Product data structure:', JSON.stringify(product, null, 2));

        console.log('[ViewProductModal] Calling getBomTabConfigAsync...');
        const enhancedBomConfig = await getBomTabConfigAsync(product, { type: 'product' });
        console.log('[ViewProductModal] getBomTabConfigAsync returned:', enhancedBomConfig);

        if (enhancedBomConfig && enhancedBomConfig.bomData.length > 0) {
          console.log('[ViewProductModal] Successfully fetched enhanced BOM configuration with', enhancedBomConfig.bomData.length, 'items');
          setBomConfig(enhancedBomConfig);
        } else {
          console.log('[ViewProductModal] Enhanced BOM config returned no data, using fallback');
          console.log('[ViewProductModal] enhancedBomConfig:', enhancedBomConfig);
          // Fallback to synchronous version if async returns no data
          const fallbackBomConfig = getBomTabConfig(product, { type: 'product', partsData });
          console.log('[ViewProductModal] Fallback BOM config:', fallbackBomConfig);
          setBomConfig(fallbackBomConfig);
        }
      } catch (error) {
        console.error('[ViewProductModal] Failed to fetch enhanced BOM configuration:', error);
        console.error('[ViewProductModal] Error details:', (error as Error).message, (error as Error).stack);
        // Fallback to synchronous version
        console.log('[ViewProductModal] Using synchronous fallback BOM configuration');
        const fallbackBomConfig = getBomTabConfig(product, { type: 'product', partsData });
        console.log('[ViewProductModal] Fallback BOM config:', fallbackBomConfig);
        setBomConfig(fallbackBomConfig);
      } finally {
        setIsLoadingBom(false);
      }
    };

    fetchBomConfig();
  }, [product, isOpen]);

  // Determine if content will be in tab mode (when BOM data exists)
  const isInTab = bomConfig?.bomData ? true : false;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={product.name}
      subtitle="Product Details"
      icon={<Package className="h-5 w-5 text-primary" />}
      {...(bomConfig || {})}
    >
      <ProductViewContent product={product} isInTab={isInTab} />
    </BaseViewModal>
  );
}

interface ViewProductButtonProps {
  product: Product;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  /**
   * Custom button content (overrides default content)
   */
  buttonContent?: React.ReactNode;
  /**
   * Children to render inside the button
   */
  children?: React.ReactNode;
}

/**
 * Button component that opens the ViewProductModal
 * Uses the enhanced ViewProductModal which fetches real part data
 */
export function ViewProductButton({
  product,
  variant = 'default',
  size = 'sm',
  className,
  buttonContent,
  children
}: ViewProductButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const defaultButtonContent = (
    <>
      <Eye className="h-4 w-4 mr-1" />
      View
    </>
  );

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsOpen(true);
        }}
        className={className || ''}
      >
        {buttonContent || children || defaultButtonContent}
      </Button>
      <ViewProductModal
        product={product}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
}
