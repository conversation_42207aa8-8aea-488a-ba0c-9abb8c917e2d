/**
 * Utility functions for analytics
 */

/**
 * Maps analytics type IDs to their corresponding data property names
 * @param type - The analytics type ID
 * @returns The corresponding data property name
 */
export function analyticsTypeMap(type: string): string {
  const mapping: Record<string, string> = {
    'inventory-trends': 'inventoryTrends',
    'stock-levels': 'stockLevels',
    'category-distribution': 'categoryDistribution',
    'inventory-value': 'inventoryValue'
  };

  return mapping[type] || '';
}

/**
 * Gets the chart data key based on analytics type
 * @param type - The analytics type ID
 * @returns The data key to use for charts
 */
export function getChartDataKey(type: string): string {
  const mapping: Record<string, string> = {
    'inventory-trends': 'value',
    'stock-levels': 'value',
    'category-distribution': 'value',
    'inventory-value': 'value'
  };

  return mapping[type] || 'value';
}

/**
 * Gets the chart title based on analytics type
 * @param type - The analytics type ID
 * @returns The chart title
 */
export function getChartTitle(type: string): string {
  const mapping: Record<string, string> = {
    'inventory-trends': 'Inventory Trends Over Time',
    'stock-levels': 'Current Stock Levels',
    'category-distribution': 'Distribution of Parts by Category',
    'inventory-value': 'Inventory Value by Category'
  };

  return mapping[type] || 'Analytics Chart';
}
