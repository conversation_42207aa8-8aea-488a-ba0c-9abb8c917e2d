'use client';

import { Badge } from '@/app/components/data-display/badge';
import { TableCell, TableRow } from '@/app/components/data-display/table';
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { cn } from '@/app/lib/utils';
import { AlertCircle, CheckCircle2, Layers } from 'lucide-react';
import React, { useState } from 'react';

interface ExpandableRowProps {
  assembly: Assembly;
  children: React.ReactNode;
  colSpan: number;
}

/**
 * Expandable row component for the assemblies table
 */
export function ExpandableRow({ assembly, children, colSpan }: ExpandableRowProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Check if assembly has valid parts
  const hasValidParts = assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0; // Changed from assembly.parts
  
  return (
    <>
      <TableRow
        className={cn(
          "cursor-pointer hover:bg-muted/50 transition-colors duration-200",
          isExpanded && "bg-muted/50 border-b-0"
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {children}
      </TableRow>
      
      {isExpanded && (
        <TableRow className="bg-muted/30">
          <TableCell colSpan={colSpan} className="p-0">
            <div className="p-4 border-t border-border/50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Assembly Details</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">ID:</span>
                      <Badge variant="outline" className="font-mono">
                        {assembly.assemblyCode} {/* Changed from assembly.assembly_id */}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">Status:</span>
                      <AssemblyStatusBadge assembly={assembly} showLabel={true} size="sm" />
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium text-muted-foreground w-24">Parts:</span>
                      <PartsCountBadge assembly={assembly} size="sm" />
                    </div>
                    {assembly.description && (
                      <div className="flex gap-2">
                        <span className="text-xs font-medium text-muted-foreground w-24">Description:</span>
                        <span className="text-xs">{assembly.description}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Parts List</h4>
                  {hasValidParts ? (
                    <div className="max-h-40 overflow-y-auto pr-2">
                      <table className="w-full text-xs">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left font-medium py-1 px-2">Part Name</th>
                            <th className="text-center font-medium py-1 px-2 w-20">Quantity</th>
                            <th className="text-center font-medium py-1 px-2 w-20">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {assembly.partsRequired?.map((part, index) => {
                            const partDetail = part.partDetails; // Use partDetails
                            const quantity = part.quantityRequired || 1; // Use quantityRequired
                            
                            return (
                              <tr key={index} className="border-b border-border/50">
                                {partDetail ? (
                                  <>
                                    <td className="py-1 px-2">{partDetail.name || 'Unnamed Part'}</td>
                                    <td className="py-1 px-2 text-center">{quantity}x</td>
                                    <td className="py-1 px-2 text-center">
                                      {(partDetail.inventory && partDetail.inventory.stockLevels?.finished !== undefined && partDetail.inventory.stockLevels.finished >= quantity) ? (
                                        <span className="inline-flex items-center text-green-600">
                                          <CheckCircle2 size={12} className="mr-1" />
                                          Available
                                        </span>
                                      ) : (partDetail.inventory && partDetail.inventory.stockLevels?.finished !== undefined && partDetail.inventory.stockLevels.finished > 0 && partDetail.inventory.stockLevels.finished < quantity) ? (
                                        <span className="inline-flex items-center text-yellow-600">
                                          <AlertCircle size={12} className="mr-1" />
                                          Partial ({partDetail.inventory.stockLevels.finished})
                                        </span>
                                      ) : (
                                        <span className="inline-flex items-center text-amber-600">
                                          <AlertCircle size={12} className="mr-1" />
                                          Unavailable
                                        </span>
                                      )}
                                    </td>
                                  </>
                                ) : (
                                  <>
                                    <td className="py-1 px-2 text-red-500">Part ID: {part.partId} (Details missing)</td>
                                    <td className="py-1 px-2 text-center">{quantity}x</td>
                                    <td className="py-1 px-2 text-center">
                                      <span className="inline-flex items-center text-red-600">
                                        <AlertCircle size={12} className="mr-1" />
                                        Invalid
                                      </span>
                                    </td>
                                  </>
                                )}
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-4 text-muted-foreground">
                      <Layers size={24} className="mb-2" />
                      <p className="text-xs">No parts defined</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
}
