"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { BOMItem, Product } from '@/app/types';
import { ChevronDown, ChevronRight, Info, Layers, Package, Wrench } from 'lucide-react';
import React, { useState } from 'react';

interface ProductNode {
  product: Product;
  children: ProductNode[];
  quantity: number;
  level: number;
}

interface ProductHierarchyProps {
  rootProduct: Product;
  bomItems: BOMItem[];
  products: Product[];
}

const ProductHierarchy: React.FC<ProductHierarchyProps> = ({ rootProduct, bomItems, products }) => {
  const { theme } = useTheme();

  // Build the product hierarchy tree
  const buildProductTree = (productId: string, level: number = 0): ProductNode | null => {
    const product = products.find(p => p.id === productId);
    if (!product) return null;

    const children: ProductNode[] = [];
    const childBomItems = bomItems.filter(item => item.parent_id === productId);

    for (const bomItem of childBomItems) {
      if (!bomItem.child_id) continue;
      const childNode = buildProductTree(bomItem.child_id, level + 1);
      if (childNode) {
        childNode.quantity = bomItem.quantity;
        children.push(childNode);
      }
    }

    return {
      product,
      children,
      quantity: 1, // Root product has quantity 1
      level
    };
  };

  const productTree = buildProductTree(rootProduct.id);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-md dark:shadow-gray-900/30">
      <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">Product Structure</h3>
      {productTree ? (
        <div className="mt-4">
          <TreeNode node={productTree} />
        </div>
      ) : (
        <div className="text-gray-500 dark:text-gray-400 italic">
          No product structure available
        </div>
      )}
    </div>
  );
};

interface TreeNodeProps {
  node: ProductNode;
}

const TreeNode: React.FC<TreeNodeProps> = ({ node }) => {
  const [expanded, setExpanded] = useState(true);
  const { theme } = useTheme();

  const getIcon = (assemblyStage?: string | null) => {
    switch (assemblyStage) {
      case 'Final Assembly':
        return <Package className="text-blue-500 dark:text-blue-400" />;
      case 'Sub-Assembly':
        return <Layers className="text-green-500 dark:text-green-400" />;
      case 'Component':
        return <Wrench className="text-yellow-500 dark:text-yellow-400" />;
      default:
        return <Info className="text-gray-500 dark:text-gray-400" />;
    }
  };

  const getBgColor = (level: number) => {
    if (theme.isDark) {
      switch (level) {
        case 0: return 'bg-gray-750';
        case 1: return 'bg-gray-800';
        case 2: return 'bg-gray-850';
        default: return 'bg-gray-900';
      }
    } else {
      switch (level) {
        case 0: return 'bg-gray-50';
        case 1: return 'bg-gray-100';
        case 2: return 'bg-gray-150';
        default: return 'bg-gray-200';
      }
    }
  };

  return (
    <div className="mb-2">
      <div
        className={`flex items-center p-3 rounded-lg ${getBgColor(node.level)} hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer`}
        onClick={() => setExpanded(!expanded)}
      >
        <div className="mr-2">
          {node.children.length > 0 ? (
            expanded ? (
              <ChevronDown size={16} className="text-gray-500 dark:text-gray-400" />
            ) : (
              <ChevronRight size={16} className="text-gray-500 dark:text-gray-400" />
            )
          ) : (
            <div className="w-4"></div>
          )}
        </div>

        <div className="mr-3">
          {getIcon(node.product.assemblyStage)}
        </div>

        <div className="flex-1">
          <div className="flex items-center">
            <span className="font-medium text-gray-800 dark:text-gray-100">{node.product.name}</span>
            {node.quantity > 1 && (
              <span className="ml-2 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-0.5 rounded-full">
                x{node.quantity}
              </span>
            )}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {node.product.id} {node.product.assemblyStage && `(${node.product.assemblyStage})`}
          </div>
        </div>

        {node.product.currentStock !== undefined && (
          <div className="text-sm">
            <span className="text-gray-500 dark:text-gray-400">Stock: </span>
            <span className={`font-medium ${
              (node.product.currentStock || 0) <= (node.product.reorderLevel || 0)
                ? 'text-red-600 dark:text-red-400'
                : 'text-green-600 dark:text-green-400'
            }`}>
              {node.product.currentStock}
            </span>
          </div>
        )}
      </div>

      {expanded && node.children.length > 0 && (
        <div className="pl-8 mt-2 border-l-2 border-gray-200 dark:border-gray-700">
          {node.children.map((child, index) => (
            <TreeNode key={child.product._id || `child-${index}`} node={child} />
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductHierarchy;