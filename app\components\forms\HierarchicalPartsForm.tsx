'use client';

import { getApiUrl } from "@/app/utils/apiUtils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import { Control, SubmitHandler, useFieldArray, useForm, UseFormReturn, useWatch } from "react-hook-form";
import { showValidationErrorToast } from '@/app/components/feedback';
import { FormErrorDisplay, InlineFormError } from '@/app/components/feedback/FormErrorDisplay';
import { LoadingInline, LoadingCard } from '@/app/components/data-display/loading';
import { z } from "zod";

// UI Components
import { Badge } from "@/app/components/data-display/badge";
import { Button } from "@/app/components/forms/Button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/app/components/forms/Form";
import { Input } from "@/app/components/forms/Input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/layout/cards/card";

// Search and Types
import { PartSearchResult } from "@/app/components/search/PartSearch";
import { FormPartData, HierarchicalPartsFormProps } from "@/app/types/form.types";

// Icons (specific imports for better tree-shaking)
import { ChevronDown, ChevronRight, Layers, Loader2, Package, Plus, PlusCircle, Search, Trash2 } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';

// Minimal Part interface (if not already globally defined or imported)
// This is for selectedPart in handlePartSelect if it can be a more detailed object than PartSearchResult
interface Part {
    _id?: string;
    partId: string; // This is usually the user-facing SKU or part number
    name: string;
    description?: string;
    partNumber?: string; // Can be same as partId or a more specific manuf. part number
    currentStock?: number;
    inventory?: {
        currentStock?: number;
        minimumStockLevel?: number;
    };
    unitOfMeasure?: string;
    category?: string;
    isAssembly?: boolean;
    // Add other fields that a full "Part" object might have if different from PartSearchResult
}

// FormPartData interface imported from form.types.ts

// Define the allowed assembly statuses
type AssemblyStatusType = "active" | "pending_review" | "design_phase" | "design_complete" | "obsolete";

// Simplified AssemblyFormData for core functionality
interface AssemblyFormData {
    _id?: string;
    name: string;
    assemblyId: string;
    description?: string; // Optional
    category?: string | null; // Optional
    status: AssemblyStatusType; // Use the defined type
    version?: number; // Optional
    notes?: string; // Optional
    partsRequired: FormPartData[];
    // Keep Quick Edit specific fields if they are truly on initialData
    assemblyYield?: number;
    estimatedManufacturingTime?: string;
    assembly_stage?: string;
    // Minimal deprecated fields for compatibility if absolutely needed by the type
    assembly_id?: string;
    assembly_name?: string;
    // Add index signature for compatibility
    [key: string]: any;
}

const defaultValues: AssemblyFormData = {
    name: '',
    assemblyId: '',
    description: '',
    category: null,
    status: 'active', // Ensure this is a valid AssemblyStatusType
    version: 1,
    notes: '',
    partsRequired: [],
    assemblyYield: 0,
    estimatedManufacturingTime: '',
    assembly_stage: '',
    assembly_id: '',
    assembly_name: '',
};

// Update partSchema and all usages to match the canonical interface
type PartSchemaType = z.ZodType<FormPartData>;

const partSchema: PartSchemaType = z.lazy(() => z.object({
    _id: z.string().optional(),
    id: z.string().optional(), // For RHF key
    partId: z.string().min(1, "Part ID is required"),
    name: z.string().min(1, "Part name is required"),
    description: z.string().optional(),
    quantityRequired: z.number().min(0.000001, "Quantity must be greater than 0"),
    isExpanded: z.boolean().default(true),
    category: z.string().optional().nullable(),
    currentStock: z.number().optional().nullable(), // Allow null
    reorderLevel: z.number().optional().nullable(),
    minimumStockLevel: z.number().optional().nullable(),
    supplier: z.string().optional().nullable(),
    technicalSpecs: z.string().optional().nullable(),
    unitOfMeasure: z.string().optional().nullable(),
    partDisplayIdentifier: z.string().optional(),
    isAssembly: z.boolean().optional().nullable(),
    partNumber: z.string().optional().nullable(),
    cost: z.number().optional().nullable(),
    location: z.string().optional().nullable(),
    additionalAttributes: z.record(z.any()).optional().nullable(),
    children: z.array(partSchema).optional(),
}).passthrough()) as PartSchemaType;

// Define the main form validation schema - made more lenient to prevent hanging
const hierarchicalFormSchema = z.object({
    assemblyCode: z.string()
        .transform(val => val?.trim() || '') // Auto-trim and handle undefined
        .refine(val => val.length > 0, { message: "Assembly code is required" })
        .refine(val => val.length <= 50, { message: "Assembly code cannot exceed 50 characters" }),
    name: z.string()
        .transform(val => val?.trim() || '') // Auto-trim and handle undefined
        .refine(val => val.length > 0, { message: "Assembly name is required" })
        .refine(val => val.length <= 100, { message: "Assembly name cannot exceed 100 characters" }),
    description: z.string()
        .optional()
        .nullable()
        .transform(val => val?.trim() || null), // Handle null and undefined
    status: z.enum(["active", "pending_review", "design_phase", "design_complete", "obsolete"])
        .default("active")
        .catch("active"), // Fallback to active if invalid
    productId: z.string().nullable().optional().default(null),
    parentId: z.string().nullable().optional().default(null),
    isTopLevel: z.boolean().default(true).catch(true), // Fallback to true
    version: z.number()
        .min(1, { message: "Version must be at least 1" })
        .default(1)
        .catch(1), // Fallback to 1 if invalid
    manufacturingInstructions: z.string().nullable().optional().default(null),
    estimatedBuildTime: z.string().nullable().optional().default(null),
    partsRequired: z.array(partSchema)
        .optional()
        .default([])
        .catch([]), // Fallback to empty array if validation fails
}).passthrough(); // Allow additional fields to prevent strict validation errors

// TypeScript type for the form values
export type HierarchicalFormValues = z.infer<typeof hierarchicalFormSchema>;

// TypeScript types for the component props imported from form.types.ts



// PartField component definition
interface PartFieldProps {
    field: any; // Use 'any' to avoid linter constraint error and allow rhfId
    nestLevel: number;
    control: Control<HierarchicalFormValues>;
    removeCurrentPart: () => void;
    pathPrefix: string;
    form: UseFormReturn<HierarchicalFormValues>;
}

const PartField: React.FC<PartFieldProps> = ({
    field,
    nestLevel,
    control,
    removeCurrentPart,
    pathPrefix,
    form,
}) => {
    console.log(`[PartField RENDER] Path: ${pathPrefix}, RHF ID: ${field.id}, Name: ${field.name}`);
    console.log(`[PartField Prop Check] field.currentStock for ${field.partId} (RHF ID: ${field.id}):`, field.currentStock);

    const watchedPart = useWatch({
        control,
        name: pathPrefix as any
    }) as FormPartData | undefined;

    console.log(`[PartField Watched Value] useWatch('${pathPrefix}').currentStock for ${field.partId} (RHF ID: ${field.id}):`, watchedPart?.currentStock);

    const { fields: childFields, append: appendChild, remove: removeChild } = useFieldArray({
        control: control as any,
        name: `${pathPrefix}.children` as any,
        keyName: "rhfId"
    });

    // State and logic for suggestions
    const [isSuggestionsVisible, setIsSuggestionsVisible] = useState(false);
    const [suggestions, setSuggestions] = useState<PartSearchResult[]>([]);
    const [isSuggestionsLoading, setIsSuggestionsLoading] = useState(false);
    const suggestionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const suggestionsContainerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    const fetchPartSuggestions = useCallback(async (query: string) => {
        console.log('[HierarchicalPartsForm] fetchPartSuggestions called with query:', query);
        if (query.length < 3) {
            console.log('[HierarchicalPartsForm] Query too short, clearing suggestions');
            setSuggestions([]);
            setIsSuggestionsLoading(false);
            return;
        }
        setIsSuggestionsLoading(true);
        try {
            const searchParams = new URLSearchParams({ search: query, limit: '10' });
            const apiUrl = getApiUrl(`/api/parts/search?${searchParams.toString()}`);
            console.log('[HierarchicalPartsForm] Making API request to:', apiUrl);
            const response = await fetch(apiUrl);
            if (!response.ok) throw new Error(`Search failed: ${response.statusText || 'Unknown error'}`);
            const data = await response.json() as { data?: PartSearchResult[] };
            console.log('[HierarchicalPartsForm] API response:', data);
            if (data.data && data.data.length > 0) {
                setSuggestions(data.data);
                console.log('[HierarchicalPartsForm] Set suggestions:', data.data.length);
            } else {
                setSuggestions([]);
                console.log('[HierarchicalPartsForm] No suggestions found');
            }
        } catch (error) {
            console.error("Error fetching part suggestions:", error);
            setSuggestions([]);
        } finally {
            setIsSuggestionsLoading(false);
        }
    }, []);

    const localHandlePartInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const query = e.target.value;
        console.log('[HierarchicalPartsForm] localHandlePartInputChange called with query:', query);
        form.setValue(pathPrefix + '.partDisplayIdentifier' as any, query);

        if (suggestionTimeoutRef.current) {
            clearTimeout(suggestionTimeoutRef.current);
            console.log('[HierarchicalPartsForm] Cleared previous timeout');
        }

        if (query.length < 3) {
            console.log('[HierarchicalPartsForm] Query too short, hiding suggestions');
            setIsSuggestionsLoading(false);
            setSuggestions([]);
            setIsSuggestionsVisible(false);
            return;
        }

        console.log('[HierarchicalPartsForm] Setting suggestions visible and scheduling fetch');
        setIsSuggestionsVisible(true);

        // Use simple timeout instead of debounce for debugging
        suggestionTimeoutRef.current = setTimeout(() => {
            console.log('[HierarchicalPartsForm] Timeout triggered, calling fetchPartSuggestions');
            fetchPartSuggestions(query);
        }, 300);
    }, [fetchPartSuggestions, form, pathPrefix]);

    const handlePartSelect = (selectedPart: PartSearchResult | Part) => {
        const currentFormValues = form.getValues(pathPrefix as any) as FormPartData;

        const selectedPartTrueId = (selectedPart as PartSearchResult)._id || (selectedPart as Part)._id;
        // Assuming PartSearchResult uses 'partNumber' as the field corresponding to our 'partId' (user-facing SKU)
        // and '_id' as its own database ID from the search collection.
        const selectedPartUserFacingId = (selectedPart as PartSearchResult).partNumber || (selectedPart as Part).partId;
        const selectedPartCurrentStock = (selectedPart as Part).inventory?.currentStock ?? 0;

        let stockToUse = selectedPartCurrentStock;

        if (watchedPart && selectedPartUserFacingId && watchedPart.partId === selectedPartUserFacingId &&
            (selectedPartCurrentStock === 0) &&
            (watchedPart.currentStock !== undefined && watchedPart.currentStock !== null && watchedPart.currentStock > 0)
        ) {
            stockToUse = watchedPart.currentStock;
        }

        const partIdToUse = selectedPartTrueId || currentFormValues?.partId;
        const nameToUse = selectedPart.name || currentFormValues?.name;
        const descriptionToUse = selectedPart.description || currentFormValues?.description;
        // partNumber in FormPartData can be the same as partIdToUse if not specified otherwise
        const partNumberToUseInForm = selectedPart.partNumber || currentFormValues?.partNumber || partIdToUse;

        const updatedValues = {
            ...currentFormValues,
            _id: currentFormValues?._id || selectedPartTrueId,
            partId: partIdToUse, // This is now the MongoDB ObjectId
            name: nameToUse,
            description: descriptionToUse,
            partNumber: partNumberToUseInForm, // This could be a more specific manufacturer part number
            partDisplayIdentifier: `${nameToUse} (${partNumberToUseInForm || partIdToUse})`,
            currentStock: stockToUse,
            children: (currentFormValues?.partId && partIdToUse !== currentFormValues.partId) ? [] : currentFormValues?.children || [],
            isExpanded: (currentFormValues?.partId && partIdToUse !== currentFormValues.partId) ? true : currentFormValues?.isExpanded || false,
            // id field for RHF is field.rhfId, not part of this data structure
        };

        // Check if the selected part's stock data might be stale (keep this for production monitoring)
        const isFromSearch = !!(selectedPart as any).inventory?.currentStock || !!(selectedPart as any)._searchResult;

        form.setValue(pathPrefix as any, updatedValues, { shouldValidate: true, shouldDirty: true });

        setIsSuggestionsVisible(false); // Hide suggestions after selection
        // Clear search query if part is selected
        // This depends on how PartQuickSearch handles its internal state vs. form state
        const partSearchQueryPath = `${pathPrefix}.partSearchQuery`;
        if (form.getValues(partSearchQueryPath as any)) {
            form.setValue(partSearchQueryPath as any, "", { shouldDirty: true });
        }

        // Fetch full part details if only partial data was provided (e.g. from search)
        // This might be redundant if selectedPart from search is comprehensive enough
        // Ensure this fetch doesn't overwrite the currentStock we just carefully set.
        /*
        fetchPartDetails(partIdToUse, pathPrefix).then(details => {
            if (details) {
                const currentData = form.getValues(pathPrefix as any) as FormPartData;
        form.setValue(pathPrefix as any, {
                    ...currentData,
                    name: details.name || currentData.name,
                    description: details.description || currentData.description,
                    partNumber: details.partNumber || currentData.partNumber,
                    // IMPORTANT: Do not overwrite currentStock here unless details.inventory.currentStock is more authoritative
                    // currentStock: details.inventory?.currentStock ?? currentData.currentStock,
                    partDisplayIdentifier: `${details.name || currentData.name} (${details.partNumber || currentData.partNumber || currentData.partId})`,
                    unit: details.unit || currentData.unit,
                    // supplier: details.supplier || currentData.supplier, // Example: if supplier is part of details
                    // category: details.category || currentData.category, // Example: if category is part of details
                }, { shouldValidate: true, shouldDirty: true });
            }
        }).catch(error => {
            console.error("[PartField] Error processing part selection:", error);
        });
        */
    };

    useEffect(() => {
        function handleEscKey(event: KeyboardEvent) {
            if (event.key === 'Escape') {
                setSuggestions([]);
                setIsSuggestionsVisible(false);
            }
        }

        function handleClickOutside(event: MouseEvent) {
            if (suggestionsContainerRef.current && !suggestionsContainerRef.current.contains(event.target as Node) &&
                inputRef.current && !inputRef.current.contains(event.target as Node)) {
                setSuggestions([]);
                setIsSuggestionsVisible(false);
            }
        }

        document.addEventListener('keydown', handleEscKey);
        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const LocalSuggestionDropdown = () => {
        if (!isSuggestionsVisible || (suggestions.length === 0 && !isSuggestionsLoading)) {
            return null;
        }
        return (
            <div
                ref={suggestionsContainerRef}
                className="absolute z-50 mt-1 w-full bg-card dark:bg-card shadow-lg rounded-md border border-border max-h-60 overflow-y-auto"
                style={{ top: '100%', left: 0, right: 0 }}
            >
                {isSuggestionsLoading ? (
                    <div className="p-3 flex items-center justify-center">
                        <Loader2 className="h-4 w-4 animate-spin mr-2 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">Searching parts...</span>
                    </div>
                ) : (
                    <ul className="py-1">
                        {suggestions.map((part) => (
                            <li
                                key={part._id}
                                className="px-3 py-2 hover:bg-accent dark:hover:bg-accent/20 cursor-pointer transition-colors"
                                onClick={() => handlePartSelect(part)}
                            >
                                <div className="flex justify-between items-center">
                                    <div>
                                        <div className="font-medium text-foreground">{part.businessName || part.name}</div>
                                        {part.businessName && (
                                            <div className="text-xs text-muted-foreground italic">{part.name}</div>
                                        )}
                                        <div className="text-xs text-muted-foreground">ID: {part.partNumber || part._id}</div>
                                    </div>
                                    <div className={`text-xs px-2 py-1 rounded ${part.inventory?.currentStock ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'}`}>
                                        Stock: {part.inventory?.currentStock ?? 0}
                                    </div>
                                </div>
                                {part.description && (
                                    <div className="text-xs text-muted-foreground mt-1 truncate">{part.description}</div>
                                )}
                            </li>
                        ))}
                    </ul>
                )}
            </div>
        );
    };

    const handleAddChildToCurrentPart = () => {
        appendChild({
            id: uuidv4(),
            partId: "", name: "", description: "", quantityRequired: 1,
            isExpanded: true, partDisplayIdentifier: "", children: []
        } as FormPartData);
        form.setValue(pathPrefix + '.isExpanded' as any, true);
    };

    const hasChildren = watchedPart?.children && watchedPart.children.length > 0;

    // Get current stock value for rendering
    const stockAtRenderPoint = watchedPart?.currentStock;

    return (
        <div
            key={field.rhfId}
            className={`part-item rounded-lg border border-border/40 p-4 mb-3 transition-all ${nestLevel > 0 ? 'ml-6' : ''} ${watchedPart?.isAssembly ? 'bg-blue-50/50 dark:bg-blue-950/20' : 'bg-card'}`}
        >
            <div className="part-header flex flex-col gap-4 md:flex-row md:items-end">
                <div className="flex-1 space-y-4">
                    <div className="flex items-center gap-2">
                        {hasChildren && (
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => form.setValue(pathPrefix + '.isExpanded' as any, !watchedPart.isExpanded)}
                                className="h-6 w-6 p-0 flex items-center justify-center rounded-full hover:bg-accent/50 transition-colors"
                            >
                                {watchedPart.isExpanded ?
                                    <ChevronDown size={18} className="text-muted-foreground" /> :
                                    <ChevronRight size={18} className="text-muted-foreground" />
                                }
                            </Button>
                        )}

                        {watchedPart?.isAssembly && (
                            <div className="text-xs font-medium bg-blue-100 dark:bg-blue-900/40 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full flex items-center">
                                <Layers size={12} className="mr-1" />
                                Assembly
                            </div>
                        )}

                        <div className="flex items-center space-x-2">
                            {(() => {
                                if (watchedPart && typeof watchedPart.currentStock === 'number') {
                                    if (watchedPart.currentStock <= 0) {
                                        return <Badge variant="destructive" className="ml-2 text-xs">Out of Stock</Badge>;
                                    }
                                }
                                return null;
                            })()}
                            {watchedPart && typeof watchedPart.currentStock === 'number' && watchedPart.currentStock > 0 && watchedPart.minimumStockLevel != null && watchedPart.currentStock < watchedPart.minimumStockLevel && (
                                <Badge variant="warning" className="ml-2 text-xs">Low Stock</Badge>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name={pathPrefix + '.partDisplayIdentifier' as any}
                            render={({ field: partDisplayField }) => (
                                <FormItem className="relative">
                                    <FormLabel className="text-xs font-medium text-muted-foreground">Part ID or Search</FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                {...partDisplayField}
                                                value={String(partDisplayField.value || '')}
                                                placeholder="Enter part code or search..."
                                                className="pl-8 bg-background border-input focus-visible:ring-1 focus-visible:ring-ring"
                                                onChange={(e) => {
                                                    partDisplayField.onChange(e); // Call React Hook Form's onChange
                                                    localHandlePartInputChange(e); // Call our custom onChange
                                                }}
                                                onFocus={() => setIsSuggestionsVisible(true)}
                                                autoComplete="off"
                                                ref={inputRef}
                                            />
                                            <LocalSuggestionDropdown />
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name={pathPrefix + '.name' as any}
                            render={({ field: formFieldRender }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-medium text-muted-foreground">Part Name</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="Part Name"
                                            {...formFieldRender}
                                            value={String(formFieldRender.value || '')}
                                            readOnly
                                            className="bg-muted/50 border-border/30 text-sm p-2 rounded-md w-full cursor-not-allowed"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                <div className="flex flex-row gap-4 items-end">
                    <FormField
                        control={form.control}
                        name={pathPrefix + '.quantityRequired' as any}
                        render={({ field: formFieldRender }) => (
                            <FormItem className="w-24">
                                <FormLabel className="text-xs font-medium text-muted-foreground">Quantity</FormLabel>
                                <FormControl>
                                    <Input
                                        {...formFieldRender}
                                        type="number"
                                        min={1}
                                        value={Number(formFieldRender.value || 1)}
                                        onChange={e => formFieldRender.onChange(Number(e.target.value))}
                                        className="bg-background border-input focus-visible:ring-1 focus-visible:ring-ring"
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name={pathPrefix + '.unitOfMeasure' as any}
                        render={({ field: uomField }) => (
                            <FormItem className="w-28">
                                <FormLabel className="text-xs font-medium text-muted-foreground">Unit</FormLabel>
                                <Select
                                    value={String(uomField.value || 'ea')}
                                    onValueChange={val => uomField.onChange(val)}
                                >
                                    <FormControl>
                                        <SelectTrigger className="bg-background border-input focus-visible:ring-1 focus-visible:ring-ring">
                                            <SelectValue placeholder="Unit" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        <SelectItem value="ea">Each (ea)</SelectItem>
                                        <SelectItem value="pcs">Pieces (pcs)</SelectItem>
                                        <SelectItem value="kg">Kilogram (kg)</SelectItem>
                                        <SelectItem value="g">Gram (g)</SelectItem>
                                        <SelectItem value="m">Meter (m)</SelectItem>
                                        <SelectItem value="cm">Centimeter (cm)</SelectItem>
                                        <SelectItem value="mm">Millimeter (mm)</SelectItem>
                                        <SelectItem value="l">Liter (l)</SelectItem>
                                        <SelectItem value="ml">Milliliter (ml)</SelectItem>
                                        <SelectItem value="box">Box</SelectItem>
                                        <SelectItem value="set">Set</SelectItem>
                                        <SelectItem value="other">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <div className="flex items-end gap-2 mb-[2px]">
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 flex items-center justify-center text-blue-500 hover:bg-blue-100/50 dark:hover:bg-blue-900/30 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 transition-colors"
                            onClick={handleAddChildToCurrentPart}
                            title="Add Child Part"
                        >
                            <PlusCircle size={18} />
                        </Button>
                        <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 flex items-center justify-center text-destructive hover:bg-destructive/10 rounded-full"
                            onClick={removeCurrentPart}
                            title="Remove Part"
                        >
                            <Trash2 size={18} />
                        </Button>
                    </div>
                </div>
            </div>

            {watchedPart?.description && (
                <div className="mt-3 text-sm text-muted-foreground bg-muted/30 p-2 rounded-md">
                    {watchedPart.description}
                </div>
            )}

            {watchedPart?.isExpanded && childFields.length > 0 && (
                <div className="mt-4 pl-2 border-l-2 border-border/50">
                    {childFields.map((childField, childIndex) => (
                        <PartField
                            key={childField.rhfId}
                            field={childField}
                            nestLevel={nestLevel + 1}
                            control={control}
                            removeCurrentPart={() => removeChild(childIndex)}
                            pathPrefix={pathPrefix + '.children.' + childIndex}
                            form={form}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export const HierarchicalPartsForm = forwardRef<{
    triggerSubmit: () => Promise<void>;
}, HierarchicalPartsFormProps>(({ initialData, mode = "create", onFormSubmit }, ref) => {
    console.log("HierarchicalPartsForm initialData:", JSON.stringify(initialData, null, 2));
    const router = useRouter();

    const mapPartsForForm = useCallback((parts: FormPartData[] = []): FormPartData[] => {
        return parts.map(p => {
            // Extract stock from the populated Part object in partId (updated approach)
            const partRef = (p as any).partId;
            const stockFromPartId = typeof partRef === 'object' && partRef !== null ? partRef.inventory?.currentStock : null;
            const stockFromPartData = (p as any).partData?.inventory?.currentStock; // Legacy fallback
            const currentStock = stockFromPartId ?? stockFromPartData ?? p.currentStock ?? 0;

            // Extract part name from populated part object or use existing name
            let partName = p.name;
            if (typeof partRef === 'object' && partRef !== null && partRef.name) {
                partName = partRef.name;
            } else if ((p as any).partDetails && (p as any).partDetails.name) {
                partName = (p as any).partDetails.name;
            }

            // Extract part display identifier (part number/code) from populated part object
            let partDisplayIdentifier = p.partDisplayIdentifier;
            if (typeof partRef === 'object' && partRef !== null) {
                // Use partNumber, partId, or _id from the populated part object
                partDisplayIdentifier = partRef.partNumber || partRef.partId || partRef._id;
            } else if (typeof partRef === 'string') {
                partDisplayIdentifier = partRef;
            } else if ((p as any).partDetails) {
                const partDetails = (p as any).partDetails;
                partDisplayIdentifier = partDetails.partNumber || partDetails.partId || partDetails._id;
            }

            console.log(`[HierarchicalPartsForm] mapPartsForForm - Part: ${partName}, partRef type: ${typeof partRef}, partDisplayIdentifier: ${partDisplayIdentifier}, final currentStock: ${currentStock}`);

            return {
                ...p,
                id: p.id || uuidv4(),
                name: partName || "Unknown Part", // Ensure name is always a string
                currentStock: currentStock,
                partDisplayIdentifier: partDisplayIdentifier || partName || "Unknown Part",
                children: p.children ? mapPartsForForm(p.children) : []
            };
        });
    }, []);

    const form = useForm({
        resolver: zodResolver(hierarchicalFormSchema),
        mode: 'onBlur',
        shouldFocusError: false, // Prevent auto-focus which can cause hanging
        shouldUnregister: false, // Keep form state stable
        criteriaMode: "firstError", // Stop at first error to prevent excessive validation
        defaultValues: useMemo(() => {
            const baseDefaults = {
                assemblyCode: "",
                name: "",
                description: "",
                status: "active" as HierarchicalFormValues['status'],
                productId: null,
                parentId: null,
                isTopLevel: true,
                version: 1,
                manufacturingInstructions: null,
                estimatedBuildTime: null,
                partsRequired: [],
            };

            if (initialData) {
                let statusValue = initialData.status;
                if (!statusValue && initialData.assembly_stage) {
                    switch (initialData.assembly_stage) {
                        case "Final Assembly": case "FINAL ASSEMBLY": statusValue = "active"; break;
                        case "Sub-Assembly": case "SUB ASSEMBLY": statusValue = "pending_review"; break;
                        default: statusValue = "active";
                    }
                }
                return {
                    ...baseDefaults,
                    assemblyCode: initialData.assemblyCode || "",
                    name: initialData.name || "",
                    description: initialData.description || "",
                    status: statusValue || "active",
                    productId: initialData.productId || null,
                    parentId: initialData.parentId || null,
                    isTopLevel: initialData.isTopLevel === undefined ? true : initialData.isTopLevel,
                    version: initialData.version || 1,
                    manufacturingInstructions: initialData.manufacturingInstructions || null,
                    estimatedBuildTime: initialData.estimatedBuildTime || null,
                    partsRequired: mapPartsForForm(initialData.partsRequired || initialData.parts || []),
                };
            }
            return baseDefaults;
        }, [initialData, mapPartsForForm])
    });

    // Memoize stable dependencies for the reset effect
    const stableInitialAssemblyId = useMemo(() => initialData?._id, [initialData?._id]);

    // Use a ref to track the last processed assembly ID to prevent unnecessary resets
    const lastProcessedAssemblyIdRef = useRef<string | undefined>(undefined);

    // Loading and submission states
    const [isLoading, setIsLoading] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        const currentAssemblyId = initialData?._id;
        console.log('[HierarchicalPartsForm] Reset Effect. Mode:', mode, 'InitialData ID:', currentAssemblyId);

        if (mode === 'edit') {
            // Only reset if we have initial data and the assembly ID has actually changed
            if (initialData && currentAssemblyId !== lastProcessedAssemblyIdRef.current) {
                console.log('[HierarchicalPartsForm] Processing new assembly ID:', currentAssemblyId, 'Previous:', lastProcessedAssemblyIdRef.current);
                lastProcessedAssemblyIdRef.current = currentAssemblyId;
                let statusValue = initialData.status;
                if (!statusValue && initialData.assembly_stage) {
                    switch (initialData.assembly_stage) {
                        case "Final Assembly": case "FINAL ASSEMBLY": statusValue = "active"; break;
                        case "Sub-Assembly": case "SUB ASSEMBLY": statusValue = "pending_review"; break;
                        default: statusValue = "active";
                    }
                }

                const dataToReset: AssemblyFormData = {
                    ...(initialData._id && { _id: initialData._id }),
                    name: String(initialData.name || ''),
                    assemblyId: String(initialData.assemblyId || ''),
                    description: String(initialData.description || ''),
                    category: String(initialData.category || null),
                    status: (statusValue || initialData.status || 'active') as AssemblyStatusType, // Cast to ensure type
                    version: Number(initialData.version || 1),
                    notes: String(initialData.notes || ''),
                    partsRequired: [],
                    assemblyYield: Number(initialData.assemblyYield),
                    estimatedManufacturingTime: String(initialData.estimatedManufacturingTime),
                    assembly_stage: String(initialData.assembly_stage),
                    assembly_id: String(initialData.assemblyId),
                    assembly_name: String(initialData.name),
                };

                dataToReset.partsRequired = (initialData.partsRequired || []).map(p => {
                    // Extract stock from the populated Part object in partId (updated approach)
                    const partRef = (p as any).partId;
                    const stockFromPartId = typeof partRef === 'object' && partRef !== null ? partRef.inventory?.currentStock : null;
                    const stockFromPartData = (p as any).partData?.inventory?.currentStock; // Legacy fallback
                    const currentStock = stockFromPartId ?? stockFromPartData ?? p.currentStock ?? 0;

                    console.log(`[HierarchicalPartsForm] Reset Effect - Part: ${p.name}, partRef type: ${typeof partRef}, stockFromPartId: ${stockFromPartId}, final currentStock: ${currentStock}`);

                    const children = (p.children || []).map(child => {
                        const childPartRef = (child as any).partId;
                        const childStockFromPartId = typeof childPartRef === 'object' && childPartRef !== null ? childPartRef.inventory?.currentStock : null;
                        const childStockFromPartData = (child as any).partData?.inventory?.currentStock; // Legacy fallback
                        const childCurrentStock = childStockFromPartId ?? childStockFromPartData ?? child.currentStock ?? 0;

                        return {
                            ...child,
                            id: child._id || child.id || uuidv4(), // RHF ID for child
                            currentStock: childCurrentStock
                        };
                    });

                    return {
                        ...p,
                        id: p._id || p.id || uuidv4(), // RHF ID for parent
                        currentStock: currentStock, // Use the extracted stock value
                        children
                    };
                });

                console.log('[HierarchicalPartsForm] partsRequired in dataToReset before reset (brief):', JSON.stringify(dataToReset.partsRequired.map(p => ({ name: p.name, currentStock: p.currentStock, id: p.id, _id: p._id })), null, 2));
                form.reset(dataToReset as any);
                console.log('[HierarchicalPartsForm] Form has been reset.');
                const valuesAfterReset = form.getValues() as any;
                if (valuesAfterReset.partsRequired && valuesAfterReset.partsRequired.length > 0) {
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired[0].currentStock after reset:', valuesAfterReset.partsRequired[0].currentStock);
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired[0] after reset (full):', JSON.stringify(valuesAfterReset.partsRequired[0], null, 2));
                } else {
                    console.log('[HierarchicalPartsForm] form.getValues().partsRequired is empty or undefined after reset.');
                }
                // setSelectedAssemblyId(initialData._id); // This was causing issues
            } else if (initialData && currentAssemblyId === lastProcessedAssemblyIdRef.current) {
                console.log('[HierarchicalPartsForm] Assembly ID unchanged, skipping reset to prevent infinite loop');
            }
        } else if (mode === 'create') {
            console.log('[HierarchicalPartsForm] Resetting form with defaultValues for create mode.');
            form.reset(defaultValues as any);
            lastProcessedAssemblyIdRef.current = undefined; // Clear the ref for create mode
        }
    }, [mode]); // Removed stableInitialAssemblyId to prevent infinite loop - use ref for tracking instead

    const { fields, append, remove } = useFieldArray({
        control: form.control as any,
        name: "partsRequired" as any,
        keyName: "rhfId"
    });

    const lastAddTime = useRef<number>(0);

    const addRootPart = useCallback((e?: React.MouseEvent) => {
        e?.preventDefault();
        e?.stopPropagation();

        // Debounce mechanism to prevent rapid successive calls
        const now = Date.now();
        if (now - lastAddTime.current < 1000) { // 1 second debounce
            console.log('[HierarchicalPartsForm] addRootPart debounced - too soon after last call');
            return;
        }
        lastAddTime.current = now;

        console.log('[HierarchicalPartsForm] addRootPart called at:', new Date().toISOString());
        append({
            // Ensure all fields of FormPartData are provided or are optional
            _id: uuidv4(), // if it's a new part from client side, this is fine
            id: uuidv4(),  // RHF key
            partId: '',
            name: '',
            description: '',
            quantityRequired: 1,
            isExpanded: true,
            category: null,
            currentStock: null,
            reorderLevel: null,
            minimumStockLevel: null,
            supplier: null,
            technicalSpecs: null,
            unitOfMeasure: 'ea',
            partDisplayIdentifier: '',
            isAssembly: false,
            partNumber: null,
            cost: null,
            location: null,
            additionalAttributes: null,
            children: []
        } as any); // Explicit cast to ensure it matches
    }, []); // Removed append dependency to prevent infinite loop - append is stable within the component lifecycle

    const onSubmit: SubmitHandler<HierarchicalFormValues> = async (formValues) => {
        console.log('[HierarchicalPartsForm] onSubmit called with values:', formValues);

        try {
            // Validate required fields before submission
            if (!formValues.assemblyCode?.trim()) {
                throw new Error('Assembly code is required');
            }
            if (!formValues.name?.trim()) {
                throw new Error('Assembly name is required');
            }

            // Ensure default values for optional fields
            const sanitizedValues = {
                ...formValues,
                status: formValues.status || 'active',
                version: formValues.version || 1,
                isTopLevel: formValues.isTopLevel !== undefined ? formValues.isTopLevel : true,
                partsRequired: formValues.partsRequired || [],
                productId: formValues.productId || null,
                parentId: formValues.parentId || null,
                manufacturingInstructions: formValues.manufacturingInstructions || null,
                estimatedBuildTime: formValues.estimatedBuildTime || null
            };

            console.log('[HierarchicalPartsForm] Sanitized values:', sanitizedValues);

            // Add timeout to prevent hanging
            const submitTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('onFormSubmit timed out after 15 seconds')), 15000)
            );

            if (!onFormSubmit) {
                throw new Error('onFormSubmit is not defined');
            }

            const submitPromise = onFormSubmit(sanitizedValues);

            // Race between submission and timeout
            await Promise.race([submitPromise, submitTimeout]);

            console.log('[HierarchicalPartsForm] onFormSubmit completed successfully');

        } catch (error) {
            console.error('[HierarchicalPartsForm] onSubmit error:', error);
            throw error; // Re-throw to be handled by triggerSubmit
        }
    };

    // Reset form when initialData changes
    useEffect(() => {
        if (initialData) {
            console.log('[HierarchicalPartsForm] Reset Effect. Mode:', mode, 'InitialData ID:', initialData._id || 'none');

            // Map the initialData to the correct form structure
            const statusValue = (['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete'] as const)
                .includes(initialData.status as any) ? initialData.status as any : 'active';

            const resetData = {
                assemblyCode: initialData.assemblyCode || "",
                name: initialData.name || "",
                description: initialData.description || "",
                status: statusValue,
                productId: initialData.productId || null,
                parentId: initialData.parentId || null,
                isTopLevel: initialData.isTopLevel === undefined ? true : initialData.isTopLevel,
                version: initialData.version || 1,
                manufacturingInstructions: initialData.manufacturingInstructions || null,
                estimatedBuildTime: initialData.estimatedBuildTime || null,
                partsRequired: mapPartsForForm(initialData.partsRequired || initialData.parts || []),
            };

            console.log('[HierarchicalPartsForm] Resetting form with data:', resetData);
            form.reset(resetData);
        } else if (mode === 'create') {
            console.log('[HierarchicalPartsForm] Resetting form with defaultValues for create mode.');
            form.reset({
                assemblyCode: "",
                name: "",
                description: "",
                status: "active" as const,
                productId: null,
                parentId: null,
                isTopLevel: true,
                version: 1,
                manufacturingInstructions: null,
                estimatedBuildTime: null,
                partsRequired: [],
            });
        }
    }, [initialData, mode, form, mapPartsForForm]);

    useImperativeHandle(ref, () => ({
        triggerSubmit: async () => {
            console.log('[HierarchicalPartsForm] triggerSubmit called');

            setIsSubmitting(true);
            setIsLoading(true);

            try {
                // Check form validation state before submission
                const formValues = form.getValues();
                console.log('[HierarchicalPartsForm] Current form values:', JSON.stringify(formValues, null, 2));

                // Check for validation errors
                const formState = form.formState;
                console.log('[HierarchicalPartsForm] Form state:', {
                    isValid: formState.isValid,
                    errors: JSON.stringify(formState.errors, null, 2),
                    isDirty: formState.isDirty,
                    isSubmitting: formState.isSubmitting,
                    isValidating: formState.isValidating
                });

                // If form has errors, log them and try to fix common issues
                if (!formState.isValid && Object.keys(formState.errors).length > 0) {
                    console.log('[HierarchicalPartsForm] Form validation errors detected:', formState.errors);

                    // Try to fix common validation issues
                    const fixedValues = { ...formValues };

                    // Ensure required fields have default values
                    if (!fixedValues.assemblyCode) fixedValues.assemblyCode = '';
                    if (!fixedValues.name) fixedValues.name = '';
                    if (!fixedValues.status) fixedValues.status = 'active';
                    if (fixedValues.version === undefined || fixedValues.version === null) fixedValues.version = 1;
                    if (fixedValues.isTopLevel === undefined) fixedValues.isTopLevel = true;
                    if (!fixedValues.partsRequired) fixedValues.partsRequired = [];

                    // Update form with fixed values
                    Object.keys(fixedValues).forEach(key => {
                        if (formValues[key] !== fixedValues[key]) {
                            console.log(`[HierarchicalPartsForm] Fixing field ${key}: ${formValues[key]} -> ${fixedValues[key]}`);
                            form.setValue(key as any, fixedValues[key], { shouldValidate: true });
                        }
                    });

                    // Wait a bit for validation to complete
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Add timeout to prevent hanging
                const submitTimeout = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Form submission timed out after 10 seconds')), 10000)
                );

                const submitPromise = form.handleSubmit(onSubmit)();

                // Race between submission and timeout
                await Promise.race([submitPromise, submitTimeout]);

                console.log('[HierarchicalPartsForm] Form submission completed successfully');

            } catch (error) {
                console.error('[HierarchicalPartsForm] Form submission error:', error);

                // If form submission fails, try direct submission with current values
                console.log('[HierarchicalPartsForm] Attempting direct submission fallback...');
                const currentValues = form.getValues();

                // Ensure we have minimum required data
                const fallbackData = {
                    assemblyCode: currentValues.assemblyCode || '',
                    name: currentValues.name || '',
                    description: currentValues.description || '',
                    status: currentValues.status || 'active',
                    version: currentValues.version || 1,
                    isTopLevel: currentValues.isTopLevel !== undefined ? currentValues.isTopLevel : true,
                    partsRequired: currentValues.partsRequired || [],
                    productId: currentValues.productId || null,
                    parentId: currentValues.parentId || null,
                    manufacturingInstructions: currentValues.manufacturingInstructions || null,
                    estimatedBuildTime: currentValues.estimatedBuildTime || null
                };

                console.log('[HierarchicalPartsForm] Fallback data:', fallbackData);
                if (onFormSubmit) {
                    await onFormSubmit(fallbackData as any);
                }
            } finally {
                setIsSubmitting(false);
                setIsLoading(false);
            }
        }
    }));

    if (form.formState.errors.partsRequired && form.formState.errors.partsRequired.message) {
        showValidationErrorToast(form.formState.errors.partsRequired.message);
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 relative">
                {isSubmitting && (
                    <LoadingCard
                        message="Saving assembly parts..."
                        className="absolute inset-0 z-10 bg-background/80 backdrop-blur-sm rounded-lg"
                    />
                )}
                <Card className="shadow-sm border-border/60 overflow-hidden">
                    <CardHeader className="bg-muted/30 pb-4">
                        <CardTitle className="flex items-center gap-2 text-xl">
                            <Package className="h-5 w-5 text-primary" />
                            Parts Required
                        </CardTitle>
                        <CardDescription>
                            Add parts to this assembly and organize them in a hierarchical structure
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-6">
                        {fields.length === 0 ? (
                            <div className="text-center py-12 px-4">
                                <div className="bg-muted/30 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                                    <Package size={32} className="text-muted-foreground/60" />
                                </div>
                                <h3 className="text-lg font-medium mb-2">No parts added yet</h3>
                                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                                    Start building your assembly by adding parts. You can search for existing parts or create new ones.
                                </p>
                                <Button
                                    type="button"
                                    onClick={addRootPart}
                                    disabled={isLoading || isSubmitting}
                                    className="mx-auto bg-primary/90 hover:bg-primary transition-colors"
                                >
                                    {isLoading ? (
                                        <LoadingInline size="sm" className="mr-2" />
                                    ) : (
                                        <PlusCircle size={18} className="mr-2" />
                                    )}
                                    Add First Part
                                </Button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {fields.map((field, index) => {
                                    const pathPrefix = `partsRequired.${index}`;
                                    // Use field data directly instead of watching to prevent re-render loops
                                    const watchedPart = field;
                                    // const partDetails = form.getValues(`${pathPrefix}.partDetails`) as Part | undefined; // Removed: partDetails is not in schema this way

                                    // New Log
                                    console.log(`[HierarchicalPartsForm] Rendering part row ${index} (${(watchedPart as any)?.name}), watchedPart.currentStock: ${(watchedPart as any)?.currentStock}, field.rhfId: ${field.rhfId}, watchedPart._id (DB): ${(watchedPart as any)?._id}`);

                                    const hasChildren = (watchedPart as any)?.children && (watchedPart as any).children.length > 0;
                                    const isExpanded = (watchedPart as any)?.isExpanded; // Simplified: use watchedPart's state

                                    return (
                                        <PartField
                                            key={field.rhfId}
                                            field={field}
                                            nestLevel={0}
                                            control={form.control as any}
                                            removeCurrentPart={() => remove(index)}
                                            pathPrefix={pathPrefix}
                                            form={form as any}
                                        />
                                    );
                                })}

                                <div className="flex justify-start pt-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={addRootPart}
                                        disabled={isLoading || isSubmitting}
                                        className="mt-2 border-dashed hover:border-solid transition-all"
                                    >
                                        {isLoading ? (
                                            <LoadingInline size="sm" className="mr-2" />
                                        ) : (
                                            <Plus size={16} className="mr-2" />
                                        )}
                                        Add Another Part
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                    {fields.length > 0 && (
                        <CardFooter className="bg-muted/20 border-t border-border/40 px-6 py-4">
                            <div className="text-sm text-muted-foreground">
                                <span className="font-medium">{fields.length}</span> top-level part{fields.length !== 1 ? 's' : ''} in this assembly
                            </div>
                        </CardFooter>
                    )}
                </Card>

                {form.formState.errors.partsRequired?.message && (
                    <FormErrorDisplay
                        error={form.formState.errors.partsRequired.message}
                        field="Parts Required"
                        className="mt-4"
                    />
                )}
                {form.formState.errors.root?.message && (
                    <FormErrorDisplay
                        error={form.formState.errors.root.message}
                        field="Form"
                        className="mt-4"
                    />
                )}
            </form>
        </Form>
    );
});

HierarchicalPartsForm.displayName = 'HierarchicalPartsForm';

// Add CSS for ripple animation (client-side only to avoid SSR issues)
if (typeof document !== 'undefined') {
    const style = document.createElement('style');
    style.textContent = `
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
.animate-ripple {
  animation: ripple 0.6s linear;
}
`;
    document.head.appendChild(style);
}

export default HierarchicalPartsForm;
