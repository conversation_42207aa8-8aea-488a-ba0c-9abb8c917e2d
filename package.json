{"name": "inventory-management-system", "private": true, "version": "0.1.0", "scripts": {"setup-env": "node scripts/create-env.cjs", "dev": "node scripts/create-env.cjs && next dev --turbopack -p 3000", "dev:8000": "node scripts/create-env.cjs && next dev --turbopack -p 8000", "dev:3000": "node scripts/create-env.cjs && next dev --turbopack -p 3000", "dev:3001": "node scripts/create-env.cjs && next dev --turbopack -p 3001", "dev:5174": "node scripts/create-env.cjs && next dev --turbopack -p 5174", "build": "next build --turbopack", "start": "next start -p 3000", "start:8000": "next start -p 8000", "start:3000": "next start -p 3000", "start:5174": "next start -p 5174", "start:prod": "node scripts/create-env.cjs && cross-env NODE_ENV=production next start", "deploy": "PORT=3000 node scripts/deploy-production.cjs", "deploy:build": "node scripts/deploy-production.cjs --build-only", "deploy:8000": "PORT=8000 node scripts/deploy-production.cjs", "deploy:3000": "PORT=3000 node scripts/deploy-production.cjs", "deploy:5174": "PORT=5174 node scripts/deploy-production.cjs", "lint": "eslint . --config eslint.config.js", "type-check": "node scripts/type-check.js check", "type-check:watch": "node scripts/type-check.js watch", "type-check:incremental": "node scripts/type-check.js incremental", "type-check:detailed": "node scripts/type-check.js detailed", "type-check:simple": "tsc --noEmit", "test-db": "node scripts/test-db-connection.cjs", "diagnose-db": "node scripts/diagnose-mongodb.cjs", "migrate-data": "node scripts/init-mongodb.cjs", "migrate-assemblies": "node scripts/migrate-assemblies.cjs", "init-assemblies": "node scripts/init-assemblies.js", "check-env": "node scripts/check-env.cjs", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:inventory": "jest __tests__/api/inventory/route.test.js", "test:transactions": "jest __tests__/api/inventory-transactions/route.test.js", "test:parts-id": "jest __tests__/api/parts/\\[id\\]/route.test.js", "test:batch-tracking": "jest __tests__/api/batch-tracking/route.test.js", "test:assemblies": "jest __tests__/api/assemblies/assemblies.test.ts", "test:assemblies:playwright": "npx playwright test tests/playwright/assembly-crud.test.js --headed", "test:create-assembly": "npx playwright test tests/playwright/create-assembly.test.js --headed", "test:update-assembly": "npx playwright test tests/playwright/update-assembly.test.js --headed", "test:delete-assembly": "npx playwright test tests/playwright/delete-assembly.test.js --headed", "test:view-assembly": "npx playwright test tests/playwright/view-assembly.test.js --headed", "test:assemblies:all": "npx playwright test tests/playwright/*-assembly.test.js --headed", "test:analytics": "jest __tests__/api/analytics/route.test.js", "test:categories": "jest __tests__/api/categories/route.test.js", "test:batches-logs": "jest __tests__/api/batches/logs.test.js", "test:batches-inventory": "jest __tests__/api/batches/inventory.test.js", "test:inventory-e2e": "./run-inventory-tests.sh", "test:inventory-crud": "npx playwright test tests/inventory-crud.spec.ts", "test:multi-warehouse": "npx playwright test tests/multi-warehouse-display.spec.ts", "test:integration-workflow": "npx playwright test tests/integration-workflow.spec.ts", "test:inventory-all": "npx playwright test tests/inventory-crud.spec.ts tests/multi-warehouse-display.spec.ts tests/integration-workflow.spec.ts", "dev:with-types": "concurrently \"npm run type-check:watch\" \"npm run dev\"", "check-all": "npm run type-check && npm run lint", "pre-build": "npm run check-all && npm run build"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@hookform/resolvers": "^5.0.1", "@next/bundle-analyzer": "15.4.2", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.12", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.5", "@radix-ui/react-slider": "^1.3.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@sentry/nextjs": "^9.34.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.3.1", "dotenv": "^16.6.1", "framer-motion": "^11.0.8", "glob": "^11.0.3", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.503.0", "mongodb": "^6.18.0", "mongoose": "^8.13.1", "next": "15.4.2", "react": "19.1.0", "react-complex-tree": "^2.6.0", "react-confirm-alert": "^3.0.6", "react-day-picker": "^9.6.7", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "recharts": "^2.12.2", "sharp": "^0.34.2", "sonner": "^2.0.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@playwright/test": "^1.54.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/recharts": "^1.8.29", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.4.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mongodb-memory-server": "^10.1.4", "newman": "^6.2.1", "postcss": "^8.4.35", "supertest": "^7.1.0", "tailwind-scrollbar": "^3.0.5", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}, "description": "Trend IMS is a web-based application designed to manage inventory, track products, assemblies, purchase orders, and provide insights into stock levels and production status. It features a dashboard for quick overview and actions, detailed views for products and assemblies, and utilizes a MongoDB database for data persistence.", "main": "capture-console-errors.js", "directories": {"doc": "docs", "lib": "lib"}, "keywords": [], "author": "", "license": "ISC"}