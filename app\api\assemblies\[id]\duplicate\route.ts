import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { NextRequest, NextResponse } from 'next/server';
// Use functions from assembly.service.ts for schema-aware operations
import {
    CanonicalAssemblyPartRequiredDto,
    CanonicalCreateAssemblyDto,
    createAssembly,
    getAssemblyById, // Corrected to use getAssemblyById
} from '@/app/services/assembly.service';
// Attempting the logically correct relative path for interfaces again
import { IAssemblyPartRequired } from '@/app/models/assembly.model';

const ROUTE_PATH = '/api/assemblies/[id]/duplicate';

interface RouteParams {
  id: string; // This is the assembly _id
}

/**
 * POST handler for duplicating an assembly
 * @param _request - The incoming request (unused)
 * @param context - Context object containing route parameters
 * @param context.params - Route parameters containing the assembly ID (_id)
 * @returns JSON response with the duplicated assembly or error
 */
async function handlePOSTDuplicate(
  _request: NextRequest,
  context?: { params: RouteParams } // Make context optional to align with middleware
) {
  const startTime = Date.now();
  if (!context || !context.params) {
    // This should not be reached in a dynamic route if Next.js provides params as expected.
    console.error(`[API] Critical: context or context.params missing in dynamic route handler for ${ROUTE_PATH}`);
    throw new Error('Internal Server Error: Route parameters missing.');
  }
  const { params } = context; // params is now RouteParams
  const assemblyIdToDuplicate = params.id; // id is the assembly _id from the route
  try {
    console.log(`[API] POST /api/assemblies/${assemblyIdToDuplicate}/duplicate - Duplicating assembly`);

    // Fetch by _id, not assemblyCode
    const originalAssembly = await getAssemblyById(assemblyIdToDuplicate, true);

    if (!originalAssembly) {
      console.log(`[API] Assembly with ID ${assemblyIdToDuplicate} not found for duplication`);
      return NextResponse.json(
        { success: false, error: `Assembly with ID ${assemblyIdToDuplicate} not found`, meta: { duration: Date.now() - startTime } },
        { status: 404 }
      );
    }

    // Map originalAssembly to CanonicalCreateAssemblyDto
    const newAssemblyDto: CanonicalCreateAssemblyDto = {
      assemblyCode: `${originalAssembly.assemblyCode}-COPY-${Date.now().toString().slice(-5)}`,
      name: `${originalAssembly.name} (Copy)`,
      parentId: originalAssembly.parentId?.toString() || null,   // Ensure string or null
      partsRequired: originalAssembly.partsRequired?.map((p: IAssemblyPartRequired) => {
        // If p.partId is populated, it's an object like { _id: ..., name: ... }.
        // If it's just an ObjectId string/object, .toString() would work directly or on ._id if it's an ObjectId instance.
        // The DTO expects a string representation of the ObjectId.
        const partIdString: string = (typeof p.partId === 'string')
          ? p.partId
          : (p.partId && typeof (p.partId as any)._id !== 'undefined')
            ? (p.partId as any)._id.toString()
            : p.partId?.toString() || ''; // Fallback for ObjectId instance, ensure string
        return {
          partId: partIdString,
          quantityRequired: p.quantityRequired || 1, // Map database field 'quantityRequired' to DTO field 'quantityRequired'
          unitOfMeasure: p.unitOfMeasure || null
        } as CanonicalAssemblyPartRequiredDto;
      }) || [],
      status: originalAssembly.status === 'obsolete' ? 'obsolete' : 'design_phase', // Use a valid status from the enum
      version: 1, // Use a number instead of a string
      createdBy: '000000000000000000000000', // TODO: Replace with actual user ID from session
    };
    
    // Remove undefined properties to ensure they don't override Mongoose defaults if any
    Object.keys(newAssemblyDto).forEach(key => (newAssemblyDto as any)[key] === undefined && delete (newAssemblyDto as any)[key]);

    try {
      const duplicatedAssembly = await createAssembly(newAssemblyDto);

      const duration = Date.now() - startTime;
      console.log(`[API] Duplicated assembly ${assemblyIdToDuplicate} successfully (${duration}ms)`);
      
      return NextResponse.json({ 
        success: true, 
        data: duplicatedAssembly, 
        meta: { duration } 
      });
    } catch (error: any) {
      // Log detailed validation errors
      if (error.name === 'ValidationError') {
        console.error('[API] Validation Error Details:', {
          errors: error.errors,
          assemblyData: newAssemblyDto,
          errorMessage: error.message
        });
      }
      // Re-throw to allow withErrorHandling middleware to process
      console.error(`[API] Error in handlePOSTDuplicate for ${assemblyIdToDuplicate}:`, error);
      throw error;
    }
  } catch (error: any) {
    // Catch any other errors
    console.error(`[API] Unexpected error in handlePOSTDuplicate for ${assemblyIdToDuplicate}:`, error);
    throw error;
  }
}

export const POST = withErrorHandling(handlePOSTDuplicate, ROUTE_PATH);
