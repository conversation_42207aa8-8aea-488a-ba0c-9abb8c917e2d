"use client";

import { But<PERSON> } from '@/app/components/forms/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { DatePicker } from '@/app/components/layout/calendar/date-picker';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import Header from '@/app/components/layout/Header';
import { Popover, PopoverContent, PopoverTrigger } from '@/app/components/navigation/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/navigation/tabs';
import { useTheme } from '@/app/contexts/ThemeContext';
import { analyticsTypeMap } from '@/app/lib/analytics-utils';
import { asAnalyticsResponse, asApiResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { getApiUrl } from '@/app/utils/apiUtils';
import { format, subMonths } from 'date-fns';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import {
    AlertTriangle,
    BarChart3,
    Calendar,
    Download,
    LineChart as LineChartIcon,
    Loader2,
    PieChart as PieChartIcon,
    RefreshCw
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, Cell, Legend, Line, LineChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import * as XLSX from 'xlsx';

// Define interfaces for analytics data
interface AnalyticsType {
  id: string;
  name: string;
  description: string;
  endpoint: string;
}

interface InventoryTrend {
  name: string;
  value: number;
  percentChange?: number;
}

interface StockLevel {
  name: string;
  inStock: number;
  lowStock: number;
  outOfStock: number;
}

interface CategoryDistribution {
  name: string;
  value: number;
}

interface InventoryValue {
  name: string;
  value: number;
}

interface DashboardSummary {
  totalItems: number;
  totalCategories: number;
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
}

interface AnalyticsData {
  inventoryTrends?: InventoryTrend[];
  stockLevels?: StockLevel[];
  categoryDistribution?: CategoryDistribution[];
  inventoryValue?: InventoryValue[];
  summary?: DashboardSummary;
  generatedAt: string;
}

// Define theme-aware chart colors using CSS variables
const getChartColors = () => [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(var(--chart-6))',
  'hsl(var(--chart-7))',
  'hsl(var(--chart-8))',
];

// Custom tooltip component for charts
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    name: string;
    color: string;
    dataKey: string;
  }>;
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border text-popover-foreground p-3 rounded-lg shadow-lg">
        <p className="text-muted-foreground font-medium">{label}</p>
        {payload.map((entry, index) => {
          // Type guard to ensure entry has the expected structure
          const entryData = entry as any;
          return (
            <p key={`item-${index}`} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value}
              {entryData.payload?.percentChange !== undefined && (
                <span className={entryData.payload.percentChange > 0 ? 'text-green-600' : 'text-destructive'}>
                  {' '}({entryData.payload.percentChange > 0 ? '+' : ''}{entryData.payload.percentChange}%)
                </span>
              )}
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

const Analytics: React.FC = () => {
  const { theme } = useTheme();
  const [analyticsTypes, setAnalyticsTypes] = useState<AnalyticsType[]>([]);
  const [selectedAnalyticsType, setSelectedAnalyticsType] = useState<string>('inventory-trends');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<string>('month');
  const [startDate, setStartDate] = useState<Date | undefined>(subMonths(new Date(), 1));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line'>('line');
  const [isExporting, setIsExporting] = useState<boolean>(false);

  // Fetch analytics types on component mount
  useEffect(() => {
    fetchAnalyticsTypes();
  }, []);

  // Fetch analytics data when type or filters change
  useEffect(() => {
    if (selectedAnalyticsType) {
      fetchAnalyticsData();
    }
  }, [selectedAnalyticsType, timeRange, startDate, endDate, categoryFilter]);

  // Fetch available analytics types
  const fetchAnalyticsTypes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(getApiUrl('/api/analytics'));

      if (!response.ok) {
        throw new Error(`Error fetching analytics types: ${response.status}`);
      }

      const data = asApiResponse<AnalyticsType[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      const analyticsData = data.data || [];
      setAnalyticsTypes(analyticsData);

      // Set initial analytics type if available
      if (analyticsData.length > 0 && analyticsData[0]?.id) {
        setSelectedAnalyticsType(analyticsData[0].id);
      }
    } catch (err) {
      console.error('Error fetching analytics types:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch analytics data based on selected type and filters
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      params.append('timeRange', timeRange);

      if (startDate && endDate) {
        params.append('startDate', startDate.toISOString());
        params.append('endDate', endDate.toISOString());
      }

      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter);
      }

      // Fetch data
      const response = await fetch(getApiUrl(`/api/analytics?type=${selectedAnalyticsType}&${params.toString()}`))
      if (!response.ok) {
        throw new Error(`Error fetching analytics data: ${response.status}`);
      }

      const data = asAnalyticsResponse(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      // Transform the data based on the selected analytics type
      let transformedData = data.data || null;

      if (transformedData) {
        // Handle legacy API response format that might have 'trends' property
        const dataWithTrends = transformedData as Record<string, unknown>;
        if (selectedAnalyticsType === 'inventory-trends' &&
            dataWithTrends.trends &&
            Array.isArray(dataWithTrends.trends)) {
          transformedData = {
            ...transformedData,
            inventoryTrends: dataWithTrends.trends
          };
        }
        setAnalyticsData({
          ...transformedData,
          generatedAt: (data as any).generatedAt || new Date().toISOString(),
        });
      } else {
        setAnalyticsData(null);
      }
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setAnalyticsData(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Get chart data based on analytics type
  const getChartData = () => {
    if (!analyticsData) return [];

    // Local implementation of analyticsTypeMap as fallback
    const getDataKey = (type: string): string => {
      const mapping: Record<string, string> = {
        'inventory-trends': 'inventoryTrends',
        'stock-levels': 'stockLevels',
        'category-distribution': 'categoryDistribution',
        'inventory-value': 'inventoryValue'
      };
      return mapping[type] || '';
    };

    // Try to use the imported function, fall back to local implementation if it fails
    let dataKey: keyof AnalyticsData | '' = '';
    try {
      dataKey = typeof analyticsTypeMap === 'function'
        ? analyticsTypeMap(selectedAnalyticsType) as keyof AnalyticsData
        : getDataKey(selectedAnalyticsType) as keyof AnalyticsData;
    } catch (error) {
      console.error('Error using analyticsTypeMap:', error);
      dataKey = getDataKey(selectedAnalyticsType) as keyof AnalyticsData;
    }

    if (dataKey && analyticsData[dataKey]) {
      const data = analyticsData[dataKey];
      return Array.isArray(data) ? (data as unknown) as Array<Record<string, unknown>> : [];
    }
    return [];
  };

  // Get analytics title based on selected type
  const getAnalyticsTitle = () => {
    const analyticsType = analyticsTypes.find(type => type.id === selectedAnalyticsType);
    return analyticsType ? analyticsType.name : 'Analytics';
  };

  // Get analytics description based on selected type
  const getAnalyticsDescription = () => {
    const analyticsType = analyticsTypes.find(type => type.id === selectedAnalyticsType);
    return analyticsType ? analyticsType.description : '';
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    if (!analyticsData) {
      console.warn("No data available to export");
      alert("No data available to export");
      return;
    }

    setIsExporting(true);

    try {
      let exportData: Array<Array<string | number>> = [];
      let headers: string[] = [];
      let title = getAnalyticsTitle();

      // Prepare data based on analytics type
      switch (selectedAnalyticsType) {
        case 'inventory-trends':
          headers = ['Period', 'Value', 'Percent Change'];
          exportData = (analyticsData.inventoryTrends || []).map(item => [
            item.name,
            item.value,
            item.percentChange !== undefined ? `${item.percentChange}%` : 'N/A'
          ]);
          break;
        case 'stock-levels':
          headers = ['Period', 'In Stock', 'Low Stock', 'Out of Stock'];
          exportData = (analyticsData.stockLevels || []).map(item => [
            item.name,
            item.inStock,
            item.lowStock,
            item.outOfStock
          ]);
          break;
        case 'category-distribution':
          headers = ['Category', 'Count'];
          exportData = (analyticsData.categoryDistribution || []).map(item => [
            item.name,
            item.value
          ]);
          break;
        case 'inventory-value':
          headers = ['Category', 'Value ($)'];
          exportData = (analyticsData.inventoryValue || []).map(item => [
            item.name,
            item.value
          ]);
          break;
        default:
          break;
      }

      if (exportData.length === 0) {
        throw new Error("No data available to export");
      }

      const fileName = `${selectedAnalyticsType}_${format}_${new Date().toISOString().split('T')[0]}`;

      if (format === 'csv') {
        // Create CSV content
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => row.join(','))
        ].join('\n');

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `${fileName}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
      else if (format === 'excel') {
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet([headers, ...exportData]);

        // Create workbook
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, title);

        // Generate Excel file and trigger download
        XLSX.writeFile(wb, `${fileName}.xlsx`);
      }
      else if (format === 'pdf') {
        // Create PDF document
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(16);
        doc.text(title, 14, 15);

        // Add generation date
        doc.setFontSize(10);
        const generatedDate = analyticsData.generatedAt
          ? new Date(analyticsData.generatedAt).toLocaleString()
          : new Date().toLocaleString();
        doc.text(`Generated: ${generatedDate}`, 14, 22);

        // Add table using jsPDF autoTable plugin
        const docWithAutoTable = doc as any;
        docWithAutoTable.autoTable({
          head: [headers],
          body: exportData,
          startY: 25,
          theme: 'grid',
          styles: {
            fontSize: 10,
            cellPadding: 3,
            lineColor: [200, 200, 200],
            lineWidth: 0.1,
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold',
          },
          alternateRowStyles: {
            fillColor: [240, 240, 240],
          },
        });

        // Save PDF
        doc.save(`${fileName}.pdf`);
      }

      alert(`Data exported in ${format.toUpperCase()} format successfully`);
    } catch (err) {
      console.error('Error exporting data:', err);
      alert(err instanceof Error ? err.message : 'Failed to export data');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Analytics" />

      <div className="px-8 pb-8">
        {isLoading && !analyticsData ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading analytics...</span>
          </div>
        ) : error ? (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 text-destructive">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <p>{error}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => {
                setError(null);
                fetchAnalyticsTypes();
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <>
            {/* Analytics Type Selection */}
            <div className="mb-6">
              <Tabs
                defaultValue={selectedAnalyticsType}
                value={selectedAnalyticsType}
                onValueChange={(value) => setSelectedAnalyticsType(value)}
                className="w-full"
              >
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    {analyticsTypes.map((type) => (
                      <TabsTrigger key={type.id} value={type.id}>
                        {type.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchAnalyticsData}
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Refresh
                    </Button>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="default"
                          size="sm"
                          disabled={isExporting || !analyticsData}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          {isExporting ? 'Exporting...' : 'Export'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48 p-0">
                        <div className="py-1">
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('csv')}
                            disabled={isExporting}
                          >
                            Export as CSV
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('excel')}
                            disabled={isExporting}
                          >
                            Export as Excel
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExport('pdf')}
                            disabled={isExporting}
                          >
                            Export as PDF
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Analytics Content */}
                {analyticsTypes.map((type) => (
                  <TabsContent key={type.id} value={type.id} className="mt-0">
                    <Card>
                      <CardHeader>
                        <CardTitle>{getAnalyticsTitle()}</CardTitle>
                        <CardDescription>{getAnalyticsDescription()}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* Filters */}
                        <div className="flex flex-wrap gap-4 mb-6">
                          <div className="flex items-center space-x-2">
                            <Select
                              value={timeRange}
                              onValueChange={setTimeRange}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Time Range" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="week">Last Week</SelectItem>
                                <SelectItem value="month">Last Month</SelectItem>
                                <SelectItem value="quarter">Last Quarter</SelectItem>
                                <SelectItem value="year">Last Year</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <DatePicker
                              date={startDate}
                              setDate={(date) => setStartDate(date)}
                              placeholder="Start Date"
                            />
                            <span>to</span>
                            <DatePicker
                              date={endDate}
                              setDate={(date) => setEndDate(date)}
                              placeholder="End Date"
                            />
                          </div>

                          {selectedAnalyticsType === 'inventory-trends' && (
                            <Select
                              value={categoryFilter}
                              onValueChange={setCategoryFilter}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                {analyticsData?.categoryDistribution?.map((category) => (
                                  <SelectItem key={category.name} value={category.name}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </div>

                        {/* Summary Cards */}
                        {analyticsData?.summary && (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Total Items</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">{analyticsData.summary.totalItems}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Categories</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">{analyticsData.summary.totalCategories}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold text-orange-500">{analyticsData.summary.lowStockCount}</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                              </CardHeader>
                              <CardContent>
                                <div className="text-2xl font-bold">${analyticsData?.summary?.totalValue != null ? analyticsData.summary.totalValue.toFixed(2) : '—'}</div>
                              </CardContent>
                            </Card>
                          </div>
                        )}

                        {/* Chart Type Selection */}
                        <div className="flex justify-end mb-4">
                          <div className="flex border rounded-md overflow-hidden">
                            <Button
                              variant={chartType === 'bar' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('bar')}
                              className="rounded-none"
                            >
                              <BarChart3 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'pie' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('pie')}
                              className="rounded-none"
                            >
                              <PieChartIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'line' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('line')}
                              className="rounded-none"
                            >
                              <LineChartIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Chart */}
                        {isLoading ? (
                          <div className="flex justify-center items-center h-64">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading data...</span>
                          </div>
                        ) : analyticsData ? (
                          <div className="h-96">
                            {getChartData().length > 0 ? (
                              <ResponsiveContainer width="100%" height="100%">
                                {chartType === 'bar' ? (
                                  <BarChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                                    <XAxis dataKey="name" tick={{ fill: "hsl(var(--muted-foreground))" }} />
                                    <YAxis tick={{ fill: "hsl(var(--muted-foreground))" }} />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    {selectedAnalyticsType === 'stock-levels' ? (
                                      <>
                                        <Bar dataKey="inStock" name="In Stock" stackId="a" fill="hsl(var(--primary))" />
                                        <Bar dataKey="lowStock" name="Low Stock" stackId="a" fill="hsl(var(--secondary))" />
                                        <Bar dataKey="outOfStock" name="Out of Stock" stackId="a" fill="hsl(var(--destructive))" />
                                      </>
                                    ) : (
                                      <Bar dataKey="value" name="Value" fill="hsl(var(--primary))" />
                                    )}
                                  </BarChart>
                                ) : chartType === 'pie' ? (
                                  <PieChart>
                                    <Pie
                                      data={getChartData()}
                                      cx="50%"
                                      cy="50%"
                                      labelLine={false}
                                      outerRadius={150}
                                      fill="hsl(var(--primary))"
                                      dataKey="value"
                                      label={({ name, percent }: { name: string; percent: number }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                    >
                                      {getChartData().map((entry: Record<string, unknown>, index: number) => {
                                        const colors = getChartColors();
                                        return (
                                          <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                                        );
                                      })}
                                    </Pie>
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                  </PieChart>
                                ) : (
                                  <LineChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                                    <XAxis dataKey="name" tick={{ fill: "hsl(var(--muted-foreground))" }} />
                                    <YAxis tick={{ fill: "hsl(var(--muted-foreground))" }} />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Legend />
                                    <Line
                                      type="monotone"
                                      dataKey="value"
                                      name="Value"
                                      stroke="hsl(var(--primary))"
                                      strokeWidth={2}
                                      dot={{ r: 4 }}
                                      activeDot={{ r: 6 }}
                                    />
                                  </LineChart>
                                )}
                              </ResponsiveContainer>
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <p className="text-muted-foreground">No data available for this analytics type.</p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-64">
                            <p className="text-muted-foreground">Select an analytics type to view data.</p>
                          </div>
                        )}
                      </CardContent>
                      <CardFooter className="text-sm text-muted-foreground">
                        {analyticsData && analyticsData.generatedAt && (
                          <div>
                            Data generated at: {format(new Date(analyticsData.generatedAt), 'MMM dd, yyyy HH:mm:ss')}
                          </div>
                        )}
                      </CardFooter>
                    </Card>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Analytics;
