import { WorkOrder } from "@/app/types/orders";
import { z } from "zod";

/**
 * Zod validation schema for the work order form
 * Matches the MongoDB schema structure
 */
export const workOrderFormSchema = z.object({
  _id: z.string().optional(), // Optional for new work orders
  woNumber: z.string().optional(), // Auto-generated for new work orders
  assemblyId: z.string().nullable().optional(),
  partIdToManufacture: z.string().nullable().optional(),
  productId: z.string().nullable().optional(),
  quantity: z.number().int().min(1, { message: "Quantity must be at least 1" }),
  status: z.enum(["pending", "in_progress", "completed", "on_hold", "cancelled", "planned", "released", "closed"]).default("pending"),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  dueDate: z.date().nullable().optional(),
  assignedTo: z.string({ required_error: "Assigned user is required" }),
  notes: z.string().max(1000, { message: "Notes cannot exceed 1000 characters" }).nullable().optional(),
  completedAt: z.date().nullable().optional(),
}).refine(data => {
  // At least one of assemblyId, partIdToManufacture, or productId must be provided
  return !!data.assemblyId || !!data.partIdToManufacture || !!data.productId;
}, {
  message: "At least one of Assembly, Part, or Product must be provided",
  path: ["assemblyId"] // Show the error on the assemblyId field
});

/**
 * Type for the form data
 */
export type WorkOrderFormData = z.infer<typeof workOrderFormSchema>;

/**
 * Props for the WorkOrderForm component
 */
export interface WorkOrderFormProps {
  /**
   * Initial data for the form
   */
  initialData: Partial<WorkOrder> | undefined;
  
  /**
   * Callback for when the form is submitted
   */
  onSubmit?: (data: WorkOrderFormData) => void;
  
  /**
   * Callback for when the form is cancelled
   */
  onCancel?: () => void;
  
  /**
   * Whether the form is in loading state
   */
  isLoading?: boolean;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Whether the form is for editing an existing work order
   */
  isEditing?: boolean;
}
