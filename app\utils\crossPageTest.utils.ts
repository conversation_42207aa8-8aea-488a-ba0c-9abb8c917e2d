/**
 * Cross-Page Testing Utilities
 * Functions to test header theme integration across all application pages
 */

/**
 * List of all main application pages to test
 */
export const TEST_PAGES = [
  { path: '/dashboard', name: 'Dashboard', description: 'Main dashboard with overview' },
  { path: '/analytics', name: 'Analytics', description: 'Analytics and reporting page' },
  { path: '/inventory', name: 'Inventory', description: 'Inventory management page' },
  { path: '/assemblies', name: 'Assemblies', description: 'Assembly management' },
  { path: '/products', name: 'Products', description: 'Product catalog' },
  { path: '/categories', name: 'Categories', description: 'Category management' },
  { path: '/purchase-orders', name: 'Purchase Orders', description: 'Purchase order management' },
  { path: '/suppliers', name: 'Suppliers', description: 'Supplier management' },
  { path: '/reports', name: 'Reports', description: 'Reports and analytics' },
  { path: '/settings', name: 'Settings', description: 'Application settings' }
];

/**
 * Test header theme toggle presence on a page
 */
export function testHeaderThemeTogglePresence(): {
  hasHeader: boolean;
  hasThemeToggle: boolean;
  hasEnhancedToggle: boolean;
  hasDropdown: boolean;
} {
  // Look for header element or header-like structures
  const header = document.querySelector('header') ||
                document.querySelector('[data-framer-motion="header-container"]') ||
                document.querySelector('.sticky.top-0') ||
                document.querySelector('[data-testid="header-controls"]')?.closest('div');

  const themeToggle = document.querySelector('[data-testid="theme-toggle"]') ||
                     document.querySelector('button[aria-label*="theme"]') ||
                     document.querySelector('button[aria-label*="Switch"]') ||
                     document.querySelector('button[aria-label*="Current theme"]');

  // Check for enhanced theme toggle (dropdown menu)
  const dropdownTrigger = document.querySelector('[role="combobox"]') ||
                         document.querySelector('[data-radix-collection-item]') ||
                         document.querySelector('button:has(svg + span + svg)') || // Button with icon + text + chevron
                         themeToggle; // Fallback to theme toggle if found

  const dropdown = document.querySelector('[role="menu"]') ||
                  document.querySelector('[data-radix-popper-content-wrapper]');

  return {
    hasHeader: !!header,
    hasThemeToggle: !!themeToggle,
    hasEnhancedToggle: !!dropdownTrigger,
    hasDropdown: !!dropdown
  };
}

/**
 * Test theme persistence across page navigation
 */
export function testThemePersistence(): {
  themeMode: string | null;
  themeVariant: string | null;
  documentClass: string;
  cssVariables: Record<string, string>;
  isThemeReady?: boolean;
} {
  const root = document.documentElement;
  const themeMode = localStorage.getItem('theme-mode') || localStorage.getItem('theme');
  const themeVariant = localStorage.getItem('theme-variant');

  // Get current CSS variables
  const computedStyle = getComputedStyle(root);
  const cssVariables = {
    primary: computedStyle.getPropertyValue('--primary').trim(),
    background: computedStyle.getPropertyValue('--background').trim(),
    foreground: computedStyle.getPropertyValue('--foreground').trim(),
    muted: computedStyle.getPropertyValue('--muted').trim()
  };

  // Check if theme is ready
  const isThemeReady = root.getAttribute('data-theme-ready') === 'true';

  return {
    themeMode: themeMode || 'light', // Default to 'light' if not found
    themeVariant: themeVariant || 'default', // Default to 'default' if not found
    documentClass: root.className,
    cssVariables,
    isThemeReady
  };
}

/**
 * Test responsive behavior of header theme toggle
 */
export function testResponsiveBehavior(): {
  screenWidth: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  headerLayout: string;
} {
  const screenWidth = window.innerWidth;
  const isMobile = screenWidth < 768;
  const isTablet = screenWidth >= 768 && screenWidth < 1024;
  const isDesktop = screenWidth >= 1024;

  // Analyze header layout - look for header or header-like structures
  const header = document.querySelector('header') ||
                document.querySelector('[data-framer-motion="header-container"]') ||
                document.querySelector('.sticky.top-0') ||
                document.querySelector('[data-testid="header-controls"]')?.closest('div');

  const headerLayout = header ?
    (header.querySelector('.space-x-1, .space-x-2, .space-x-3, [data-testid="header-controls"]') ? 'spaced' : 'compact') :
    'not-found';

  return {
    screenWidth,
    isMobile,
    isTablet,
    isDesktop,
    headerLayout
  };
}

/**
 * Test keyboard accessibility
 */
export function testKeyboardAccessibility(): {
  focusableElements: number;
  tabOrder: string[];
  hasKeyboardShortcuts: boolean;
  ariaLabels: number;
} {
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  const tabOrder = Array.from(focusableElements).map((el, index) => {
    const tagName = el.tagName.toLowerCase();
    const ariaLabel = el.getAttribute('aria-label');
    const title = el.getAttribute('title');
    const text = el.textContent?.trim().substring(0, 20);
    return `${index + 1}. ${tagName}${ariaLabel ? ` (${ariaLabel})` : ''}${title ? ` [${title}]` : ''}${text ? ` "${text}"` : ''}`;
  });

  const elementsWithAriaLabels = document.querySelectorAll('[aria-label]');
  
  // Check for keyboard shortcuts (look for event listeners or data attributes)
  const hasKeyboardShortcuts = document.querySelector('[data-hotkey], [data-shortcut]') !== null ||
                               document.addEventListener.toString().includes('keydown');

  return {
    focusableElements: focusableElements.length,
    tabOrder,
    hasKeyboardShortcuts,
    ariaLabels: elementsWithAriaLabels.length
  };
}

/**
 * Test theme switching functionality
 */
export async function testThemeSwitching(): Promise<{
  canToggleMode: boolean;
  canSwitchVariants: boolean;
  animationsWork: boolean;
  persistenceWorks: boolean;
}> {
  // Get current path for context
  const currentPath = window.location.pathname;

  // Prevent recursion by checking if we're already in a test
  if ((window as any).__themeSwitchingTestRunning) {
    return {
      canToggleMode: false,
      canSwitchVariants: false,
      animationsWork: false,
      persistenceWorks: false
    };
  }

  (window as any).__themeSwitchingTestRunning = true;

  try {
    // Get initial theme state directly from localStorage and DOM
    const initialMode = localStorage.getItem('theme-mode') || localStorage.getItem('theme') || 'light';
    const initialVariant = localStorage.getItem('theme-variant') || 'default';
    const initialClass = document.documentElement.className;

    // Test mode toggle - handle both simple and dropdown modes
    const themeToggle = document.querySelector(
      'button[aria-label*="Switch"], button[aria-label*="theme"], button[aria-label*="Current theme"], button[data-testid="theme-toggle"]'
    ) as HTMLButtonElement;
    let canToggleMode = false;
    let canSwitchVariants = false;

    if (themeToggle) {
      // Check if this is a dropdown toggle (has role="combobox") or simple toggle
      const isDropdownMode = themeToggle.getAttribute('role') === 'combobox';

      if (isDropdownMode) {
        // Check if dropdown is already open
        const isAlreadyOpen = themeToggle.getAttribute('data-state') === 'open';

        if (!isAlreadyOpen) {
          // Open the dropdown
          themeToggle.click();
          await new Promise(resolve => setTimeout(resolve, 300));
        }

        // Look for the dropdown content (may be in a portal)
        const dropdownContent = document.querySelector('[data-radix-popper-content-wrapper]') ||
                               document.querySelector('[role="menu"]');

        if (dropdownContent) {
          // Look for the mode toggle menu item within the dropdown
          const modeToggleItem = Array.from(dropdownContent.querySelectorAll('[role="menuitem"]')).find(item =>
            item.textContent?.includes('Switch to') && (item.textContent?.includes('Dark') || item.textContent?.includes('Light'))
          ) as HTMLElement;

          if (modeToggleItem) {
            modeToggleItem.click();
            await new Promise(resolve => setTimeout(resolve, 500));

            // Check theme change directly
            const newMode = localStorage.getItem('theme-mode') || localStorage.getItem('theme') || 'light';
            const newClass = document.documentElement.className;
            canToggleMode = newMode !== initialMode || newClass !== initialClass;
          }

          // Test variant switching - check if menu items are available
          const menuItems = dropdownContent.querySelectorAll('[role="menuitem"]');
          canSwitchVariants = menuItems.length > 3; // Should have more than just basic options

          // Close dropdown if still open
          if (document.querySelector('[role="menu"]')) {
            // Click outside to close
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }
      } else {
        // For simple mode, direct click should toggle theme
        themeToggle.click();
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check theme change directly
        const newMode = localStorage.getItem('theme-mode') || localStorage.getItem('theme') || 'light';
        const newClass = document.documentElement.className;
        canToggleMode = newMode !== initialMode || newClass !== initialClass;

        // Simple mode doesn't have variant switching
        canSwitchVariants = false;
      }
    }

    // Test animations
    const animatedElements = document.querySelectorAll('[class*="transition"], [data-framer-motion]');
    const animationsWork = animatedElements.length > 0;

    // Test persistence - check localStorage directly
    const finalMode = localStorage.getItem('theme-mode') || localStorage.getItem('theme');
    const finalVariant = localStorage.getItem('theme-variant');
    const persistenceWorks = Boolean(finalMode) && Boolean(finalVariant);

    return {
      canToggleMode,
      canSwitchVariants,
      animationsWork,
      persistenceWorks
    };
  } finally {
    (window as any).__themeSwitchingTestRunning = false;
  }
}

/**
 * Run comprehensive cross-page test
 */
export async function runCrossPageTest(currentPath: string): Promise<{
  page: string;
  headerPresence: ReturnType<typeof testHeaderThemeTogglePresence>;
  themePersistence: ReturnType<typeof testThemePersistence>;
  responsiveBehavior: ReturnType<typeof testResponsiveBehavior>;
  keyboardAccessibility: ReturnType<typeof testKeyboardAccessibility>;
  themeSwitching: Awaited<ReturnType<typeof testThemeSwitching>>;
  timestamp: number;
}> {
  const results = {
    page: currentPath,
    headerPresence: testHeaderThemeTogglePresence(),
    themePersistence: testThemePersistence(),
    responsiveBehavior: testResponsiveBehavior(),
    keyboardAccessibility: testKeyboardAccessibility(),
    themeSwitching: await testThemeSwitching(),
    timestamp: Date.now()
  };

  return results;
}

/**
 * Generate test report
 */
export function generateTestReport(results: Awaited<ReturnType<typeof runCrossPageTest>>[]): {
  totalPages: number;
  passedTests: number;
  failedTests: number;
  score: number;
  issues: string[];
  recommendations: string[];
} {
  const totalPages = results.length;
  let passedTests = 0;
  let failedTests = 0;
  const issues: string[] = [];
  const recommendations: string[] = [];

  results.forEach(result => {
    const { page, headerPresence, themePersistence, responsiveBehavior, keyboardAccessibility, themeSwitching } = result;

    // Check header presence
    if (headerPresence.hasHeader && headerPresence.hasThemeToggle) {
      passedTests++;
    } else {
      failedTests++;
      issues.push(`${page}: Missing header or theme toggle`);
    }

    // Check theme persistence - improved validation
    if (Boolean(themePersistence.themeMode) && Boolean(themePersistence.themeVariant)) {
      passedTests++;
    } else {
      failedTests++;
      issues.push(`${page}: Theme persistence issues - mode: ${themePersistence.themeMode}, variant: ${themePersistence.themeVariant}`);
    }

    // Check responsive behavior
    if (responsiveBehavior.headerLayout !== 'not-found') {
      passedTests++;
    } else {
      failedTests++;
      issues.push(`${page}: Header layout issues`);
    }

    // Check accessibility
    if (keyboardAccessibility.focusableElements > 0 && keyboardAccessibility.ariaLabels > 0) {
      passedTests++;
    } else {
      failedTests++;
      issues.push(`${page}: Accessibility issues`);
    }

    // Check theme switching functionality
    if (themeSwitching.canToggleMode && themeSwitching.persistenceWorks) {
      passedTests++;
    } else {
      failedTests++;
      issues.push(`${page}: Theme switching issues`);
    }
  });

  const score = totalPages > 0 ? Math.round((passedTests / (passedTests + failedTests)) * 100) : 0;

  // Generate recommendations
  if (issues.length > 0) {
    recommendations.push('Fix header theme toggle integration on pages with issues');
  }
  if (score < 90) {
    recommendations.push('Improve theme persistence and switching functionality');
  }
  if (results.some(r => r.keyboardAccessibility.ariaLabels < 3)) {
    recommendations.push('Add more ARIA labels for better accessibility');
  }

  return {
    totalPages,
    passedTests,
    failedTests,
    score,
    issues,
    recommendations
  };
}
