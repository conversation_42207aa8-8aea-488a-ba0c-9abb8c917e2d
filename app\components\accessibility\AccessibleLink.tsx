'use client';

import React from 'react';
import Link from 'next/link';
import { VisuallyHidden } from '@/app/components/accessibility/VisuallyHidden';
import { cn } from '@/app/lib/utils';
import { shouldReduceAnimations } from '@/app/utils/accessibility';
import { ExternalLink } from 'lucide-react';

export interface AccessibleLinkProps {
  /**
   * URL to navigate to
   */
  href: string;
  
  /**
   * Link content
   */
  children: React.ReactNode;
  
  /**
   * Additional accessible label (for icon-only links or clarification)
   */
  accessibleLabel?: string;
  
  /**
   * Whether the link is external (opens in new tab)
   */
  isExternal?: boolean;
  
  /**
   * Whether to show the external link icon
   */
  showExternalIcon?: boolean;
  
  /**
   * Whether the link is currently active
   */
  isActive?: boolean;
  
  /**
   * Whether to show a visible focus indicator even when using mouse
   */
  alwaysShowFocus?: boolean;
  
  /**
   * Whether to reduce motion for animations
   */
  reduceMotion?: boolean;
  
  /**
   * Additional CSS class names
   */
  className?: string;
  
  /**
   * Additional props to pass to the link
   */
  [key: string]: any;
}

/**
 * AccessibleLink component for accessible links with proper attributes
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleLink: React.FC<AccessibleLinkProps> = ({
  href,
  children,
  accessibleLabel,
  isExternal = false,
  showExternalIcon = true,
  isActive = false,
  alwaysShowFocus = false,
  reduceMotion: propReduceMotion,
  className,
  ...props
}) => {
  // Determine if we should reduce motion based on props or system preferences
  const reduceMotion = propReduceMotion ?? shouldReduceAnimations();
  
  // Determine if we need a visually hidden label
  const needsVisuallyHiddenLabel = accessibleLabel && 
    (!children || typeof children !== 'string' || React.Children.count(children) === 0);
  
  // Enhanced class names for focus visibility
  const enhancedClassName = cn(
    'transition-colors',
    alwaysShowFocus && 'focus:ring-2 focus:ring-theme-focus focus:ring-offset-2 focus:outline-none',
    reduceMotion && 'transition-none animate-none',
    isActive && 'font-medium',
    className
  );
  
  // External link attributes
  const externalLinkProps = isExternal ? {
    target: '_blank',
    rel: 'noopener noreferrer',
    'aria-label': accessibleLabel || (typeof children === 'string' ? `${children} (opens in new tab)` : undefined)
  } : {};
  
  // If it's an external link, use a regular anchor tag
  if (isExternal) {
    return (
      <a
        href={href}
        className={enhancedClassName}
        {...externalLinkProps}
        {...props}
      >
        {children}
        {showExternalIcon && (
          <ExternalLink size={14} className="ml-1 inline-block" />
        )}
        {needsVisuallyHiddenLabel && <VisuallyHidden>{accessibleLabel}</VisuallyHidden>}
      </a>
    );
  }
  
  // For internal links, use Next.js Link component
  return (
    <Link
      href={href}
      className={enhancedClassName}
      aria-current={isActive ? 'page' : undefined}
      {...props}
    >
      {children}
      {needsVisuallyHiddenLabel && <VisuallyHidden>{accessibleLabel}</VisuallyHidden>}
    </Link>
  );
};

export default AccessibleLink;
