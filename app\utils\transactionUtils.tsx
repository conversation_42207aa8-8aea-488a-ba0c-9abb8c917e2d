import { 
  ArrowDownLeft, 
  ArrowUpRight, 
  Factory, 
  Package, 
  RefreshCw, 
  Truck 
} from 'lucide-react';

/**
 * Transaction type definitions and mappings
 */
export const TRANSACTION_TYPES = {
  STOCK_IN_PURCHASE: 'stock_in_purchase',
  STOCK_OUT_PRODUCTION: 'stock_out_production',
  ADJUSTMENT_CYCLE_COUNT: 'adjustment_cycle_count',
  STOCK_IN_PRODUCTION: 'stock_in_production',
  TRANSFER_OUT: 'transfer_out',
  TRANSFER_IN: 'transfer_in',
  SALES_SHIPMENT: 'sales_shipment'
} as const;

/**
 * Transaction type display names
 */
export const TRANSACTION_TYPE_LABELS: Record<string, string> = {
  'stock_in_purchase': 'Stock In Purchase',
  'stock_out_production': 'Stock Out Production',
  'stock_in_production': 'Stock In Production',
  'adjustment_cycle_count': 'Adjustment Cycle Count',
  'adjustment_manual': 'Adjustment Manual',
  'transfer_in': 'Transfer In',
  'transfer_out': 'Transfer Out',
  'sales_shipment': 'Sales Shipment',
  'return_from_production': 'Return From Production',
  'return_to_supplier': 'Return To Supplier',
  // Legacy support
  'purchase_receipt': 'Purchase Receipt',
  'production_consumption': 'Production Consumption',
  'stock_adjustment': 'Stock Adjustment',
  'production_output': 'Production Output',
  'internal_transfer_out': 'Internal Transfer Out',
  'internal_transfer_in': 'Internal Transfer In',
  'stock_in': 'Stock In',
  'stock_out': 'Stock Out'
};

/**
 * Get the appropriate icon for a transaction type
 */
export function getTransactionTypeIcon(transactionType: string, size: 'sm' | 'md' | 'lg' = 'md') {
  const sizeClass = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }[size];

  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return <ArrowDownLeft className={`${sizeClass} text-green-600`} />;
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return <ArrowUpRight className={`${sizeClass} text-red-600`} />;
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return <RefreshCw className={`${sizeClass} text-blue-600`} />;
  }
  if (type.includes('transfer')) {
    return <Truck className={`${sizeClass} text-amber-600`} />;
  }
  if (type.includes('production') && (type.includes('stock in') || type.includes('output'))) {
    return <Factory className={`${sizeClass} text-purple-600`} />;
  }
  
  return <Package className={`${sizeClass} text-gray-600`} />;
}

/**
 * Get the appropriate color class for transaction type
 */
export function getTransactionTypeColor(transactionType: string): string {
  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return 'text-green-600';
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return 'text-red-600';
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return 'text-blue-600';
  }
  if (type.includes('transfer')) {
    return 'text-amber-600';
  }
  if (type.includes('production')) {
    return 'text-purple-600';
  }
  
  return 'text-gray-600';
}

/**
 * Get the appropriate badge variant for transaction type
 */
export function getTransactionTypeBadgeVariant(transactionType: string): "default" | "secondary" | "destructive" | "outline" {
  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return 'default'; // Green-ish
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return 'destructive'; // Red-ish
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return 'secondary'; // Blue-ish
  }
  
  return 'outline';
}

/**
 * Format transaction type for display
 */
export function formatTransactionType(type: string): string {
  return TRANSACTION_TYPE_LABELS[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Get quantity change display information
 */
export function getQuantityChangeDisplay(quantity: number) {
  if (quantity > 0) {
    return {
      icon: <ArrowDownLeft className="h-4 w-4 text-green-600" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      prefix: '+',
      label: 'Stock Increase'
    };
  } else if (quantity < 0) {
    return {
      icon: <ArrowUpRight className="h-4 w-4 text-red-600" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      prefix: '',
      label: 'Stock Decrease'
    };
  } else {
    return {
      icon: <RefreshCw className="h-4 w-4 text-gray-600" />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50 dark:bg-gray-900/20',
      prefix: '',
      label: 'No Change'
    };
  }
}

/**
 * Get reference type display information
 */
export function getReferenceTypeInfo(referenceType?: string) {
  const typeMap: Record<string, { label: string; color: string }> = {
    'PurchaseOrder': { label: 'Purchase Order', color: 'text-blue-600' },
    'WorkOrder': { label: 'Work Order', color: 'text-purple-600' },
    'SalesOrder': { label: 'Sales Order', color: 'text-green-600' },
    'StockAdjustment': { label: 'Stock Adjustment', color: 'text-amber-600' }
  };

  return typeMap[referenceType || ''] || { label: referenceType || 'Unknown', color: 'text-gray-600' };
}

/**
 * Format transaction date for display
 */
export function formatTransactionDate(date: string | Date): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Generate logical stock movement description based on transaction type and data
 * Enhanced for V4 schema with proper location/warehouse population
 */
export function getStockMovementDescription(transaction: any): string {
  const type = transaction.transactionType?.toLowerCase() || '';
  const quantity = Math.abs(transaction.quantity || 0);
  const itemName = transaction.itemName || transaction.partNumber || 'Unknown Item';

  // Helper function to format location with enhanced V4 schema support
  const formatLocation = (locationData: any, stockType?: string) => {
    if (!locationData) return 'Unknown Location';

    // V4 Schema: Use populated location and warehouse data
    const warehouseName = locationData.warehouseName || locationData.warehouse?.name || 'Unknown Warehouse';

    // Enhanced location name with fallbacks for null/empty names
    let locationName = locationData.locationName || locationData.location?.name;
    if (!locationName) {
      // Fallback to description or default based on location type
      const description = locationData.location?.description || '';
      const locationType = locationData.location?.locationType || '';

      if (description.includes('Default legacy location')) {
        locationName = `(Default Area)`;
      } else if (locationType) {
        locationName = locationType;
      } else {
        locationName = 'Unknown Location';
      }
    }

    const stockTypeName = stockType || locationData.stockType || '';
    const stockTypeDisplay = stockTypeName ? ` (${stockTypeName})` : '';

    return `${warehouseName} - ${locationName}${stockTypeDisplay}`;
  };

  // Extract location information from V4 schema
  const fromLocation = transaction.from;
  const toLocation = transaction.to;

  // Generate description based on transaction type
  if (type.includes('purchase') || type.includes('receipt') || type.includes('stock in')) {
    const destination = formatLocation(toLocation, toLocation?.stockType);
    return `Added ${quantity} units of ${itemName} to ${destination}`;
  }

  if (type.includes('sales') || type.includes('shipment') || type.includes('stock out')) {
    const source = formatLocation(fromLocation, fromLocation?.stockType);
    return `Removed ${quantity} units of ${itemName} from ${source}`;
  }

  if (type.includes('transfer') || type.includes('internal')) {
    const source = formatLocation(fromLocation, fromLocation?.stockType);
    const destination = formatLocation(toLocation, toLocation?.stockType);
    return `Moved ${quantity} units of ${itemName} from ${source} to ${destination}`;
  }

  if (type.includes('adjustment')) {
    const location = formatLocation(toLocation || fromLocation, (toLocation || fromLocation)?.stockType);
    const sign = transaction.quantity >= 0 ? '+' : '';
    return `Adjusted ${sign}${transaction.quantity} units of ${itemName} in ${location}`;
  }

  if (type.includes('production') && (type.includes('receipt') || type.includes('output') || type.includes('stock in'))) {
    const destination = formatLocation(toLocation, toLocation?.stockType);
    return `Produced ${quantity} units of ${itemName} added to ${destination}`;
  }

  if (type.includes('production') && (type.includes('consumption') || type.includes('issue') || type.includes('stock out'))) {
    const source = formatLocation(fromLocation, fromLocation?.stockType);
    return `Consumed ${quantity} units of ${itemName} from ${source}`;
  }

  // Default fallback
  const location = formatLocation(toLocation || fromLocation, (toLocation || fromLocation)?.stockType);
  return `${formatTransactionType(transaction.transactionType || 'Unknown')}: ${quantity} units of ${itemName} at ${location}`;
}

/**
 * Get warehouse and location display information
 * Enhanced for V4 schema with proper location/warehouse population
 */
export function getWarehouseLocationDisplay(transaction: any): {
  source?: { warehouse: string; location: string; stockType?: string };
  destination?: { warehouse: string; location: string; stockType?: string };
} {
  const type = transaction.transactionType?.toLowerCase() || '';

  // Helper function to extract location data from V4 schema
  const extractLocationData = (locationData: any) => {
    if (!locationData) return { warehouse: '', location: '', stockType: '' };

    return {
      warehouse: locationData.warehouseName || locationData.warehouse?.name || '',
      location: locationData.locationName || locationData.location?.name || '',
      stockType: locationData.stockType || ''
    };
  };

  const source = extractLocationData(transaction.from);
  const destination = extractLocationData(transaction.to);

  // Return appropriate data based on transaction type
  if (type.includes('purchase') || type.includes('receipt') || type.includes('stock in')) {
    return { destination };
  }

  if (type.includes('sales') || type.includes('shipment') || type.includes('stock out')) {
    return { source };
  }

  if (type.includes('transfer') || type.includes('internal')) {
    return { source, destination };
  }

  if (type.includes('adjustment')) {
    return { destination: destination.warehouse ? destination : source };
  }

  if (type.includes('production')) {
    if (type.includes('receipt') || type.includes('output') || type.includes('stock in')) {
      return { destination };
    } else {
      return { source };
    }
  }

  // Default fallback
  return { destination: destination.warehouse ? destination : source };
}

/**
 * Get stock level status and color
 */
export function getStockLevelStatus(stock: number) {
  if (stock <= 0) {
    return {
      status: 'Out of Stock',
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20'
    };
  } else if (stock < 10) {
    return {
      status: 'Low Stock',
      color: 'text-amber-600',
      bgColor: 'bg-amber-50 dark:bg-amber-900/20'
    };
  } else if (stock < 50) {
    return {
      status: 'Medium Stock',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    };
  } else {
    return {
      status: 'Good Stock',
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20'
    };
  }
}
