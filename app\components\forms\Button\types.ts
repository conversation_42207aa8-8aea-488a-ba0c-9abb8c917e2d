import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

/**
 * Button variants using class-variance-authority
 */
export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-theme-focus focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-accent-primary text-bg-primary hover:bg-accent-primary/90",
        destructive:
          "bg-theme-error text-white hover:bg-theme-error/90",
        outline:
          "border border-border-primary bg-bg-primary hover:bg-theme-hover text-text-primary",
        secondary:
          "bg-bg-secondary text-text-primary hover:bg-theme-hover",
        ghost: "hover:bg-theme-hover text-text-primary",
        link: "text-accent-primary underline-offset-4 hover:underline",
        success: "bg-theme-success text-white hover:bg-theme-success/90",
        warning: "bg-theme-warning text-white hover:bg-theme-warning/90",
        info: "bg-theme-info text-white hover:bg-theme-info/90",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

/**
 * Props for the Button component
 */
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}