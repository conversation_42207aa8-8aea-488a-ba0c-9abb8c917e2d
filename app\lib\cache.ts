/**
 * Simple In-Memory Cache for API Performance Optimization
 * 
 * Provides basic caching functionality for frequently accessed data
 * to reduce database queries and improve response times.
 * 
 * Note: This is an in-memory cache suitable for single-instance deployments.
 * For production with multiple instances, consider Redis or similar.
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
  hits: number;
}

class SimpleCache {
  private cache = new Map<string, CacheEntry<any>>();
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize: number = 1000, defaultTTL: number = 300000) { // 5 minutes default
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 300000);
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Increment hit counter
    entry.hits++;
    
    return entry.data as T;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      hits: 0
    };

    this.cache.set(key, entry);
  }

  /**
   * Delete data from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{ key: string; hits: number; age: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      hits: entry.hits,
      age: Date.now() - entry.timestamp
    }));

    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const hitRate = entries.length > 0 ? totalHits / entries.length : 0;

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate,
      entries: entries.sort((a, b) => b.hits - a.hits) // Sort by hits descending
    };
  }

  /**
   * Remove expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`[Cache] Cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  /**
   * Evict oldest entries when cache is full
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }
}

// Create singleton cache instance
const cache = new SimpleCache();

/**
 * Cache wrapper for async functions
 */
export async function withCache<T>(
  key: string,
  fetchFunction: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Try to get from cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    console.log(`[Cache] Hit for key: ${key}`);
    return cached;
  }

  // Fetch data and cache it
  console.log(`[Cache] Miss for key: ${key}, fetching...`);
  const data = await fetchFunction();
  cache.set(key, data, ttl);
  
  return data;
}

/**
 * Generate cache key for parts API
 */
export function generatePartsKey(
  page: number,
  limit: number,
  filters: Record<string, any> = {},
  sort: Record<string, any> = {}
): string {
  const filterStr = Object.keys(filters)
    .sort()
    .map(key => `${key}:${filters[key]}`)
    .join('|');
  
  const sortStr = Object.keys(sort)
    .sort()
    .map(key => `${key}:${sort[key]}`)
    .join('|');

  return `parts:${page}:${limit}:${filterStr}:${sortStr}`;
}

/**
 * Generate cache key for search results
 */
export function generateSearchKey(
  query: string,
  page: number,
  limit: number,
  filters: Record<string, any> = {}
): string {
  const filterStr = Object.keys(filters)
    .sort()
    .map(key => `${key}:${filters[key]}`)
    .join('|');

  return `search:${encodeURIComponent(query)}:${page}:${limit}:${filterStr}`;
}

/**
 * Invalidate cache entries by pattern
 */
export function invalidatePattern(pattern: string): number {
  const stats = cache.getStats();
  let deletedCount = 0;

  stats.entries.forEach(entry => {
    if (entry.key.includes(pattern)) {
      cache.delete(entry.key);
      deletedCount++;
    }
  });

  console.log(`[Cache] Invalidated ${deletedCount} entries matching pattern: ${pattern}`);
  return deletedCount;
}

/**
 * Cache warming for frequently accessed data
 */
export async function warmCache(
  keys: Array<{ key: string; fetchFunction: () => Promise<any>; ttl?: number }>
): Promise<void> {
  console.log(`[Cache] Warming cache with ${keys.length} entries...`);
  
  const promises = keys.map(async ({ key, fetchFunction, ttl }) => {
    try {
      const data = await fetchFunction();
      cache.set(key, data, ttl);
      console.log(`[Cache] Warmed: ${key}`);
    } catch (error) {
      console.error(`[Cache] Failed to warm ${key}:`, error);
    }
  });

  await Promise.all(promises);
  console.log('[Cache] Cache warming completed');
}

// Export cache instance and utilities
export { cache };
export default {
  get: cache.get.bind(cache),
  set: cache.set.bind(cache),
  delete: cache.delete.bind(cache),
  clear: cache.clear.bind(cache),
  has: cache.has.bind(cache),
  getStats: cache.getStats.bind(cache),
  withCache,
  generatePartsKey,
  generateSearchKey,
  invalidatePattern,
  warmCache
};
