'use client';

import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface DeleteAssemblyActionProps {
  assembly: Assembly;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Delete assembly action component
 */
export function DeleteAssemblyAction({
  assembly,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: DeleteAssemblyActionProps) {
  const router = useRouter();
  const { deleteAssembly } = useAssemblies();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!assembly._id) {
      setIsDialogOpen(false);
      return;
    }

    setIsDeleting(true);

    try {
      const success = await deleteAssembly(assembly._id);

      if (success) {
        // Close dialog
        setIsDialogOpen(false);

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Refresh the page
        router.refresh();
      }
    } finally {
      setIsDeleting(false);
    }
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="destructive"
            size={size}
            onClick={() => setIsDialogOpen(true)}
          >
            <Trash2 size={16} className="mr-2" />
            Delete
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={() => setIsDialogOpen(true)}
            className="text-red-500 hover:text-red-700 hover:bg-red-100"
          >
            <Trash2 size={16} className="mr-2" />
            Delete
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size={size}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setIsDialogOpen(true);
                  }}
                  className={`text-red-500 hover:text-red-700 hover:bg-red-100 ${className || ''}`}
                  id={id}
                  style={{ position: 'relative', zIndex: 30 }}
                >
                  <Trash2 size={size === 'sm' ? 16 : 20} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Delete Assembly</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Assembly"
        description={
          <>
            <p>Are you sure you want to delete the assembly <strong>"{assembly.name}"</strong>?</p>
            <p className="mt-2 text-sm">This action cannot be undone. This will permanently delete the assembly and remove all of its data from our servers.</p>
          </>
        }
        confirmLabel="Delete Assembly"
        cancelLabel="Cancel"
        variant="destructive"
        isLoading={isDeleting}
      />
    </>
  );
}