import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import { logOperation } from '@/app/services/logging';
import {
  validateObjectIdArray,
  parseJsonWithValidation,
  createValidationErrorResponse
} from '@/app/lib/validation-utils';
import { withComprehensiveValidation } from '@/app/middlewares/apiValidation';
import { executeBulkStockUpdate, BulkStockUpdateParams } from '@/app/services/inventory.service';
import { handleMongoDBError } from '@/app/services/mongodb';

/**
 * Bulk inventory fetching endpoint to prevent N+1 queries
 * Fetches inventory data for multiple parts in a single request
 */
// Type definitions for aggregation results
interface InventoryAggregationResult {
  _id: string;
  partId: string;
  totalStock: number;
  stockLevels: {
    stockType: string;
    quantity: number;
    locationId: string;
  }[];
  lastUpdated: Date;
}

interface BulkInventoryResult {
  [partId: string]: {
    totalStock: number;
    stockLevels: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    lastUpdated: Date | null;
  };
}

async function handleBulkInventoryFetch(request: NextRequest) {
  try {
    const { db } = await connectToDatabase();

    // Enhanced JSON parsing with validation
    const parseResult = await parseJsonWithValidation(request, {
      requireContentType: true,
      maxSize: 1024 * 1024 // 1MB max
    });

    if (!parseResult.success) {
      return createValidationErrorResponse(
        parseResult.error!,
        parseResult.statusCode!
      );
    }

    const { partIds } = parseResult.data;

    // Enhanced ObjectId array validation with reasonable limits
    const validation = validateObjectIdArray(partIds, 'partIds'); // Use default limit
    if (!validation.isValid) {
      return createValidationErrorResponse(validation.error!);
    }

    logOperation('BULK_INVENTORY_FETCH', 'api', {
      partCount: validation.validIds!.length,
      requestSize: JSON.stringify(parseResult.data).length
    });

    // Convert validated string IDs to ObjectIds
    const objectIds = validation.validIds!.map(id => new mongoose.Types.ObjectId(id));

    // Aggregate inventory data for all requested parts
    const inventoryData = await db.collection('inventories').aggregate([
      {
        $match: { partId: { $in: objectIds } }
      },
      {
        $group: {
          _id: '$partId',
          totalStock: { $sum: '$quantity' },
          stockLevels: {
            $push: {
              stockType: '$stockType',
              quantity: '$quantity',
              locationId: '$locationId'
            }
          },
          lastUpdated: { $max: '$lastUpdated' }
        }
      },
      {
        $project: {
          partId: '$_id',
          totalStock: 1,
          stockLevels: {
            $arrayToObject: {
              $map: {
                input: [
                  { k: 'raw', v: 0 },
                  { k: 'hardening', v: 0 },
                  { k: 'grinding', v: 0 },
                  { k: 'finished', v: 0 },
                  { k: 'rejected', v: 0 }
                ],
                as: 'stockType',
                in: {
                  k: '$$stockType.k',
                  v: {
                    $sum: {
                      $map: {
                        input: {
                          $filter: {
                            input: '$stockLevels',
                            cond: { $eq: ['$$this.stockType', '$$stockType.k'] }
                          }
                        },
                        as: 'stock',
                        in: '$$stock.quantity'
                      }
                    }
                  }
                }
              }
            }
          },
          lastUpdated: 1
        }
      }
    ]).toArray() as InventoryAggregationResult[];

    // Create a map for easy lookup
    const inventoryMap = new Map<string, BulkInventoryResult[string]>();
    inventoryData.forEach((item: InventoryAggregationResult) => {
      inventoryMap.set(item.partId.toString(), {
        totalStock: item.totalStock,
        stockLevels: item.stockLevels as any, // The aggregation pipeline transforms this
        lastUpdated: item.lastUpdated
      });
    });

    // Ensure all requested parts have entries (even if zero stock)
    const result: BulkInventoryResult = {};
    validation.validIds!.forEach((partId: string) => {
      result[partId] = inventoryMap.get(partId) || {
        totalStock: 0,
        stockLevels: {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: 0,
          rejected: 0
        },
        lastUpdated: null
      };
    });

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        requestedCount: validation.validIds!.length,
        foundCount: inventoryData.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in bulk inventory fetch:', error);

    // Enhanced error handling with proper status codes
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        return createValidationErrorResponse('Request timeout', 408);
      }
      if (error.message.includes('connection')) {
        return createValidationErrorResponse('Database connection error', 503);
      }
    }

    return createValidationErrorResponse('Failed to fetch bulk inventory data', 500);
  }
}

/**
 * PUT /api/inventories/bulk
 *
 * Atomically updates multiple inventory records for a given part.
 * All updates are processed within a single MongoDB transaction to ensure data integrity.
 */
async function handleBulkInventoryUpdate(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log(`[API] PUT /api/inventories/bulk - Starting bulk update`);

    // Enhanced JSON parsing with validation
    const parseResult = await parseJsonWithValidation(request, {
      requireContentType: true,
      maxSize: 1024 * 1024 // 1MB max
    });

    if (!parseResult.success) {
      return createValidationErrorResponse(
        parseResult.error!,
        parseResult.statusCode!
      );
    }

    const body = parseResult.data;

    // Validate required fields
    const requiredFields = ['partId', 'userId', 'reason', 'updates'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return createValidationErrorResponse(
          `Missing required field: ${field}`,
          400
        );
      }
    }

    // Validate updates array
    if (!Array.isArray(body.updates) || body.updates.length === 0) {
      return createValidationErrorResponse(
        'Updates must be a non-empty array',
        400
      );
    }

    // Validate each update object
    for (let i = 0; i < body.updates.length; i++) {
      const update = body.updates[i];
      const requiredUpdateFields = ['locationId', 'stockType', 'newQuantity'];

      for (const field of requiredUpdateFields) {
        if (update[field] === undefined || update[field] === null) {
          return createValidationErrorResponse(
            `Missing required field '${field}' in update at index ${i}`,
            400
          );
        }
      }

      // Validate stock type
      const validStockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
      if (!validStockTypes.includes(update.stockType)) {
        return createValidationErrorResponse(
          `Invalid stock type '${update.stockType}' at index ${i}. Must be one of: ${validStockTypes.join(', ')}`,
          400
        );
      }

      // Validate quantity is non-negative number
      if (typeof update.newQuantity !== 'number' || update.newQuantity < 0) {
        return createValidationErrorResponse(
          `Invalid newQuantity at index ${i}. Must be a non-negative number`,
          400
        );
      }
    }

    // Prepare parameters for the service function
    const bulkUpdateParams: BulkStockUpdateParams = {
      partId: body.partId,
      userId: body.userId,
      reason: body.reason,
      updates: body.updates
    };

    logOperation('BULK_INVENTORY_UPDATE', 'api', {
      partId: body.partId,
      updateCount: body.updates.length,
      userId: body.userId
    });

    console.log(`[API] Executing bulk stock update for part ${body.partId} with ${body.updates.length} updates`);

    // Execute the bulk update
    const result = await executeBulkStockUpdate(bulkUpdateParams);

    const duration = Date.now() - startTime;

    console.log(`[API] Bulk stock update completed successfully in ${duration}ms - Updated ${result.updatedRecords} records`);

    return NextResponse.json({
      success: true,
      message: `Inventory for part updated successfully. ${result.updatedRecords} records updated.`,
      data: {
        updatedRecords: result.updatedRecords,
        transactionIds: result.transactionIds
      },
      meta: {
        duration: `${duration}ms`,
        partId: body.partId,
        updatesProcessed: body.updates.length,
        transactionsCreated: result.transactionIds.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: unknown) {
    const duration = Date.now() - startTime;

    console.error(`[API] PUT /api/inventories/bulk failed:`, error);

    if (error instanceof Error) {
      // Handle known MongoDB errors
      const mongoError = handleMongoDBError(error);

      return createValidationErrorResponse(
        mongoError.message,
        mongoError.status || 500
      );
    }

    // Handle unknown errors
    return createValidationErrorResponse(
      'An unexpected error occurred during bulk inventory update',
      500
    );
  }
}

// Apply comprehensive validation middleware
export const POST = withComprehensiveValidation(handleBulkInventoryFetch, {
  allowedMethods: ['POST'],
  requireContentType: true,
  maxRequestSize: 1024 * 1024, // 1MB
  rateLimit: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100 // 100 requests per minute
  }
});

export const PUT = withComprehensiveValidation(handleBulkInventoryUpdate, {
  allowedMethods: ['PUT'],
  requireContentType: true,
  maxRequestSize: 1024 * 1024, // 1MB
  rateLimit: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50 // 50 requests per minute for updates
  }
});
