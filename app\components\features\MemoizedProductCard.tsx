'use client';

import React from 'react';
import ProductCard from '@/app/components/features/ProductCard';

/**
 * Product data structure for the ProductCard component
 */
interface ProductCardProduct {
  /** Unique identifier for the product */
  id: string;
  /** Name of the product */
  name: string;
  /** Optional description of the product */
  description?: string | null | undefined;
  /** Optional URL to the product image */
  imageUrl?: string | null | undefined;
  /** Current stock quantity */
  currentStock: number;
  /** Minimum stock level before reordering */
  reorderLevel: number;
  /** Optional product category */
  category?: string | null | undefined;
  /** Optional product price */
  price?: number;
  /** Optional total value of on-hand inventory */
  onHandValue?: number | null | undefined;
  /** Optional demand level classification */
  demand?: 'High' | 'Medium' | 'Low' | null;
}

/**
 * Props for the MemoizedProductCard component
 */
interface MemoizedProductCardProps {
  /** Product data to display */
  product: ProductCardProduct;
  /** Whether this is a placeholder card */
  isPlaceholder?: boolean;
  /** Whether this card should be featured/highlighted */
  isFeatured?: boolean;
}

/**
 * Memoized ProductCard component to prevent unnecessary re-renders
 * This component only re-renders when its props actually change
 */
export const MemoizedProductCard = React.memo<MemoizedProductCardProps>(
  ({ product, isPlaceholder = false, isFeatured = false }) => {
    return (
      <ProductCard 
        product={product} 
        isPlaceholder={isPlaceholder} 
        isFeatured={isFeatured} 
      />
    );
  },
  // Custom comparison function for better memoization
  (prevProps, nextProps) => {
    // Compare product properties that affect rendering
    const prevProduct = prevProps.product;
    const nextProduct = nextProps.product;
    
    return (
      prevProduct.id === nextProduct.id &&
      prevProduct.name === nextProduct.name &&
      prevProduct.description === nextProduct.description &&
      prevProduct.imageUrl === nextProduct.imageUrl &&
      prevProduct.currentStock === nextProduct.currentStock &&
      prevProduct.reorderLevel === nextProduct.reorderLevel &&
      prevProduct.category === nextProduct.category &&
      prevProduct.price === nextProduct.price &&
      prevProduct.onHandValue === nextProduct.onHandValue &&
      prevProduct.demand === nextProduct.demand &&
      prevProps.isPlaceholder === nextProps.isPlaceholder &&
      prevProps.isFeatured === nextProps.isFeatured
    );
  }
);

MemoizedProductCard.displayName = "MemoizedProductCard";
