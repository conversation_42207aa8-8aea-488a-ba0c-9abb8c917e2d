"use client";

/**
 * Theme class cache for performance optimization
 * Prevents repeated class string generation
 */
class ThemeClassCache {
  private cache = new Map<string, string>();
  private maxSize = 1000;
  private hitCount = 0;
  private missCount = 0;
  
  /**
   * Get cached class string or generate and cache it
   * @param key Cache key
   * @param generator Function to generate the class string
   * @returns Cached or generated class string
   */
  get(key: string, generator: () => string): string {
    if (this.cache.has(key)) {
      this.hitCount++;
      return this.cache.get(key)!;
    }
    
    this.missCount++;
    
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    
    const value = generator();
    this.cache.set(key, value);
    return value;
  }
  
  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }
  
  /**
   * Get cache statistics
   */
  getStats() {
    const total = this.hitCount + this.missCount;
    return {
      size: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: total > 0 ? (this.hitCount / total) * 100 : 0,
      maxSize: this.maxSize,
    };
  }
  
  /**
   * Set maximum cache size
   * @param size New maximum size
   */
  setMaxSize(size: number): void {
    this.maxSize = size;
    
    // Trim cache if it exceeds new max size
    while (this.cache.size > this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      } else {
        break; // Safety break if no keys available
      }
    }
  }
  
  /**
   * Check if a key exists in cache
   * @param key Cache key to check
   * @returns True if key exists
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }
  
  /**
   * Remove a specific key from cache
   * @param key Cache key to remove
   * @returns True if key was removed
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }
}

/**
 * Global theme class cache instance
 */
export const themeClassCache = new ThemeClassCache();

/**
 * Memoized class name generator
 * Uses cache to prevent repeated string concatenations
 */
export const memoizedClassName = (
  baseClasses: string,
  conditionalClasses: Record<string, boolean> = {},
  additionalClasses: string[] = []
): string => {
  const cacheKey = `${baseClasses}|${JSON.stringify(conditionalClasses)}|${additionalClasses.join(',')}`;
  
  return themeClassCache.get(cacheKey, () => {
    const classes = [baseClasses];
    
    // Add conditional classes
    Object.entries(conditionalClasses).forEach(([className, condition]) => {
      if (condition) {
        classes.push(className);
      }
    });
    
    // Add additional classes
    classes.push(...additionalClasses);
    
    return classes.filter(Boolean).join(' ');
  });
};

/**
 * Performance-optimized theme class builder
 */
export class ThemeClassBuilder {
  private classes: string[] = [];
  private conditionals: Array<{ condition: boolean; classes: string }> = [];
  
  /**
   * Add base classes
   * @param classes Space-separated class names
   * @returns This builder instance
   */
  base(classes: string): this {
    this.classes.push(classes);
    return this;
  }
  
  /**
   * Add conditional classes
   * @param condition Boolean condition
   * @param classes Classes to add if condition is true
   * @returns This builder instance
   */
  when(condition: boolean, classes: string): this {
    this.conditionals.push({ condition, classes });
    return this;
  }
  
  /**
   * Add classes for specific theme mode
   * @param mode Theme mode to check
   * @param currentMode Current theme mode
   * @param classes Classes to add if modes match
   * @returns This builder instance
   */
  forMode(mode: 'light' | 'dark', currentMode: string, classes: string): this {
    return this.when(currentMode === mode, classes);
  }
  
  /**
   * Add classes for specific theme variant
   * @param variant Theme variant to check
   * @param currentVariant Current theme variant
   * @param classes Classes to add if variants match
   * @returns This builder instance
   */
  forVariant(variant: string, currentVariant: string, classes: string): this {
    return this.when(currentVariant === variant, classes);
  }
  
  /**
   * Build the final class string with caching
   * @returns Optimized class string
   */
  build(): string {
    const cacheKey = `builder|${this.classes.join('|')}|${JSON.stringify(this.conditionals)}`;
    
    return themeClassCache.get(cacheKey, () => {
      const allClasses = [...this.classes];
      
      this.conditionals.forEach(({ condition, classes }) => {
        if (condition) {
          allClasses.push(classes);
        }
      });
      
      return allClasses.filter(Boolean).join(' ');
    });
  }
  
  /**
   * Reset the builder for reuse
   * @returns This builder instance
   */
  reset(): this {
    this.classes = [];
    this.conditionals = [];
    return this;
  }
}

/**
 * Create a new theme class builder instance
 * @returns New ThemeClassBuilder instance
 */
export const createThemeClassBuilder = (): ThemeClassBuilder => {
  return new ThemeClassBuilder();
};

/**
 * Debounced theme class generator
 * Useful for components that update frequently
 */
export const createDebouncedThemeClass = (delay = 100) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastResult = '';
  
  return (generator: () => string): string => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      lastResult = generator();
      timeoutId = null;
    }, delay);
    
    return lastResult;
  };
};

/**
 * Theme class utilities for common patterns
 */
export const themeClassUtils = {
  /**
   * Generate interactive element classes
   * @param baseClasses Base classes
   * @param isDisabled Whether element is disabled
   * @returns Interactive classes
   */
  interactive: (baseClasses: string, isDisabled = false): string => {
    return memoizedClassName(baseClasses, {
      'hover:bg-accent/50 focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200': !isDisabled,
      'opacity-50 cursor-not-allowed': isDisabled,
    });
  },
  
  /**
   * Generate card classes with variants
   * @param variant Card variant
   * @param isInteractive Whether card is interactive
   * @returns Card classes
   */
  card: (variant: 'default' | 'elevated' | 'interactive' = 'default', isInteractive = false): string => {
    const baseClasses = 'bg-card text-card-foreground border-border rounded-lg border';
    
    return memoizedClassName(baseClasses, {
      'shadow-sm': variant === 'default',
      'shadow-lg': variant === 'elevated',
      'shadow-sm hover:shadow-md cursor-pointer hover:bg-accent/5': variant === 'interactive' || isInteractive,
    });
  },
  
  /**
   * Generate button classes with variants
   * @param variant Button variant
   * @param size Button size
   * @returns Button classes
   */
  button: (
    variant: 'primary' | 'secondary' | 'ghost' | 'outline' = 'primary',
    size: 'sm' | 'md' | 'lg' = 'md'
  ): string => {
    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
    
    const variantClasses = {
      primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    };
    
    const sizeClasses = {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4',
      lg: 'h-12 px-6 text-lg',
    };
    
    return memoizedClassName(baseClasses, {}, [variantClasses[variant], sizeClasses[size]]);
  },
};

/**
 * Performance monitoring for theme operations
 */
export const themePerformanceMonitor = {
  startTime: 0,
  
  /**
   * Start performance measurement
   */
  start(): void {
    this.startTime = performance.now();
  },
  
  /**
   * End performance measurement and log result
   * @param operation Operation name
   */
  end(operation: string): void {
    const duration = performance.now() - this.startTime;
    
    if (duration > 10) { // Log operations taking more than 10ms
      console.warn(`Theme operation "${operation}" took ${duration.toFixed(2)}ms`);
    }
  },
  
  /**
   * Measure a function's performance
   * @param operation Operation name
   * @param fn Function to measure
   * @returns Function result
   */
  measure<T>(operation: string, fn: () => T): T {
    this.start();
    const result = fn();
    this.end(operation);
    return result;
  },
};

/**
 * Clear all theme-related caches
 * Useful for development or when theme configuration changes
 */
export const clearThemeCaches = (): void => {
  themeClassCache.clear();
  console.log('Theme caches cleared');
};
