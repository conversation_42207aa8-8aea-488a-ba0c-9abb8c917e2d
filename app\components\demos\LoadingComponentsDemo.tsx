"use client";

import React, { useState } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import {
  LoadingSpinner,
  LoadingOverlay,
  LoadingInline,
  LoadingCard,
  SearchLoadingIndicator,
  LoadingSkeleton,
  PageLoadingSkeleton,
  ChartLoadingSkeleton,
  FormLoadingSkeleton,
} from '@/app/components/data-display/loading';

/**
 * Demo component showcasing all standardized loading components
 * This component demonstrates the usage and variants of the loading system
 */
export const LoadingComponentsDemo: React.FC = () => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleProgressDemo = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  return (
    <div className="container mx-auto py-8 px-4 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Loading Components Demo</h1>
        <p className="text-muted-foreground">
          Comprehensive showcase of standardized loading components
        </p>
      </div>

      {/* LoadingSpinner Variants */}
      <Card>
        <CardHeader>
          <CardTitle>LoadingSpinner Variants</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Size variants */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Sizes</h3>
            <div className="flex items-center gap-4 flex-wrap">
              <LoadingSpinner size="xs" message="XS" />
              <LoadingSpinner size="sm" message="SM" />
              <LoadingSpinner size="md" message="MD" />
              <LoadingSpinner size="lg" message="LG" />
              <LoadingSpinner size="xl" message="XL" />
              <LoadingSpinner size="2xl" message="2XL" />
            </div>
          </div>

          {/* Color variants */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Color Variants</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <LoadingSpinner variant="default" message="Default" />
              <LoadingSpinner variant="muted" message="Muted" />
              <LoadingSpinner variant="accent" message="Accent" />
              <LoadingSpinner variant="destructive" message="Destructive" />
              <LoadingSpinner variant="success" message="Success" />
              <LoadingSpinner variant="warning" message="Warning" />
            </div>
          </div>

          {/* Container variants */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Container Types</h3>
            <div className="space-y-4">
              <div className="border rounded p-4">
                <p className="text-sm text-muted-foreground mb-2">Inline:</p>
                <LoadingSpinner container="inline" message="Inline loading" />
              </div>
              
              <div className="border rounded p-4">
                <p className="text-sm text-muted-foreground mb-2">Block:</p>
                <LoadingSpinner container="block" message="Block loading" />
              </div>
              
              <div className="border rounded p-4">
                <p className="text-sm text-muted-foreground mb-2">Card:</p>
                <LoadingSpinner container="card" message="Card loading" />
              </div>
            </div>
          </div>

          {/* Progress demo */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Progress Indicator</h3>
            <div className="space-y-4">
              <Button onClick={handleProgressDemo}>Start Progress Demo</Button>
              <LoadingSpinner 
                progress={progress} 
                showProgress 
                message="Processing data..." 
                container="block"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Specialized Components */}
      <Card>
        <CardHeader>
          <CardTitle>Specialized Loading Components</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">LoadingInline</h3>
            <div className="flex items-center gap-4">
              <Button disabled>
                <LoadingInline size="sm" />
                Loading...
              </Button>
              <LoadingInline variant="muted" message="Processing..." />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">LoadingCard</h3>
            <LoadingCard message="Loading content..." />
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">SearchLoadingIndicator</h3>
            <SearchLoadingIndicator message="Searching for parts..." />
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">LoadingOverlay</h3>
            <div className="space-y-2">
              <Button onClick={() => setShowOverlay(true)}>Show Overlay Demo</Button>
              <p className="text-sm text-muted-foreground">
                Click to see full-screen overlay loading
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Skeleton Components */}
      <Card>
        <CardHeader>
          <CardTitle>Skeleton Loading Components</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">LoadingSkeleton Variants</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-muted-foreground mb-2">List Skeleton:</p>
                <LoadingSkeleton variant="list" count={3} />
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Card Skeleton:</p>
                <LoadingSkeleton variant="card" count={2} />
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Grid Skeleton:</p>
                <LoadingSkeleton variant="grid" count={6} columns={3} />
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Form Skeleton:</p>
                <LoadingSkeleton variant="form" count={4} showHeader />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">ChartLoadingSkeleton</h3>
            <ChartLoadingSkeleton title="Sample Chart" showLegend />
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">FormLoadingSkeleton</h3>
            <FormLoadingSkeleton fields={4} showSubmit />
          </div>
        </CardContent>
      </Card>

      {/* Page Loading Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Page Loading Skeleton</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg overflow-hidden">
            <PageLoadingSkeleton contentType="grid" showBreadcrumb />
          </div>
        </CardContent>
      </Card>

      {/* Overlay Demo */}
      {showOverlay && (
        <LoadingOverlay 
          message="Loading assembly form..." 
          onClick={() => setShowOverlay(false)}
        />
      )}
    </div>
  );
};
