'use client';

import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { useTheme } from '@/app/contexts/ThemeContext';
import { Filter, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { FilterState, countActiveFilters } from './types';

interface DataTableToolbarProps {
  filters: FilterState;
  showFilters: boolean;
  onToggleFilters: () => void;
  onResetFilters: () => void;
}

/**
 * Toolbar component for the data table with filter controls
 */
export const DataTableToolbar: React.FC<DataTableToolbarProps> = ({
  filters,
  showFilters,
  onToggleFilters,
  onResetFilters,
}) => {
  const { theme } = useTheme();
  const activeFiltersCount = countActiveFilters(filters);
  const [shouldAnimate, setShouldAnimate] = useState(false);

  // Add pulse animation to the badge when it mounts or count changes
  useEffect(() => {
    if (activeFiltersCount > 0) {
      setShouldAnimate(true);

      const timer = setTimeout(() => {
        setShouldAnimate(false);
      }, 800);

      return () => clearTimeout(timer);
    }
    // Return undefined explicitly when condition is not met
    return undefined;
  }, [activeFiltersCount]);

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          onClick={onToggleFilters}
          className="flex items-center gap-2"
        >
          <Filter size={16} />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <Badge
              variant="default"
              className={`ml-1 bg-primary dark:bg-brand-blue text-white h-5 w-5 flex items-center justify-center p-0 rounded-full ${shouldAnimate ? 'animate-ping-once' : ''}`}
            >
              {activeFiltersCount}
            </Badge>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onResetFilters}
            className="h-8 gap-1 text-xs text-muted-foreground dark:text-dark-text-secondary hover:text-foreground dark:hover:text-dark-text-primary"
          >
            <X size={14} />
            <span>Clear all</span>
          </Button>
        )}
      </div>
    </div>
  );
};

// Add the custom animation to the global styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ping-once {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.5);
        opacity: 0.5;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }
    
    .animate-ping-once {
      animation: ping-once 0.8s cubic-bezier(0, 0, 0.2, 1);
    }
  `;
  document.head.appendChild(style);
}
