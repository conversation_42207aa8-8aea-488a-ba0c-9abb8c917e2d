import { NextRequest, NextResponse } from 'next/server';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
// Import service functions and error handler
import { getPurchaseOrder, updatePurchaseOrder, deletePurchaseOrder, handleMongoDBError } from '@/app/services/mongodb';

const ROUTE_PATH = '/api/purchase-orders/[id]';

/**
 * GET handler for fetching a specific purchase order by its poNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the PO Number
 * @returns JSON response with purchase order data or error
 */
async function handleGET(
  _request: NextRequest,
  context?: { params: { id: string } }
) {
  const startTime = Date.now();
  if (!context || !context.params) {
    throw new Error('Internal Server Error: Route parameters missing.');
  }
  const { id: poNumber } = context.params; // The unique string identifier (poNumber)
  
  console.log(`[API] GET /api/purchase-orders/${poNumber} - Fetching purchase order`);

  // Call the service function to get the purchase order
  // Note: getPurchaseOrder service function expects the poNumber
  const purchaseOrder = await getPurchaseOrder(poNumber);

  const duration = Date.now() - startTime;

  if (!purchaseOrder) {
    console.log(`[API] Purchase Order ${poNumber} not found (${duration}ms)`);
    return NextResponse.json(
      { data: null, error: `Purchase Order with number ${poNumber} not found`, meta: { duration } },
      { status: 404 }
    );
  }

  console.log(`[API] Fetched purchase order ${poNumber} successfully (${duration}ms)`);
  return NextResponse.json({ data: purchaseOrder, error: null, meta: { duration } });
}

/**
 * PUT handler for updating a specific purchase order by its poNumber
 * @param request - The incoming request with updated PO data
 * @param params - Route parameters including the PO Number
 * @returns JSON response with updated PO data or error
 */
async function handlePUT(
  request: NextRequest,
  context?: { params: { id: string } }
) {
  const startTime = Date.now();
  if (!context || !context.params) {
    throw new Error('Internal Server Error: Route parameters missing.');
  }
  const { id: poNumber } = context.params;
  
  console.log(`[API] PUT /api/purchase-orders/${poNumber} - Updating purchase order`);
  const updateData = await request.json();

  if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
    return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
  }

  // Call the service function to update the purchase order
  // Note: updatePurchaseOrder service function expects the poNumber
  const updatedPurchaseOrder = await updatePurchaseOrder(poNumber, updateData);

  const duration = Date.now() - startTime;

  console.log(`[API] Updated purchase order ${poNumber} successfully (${duration}ms)`);
  return NextResponse.json({ data: updatedPurchaseOrder, error: null, meta: { duration } });
}

/**
 * DELETE handler for removing a specific purchase order by its poNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the PO Number
 * @returns JSON response indicating success or failure
 */
async function handleDELETE(
  _request: NextRequest,
  context?: { params: { id: string } }
) {
  const startTime = Date.now();
  if (!context || !context.params) {
    throw new Error('Internal Server Error: Route parameters missing.');
  }
  const { id: poNumber } = context.params;
  
  console.log(`[API] DELETE /api/purchase-orders/${poNumber} - Deleting purchase order`);

  // Call the service function to delete the purchase order
  // Note: deletePurchaseOrder service function expects the poNumber
  const result = await deletePurchaseOrder(poNumber);

  const duration = Date.now() - startTime;

  const successMessage = result?.message || `Purchase Order ${poNumber} deleted successfully`;
  console.log(`[API] Deleted purchase order ${poNumber} successfully (${duration}ms)`);
  return NextResponse.json({ success: true, message: successMessage, error: null, meta: { duration } });
}

export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const PUT = withErrorHandling(handlePUT, ROUTE_PATH);
export const DELETE = withErrorHandling(handleDELETE, ROUTE_PATH);
