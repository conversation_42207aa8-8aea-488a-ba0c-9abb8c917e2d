import { NextRequest, NextResponse } from 'next/server';
import { generateCategoryDistribution } from '@/app/services/analytics';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';
import { standardizeAnalyticsResponse } from '@/app/lib/analytics-helpers';
import { InvalidParameterError, DatabaseQueryError, DataProcessingError, AnalyticsError } from '@/app/lib/errors';

/**
 * GET handler for generating category distribution data
 * @param request - The incoming request
 * @returns JSON response with category distribution data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    // Log API request
    await logApiRequest('GET', '/api/analytics/category-distribution', null, true);
    
    try {
      // Generate the report
      const reportData = await generateCategoryDistribution({});
      
      // Standardize the response format to ensure consistent structure
      const standardizedData = standardizeAnalyticsResponse(reportData);
      
      const duration = Date.now() - startTime;
      
      return successResponse(
        standardizedData,
        'Category distribution data generated successfully',
        { duration }
      );
    } catch (serviceError) {
      const duration = Date.now() - startTime;
      let errorCode = 'ANALYTICS_ERROR';
      let statusCode = 500;
      let message = 'An error occurred generating category distribution data';
      let details: any[] = [];
      
      // Handle specific error types
      if (serviceError instanceof InvalidParameterError) {
        errorCode = 'INVALID_PARAMETER';
        statusCode = 400;
        message = serviceError.message;
        details = serviceError.details || [];
      } else if (serviceError instanceof DatabaseQueryError) {
        errorCode = 'DATABASE_ERROR';
        statusCode = 503;
        message = 'Database error while generating category distribution data';
        details = serviceError.details || [];
      } else if (serviceError instanceof DataProcessingError) {
        errorCode = 'DATA_PROCESSING_ERROR';
        statusCode = 500;
        message = 'Error processing data for category distribution';
        details = serviceError.details || [];
      } else if (serviceError instanceof AnalyticsError) {
        message = serviceError.message;
        details = serviceError.details || [];
      }
      
      await logError('API', `Error in GET /api/analytics/category-distribution (${duration}ms): ${message}`, serviceError);
      
      return errorResponse(
        errorCode,
        message,
        details,
        statusCode
      );
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Unhandled error in GET /api/analytics/category-distribution (${duration}ms)`, error);
    
    return errorResponse(
      'INTERNAL_SERVER_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
