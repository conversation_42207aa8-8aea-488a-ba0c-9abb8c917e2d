import {
    deletePartService,
    getPartById,
    getPartByPartNumberService,
    handleMongoDBError,
    UpdatePartDto,
    updatePartService,
} from '@/app/services/part.service';
import { NextRequest, NextResponse } from 'next/server';
// import { getPart } from '@/app/services/mongodb'; // Removed
import mongoose from 'mongoose';

// Helper function to generate request ID
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * GET handler for fetching a specific part by ID
 * @param request - The incoming request
 * @param context - Route context including the part ID (Promise in Next.js 15)
 * @returns JSON response with part data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const identifier = decodeURIComponent(resolvedParams.id);
  try {
    console.log(`[API] GET /api/parts/${identifier} - Fetching part with dual-service architecture`);

    // Extract request context for dual-service architecture
    const userId = request.headers.get('x-user-id') || undefined;
    const requestId = request.headers.get('x-request-id') || generateRequestId();
    const context = { userId, requestId };

    let part;
    if (mongoose.Types.ObjectId.isValid(identifier)) {
      console.log(`[API] GET /api/parts/${identifier} - Attempting to fetch part by ObjectId using unified service.`);
      part = await getPartById(identifier);
    } else {
      console.log(`[API] GET /api/parts/${identifier} - Attempting to fetch part by partNumber using unified service.`);
      part = await getPartByPartNumberService(identifier);
    }

    const duration = Date.now() - startTime;
    if (!part) {
      console.log(`[API] GET /api/parts/${identifier} - Part not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Part with identifier '${identifier}' not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] GET /api/parts/${identifier} - Part fetched successfully (${duration}ms)`);
    return NextResponse.json({ data: part, error: null, meta: { duration } });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] GET /api/parts/${identifier} - Error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific part by ID
 * @param request - The incoming request with updated part data
 * @param context - Route context including the part ID (Promise in Next.js 15)
 * @returns JSON response with updated part data
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const partId = decodeURIComponent(resolvedParams.id);
  try {
    console.log(`[API] PUT /api/parts/${partId} - Updating part`);
    const requestBody = await request.json() as Partial<UpdatePartDto>;

    if (!requestBody || Object.keys(requestBody).length === 0) {
      return NextResponse.json(
        { data: null, error: 'Update data is required and cannot be empty', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(partId)) {
        return NextResponse.json(
            { data: null, error: 'Invalid Part ID format. Must be a valid ObjectId string.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }

    // Strict DTO validation - V4 Schema: Parts only, no inventory
    const allowedTopLevelKeys: (keyof UpdatePartDto)[] = [
      'name', 'businessName', 'description', 'technicalSpecs', 'isManufactured',
      'reorderLevel', 'status', 'supplierId',
      'unitOfMeasure', 'standardCost', 'abcClassification', 'categoryId', // Changed costPrice to standardCost, added abcClassification
      // NEW: Part Master Data Planning Parameters
      'planningMethod', 'safetyStockLevel', 'maximumStockLevel', 'leadTimeDays', 'averageDailyUsage'
    ];
    // REMOVED: allowedInventoryKeys - inventory updates use /api/inventories endpoints

    const validatedUpdateData: UpdatePartDto = {};

    for (const key in requestBody) {
      if (allowedTopLevelKeys.includes(key as keyof UpdatePartDto)) {
        // REMOVED: inventory field handling - V4 Schema uses separate inventories collection
        if (key === 'inventory') {
          return NextResponse.json(
            { data: null, error: 'Inventory updates not supported in parts API. Use /api/inventories endpoints instead.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
          );
        } else {
          // @ts-ignore - assigning to validatedUpdateData
          validatedUpdateData[key] = requestBody[key];
        }
      } else {
        return NextResponse.json(
          { data: null, error: `Invalid field in request body: ${key}`, meta: { duration: Date.now() - startTime } },
          { status: 400 }
        );
      }
    }

    if (Object.keys(validatedUpdateData).length === 0 && Object.keys(requestBody).length > 0) {
        // This case means the requestBody had keys, but none were valid according to our DTO.
        // The earlier loop would have caught specific invalid keys. This is a fallback.
        return NextResponse.json(
            { data: null, error: 'Update data contains no valid fields.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }
    // If validatedUpdateData is empty AND requestBody was also empty, the initial check for empty requestBody handles it.

    const updateData = validatedUpdateData;

    const updatedPart = await updatePartService(partId, updateData);
    const duration = Date.now() - startTime;

    if (!updatedPart) { // Should be handled by service throwing an error, but as a fallback.
      console.log(`[API] PUT /api/parts/${partId} - Part not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Part with ID ${partId} not found`, meta: { duration } },
        { status: 404 }
      );
    }
    console.log(`[API] PUT /api/parts/${partId} - Part updated successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedPart, error: null, meta: { duration } });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] PUT /api/parts/${partId} - Error (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific part by ID
 * @param request - The incoming request
 * @param context - Route context including the part ID (Promise in Next.js 15)
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const partId = decodeURIComponent(resolvedParams.id);
  try {
    console.log(`[API] DELETE /api/parts/${partId} - Deleting part`);

    if (!mongoose.Types.ObjectId.isValid(partId)) {
        return NextResponse.json(
            { success: false, error: 'Invalid Part ID format. Must be a valid ObjectId string.', meta: { duration: Date.now() - startTime } },
            { status: 400 }
        );
    }

    await deletePartService(partId); // Now returns void
    const duration = Date.now() - startTime;
    
    console.log(`[API] DELETE /api/parts/${partId} - Part deleted successfully (${duration}ms)`);
    // Return 200 OK with a success message.
    // A 204 No Content response is also an option, but a JSON response can be more informative.
    return NextResponse.json({ success: true, message: `Part with ID ${partId} deleted successfully.`, meta: { duration } }, { status: 200 });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    // Log the error message and statusCode if available
    console.error(
      `[API] DELETE /api/parts/${partId} - Error (${duration}ms): `,
      error.message,
      error.statusCode ? `Status Code: ${error.statusCode}` : ''
    );
    
    const statusCode = error.statusCode || 500; // Use statusCode from the error if present, otherwise default to 500
    const errorMessage = error.message || 'An unexpected error occurred while deleting the part.';

    return NextResponse.json(
      { success: false, error: errorMessage, meta: { duration } },
      { status: statusCode }
    );
  }
}