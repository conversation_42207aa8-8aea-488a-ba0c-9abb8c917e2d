"use client";

import { <PERSON>ert, AlertDescription, AlertTitle } from '@/app/components/data-display/alert';
import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { captureException, captureMessage } from '@/app/lib/logging-utils';
import { env } from '@/app/utils/env';
import { AlertCircle, CheckCircle2, Info, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';

/**
 * SentryMonitor component for monitoring and testing Sentry integration
 */
export function SentryMonitor() {
  const [status, setStatus] = useState<'idle' | 'checking' | 'success' | 'error'>('idle');
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [lastTestedAt, setLastTestedAt] = useState<string | null>(null);

  // Check Sentry connectivity
  const checkSentryConnection = async () => {
    try {
      setStatus('checking');

      // Mock connectivity check since we're using console logging
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate check
      const connected = true; // Always connected for console logging

      setIsConnected(connected);
      setStatus('success');
      setLastTestedAt(new Date().toLocaleTimeString());

      // Log the result
      if (connected) {
        console.log('[Sentry] Connection test successful');
        captureMessage('Sentry connection test successful', 'info');
      } else {
        console.warn('[Sentry] Connection test failed');
        captureMessage('Sentry connection test failed', 'warning');
      }
    } catch (error) {
      console.error('[Sentry] Error checking connectivity:', error);
      setIsConnected(false);
      setErrorMessage(error instanceof Error ? error.message : String(error));
      setStatus('error');
      setLastTestedAt(new Date().toLocaleTimeString());
    }
  };

  // Send a test error to Sentry
  const sendTestError = () => {
    try {
      // Create a test error with a clearer message
      const testError = new Error('[TEST ERROR] This is an intentional test error from SentryMonitor component');

      // Add a custom property to identify it as a test
      (testError as any).isTestError = true;

      // Throw the test error
      throw testError;
    } catch (error) {
      // Capture the error with Sentry
      captureException(error, {
        tags: {
          component: 'SentryMonitor',
          test: 'true',
          intentional: 'true'
        },
        extra: {
          timestamp: new Date().toISOString(),
          environment: process.env.NODE_ENV,
          isTest: true,
          description: 'This error was intentionally triggered for testing purposes'
        }
      });

      // Update state
      setStatus('success');
      setLastTestedAt(new Date().toLocaleTimeString());
      console.log('[Sentry] Test error sent successfully');

      // Show a success message to the user
      alert('Test error successfully sent to Sentry! This error is intentional and used for testing purposes.');
    }
  };

  // Send a test message to Sentry
  const sendTestMessage = () => {
    // Capture a test message with Sentry
    captureMessage('This is a test message from SentryMonitor component', 'info', {
      tags: {
        component: 'SentryMonitor',
        test: 'true'
      },
      extra: {
        timestamp: new Date().toISOString(),
        environment: env.NODE_ENV
      }
    });

    // Update state
    setStatus('success');
    setLastTestedAt(new Date().toLocaleTimeString());
    console.log('[Sentry] Test message sent successfully');
  };

  // Check connection on mount
  useEffect(() => {
    checkSentryConnection();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Sentry Monitoring</CardTitle>
            <CardDescription>
              Monitor and test Sentry error tracking integration
            </CardDescription>
          </div>
          {isConnected !== null && (
            <Badge variant={isConnected ? 'success' : 'destructive'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status alerts */}
        {status === 'checking' && (
          <Alert>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertTitle>Checking Sentry connection...</AlertTitle>
            <AlertDescription>
              Please wait while we verify the logging system.
            </AlertDescription>
          </Alert>
        )}

        {status === 'success' && isConnected && (
          <Alert>
            <CheckCircle2 className="h-4 w-4 text-theme-success" />
            <AlertTitle>Sentry is properly configured</AlertTitle>
            <AlertDescription>
              Sentry is connected and ready to capture errors and events.
              {lastTestedAt && ` Last tested at ${lastTestedAt}.`}
            </AlertDescription>
          </Alert>
        )}

        {(status === 'error' || (status === 'success' && !isConnected)) && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Sentry connection issue</AlertTitle>
            <AlertDescription>
              {errorMessage || 'Sentry is not connected. Check your configuration.'}
              {lastTestedAt && ` Last tested at ${lastTestedAt}.`}
            </AlertDescription>
          </Alert>
        )}

        {/* Information about Sentry */}
        <Alert variant="info">
          <Info className="h-4 w-4" />
          <AlertTitle>About Sentry</AlertTitle>
          <AlertDescription>
            Sentry provides real-time error tracking and helps identify and fix issues in your application.
            Use the buttons below to test the integration.
          </AlertDescription>
        </Alert>
      </CardContent>

      <CardFooter className="flex flex-col space-y-4">
        <div className="flex justify-between w-full">
          <Button
            variant="outline"
            onClick={checkSentryConnection}
            disabled={status === 'checking'}
          >
            {status === 'checking' ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Checking...
              </>
            ) : (
              'Check Connection'
            )}
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={sendTestMessage}
              disabled={status === 'checking'}
            >
              Send Test Message
            </Button>

            <Button
              variant="default"
              onClick={sendTestError}
              disabled={status === 'checking'}
              title="This will intentionally generate a test error to verify Sentry error tracking"
            >
              Send Test Error (Safe)
            </Button>
          </div>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 italic bg-gray-50 dark:bg-gray-800 p-2 rounded-md border border-gray-200 dark:border-gray-700">
          <strong>Note:</strong> The test error is intentional and helps verify that error logging is working properly.
          It will not affect the application's functionality and is logged to the console.
        </div>
      </CardFooter>
    </Card>
  );
}
