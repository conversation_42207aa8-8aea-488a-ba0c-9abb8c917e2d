# MongoDB Schema Definition (IMS Database) - Revised

This schema definition reflects the updated structure of the Inventory Management System (IMS) database. It incorporates the different states a part can be in during the manufacturing process (raw, hardening, grinding, finished, rejected) and uses `ObjectId` for references to ensure data integrity. Each collection description is followed by a sample JSON document.

---

## 1. `parts` Collection (Revised for MVP)

This collection stores the master data and **global planning parameters** for each part.

* `_id`: `ObjectId` (Primary Key)
* `partNumber`: `String` (Unique business identifier. **Indexed**.)
* `name`: `String`
* `businessName`: `String | Null`
* `description`: `String`
* `technicalSpecs`: `String | Null`
* `isManufactured`: `Boolean`
* `status`: `String` (e.g., "active", "inactive", "obsolete")
* `supplierId`: `ObjectId | Null` (Reference to `suppliers._id`)
* `unitOfMeasure`: `String`
* `standardCost`: `Double` (Standard manufacturing or acquisition cost)
* `abcClassification`: `String | Null` (e.g., "A", "B", "C")
* `categoryId`: `ObjectId | Null` (Reference to `categories._id`)
* **`planningMethod`**: `String` (e.g., "ReorderPoint", "MinMax")
* **`reorderLevel`**: `Int32`
* **`safetyStockLevel`**: `Int32`
* **`maximumStockLevel`**: `Int32`
* **`leadTimeDays`**: `Int32` (Time from order to receipt)
* **`averageDailyUsage`**: `Double`
* `createdAt`: `Date`
* `updatedAt`: `Date`

**Sample Data:**
```json
{
  "_id": { "$oid": "6646a1b2c3d4e5f6a0000001" },
  "partNumber": "2E34.201B",
  "name": "Closing cylinder",
  "businessName": "Big Male Cylinder",
  "description": "High-pressure closing cylinder for tamping units.",
  "technicalSpecs": "Material: Hardened Steel, Max Pressure: 3000 PSI",
  "isManufactured": true,
  "status": "active",
  "supplierId": null,
  "unitOfMeasure": "pcs",
  "standardCost": 22.50,
  "abcClassification": "A",
  "categoryId": { "$oid": "65f000020000000000000006" },
  "planningMethod": "ReorderPoint",
  "reorderLevel": 20,
  "safetyStockLevel": 10,
  "maximumStockLevel": 150,
  "leadTimeDays": 14,
  "averageDailyUsage": 2.5,
  "createdAt": { "$date": "2024-05-16T12:00:00.000Z" },
  "updatedAt": { "$date": "2025-08-14T14:55:00.000Z" }
}
```
---

## 2. `inventories` Collection (Revised)

The `inventories` collection now points to a specific `locationId`, providing the most granular level of tracking.

* `_id`: `ObjectId` (Primary Key)
* `partId`: `ObjectId` (Reference to `parts._id`. **Indexed**.)
* `locationId`: `ObjectId` (Reference to `locations._id`. **Indexed**.)
* `stockType`: `String` (e.g., "raw", "hardening", "finished". **Indexed**.)
* `quantity`: `Int32` (The current quantity on hand for this combination)
* `lastUpdated`: `Date`

**Crucial Index:** A compound index on `{ partId: 1, locationId: 1, stockType: 1 }` is now the most important index for performance.

**Sample Data:**
```json
// 10 units of DL23.108 in 'hardening' state at the HARDENING-AREA-1
{
  "_id": { "$oid": "68a0b1c2d3e4f5a6b7c8d9e0" },
  "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a00a" },
  "locationId": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a2" },
  "stockType": "hardening",
  "quantity": 10,
  "lastUpdated": { "$date": "2025-07-26T01:14:00.000Z" }
}

// 50 'finished' units of the same part in a specific bin
{
  "_id": { "$oid": "68a0b1c2d3e4f5a6b7c8d9e1" },
  "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a00a" },
  "locationId": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a1" },
  "stockType": "finished",
  "quantity": 50,
  "lastUpdated": { "$date": "2025-07-25T18:30:00.000Z" }
}
```

---

## 3. `transactions` Collection (Revised)

The transaction log is updated to record movements between specific locations.

* `_id`: `ObjectId` (Primary Key)
* `partId`: `ObjectId` (Reference to `parts._id`)
* `quantity`: `Int32`
* `transactionType`: `String`
* `from`: `Object | Null`
  * `locationId`: `ObjectId`
  * `stockType`: `String`
* `to`: `Object | Null`
  * `locationId`: `ObjectId`
  * `stockType`: `String`
* `transactionDate`: `Date`
* `referenceNumber`: `String | Null`
* `referenceType`: `String | Null`
* `userId`: `ObjectId`
* `notes`: `String`
* `createdAt`: `Date`

**Sample Data:**
```json
// Moving 5 parts from the hardening area to a storage bin after production
{
  "_id": { "$oid": "68b1c2d3e4f5a6b7c8d9e4f5" },
  "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a00a" },
  "quantity": 5,
  "transactionType": "internal_transfer",
  "from": {
    "locationId": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a2" }, // from HARDENING-AREA-1
    "stockType": "hardening"
  },
  "to": {
    "locationId": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a1" }, // to Bin A5-R2-S3
    "stockType": "finished"
  },
  "transactionDate": { "$date": "2025-07-26T01:10:00.000Z" },
  "referenceNumber": "BATCH-TA-20250726-001",
  "referenceType": "Batch",
  "userId": { "$oid": "65f000010000000000000003" },
  "notes": "Transferring completed parts to storage bin.",
  "createdAt": { "$date": "2025-07-26T01:12:00.000Z" }
}
```

## 4. `warehouses` Collection (Revised)

This collection defines the high-level physical buildings or areas.

* `_id`: `ObjectId` (Primary Key)
* `name`: `String` (e.g., "Main Production Warehouse", "Finished Goods Depot")
* `address`: `String` (Physical address of the warehouse)
* `manager`: `String` (Name of the person in charge)
* `contact`: `String` (Contact email or phone number for the warehouse)
* `isBinTracked`: `Boolean` (If true, the system enforces the use of specific locations (bins/shelves) for inventory. If false, a single default location can be used.)
* `createdAt`: `Date`
* `updatedAt`: `Date`

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000000000000000000001" },
  "name": "Main Production Warehouse",
  "address": "123 Manufacturing Lane, InduCity, ST 54321",
  "manager": "Sarah Miller",
  "contact": "<EMAIL>",
  "isBinTracked": true,
  "createdAt": { "$date": "2023-01-01T10:00:00.000Z" },
  "updatedAt": { "$date": "2025-07-26T01:30:00.000Z" }
}
```

---


## 5. `locations` Collection (New)

This new collection defines every specific storage spot within a warehouse. This is the key to bin-level tracking.

* `_id`: `ObjectId` (Primary Key)
* `warehouseId`: `ObjectId` (Reference to the parent `warehouses._id`. **Indexed**.)
* `name`: `String` (A unique, human-readable code for the location, e.g., "A5-R2-S3", "BIN-1024", "HARDENING-AREA-1". **Indexed**.)
* `description`: `String` (Optional longer description, e.g., "Aisle 5, Rack 2, Shelf 3")
* `locationType`: `String` (e.g., "Bin", "Shelf", "Floor Area", "Staging")
* `capacity`: `Object | Null` (Optional: to store dimensions or weight limits)
  * `maxWeightKg`: `Double`
  * `volumeM3`: `Double`
* `createdAt`: `Date`
* `updatedAt`: `Date`

**Sample Data:**
```json
// A specific bin in the Main Production Warehouse
{
  "_id": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a1" },
  "warehouseId": { "$oid": "65f000000000000000000001" },
  "name": "A5-R2-S3",
  "description": "Aisle 5, Rack 2, Shelf 3",
  "locationType": "Bin",
  "capacity": { "maxWeightKg": 50, "volumeM3": 0.5 },
  "createdAt": { "$date": "2024-01-15T11:00:00.000Z" },
  "updatedAt": { "$date": "2024-01-15T11:00:00.000Z" }
}
// A general area for a manufacturing process
{
  "_id": { "$oid": "69c1d2e3f4a5b6c7d8e9f0a2" },
  "warehouseId": { "$oid": "65f000000000000000000001" },
  "name": "HARDENING-AREA-1",
  "description": "Staging area for parts undergoing hardening",
  "locationType": "Floor Area",
  "capacity": null,
  "createdAt": { "$date": "2024-01-15T11:00:00.000Z" },
  "updatedAt": { "$date": "2024-01-15T11:00:00.000Z" }
}
```

## 6. `suppliers` Collection

Information about part suppliers.

- `_id`: `ObjectId` (Primary Key)
- `supplier_id`: `String` (Unique business identifier for the supplier, e.g., "SUP001")
- `name`: `String` (Name of the supplier company)
- `contactPerson`: `String` (Name of the primary contact at the supplier)
- `email`: `String` (Supplier contact email)
- `phone`: `String` (Supplier contact phone number)
- `address`: `String` (Supplier's physical address)
- `specialty`: `Array<String>` (List of product types or part categories the supplier specializes in)
- `rating`: `Double | Int32` (Supplier performance rating)
- `payment_terms`: `String | Null` (e.g., "Net 30", "Net 60")
- `delivery_terms`: `String | Null` (e.g., "FOB Destination", "EXW")
- `is_active`: `Boolean` (Indicates if the supplier is currently active)
- `createdAt`: `Date` (Timestamp of supplier record creation)
- `updatedAt`: `Date` (Timestamp of last supplier record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "681f796ad6a21248b8ec75ff" },
  "supplier_id": "SUP001",
  "name": "Tamping Systems",
  "contactPerson": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "address": "123 Main St, Anytown, USA",
  "specialty": ["Tamping Units", "Railway Maintenance Equipment"],
  "rating": 4.5,
  "payment_terms": "Net 30",
  "delivery_terms": "FOB Destination",
  "is_active": true,
  "createdAt": { "$date": "2025-05-10T16:06:02.256Z" },
  "updatedAt": { "$date": "2025-05-10T16:06:02.256Z" }
}
```

---

## 7. `users` Collection

User accounts for the IMS.

- `_id`: `ObjectId` (Primary Key)
- `user_id`: `String` (Unique business identifier)
- `username`: `String` (Unique login username)
- `email`: `String` (User's email address, should be unique)
- `first_name`: `String` (User's first name)
- `last_name`: `String` (User's last name)
- `roles`: `Array<String>` (User roles, e.g., ["admin"], ["manager", "staff"])
- `password_hash`: `String` (Securely hashed password)
- `is_active`: `Boolean` (Indicates if the user account is active)
- `department`: `String | Null` (User's department)
- `job_title`: `String | Null` (User's job title)
- `phone_number`: `String | Null` (User's phone number)
- `last_login_date`: `Date | Null` (Timestamp of the user's last login)
- `createdAt`: `Date` (Timestamp of user record creation)
- `updatedAt`: `Date` (Timestamp of last user record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000010000000000000001" },
  "username": "admin_user",
  "email": "<EMAIL>",
  "user_id": "USR-001",
  "first_name": "Admin",
  "last_name": "User",
  "roles": ["admin"],
  "password_hash": "$2b$12$abcdefghijklmnopqrstuvwxyzABCDEFG",
  "is_active": true,
  "department": "IT",
  "job_title": "System Administrator",
  "phone_number": "******-0123",
  "last_login_date": { "$date": "2025-05-10T10:00:00.000Z" },
  "createdAt": { "$date": "2023-01-01T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-10T10:00:00.000Z" }
}
```

---

## 8. `categories` Collection

Hierarchical categorization for parts and products.

- `_id`: `ObjectId` (Primary Key)
- `name`: `String` (Name of the category, unique within its level)
- `description`: `String` (Optional description of the category)
- `parentCategory`: `ObjectId | Null` (Reference to `categories._id` of the parent category, null for top-level categories)
- `createdAt`: `Date` (Timestamp of category record creation)
- `updatedAt`: `Date` (Timestamp of last category record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000020000000000000001" },
  "name": "Bearings",
  "description": "All types of bearings.",
  "parentCategory": null,
  "createdAt": { "$date": "2023-01-05T10:00:00.000Z" },
  "updatedAt": { "$date": "2023-01-05T10:00:00.000Z" }
}
```

---

## 9. `products` Collection (Revised for Hierarchical Assemblies)

Defines finished goods for sale. Products are composed of a hierarchical structure of assemblies.

- `_id`: `ObjectId` (Primary Key)
- `productCode`: `String` (Unique business code for the product, e.g., "PROD-TAMP-MACHINE-01". **Should be indexed.**)
- `name`: `String` (Name of the product)
- `description`: `String` (Description of the product)
- `categoryId`: `ObjectId` (Reference to `categories._id`)
- `status`: `String` (e.g., "active", "discontinued", "in_development")
- `sellingPrice`: `Double` (Price at which the product is sold)
- `components`: `Array<Object>` (List of component assemblies, supports hierarchical structure)
    - `assemblyId`: `ObjectId` (Reference to `assemblies._id` of the component assembly)
    - `quantityRequired`: `Int32`
    - `children`: `Array<Object>` (Optional array of child assemblies, follows the same structure for nesting)
- `createdAt`: `Date` (Timestamp of product record creation)
- `updatedAt`: `Date` (Timestamp of last product record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "6628c5f0a1b2c3d4e5f6a7b2" },
  "productCode": "PROD-TAMP-MACHINE-01",
  "name": "Complete Tamping Machine",
  "description": "Fully operational tamping machine, including power unit and two tamping heads.",
  "categoryId": { "$oid": "65f000020000000000000006" },
  "status": "active",
  "sellingPrice": 150000.00,
  "components": [
    {
      "assemblyId": { "$oid": "681f796bd6a21248b8ec7641" },
      "quantityRequired": 1,
      "children": []
    },
    {
      "assemblyId": { "$oid": "681f796bd6a21248b8ec7640" },
      "quantityRequired": 2,
      "children": [
        {
          "assemblyId": { "$oid": "681f796bd6a21248b8ec7655" },
          "quantityRequired": 1,
          "children": []
        }
      ]
    }
  ],
  "createdAt": { "$date": "2024-02-11T11:00:00.000Z" },
  "updatedAt": { "$date": "2025-07-15T12:30:00.000Z" }
}
```
---


## 10. `assemblies` Collection

Defines Bills of Materials (BOMs) for manufactured items. Assemblies are composed of parts and can be nested to create hierarchical structures.

- `_id`: `ObjectId` (Primary Key)
- `assemblyCode`: `String` (Unique business code for the assembly, e.g., "ASM-TA-100". **Should be indexed.**)
- `name`: `String` (Name of the assembly)
- `parentId`: `ObjectId | Null` (Reference to another `assemblies._id` if this is a sub-assembly)
- `isTopLevel`: `Boolean` (Indicates if this is a top-level assembly, not part of another assembly.)
- `partsRequired`: `Array<Object>` (List of component parts and their quantities, supports hierarchical structure)
    - `partId`: `ObjectId` (Reference to `parts._id` of the component part)
    - `quantityRequired`: `Int32`
    - `unitOfMeasure`: `String` (Should ideally match the UoM of the referenced part)
    - `children`: `Array<Object>` (Optional array of child parts, same structure as `partsRequired` for hierarchical assemblies)
- `status`: `String` (Assembly definition status, e.g., "active", "pending_review", "obsolete")
- `version`: `Int32` (Version number of the assembly definition)
- `manufacturingInstructions`: `String | Null` (Link to or text of manufacturing SOPs)
- `estimatedBuildTime`: `String | Null` (e.g., "1.5 hours", "30 minutes")
- `createdAt`: `Date` (Timestamp of assembly definition creation)
- `updatedAt`: `Date` (Timestamp of last assembly definition update)

**Sample Data:**
```json
{
  "_id": { "$oid": "681f796bd6a21248b8ec7640" },
  "assemblyCode": "ASM-TA-100",
  "name": "Tamping Arm Assembly",
  "parentId": null,
  "isTopLevel": true,
  "partsRequired": [
    {
      "partId": { "$oid": "6640f1000000000000000001" },
      "quantityRequired": 2,
      "unitOfMeasure": "pcs",
      "children": [
        {
          "partId": { "$oid": "6640f1000000000000000003" },
          "quantityRequired": 4,
          "unitOfMeasure": "pcs",
          "children": []
        }
      ]
    },
    {
      "partId": { "$oid": "6640f1000000000000000002" },
      "quantityRequired": 8,
      "unitOfMeasure": "pcs",
      "children": []
    }
  ],
  "status": "active",
  "version": 1,
  "manufacturingInstructions": "Follow SOP-ASM-100 for assembly.",
  "estimatedBuildTime": "1.5 hours",
  "createdAt": { "$date": "2025-05-10T16:06:03.385Z" },
  "updatedAt": { "$date": "2025-05-11T10:00:00.000Z" }
}
```

---

## 11. `purchaseorders` Collection

Orders placed with suppliers for acquiring parts.

- `_id`: `ObjectId` (Primary Key)
- `poNumber`: `String` (Unique purchase order number, e.g., "PO-2025-001")
- `supplierId`: `ObjectId` (Reference to `suppliers._id`)
- `orderDate`: `Date` (Date the order was placed)
- `expectedDeliveryDate`: `Date` (Expected delivery date from the supplier)
- `items`: `Array<Object>` (List of parts being ordered)
    - `partId`: `ObjectId` (Reference to `parts._id`)
    - `description`: `String` (Part description, can be copied from part record at time of PO creation)
    - `quantity`: `Int32` (Quantity ordered)
    - `unitPrice`: `Double` (Price per unit at the time of order)
    - `lineTotal`: `Double` (Calculated as quantity * unitPrice)
    - `receivedQuantity`: `Int32` (Quantity received against this line item so far)
- `totalAmount`: `Double` (Total cost of the order)
- `status`: `String` (Order status, e.g., "pending_approval", "ordered", "partially_received", "fully_received", "cancelled")
- `notes`: `String` (Additional notes for the PO)
- `shippingAddress`: `String` (Address where the goods are to be shipped)
- `billingAddress`: `String` (Address for billing purposes)
- `termsAndConditions`: `String` (Specific terms related to this PO)
- `createdBy`: `ObjectId` (Reference to `users._id` of the user who created the PO)
- `approvedBy`: `ObjectId | Null` (Reference to `users._id` of the user who approved the PO)
- `approvalDate`: `Date | Null` (Timestamp of PO approval)
- `createdAt`: `Date` (Timestamp of PO record creation)
- `updatedAt`: `Date` (Timestamp of last PO record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000050000000000000001" },
  "poNumber": "PO-2025-001",
  "supplierId": { "$oid": "681f796ad6a21248b8ec7600" },
  "orderDate": { "$date": "2025-05-01T10:00:00.000Z" },
  "expectedDeliveryDate": { "$date": "2025-05-15T10:00:00.000Z" },
  "items": [
    {
      "partId": { "$oid": "6640f0a0a1b2c3d4e5f6a001" },
      "description": "Roller bearing Plasser & Theurer/SKF/FAG make",
      "quantity": 50,
      "unitPrice": 70.00,
      "lineTotal": 3500.00,
      "receivedQuantity": 0
    }
  ],
  "totalAmount": 3590.00,
  "status": "ordered",
  "notes": "Standard order for stock replenishment.",
  "shippingAddress": "123 Manufacturing Lane, InduCity, ST 54321",
  "billingAddress": "123 Manufacturing Lane, InduCity, ST 54321",
  "termsAndConditions": "Net 30 days. Delivery by May 15th.",
  "createdBy": { "$oid": "65f000010000000000000002" },
  "approvedBy": { "$oid": "65f000010000000000000001" },
  "approvalDate": { "$date": "2025-05-01T11:00:00.000Z" },
  "createdAt": { "$date": "2025-05-01T10:05:00.000Z" },
  "updatedAt": { "$date": "2025-05-01T11:05:00.000Z" }
}
```

---

## 12. `workorders` Collection

Orders to manufacture parts or assemble products.

- `_id`: `ObjectId` (Primary Key)
- `woNumber`: `String` (Unique work order number, e.g., "WO-2025-001")
- `assemblyId`: `ObjectId | Null` (Reference to `assemblies._id` if producing an assembly)
- `partIdToManufacture`: `ObjectId | Null` (Reference to `parts._id` if manufacturing a specific part not defined as an assembly)
- `productId`: `ObjectId | Null` (Reference to `products._id` if the WO is to produce a saleable product)
- `quantity`: `Int32` (Quantity to produce)
- `status`: `String` (Order status, e.g., "planned", "released", "in_progress", "completed", "on_hold", "cancelled")
- `priority`: `String` (Priority level, e.g., "low", "medium", "high", "urgent")
- `dueDate`: `Date` (Date by which the work order should be completed)
- `startDate`: `Date | Null` (Actual start date of production)
- `completedAt`: `Date | Null` (Actual completion timestamp)
- `assignedTo`: `ObjectId | Null` (Reference to `users._id` or a team/department responsible)
- `notes`: `String` (Additional notes for the work order)
- `sourceDemand`: `String | Null` (Origin of the demand, e.g., "SalesOrder-SO2025-105", "StockReplenishment", "Project-XYZ")
- `createdAt`: `Date` (Creation timestamp)
- `updatedAt`: `Date` (Last update timestamp)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000060000000000000001" },
  "woNumber": "WO-2025-001",
  "assemblyId": { "$oid": "681f796bd6a21248b8ec7640" },
  "partIdToManufacture": null,
  "productId": null,
  "quantity": 10,
  "status": "planned",
  "priority": "medium",
  "dueDate": { "$date": "2025-05-25T17:00:00.000Z" },
  "startDate": null,
  "completedAt": null,
  "assignedTo": { "$oid": "65f000010000000000000003" },
  "notes": "Standard production run for Tamping Arm Assembly.",
  "sourceDemand": "StockReplenishment",
  "createdAt": { "$date": "2025-05-12T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-12T09:00:00.000Z" }
}
```

---

## 13. `batches` Collection

Tracks production runs, often as sub-units of work orders.

- `_id`: `ObjectId` (Primary Key)
- `batchCode`: `String` (Unique code for the batch, e.g., "BATCH-TA-20250513-001")
- `workOrderId`: `ObjectId` (Reference to the parent `workorders._id`)
- `partId`: `ObjectId | Null` (Reference to `parts._id` being produced, if not an assembly)
- `assemblyId`: `ObjectId | Null` (Reference to `assemblies._id` being produced)
- `quantityPlanned`: `Int32` (Planned quantity for this batch)
- `quantityProduced`: `Int32` (Actual quantity successfully produced in this batch)
- `quantityScrapped`: `Int32` (Quantity scrapped during this batch production)
- `startDate`: `Date | Null` (Actual production start date for this batch)
- `endDate`: `Date | Null` (Actual production end date for this batch)
- `status`: `String` (Batch status, e.g., "pending", "in_progress", "completed", "paused")
- `notes`: `String` (Additional notes specific to this batch)
- `assignedMachine`: `String | Null` (Identifier for the machine or workstation used)
- `createdAt`: `Date` (Timestamp of batch record creation)
- `updatedAt`: `Date` (Timestamp of last batch record update)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000070000000000000001" },
  "batchCode": "BATCH-TA-20250513-001",
  "workOrderId": { "$oid": "65f000060000000000000001" },
  "partId": null,
  "assemblyId": { "$oid": "681f796bd6a21248b8ec7640" },
  "quantityPlanned": 10,
  "quantityProduced": 0,
  "quantityScrapped": 0,
  "startDate": null,
  "endDate": null,
  "status": "pending",
  "notes": "First batch for WO-2025-001.",
  "assignedMachine": "ASM-LINE-01",
  "createdAt": { "$date": "2025-05-13T09:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-13T09:00:00.000Z" }
}
```

---

## 14. `batchlogs` Collection

Logs events and activities related to specific production batches.

- `_id`: `ObjectId` (Primary Key)
- `batchId`: `ObjectId` (Reference to `batches._id`)
- `timestamp`: `Date` (Time of the log entry)
- `event`: `String` (Description of the event, e.g., "Material Issued", "QC Checkpoint Passed", "Machine Stoppage")
- `userId`: `ObjectId` (Reference to `users._id` of the user who logged or is associated with the event)
- `details`: `String` (Additional textual details about the event)
- `data`: `Object | Null` (Flexible field for structured data related to the event, e.g., `{ "material_codes": [...], "checkpoint": "QC1" }`)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000080000000000000001" },
  "batchId": { "$oid": "65f000070000000000000002" },
  "timestamp": { "$date": "2025-05-13T10:05:00.000Z" },
  "event": "Material Issued for 5 units",
  "userId": { "$oid": "65f000010000000000000003" },
  "details": "Issued: Raw Steel Bar x5, Keys x10, Washers x10",
  "data": { "material_codes": ["RAW-STL-001", "KEY-007", "WSH-016"] }
}
```

---

## 15. `deliveries` Collection

Tracks inbound (from suppliers) and outbound (to customers) shipments.

- `_id`: `ObjectId` (Primary Key)
- `deliveryId`: `String` (Unique delivery identifier, e.g., "DEL-2025-001-IN")
- `referenceType`: `String` (Type of reference, e.g., "PurchaseOrder", "SalesOrder", "TransferOrder")
- `referenceId`: `ObjectId` (Reference to the related document in another collection)
- `supplierId`: `ObjectId | Null` (Reference to `suppliers._id` for inbound deliveries)
- `customerId`: `ObjectId | Null` (Reference to an implied `customers` collection for outbound deliveries)
- `status`: `String` (Delivery status, e.g., "scheduled", "in_transit", "delivered", "partially_delivered", "delayed", "cancelled")
- `scheduledDate`: `Date` (Scheduled date for delivery/shipment)
- `actualDate`: `Date | Null` (Actual date of delivery/shipment)
- `trackingNumber`: `String | Null` (Carrier tracking number)
- `carrier`: `String | Null` (Name of the shipping carrier, e.g., "FedEx", "UPS")
- `notes`: `String` (Additional notes regarding the delivery)
- `receivedBy`: `ObjectId | Null` (Reference to `users._id` who received/confirmed an inbound delivery)
- `itemsDelivered`: `Array<Object>` (List of items in the delivery)
    - `partId`: `ObjectId | Null` (Reference to `parts._id`.)
    - `productId`: `ObjectId | Null` (Reference to `products._id`)
    - `quantity`: `Int32`
- `createdAt`: `Date` (Creation timestamp)
- `updatedAt`: `Date` (Last update timestamp)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f000090000000000000001" },
  "deliveryId": "DEL-2025-001-IN",
  "referenceType": "PurchaseOrder",
  "referenceId": { "$oid": "65f000050000000000000001" },
  "supplierId": { "$oid": "681f796ad6a21248b8ec7600" },
  "customerId": null,
  "status": "scheduled",
  "scheduledDate": { "$date": "2025-05-15T10:00:00.000Z" },
  "actualDate": null,
  "trackingNumber": "TRK123456789",
  "carrier": "FedEx",
  "notes": "Awaiting delivery of PO-2025-001.",
  "receivedBy": null,
  "itemsDelivered": [],
  "createdAt": { "$date": "2025-05-12T11:00:00.000Z" },
  "updatedAt": { "$date": "2025-05-12T11:00:00.000Z" }
}
```

---

## 16. `settings` Collection

System-wide configurations and parameters.

- `_id`: `ObjectId` (Primary Key)
- `key`: `String` (Unique setting key, e.g., "default_notification_email")
- `value`: `String` (Setting value; actual data type depends on `dataType` field)
- `description`: `String` (Description of the setting)
- `dataType`: `String` (Data type of the value, e.g., "string", "integer", "boolean", "json")
- `group`: `String` (Setting group for organization, e.g., "Notifications", "Inventory", "System")
- `lastModifiedBy`: `ObjectId` (Reference to `users._id` who last modified the setting)
- `lastModifiedAt`: `Date` (Last modification timestamp)
- `isSystemEditableOnly`: `Boolean` (If true, setting can only be changed by system processes, not through UI by regular admins)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f0000c0000000000000001" },
  "key": "default_notification_email",
  "value": "<EMAIL>",
  "description": "Default email address for system notifications.",
  "dataType": "string",
  "group": "Notifications",
  "lastModifiedBy": { "$oid": "65f000010000000000000001" },
  "lastModifiedAt": { "$date": "2023-01-01T12:00:00.000Z" },
  "isSystemEditableOnly": false
}
```

---

## 17. `IMS_TEJ` Collection (System Transaction Event Journal / Logs)

Comprehensive logs for system events, errors, and important transactions.

- `_id`: `ObjectId` (Primary Key)
- `timestamp`: `Date` (Log timestamp)
- `eventType`: `String` (Type of event, e.g., "UserLogin", "InventoryUpdateFailure", "ApiRequest")
- `level`: `String` (Log level, e.g., "INFO", "WARN", "ERROR", "DEBUG", "FATAL")
- `message`: `String` (Log message)
- `source`: `String` (Source of the log, e.g., "API-AuthModule", "BackgroundJob-StockSync", "WorkOrderService")
- `details`: `Object` (Additional structured details, e.g., `{ "ip_address": "...", "user_agent": "...", "error_code": "..." }`)
- `userId`: `ObjectId | Null` (Reference to `users._id` if the event is associated with a user)
- `correlationId`: `String | Null` (ID to correlate logs related to a single operation or request flow)

**Sample Data:**
```json
{
  "_id": { "$oid": "65f0000d0000000000000001" },
  "timestamp": { "$date": "2025-05-14T10:05:00.000Z" },
  "eventType": "UserLogin",
  "level": "INFO",
  "message": "User admin_user logged in successfully.",
  "source": "API-AuthModule",
  "details": { "ip_address": "*************", "user_agent": "Chrome/100.0" },
  "userId": { "$oid": "65f000010000000000000001" },
  "correlationId": "corr-login-xyz123"
}
```


## Schema Design Recommendations (v3)

1.  **Dedicated Inventory Collection (Key Change)**: The most significant improvement is the creation of the `inventory_stock` collection. This separates static part data from dynamic stock data, which is the best practice for handling inventory that exists in multiple locations and/or states. It avoids the "unbounded array" anti-pattern and keeps documents small and efficient.

2.  **Event-Sourced Logic**: The `transactions` collection should be treated as an immutable log. Your application logic should be event-driven: when a transaction is created, it triggers an update to the relevant document(s) in the `inventory_stock` collection. For example, an `internal_transfer` transaction results in two atomic updates: one decrement (`from`) and one increment (`to`). This creates a fully auditable and robust system.

3.  **Indexing Strategy**:
    * **`parts.partNumber`**: Must be indexed for fast lookups of part master data.
    * **`inventory_stock`**: A compound index on `{ partId: 1, warehouseId: 1, stockType: 1 }` is **critical**. It will ensure that finding the quantity for a specific item, in a specific state, at a specific location is extremely fast. This will be the most frequently queried collection.
    * Continue to index other key lookup fields like `poNumber`, `woNumber`, etc., as previously recommended.

4.  **Application-Level Joins (`$lookup`)**: To get a complete view of a part and all its stock levels, you will use MongoDB's `$lookup` aggregation pipeline stage. For example, you can aggregate from the `parts` collection and `$lookup` into the `inventory_stock` collection on the `partId` field. This is a standard and performant operation.

This v3 schema provides a powerful, scalable, and maintainable foundation for your Inventory Management System.


## Schema Design Recommendations

1.  **Embedded Documents**: The schema makes good use of embedded documents (e.g., `inventory` in `parts`, `items` in `purchaseorders`), which is efficient for data that is frequently accessed together.

2.  **References (`ObjectId`)**: References between collections are appropriately implemented using `ObjectId`s, which is crucial for data integrity and performance.

3.  **Schema Versioning**: Consider adding a `schemaVersion` field to documents to help manage future schema evolutions.

4.  **Indexing**:
    * Ensure `_id` fields are leveraged for primary lookups.
    * Create indexes on fields frequently used in queries, sorts, and joins (`$lookup`).
    * **Crucially, index `parts.partNumber` as it will be a common lookup field.**
    * Other candidates for indexing include: `warehouses.location_id`, `suppliers.supplier_id`, `users.username`, `products.productCode`, `assemblies.assemblyCode`, `transactions.transactionDate`, `transactions.partId`, `purchaseorders.poNumber`, `workorders.woNumber`.

5.  **Data Duplication (Denormalization)**: The current schema maintains a good balance. Small, relatively static pieces of information might be duplicated in transaction-like records if it simplifies queries and the overhead of updating is low.

6.  **Data Integrity**: Use application-level validation to ensure data consistency (e.g., valid `ObjectId` references, correct enum values for `status` and `type` fields).

7.  **Review `deliveries.itemsDelivered`**: The structure for `itemsDelivered` could be more flexible. Consider `[{ itemId: ObjectId, itemType: String, quantity: Int32 }]` where `itemType` could be "Part" or "Product".

8.  **Event-Sourced Transactions**: The `transactions` collection is designed as an immutable log of events. It does not store the running total (`previousStock`, `newStock`). Instead, each transaction atomically records a movement. The current stock level for any `stockType` in the `parts` collection should be updated based on these transaction events. This approach ensures a reliable audit trail and prevents data corruption.

9.  **Considerations for `warehouses` and `suppliers`**:
    * `warehouses` should have a `location_id` field to link to `locations`.
    * `suppliers` should have a `supplier_id` field to link to `suppliers`.


## Schema Design Recommendations (v4)

1. **Hierarchical Location Model (Key Change)**: The schema now supports a full `Warehouse -> Location -> Inventory` hierarchy. This is achieved by adding the `locations` collection and updating the `inventories` collection to point to a `locationId`. This is a robust model for any system requiring bin-level tracking. The `isBinTracked` flag on the `warehouses` collection adds crucial flexibility.

2. **Event-Sourced Logic**: The `transactions` collection remains the immutable source of truth. Application logic must now use the `locationId` within the `from` and `to` fields to perform atomic updates on the correct documents in the `inventories` collection.

3. **Indexing Strategy**:
   * **`locations.name` and `locations.warehouseId`**: These should be indexed to quickly find locations within a warehouse.
   * **`inventories`**: The compound index on `{ partId: 1, locationId: 1, stockType: 1 }` is **absolutely critical** for performance. This will be the most frequently used index to find and update stock levels.
   * **`parts.partNumber`**: Remains essential for looking up part master data.

4. **Querying and Aggregation**: With this structure, you can now perform powerful queries:
   * **Find a part's total stock:** `$group` on `partId` in the `inventories` collection.
   * **Find all stock in a warehouse:** `$lookup` from `locations` into `inventories` where `warehouseId` matches.
   * **Find all empty locations in a warehouse:** `$lookup` from `locations` into `inventories` and filter for locations with no matching inventory records.

This v4 schema is extremely capable and provides the foundation for a sophisticated, enterprise-grade inventory and warehouse management system.

## Schema Design Recommendations (MVP)

Global Planning Parameters: For the MVP, all planning data (reorderLevel, safetyStockLevel, etc.) is stored directly in the parts collection. This simplifies development as there is only one set of rules for each part across all locations.

Lean Inventory Records: The inventories collection is kept extremely lean, focusing only on quantity. This optimizes performance for the most frequent database operations (updating stock counts).

Clear Path for Future Expansion: When the business requires location-specific planning, the migration path is straightforward:

Create a new inventory_planning collection.

Write a script to migrate the planning fields from each document in the parts collection to a new "global" document in the inventory_planning collection.

Remove the planning fields from the parts collection.

Update the application logic to query the new collection.

This approach gives you the simplicity you need now, without closing the door on future enhancements.