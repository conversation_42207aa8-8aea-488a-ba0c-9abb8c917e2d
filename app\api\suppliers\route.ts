import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new supplier service
import { createSupplier, getAllSuppliers, handleMongoDBError } from '@/app/services/supplier.service';
import { CreateSupplierRequestBody } from '@/app/types/form.types';
// Remove direct model/db connection imports

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching suppliers with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with suppliers data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/suppliers - Fetching suppliers');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object ---
    // Example: Allow filtering by name (can be extended)
    const filter: any = {};
    const nameFilter = url.searchParams.get('name');
    if (nameFilter) {
      filter.name = new RegExp(nameFilter, 'i'); // Case-insensitive regex search
    }

    // Add filter for active status if provided
    const activeFilter = url.searchParams.get('is_active');
    if (activeFilter !== null) {
      filter.is_active = activeFilter === 'true';
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling getAllSuppliers service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await getAllSuppliers(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service getAllSuppliers completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.suppliers,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/suppliers (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new supplier
 * @param request - The incoming request with supplier data
 * @returns JSON response with the newly created supplier
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/suppliers - Creating new supplier');
    const supplierData = await request.json() as CreateSupplierRequestBody;

    // Basic validation
    if (!supplierData || typeof supplierData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid supplier data provided' }, { status: 400 });
    }

    // Add checks for required fields based on the updated schema
    if (!supplierData.name) {
        return NextResponse.json({ data: null, error: 'Missing required field: name' }, { status: 400 });
    }
    if (!supplierData.contactPerson) {
        return NextResponse.json({ data: null, error: 'Missing required field: contactPerson' }, { status: 400 });
    }
    if (!supplierData.email) {
        return NextResponse.json({ data: null, error: 'Missing required field: email' }, { status: 400 });
    }
    if (!supplierData.phone) {
        return NextResponse.json({ data: null, error: 'Missing required field: phone' }, { status: 400 });
    }
    if (!supplierData.address) {
        return NextResponse.json({ data: null, error: 'Missing required field: address' }, { status: 400 });
    }

    console.log(`[API] Calling createSupplier service with data: ${JSON.stringify(supplierData)}`);

    // Call the createSupplier service function
    const savedSupplier = await createSupplier(supplierData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createSupplier completed in ${duration}ms`);

    return NextResponse.json({ data: savedSupplier, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/suppliers (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
