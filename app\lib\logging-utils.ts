/**
 * Utility functions for error tracking and monitoring
 * Simple console-based logging (replace with your preferred error tracking service)
 */

/**
 * Severity levels for events
 */
export type SeverityLevel = 'fatal' | 'error' | 'warning' | 'info' | 'debug';

/**
 * Options for capturing exceptions and messages
 */
export interface CaptureOptions {
  /** Tags to add to the event */
  tags?: Record<string, string>;
  /** Extra context to add to the event */
  extra?: Record<string, any>;
  /** User information to associate with the event */
  user?: {
    id?: string;
    email?: string;
    username?: string;
    [key: string]: any;
  };
  /** Level of the event */
  level?: SeverityLevel;
  /** Fingerprint to group events */
  fingerprint?: string[];
  /** Whether to sample the event */
  sample?: boolean;
}

/**
 * Capture an exception with console logging
 * 
 * @param error - The error to capture
 * @param options - Additional context and options
 * @returns Mock event ID for compatibility
 */
export const captureException = (error: Error | unknown, options?: CaptureOptions | Record<string, any>) => {
  // Check if this is a test error
  const isTestError =
    (error instanceof Error && (error as any).isTestError) ||
    (options?.tags?.test === 'true') ||
    (options?.extra?.isTest === true);

  // Use different log level for test errors
  if (isTestError) {
    console.log('[Test Error]', error);
  } else {
    console.error('[Error]', error);
    
    // Log additional context if provided
    if (options?.tags) {
      console.error('Tags:', options.tags);
    }
    if (options?.extra) {
      console.error('Extra context:', options.extra);
    }
    if (options?.user) {
      console.error('User context:', options.user);
    }
  }

  // Return a mock event ID for compatibility
  return 'mock-event-id';
};

/**
 * Capture a message with console logging
 * 
 * @param message - The message to capture
 * @param level - The severity level
 * @param options - Additional context and options
 * @returns Mock event ID for compatibility
 */
export const captureMessage = (
  message: string,
  level: SeverityLevel = 'info',
  options?: CaptureOptions | Record<string, any>
) => {
  // Map severity levels to console methods
  const logMethod = level === 'fatal' || level === 'error' ? 'error' :
                   level === 'warning' ? 'warn' : 'log';
  console[logMethod](`[${level.toUpperCase()}] ${message}`);

  if (options?.tags) {
    console.log('Tags:', options.tags);
  }
  if (options?.extra) {
    console.log('Extra context:', options.extra);
  }

  return 'mock-event-id';
};

/**
 * Start a transaction for performance monitoring (mock implementation)
 */
export const startTransaction = (
  name: string,
  op?: string,
  options?: {
    tags?: Record<string, string>;
    data?: Record<string, any>;
  }
) => {
  console.log(`[Transaction] Starting: ${name} (${op || 'unknown'})`);
  
  return {
    setTag: (key: string, value: string) => console.log(`[Transaction Tag] ${key}: ${value}`),
    setData: (key: string, value: any) => console.log(`[Transaction Data] ${key}:`, value),
    finish: () => console.log(`[Transaction] Finished: ${name}`)
  };
};

/**
 * Set user context (mock implementation)
 */
export const setUser = (user: CaptureOptions['user'] | null) => {
  console.log('[User Context]', user);
};

/**
 * Set extra context (mock implementation)
 */
export const setExtra = (key: string, value: any) => {
  console.log(`[Extra Context] ${key}:`, value);
};

/**
 * Set tag (mock implementation)
 */
export const setTag = (key: string, value: string) => {
  console.log(`[Tag] ${key}: ${value}`);
};

/**
 * Add breadcrumb (mock implementation)
 */
export const addBreadcrumb = (breadcrumb: { message: string; category?: string; level?: string }) => {
  console.log('[Breadcrumb]', breadcrumb);
};

/**
 * Check if error tracking is enabled (always true for console logging)
 */
export const isSentryEnabled = (): boolean => {
  return true;
};

/**
 * Wrap a function with error tracking
 */
export const withErrorTracking = <T extends (...args: any[]) => any>(
  fn: T,
  context?: string
): T => {
  return ((...args: any[]) => {
    try {
      return fn(...args);
    } catch (error) {
      captureException(error, {
        tags: { context: context || 'unknown' },
        extra: { args }
      });
      throw error;
    }
  }) as T;
};

/**
 * Flush events (mock implementation)
 */
export const flush = (timeout?: number): Promise<boolean> => {
  console.log('[Flush] All events logged to console');
  return Promise.resolve(true);
};

/**
 * Close SDK (mock implementation)
 */
export const close = (): Promise<boolean> => {
  console.log('[Close] Logging utility closed');
  return Promise.resolve(true);
};
