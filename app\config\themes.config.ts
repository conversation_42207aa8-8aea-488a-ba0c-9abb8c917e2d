/**
 * Theme Registry Configuration
 * Defines all available theme variants with their light and dark mode configurations
 */

import { ThemeConfig, ThemeVariantDefinition } from '@/app/types/theme.types';



/**
 * Default theme variant (preserves your current beloved dark theme)
 */
const defaultTheme: ThemeVariantDefinition = {
  variant: 'default',
  name: 'Default',
  description: 'Your current theme - the one you love! Classic shadcn/ui with your custom dark styling',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#0f172a',
      card: '#ffffff',
      cardForeground: '#0f172a',
      popover: '#ffffff',
      popoverForeground: '#0f172a',
      primary: '#3b82f6',
      primaryForeground: '#f8fafc',
      secondary: '#f1f5f9',
      secondaryForeground: '#0f172a',
      muted: '#f1f5f9',
      mutedForeground: '#64748b',
      accent: '#f1f5f9',
      accentForeground: '#0f172a',
      destructive: '#ef4444',
      destructiveForeground: '#f8fafc',
      border: '#e2e8f0',
      input: '#e2e8f0',
      ring: '#3b82f6'
    },
    dark: {
      background: '#1f1f1f',
      foreground: '#f8fafc',
      card: '#262626',
      cardForeground: '#f8fafc',
      popover: '#262626',
      popoverForeground: '#f8fafc',
      primary: '#3b82f6',
      primaryForeground: '#f8fafc',
      secondary: '#262626',
      secondaryForeground: '#f8fafc',
      muted: '#262626',
      mutedForeground: '#94a3b8',
      accent: '#262626',
      accentForeground: '#f8fafc',
      destructive: '#ef4444',
      destructiveForeground: '#f8fafc',
      border: '#374151',
      input: '#374151',
      ring: '#3b82f6'
    }
  }
};

/**
 * Blue theme variant - Professional and corporate
 */
const blueTheme: ThemeVariantDefinition = {
  variant: 'blue',
  name: 'Ocean Blue',
  description: 'Professional blue theme inspired by ocean depths',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#0f172a',
      card: '#ffffff',
      cardForeground: '#0f172a',
      popover: '#ffffff',
      popoverForeground: '#0f172a',
      primary: '#0ea5e9',
      primaryForeground: '#f8fafc',
      secondary: '#e0f2fe',
      secondaryForeground: '#0f172a',
      muted: '#f0f9ff',
      mutedForeground: '#64748b',
      accent: '#f0f9ff',
      accentForeground: '#0f172a',
      destructive: '#ef4444',
      destructiveForeground: '#f8fafc',
      border: '#e2e8f0',
      input: '#e2e8f0',
      ring: '#0ea5e9'
    },
    dark: {
      background: '#082f49',
      foreground: '#f8fafc',
      card: '#0c4a6e',
      cardForeground: '#f8fafc',
      popover: '#0c4a6e',
      popoverForeground: '#f8fafc',
      primary: '#38bdf8',
      primaryForeground: '#f8fafc',
      secondary: '#0c4a6e',
      secondaryForeground: '#f8fafc',
      muted: '#0c4a6e',
      mutedForeground: '#94a3b8',
      accent: '#0c4a6e',
      accentForeground: '#f8fafc',
      destructive: '#ef4444',
      destructiveForeground: '#f8fafc',
      border: '#1e40af',
      input: '#1e40af',
      ring: '#38bdf8'
    }
  }
};

/**
 * GitHub Neutral theme variant - Clean and professional like GitHub
 */
const githubTheme: ThemeVariantDefinition = {
  variant: 'github',
  name: 'GitHub Neutral',
  description: 'Clean and professional theme inspired by GitHub\'s design system',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#1f2328',
      card: '#ffffff',
      cardForeground: '#1f2328',
      popover: '#ffffff',
      popoverForeground: '#1f2328',
      primary: '#0969da',
      primaryForeground: '#ffffff',
      secondary: '#f6f8fa',
      secondaryForeground: '#1f2328',
      muted: '#f6f8fa',
      mutedForeground: '#656d76',
      accent: '#f6f8fa',
      accentForeground: '#1f2328',
      destructive: '#d1242f',
      destructiveForeground: '#ffffff',
      border: '#d1d9e0',
      input: '#d1d9e0',
      ring: '#0969da'
    },
    dark: {
      background: '#0d1117',
      foreground: '#f0f6fc',
      card: '#161b22',
      cardForeground: '#f0f6fc',
      popover: '#161b22',
      popoverForeground: '#f0f6fc',
      primary: '#2f81f7',
      primaryForeground: '#ffffff',
      secondary: '#21262d',
      secondaryForeground: '#f0f6fc',
      muted: '#21262d',
      mutedForeground: '#8b949e',
      accent: '#21262d',
      accentForeground: '#f0f6fc',
      destructive: '#f85149',
      destructiveForeground: '#ffffff',
      border: '#30363d',
      input: '#30363d',
      ring: '#2f81f7'
    }
  }
};

/**
 * Linear Purple theme variant - Modern and sleek like Linear
 */
const linearTheme: ThemeVariantDefinition = {
  variant: 'linear',
  name: 'Linear Purple',
  description: 'Modern and sleek theme inspired by Linear\'s interface',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#0c0c0c',
      card: '#ffffff',
      cardForeground: '#0c0c0c',
      popover: '#ffffff',
      popoverForeground: '#0c0c0c',
      primary: '#5e6ad2',
      primaryForeground: '#ffffff',
      secondary: '#f8f9ff',
      secondaryForeground: '#0c0c0c',
      muted: '#f8f9ff',
      mutedForeground: '#6b7280',
      accent: '#f8f9ff',
      accentForeground: '#0c0c0c',
      destructive: '#ef4444',
      destructiveForeground: '#ffffff',
      border: '#e5e7eb',
      input: '#e5e7eb',
      ring: '#5e6ad2'
    },
    dark: {
      background: '#0c0c0c',
      foreground: '#ffffff',
      card: '#1a1a1a',
      cardForeground: '#ffffff',
      popover: '#1a1a1a',
      popoverForeground: '#ffffff',
      primary: '#8b5cf6',
      primaryForeground: '#ffffff',
      secondary: '#1a1a1a',
      secondaryForeground: '#ffffff',
      muted: '#1a1a1a',
      mutedForeground: '#9ca3af',
      accent: '#1a1a1a',
      accentForeground: '#ffffff',
      destructive: '#ef4444',
      destructiveForeground: '#ffffff',
      border: '#2a2a2a',
      input: '#2a2a2a',
      ring: '#8b5cf6'
    }
  }
};
/**
 * Vercel Minimal theme variant - High contrast black and white like Vercel
 */
const vercelTheme: ThemeVariantDefinition = {
  variant: 'vercel',
  name: 'Vercel Minimal',
  description: 'High contrast minimal theme inspired by Vercel\'s design',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#000000',
      card: '#ffffff',
      cardForeground: '#000000',
      popover: '#ffffff',
      popoverForeground: '#000000',
      primary: '#000000',
      primaryForeground: '#ffffff',
      secondary: '#fafafa',
      secondaryForeground: '#000000',
      muted: '#fafafa',
      mutedForeground: '#666666',
      accent: '#fafafa',
      accentForeground: '#000000',
      destructive: '#e60000',
      destructiveForeground: '#ffffff',
      border: '#eaeaea',
      input: '#eaeaea',
      ring: '#000000'
    },
    dark: {
      background: '#000000',
      foreground: '#ffffff',
      card: '#111111',
      cardForeground: '#ffffff',
      popover: '#111111',
      popoverForeground: '#ffffff',
      primary: '#ffffff',
      primaryForeground: '#000000',
      secondary: '#111111',
      secondaryForeground: '#ffffff',
      muted: '#111111',
      mutedForeground: '#888888',
      accent: '#111111',
      accentForeground: '#ffffff',
      destructive: '#ff4444',
      destructiveForeground: '#ffffff',
      border: '#333333',
      input: '#333333',
      ring: '#ffffff'
    }
  }
};

/**
 * Enterprise Green theme variant - Professional business theme
 */
const enterpriseTheme: ThemeVariantDefinition = {
  variant: 'enterprise',
  name: 'Enterprise Green',
  description: 'Professional business theme with emerald green accents',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#0f172a',
      card: '#ffffff',
      cardForeground: '#0f172a',
      popover: '#ffffff',
      popoverForeground: '#0f172a',
      primary: '#059669',
      primaryForeground: '#ffffff',
      secondary: '#f0fdf4',
      secondaryForeground: '#0f172a',
      muted: '#f0fdf4',
      mutedForeground: '#64748b',
      accent: '#f0fdf4',
      accentForeground: '#0f172a',
      destructive: '#dc2626',
      destructiveForeground: '#ffffff',
      border: '#e2e8f0',
      input: '#e2e8f0',
      ring: '#059669'
    },
    dark: {
      background: '#064e3b',
      foreground: '#f0fdf4',
      card: '#065f46',
      cardForeground: '#f0fdf4',
      popover: '#065f46',
      popoverForeground: '#f0fdf4',
      primary: '#10b981',
      primaryForeground: '#ffffff',
      secondary: '#065f46',
      secondaryForeground: '#f0fdf4',
      muted: '#065f46',
      mutedForeground: '#94a3b8',
      accent: '#065f46',
      accentForeground: '#f0fdf4',
      destructive: '#ef4444',
      destructiveForeground: '#ffffff',
      border: '#047857',
      input: '#047857',
      ring: '#10b981'
    }
  }
};

/**
 * Professional Navy theme variant - Corporate-friendly navy theme
 */
const navyTheme: ThemeVariantDefinition = {
  variant: 'navy',
  name: 'Professional Navy',
  description: 'Corporate-friendly theme with deep navy and indigo accents',
  colors: {
    light: {
      background: '#ffffff',
      foreground: '#1e293b',
      card: '#ffffff',
      cardForeground: '#1e293b',
      popover: '#ffffff',
      popoverForeground: '#1e293b',
      primary: '#1e40af',
      primaryForeground: '#ffffff',
      secondary: '#f1f5f9',
      secondaryForeground: '#1e293b',
      muted: '#f1f5f9',
      mutedForeground: '#64748b',
      accent: '#f1f5f9',
      accentForeground: '#1e293b',
      destructive: '#dc2626',
      destructiveForeground: '#ffffff',
      border: '#e2e8f0',
      input: '#e2e8f0',
      ring: '#1e40af'
    },
    dark: {
      background: '#0f172a',
      foreground: '#f8fafc',
      card: '#1e293b',
      cardForeground: '#f8fafc',
      popover: '#1e293b',
      popoverForeground: '#f8fafc',
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      secondary: '#1e293b',
      secondaryForeground: '#f8fafc',
      muted: '#1e293b',
      mutedForeground: '#94a3b8',
      accent: '#1e293b',
      accentForeground: '#f8fafc',
      destructive: '#ef4444',
      destructiveForeground: '#ffffff',
      border: '#334155',
      input: '#334155',
      ring: '#3b82f6'
    }
  }
};

/**
 * Modern theme variant - Sleek and contemporary
 */
const modernTheme: ThemeVariantDefinition = {
  variant: 'modern',
  name: 'Modern',
  description: 'A sleek and contemporary theme with a modern color palette.',
  colors: {
    light: {
        background: '#f9f9f9',
        foreground: '#1a1a1a',
        card: '#ffffff',
        cardForeground: '#1a1a1a',
        popover: '#ffffff',
        popoverForeground: '#1a1a1a',
        primary: '#007acc',
        primaryForeground: '#ffffff',
        secondary: '#f2f2f2',
        secondaryForeground: '#1a1a1a',
        muted: '#f2f2f2',
        mutedForeground: '#666666',
        accent: '#f2f2f2',
        accentForeground: '#1a1a1a',
        destructive: '#e53e3e',
        destructiveForeground: '#ffffff',
        border: '#e0e0e0',
        input: '#e0e0e0',
        ring: '#007acc'
    },
    dark: {
        background: '#1a1a1a',
        foreground: '#f9f9f9',
        card: '#2a2a2a',
        cardForeground: '#f9f9f9',
        popover: '#2a2a2a',
        popoverForeground: '#f9f9f9',
        primary: '#007acc',
        primaryForeground: '#ffffff',
        secondary: '#2a2a2a',
        secondaryForeground: '#f9f9f9',
        muted: '#2a2a2a',
        mutedForeground: '#999999',
        accent: '#2a2a2a',
        accentForeground: '#f9f9f9',
        destructive: '#e53e3e',
        destructiveForeground: '#ffffff',
        border: '#3a3a3a',
        input: '#3a3a3a',
        ring: '#007acc'
    }
  }
};



/**
 * Complete theme registry
 * Note: The 'default' theme preserves your current light/dark theme exactly as configured
 */
export const THEME_REGISTRY: ThemeVariantDefinition[] = [
  defaultTheme,  // Your current beloved theme - preserved exactly!
  blueTheme,
  githubTheme,
  linearTheme,
  vercelTheme,
  enterpriseTheme,
  navyTheme,
  modernTheme
];

/**
 * Get theme configuration by variant and mode
 */
export function getThemeConfig(variant: string, mode: 'light' | 'dark'): ThemeConfig | null {
  const themeVariant = THEME_REGISTRY.find(t => t.variant === variant);
  if (!themeVariant) return null;

  // Create a ThemeConfig from the color palette
  const colors = themeVariant.colors[mode];
  return {
    id: `${variant}-${mode}`,
    name: `${themeVariant.name} ${mode === 'light' ? 'Light' : 'Dark'}`,
    variant: variant as any,
    mode,
    description: themeVariant.description || '',
    preview: {
      primary: colors.primary,
      secondary: colors.secondary,
      accent: colors.accent,
      background: colors.background
    },
    className: `theme-${variant}-${mode}`,
    colors: {
      [mode]: colors
    }
  };
}

/**
 * Get all available theme variants
 */
export function getAvailableThemes(): ThemeVariantDefinition[] {
  return THEME_REGISTRY;
}

/**
 * Default theme configuration
 */
export const DEFAULT_THEME_VARIANT = 'default';
export const DEFAULT_THEME_MODE = 'light';
