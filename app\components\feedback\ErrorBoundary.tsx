"use client";

import { Component, ErrorInfo, ReactNode } from 'react';
import ErrorDisplay from './ErrorDisplay/ErrorDisplay';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * Error Boundary component that catches JavaScript errors anywhere in its child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Check if this is a test error
    const isTestError = (error as any).isTestError ||
                        error.message.includes('[TEST ERROR]') ||
                        error.message.includes('test error');

    // Log the error with appropriate handling
    if (isTestError) {
      // Log test errors differently
      console.log('[Test Error caught by ErrorBoundary]:', error.message);
    } else {
      // Log real errors as errors
      console.error('Error caught by ErrorBoundary:', error, errorInfo);
      console.error('Component stack:', errorInfo.componentStack);
    }
  }

  resetErrorBoundary = (): void => {
    this.setState({
      hasError: false,
      error: null
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ErrorDisplay
          error={this.state.error || new Error('An unknown error occurred')}
          onRetry={this.resetErrorBoundary}
          message="Something went wrong"
          suggestion="Please try again or contact support if the problem persists"
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
