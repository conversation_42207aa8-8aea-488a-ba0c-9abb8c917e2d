import { NextRequest, NextResponse } from 'next/server';
import { getChildCategories, getTopLevelCategories, handleMongoDBError } from '@/app/services/category.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching child categories of a specified parent
 * @param request - The incoming request
 * @returns JSON response with child categories data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/categories/child - Fetching child categories');
    const url = new URL(request.url);

    // Get parent category ID (null for top-level)
    const parentId = url.searchParams.get('parent') || null;
    
    // Check if we specifically want top-level categories
    const topLevel = url.searchParams.get('topLevel') === 'true';

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Prepare options for service function
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
    };

    let result;
    
    if (topLevel) {
      console.log(`[API] Calling getTopLevelCategories service with options: ${JSON.stringify(options)}`);
      result = await getTopLevelCategories(options);
    } else {
      console.log(`[API] Calling getChildCategories service with parentId: ${parentId}, options: ${JSON.stringify(options)}`);
      result = await getChildCategories(parentId, options);
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Service ${topLevel ? 'getTopLevelCategories' : 'getChildCategories'} completed in ${duration}ms with ${result.categories.length} results`);

    // Return response
    return NextResponse.json({
      data: result.categories,
      pagination: result.pagination,
      error: null,
      meta: { duration, parentId }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/categories/child (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
} 