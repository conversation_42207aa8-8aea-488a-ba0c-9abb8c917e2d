/**
 * Migration script to convert existing inventory data to new granular stock tracking schema
 * 
 * This script:
 * 1. Converts existing currentStock to stockLevels.finished
 * 2. Maps legacy transactions to new from/to structure where possible
 * 3. Adds database indexes for performance
 * 
 * Run with: npx ts-node scripts/migrate-inventory-schema.ts
 */

import mongoose from 'mongoose';
import connectToDatabase from '../app/lib/mongodb';
import Part from '../app/models/part.model';
import InventoryTransaction from '../app/models/inventorytransaction.model';

interface MigrationStats {
  partsProcessed: number;
  partsUpdated: number;
  transactionsProcessed: number;
  transactionsUpdated: number;
  errors: string[];
}

// Type for legacy transaction types
type LegacyTransactionType =
  | 'stock_in_purchase'
  | 'stock_out_production'
  | 'adjustment_cycle_count'
  | 'stock_in_production'
  | 'transfer_out'
  | 'transfer_in'
  | 'sales_shipment'
  | 'adjustment_manual'
  | 'stock_out_production';

async function migrateParts(): Promise<{ processed: number; updated: number; errors: string[] }> {
  console.log('🔄 Migrating parts to new stockLevels structure...');
  
  const stats: { processed: number; updated: number; errors: string[] } = { processed: 0, updated: 0, errors: [] };
  
  try {
    // Find all parts that still use the old currentStock structure
    const parts = await Part.find({
      'inventory.currentStock': { $exists: true },
      'inventory.stockLevels': { $exists: false }
    });
    
    console.log(`Found ${parts.length} parts to migrate`);
    
    for (const part of parts) {
      stats.processed++;
      
      try {
        const currentStock = part.inventory.currentStock || 0;
        
        // Create new stockLevels structure with currentStock as finished
        const stockLevels = {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: currentStock,
          rejected: 0
        };
        
        // Update the part with new structure
        await Part.findByIdAndUpdate(part._id, {
          $set: {
            'inventory.stockLevels': stockLevels,
            'inventory.lastStockUpdate': new Date()
          },
          // Keep currentStock for backward compatibility during transition
          // $unset: { 'inventory.currentStock': 1 }
        });
        
        stats.updated++;
        
        if (stats.processed % 100 === 0) {
          console.log(`Processed ${stats.processed} parts...`);
        }
        
      } catch (error: any) {
        const errorMsg = `Part ${part._id}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(`Error migrating part ${part._id}:`, error.message);
      }
    }
    
  } catch (error: any) {
    const errorMsg = `Parts migration error: ${error.message}`;
    stats.errors.push(errorMsg);
    console.error('Error in parts migration:', error);
  }
  
  return stats;
}

async function migrateTransactions(): Promise<{ processed: number; updated: number; errors: string[] }> {
  console.log('🔄 Migrating transactions to event-sourced structure...');
  
  const stats: { processed: number; updated: number; errors: string[] } = { processed: 0, updated: 0, errors: [] };
  
  try {
    // Find all transactions that don't have the new from/to structure
    const transactions = await InventoryTransaction.find({
      $and: [
        { from: { $exists: false } },
        { to: { $exists: false } },
        { transactionType: { $exists: true } }
      ]
    }).populate('warehouseId', 'name location_id');
    
    console.log(`Found ${transactions.length} transactions to migrate`);
    
    for (const transaction of transactions) {
      stats.processed++;
      
      try {
        let from = null;
        let to = null;
        let newTransactionType = transaction.transactionType;
        
        // Map legacy transaction types to new event-sourced structure
        const legacyType = transaction.transactionType as LegacyTransactionType;
        switch (legacyType) {
          case 'stock_in_purchase':
            // External purchase → Raw stock
            newTransactionType = 'purchase_receipt';
            from = null; // External source
            to = {
              warehouseId: transaction.warehouseId,
              stockType: 'raw'
            };
            break;
            
          case 'sales_shipment':
            // Finished stock → External customer
            from = {
              warehouseId: transaction.warehouseId,
              stockType: 'finished'
            };
            to = null; // External destination
            break;
            
          case 'transfer_in':
          case 'transfer_out':
            // Internal transfer - assume finished stock
            newTransactionType = 'internal_transfer';
            if (legacyType === 'transfer_out') {
              from = {
                warehouseId: transaction.warehouseId,
                stockType: 'finished'
              };
              to = null; // Unknown destination
            } else {
              from = null; // Unknown source
              to = {
                warehouseId: transaction.warehouseId,
                stockType: 'finished'
              };
            }
            break;

          case 'adjustment_cycle_count':
          case 'adjustment_manual':
            // Stock adjustment
            newTransactionType = 'adjustment';
            // For adjustments, we can't determine from/to, so leave as null
            break;

          case 'stock_out_production':
            // Production consumption - assume finished to production
            newTransactionType = 'process_move';
            from = {
              warehouseId: transaction.warehouseId,
              stockType: 'finished'
            };
            to = null; // Production process
            break;

          case 'stock_in_production':
            // Production output - assume production to finished
            newTransactionType = 'process_move';
            from = null; // Production process
            to = {
              warehouseId: transaction.warehouseId,
              stockType: 'finished'
            };
            break;
            
          default:
            // Unknown transaction type - leave as adjustment
            newTransactionType = 'adjustment';
            break;
        }
        
        // Update the transaction with new structure
        const updateData: any = {
          transactionType: newTransactionType
        };
        
        if (from !== null) {
          updateData.from = from;
        }
        if (to !== null) {
          updateData.to = to;
        }
        
        await InventoryTransaction.findByIdAndUpdate(transaction._id, {
          $set: updateData
        });
        
        stats.updated++;
        
        if (stats.processed % 100 === 0) {
          console.log(`Processed ${stats.processed} transactions...`);
        }
        
      } catch (error: any) {
        const errorMsg = `Transaction ${transaction._id}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(`Error migrating transaction ${transaction._id}:`, error.message);
      }
    }
    
  } catch (error: any) {
    const errorMsg = `Transactions migration error: ${error.message}`;
    stats.errors.push(errorMsg);
    console.error('Error in transactions migration:', error);
  }
  
  return stats;
}

async function addDatabaseIndexes(): Promise<void> {
  console.log('🔄 Adding database indexes for performance...');
  
  try {
    // Add indexes for new stockLevels structure
    await Part.collection.createIndex({ 'inventory.stockLevels.finished': 1 });
    await Part.collection.createIndex({ 'inventory.stockLevels.raw': 1 });
    await Part.collection.createIndex({ 'inventory.lastStockUpdate': -1 });
    
    // Add indexes for new transaction structure
    await InventoryTransaction.collection.createIndex({ 'from.warehouseId': 1, transactionDate: -1 });
    await InventoryTransaction.collection.createIndex({ 'to.warehouseId': 1, transactionDate: -1 });
    await InventoryTransaction.collection.createIndex({ 'from.stockType': 1 });
    await InventoryTransaction.collection.createIndex({ 'to.stockType': 1 });
    
    console.log('✅ Database indexes added successfully');
    
  } catch (error: any) {
    console.error('Error adding database indexes:', error);
    throw error;
  }
}

async function runMigration(): Promise<void> {
  console.log('🚀 Starting inventory schema migration...');
  
  try {
    await connectToDatabase();
    console.log('✅ Connected to database');
    
    const migrationStats: MigrationStats = {
      partsProcessed: 0,
      partsUpdated: 0,
      transactionsProcessed: 0,
      transactionsUpdated: 0,
      errors: []
    };
    
    // Migrate parts
    const partsStats = await migrateParts();
    migrationStats.partsProcessed = partsStats.processed;
    migrationStats.partsUpdated = partsStats.updated;
    migrationStats.errors.push(...partsStats.errors);
    
    // Migrate transactions
    const transactionsStats = await migrateTransactions();
    migrationStats.transactionsProcessed = transactionsStats.processed;
    migrationStats.transactionsUpdated = transactionsStats.updated;
    migrationStats.errors.push(...transactionsStats.errors);
    
    // Add database indexes
    await addDatabaseIndexes();
    
    // Print migration summary
    console.log('\n📊 Migration Summary:');
    console.log(`Parts processed: ${migrationStats.partsProcessed}`);
    console.log(`Parts updated: ${migrationStats.partsUpdated}`);
    console.log(`Transactions processed: ${migrationStats.transactionsProcessed}`);
    console.log(`Transactions updated: ${migrationStats.transactionsUpdated}`);
    console.log(`Errors: ${migrationStats.errors.length}`);
    
    if (migrationStats.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      migrationStats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    console.log('\n✅ Migration completed successfully!');
    
  } catch (error: any) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration().catch(console.error);
}

export { runMigration, migrateParts, migrateTransactions, addDatabaseIndexes };
