import Batch, { IBatch } from '@/app/models/batch.model';
import { IInventoryTransaction, InventoryTransaction } from '@/app/models/inventorytransaction.model';
import { handleMongoDBError } from '@/app/services/mongodb';
import { Types } from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the batch ID
}

/**
 * GET handler for fetching transactions for a specific batch
 * @param request - The incoming request
 * @param context - Route context containing the batch ID (Promise in Next.js 15)
 * @returns JSON response with the batch transactions
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    console.log(`[API] GET /api/batches/${id}/transactions - Fetching batch transactions`);

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sortField = searchParams.get('sortField') || 'transactionDate';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;

    // Validate that the ID is a valid ObjectId
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { data: null, error: 'Invalid batch ID format', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find the batch to get its batchCode
    const batch = await (Batch as any).findById(id).select('batchCode').lean() as IBatch | null;
    if (!batch) {
      return NextResponse.json(
        { data: null, error: `Batch with ID ${id} not found`, meta: { duration: Date.now() - startTime } },
        { status: 404 }
      );
    }

    // Find transactions for this batch using its batchCode
    const transactions = await (InventoryTransaction as any).find({ referenceNumber: batch.batchCode })
      .sort({ [sortField]: sortOrder })
      .skip(skip)
      .limit(limit)
      .select('partId transactionType quantity previousStock newStock transactionDate referenceNumber notes createdAt')
      .populate({
        path: 'partId',
        // model: 'Part', // Not strictly necessary if schema ref is correct
        select: '_id partNumber name description' // Added partNumber for consistency
      })
      .lean() as IInventoryTransaction[];

    // Get total count for pagination (batch already fetched and validated above)
    const totalCount = await InventoryTransaction.countDocuments({ referenceNumber: batch.batchCode });

    const duration = Date.now() - startTime;
    console.log(`[API] Batch transactions query completed in ${duration}ms`);

    return NextResponse.json({
      data: {
        transactions,
        pagination: {
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          currentPage: page,
          limit
        }
      },
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches/[id]/transactions (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
