/**
 * Inventory Types for Trend IMS
 * 
 * This file contains type definitions for inventory-related entities
 * including Products, Assemblies, Parts, and related interfaces.
 */

// ============================================================================
// Product Types
// ============================================================================

/**
 * Product interface based on the product model
 */
export interface Product {
  _id: string;
  id: string; // Frontend compatibility mapping from _id
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  assemblyId?: string | null;
  partId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  // Additional properties for frontend compatibility
  currentStock?: number;
  reorderLevel?: number;
  supplierManufacturer?: string;

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  inventory?: ProductInventory;
}

/**
 * Product inventory details
 */
export interface ProductInventory {
  stockLevels?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  currentStock: number; // Required for backward compatibility
  warehouseId?: string;
  safetyStockLevel?: number;
  maximumStockLevel?: number;
  averageDailyUsage?: number;
  abcClassification?: string;
  lastStockUpdate?: Date | null;
}

// ============================================================================
// Assembly Types
// ============================================================================

/**
 * Assembly interface based on the assembly model
 */
export interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
  description?: string | null;
  productId?: string | null;
  parentId?: string | null;
  isTopLevel: boolean;
  partsRequired: AssemblyPartRequired[];
  subAssemblies?: SubAssemblyRequired[];
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete' | 'archived';
  version: number;
  manufacturingInstructions?: string | null;
  estimatedBuildTime?: string | null;
  manufacturingLeadTime?: string | null;
  costData?: AssemblyCostData | null;
  notes?: string | null;
  createdBy?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Assembly part requirement
 */
export interface AssemblyPartRequired {
  partId: string;
  partNumber?: string;
  partName?: string;
  quantityRequired: number;
  unitOfMeasure?: string;
  notes?: string | null;
  isOptional?: boolean;
  alternativePartIds?: string[];
}

/**
 * Sub-assembly requirement
 */
export interface SubAssemblyRequired {
  subAssemblyId: string;
  subAssemblyCode?: string;
  subAssemblyName?: string;
  quantityRequired: number;
  notes?: string | null;
  isOptional?: boolean;
}

/**
 * Assembly cost data
 */
export interface AssemblyCostData {
  materialCost?: number;
  laborCost?: number;
  overheadCost?: number;
  totalCost?: number;
  costPerUnit?: number;
  lastUpdated?: Date;
}

// ============================================================================
// Part Types
// ============================================================================

/**
 * Part interface based on the part model
 */
export interface Part {
  _id: string;
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  inventory: PartInventory;
  supplierId?: string | null;
  unitOfMeasure: string;
  standardCost: number; // Changed from costPrice to match target schema
  abcClassification?: string | null; // NEW FIELD: ABC classification (A, B, C)
  categoryId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Part inventory details
 */
export interface PartInventory {
  currentStock: number;
  stockLevels?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  warehouseId: string;
  safetyStockLevel: number;
  maximumStockLevel: number;
  averageDailyUsage: number;
  abcClassification: string;
  lastStockUpdate?: Date | null;
}

// ============================================================================
// Inventory Level Types
// ============================================================================

/**
 * Inventory level interface for tracking stock across different item types
 */
export interface InventoryLevel {
  _id: string;
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  warehouseId: string;
  quantityOnHand: number;
  quantityAllocated?: number;
  quantityAvailable?: number; // Virtual field: quantityOnHand - quantityAllocated
  locationInWarehouse?: string | null;
  reorderLevel?: number | null;
  safetyStockLevel?: number | null;
  maximumStockLevel?: number | null;
  averageDailyUsage?: number | null;
  abcClassification?: string | null;
  lastStockUpdate: Date;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Inventory Transaction Types
// ============================================================================

/**
 * Inventory transaction interface for tracking stock movements
 */
export interface InventoryTransaction {
  _id: string;
  transactionNumber: string;
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  transactionType: 'receipt' | 'issue' | 'adjustment' | 'transfer' | 'return';
  quantity: number;
  unitCost?: number | null;
  totalCost?: number | null;
  warehouseId: string;
  locationInWarehouse?: string | null;
  referenceNumber?: string | null;
  referenceType?: string | null;
  notes?: string | null;
  performedBy: string;
  transactionDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Batch and Lot Tracking Types
// ============================================================================

/**
 * Batch interface for lot tracking
 */
export interface Batch {
  _id: string;
  batchNumber: string;
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  quantity: number;
  expirationDate?: Date | null;
  manufacturingDate?: Date | null;
  supplierId?: string | null;
  qualityStatus: 'pending' | 'approved' | 'rejected' | 'quarantined';
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Utility Types for Inventory
// ============================================================================

/**
 * Stock status enumeration
 */
export type StockStatus = 'in_stock' | 'low_stock' | 'out_of_stock' | 'discontinued';

/**
 * ABC Classification for inventory management
 */
export type ABCClassification = 'A' | 'B' | 'C';

/**
 * Inventory movement types
 */
export type InventoryMovementType = 'receipt' | 'issue' | 'adjustment' | 'transfer' | 'return';

/**
 * Quality status for batches and lots
 */
export type QualityStatus = 'pending' | 'approved' | 'rejected' | 'quarantined';

/**
 * Item types that can be tracked in inventory
 */
export type InventoryItemType = 'Part' | 'Assembly' | 'Product';

// ============================================================================
// Search and Filter Types
// ============================================================================

/**
 * Inventory search filters
 */
export interface InventorySearchFilters {
  itemType?: InventoryItemType[];
  status?: string[];
  warehouseId?: string[];
  categoryId?: string[];
  supplierId?: string[];
  stockStatus?: StockStatus[];
  abcClassification?: ABCClassification[];
  searchTerm?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Inventory sort options
 */
export interface InventorySortOptions {
  field: 'name' | 'partNumber' | 'currentStock' | 'reorderLevel' | 'lastStockUpdate' | 'createdAt';
  direction: 'asc' | 'desc';
}
