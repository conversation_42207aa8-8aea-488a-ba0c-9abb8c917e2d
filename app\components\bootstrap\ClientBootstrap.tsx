'use client';

import { fixHmrFetch, suppressHmrErrors } from '@/app/lib/dev-utils';
import { env } from '@/app/utils/env';
import { safeApiCall } from '@/app/utils/safeFetch';
import { useEffect, useState } from 'react';
import ThemeBootstrap from './ThemeBootstrap';

/**
 * Client-side component that bootstraps the application
 * This component is responsible for initializing client-side services
 * and monitoring the application state
 */
export default function ClientBootstrap({ children }: { children: React.ReactNode }) {
  const [isBootstrapped, setIsBootstrapped] = useState(false);

  useEffect(() => {
    // Setup HMR error suppression in development mode
    let restoreConsole: (() => void) | undefined;
    let restoreFetch: (() => void) | undefined;

    if (env.NODE_ENV === 'development') {
      console.log('[ClientBootstrap] Setting up development mode error handling...');
      // Apply HMR error suppression
      restoreConsole = suppressHmrErrors();
      // Fix HMR fetch
      restoreFetch = fixHmrFetch();
    }

    // Bootstrap client-side only features
    const bootstrapClient = async () => {
      try {
        console.log('[ClientBootstrap] Initializing client-side bootstrap...');

        // Use safe API call to prevent HTML error responses
        const result = await safeApiCall('/api/status');

        if (result.success) {
          console.log('[ClientBootstrap] Application status:', result.data);
        } else {
          console.warn('[ClientBootstrap] Failed to fetch application status:', result.error);
          // Don't throw error, just log it and continue
        }

        setIsBootstrapped(true);
      } catch (error) {
        console.error('[ClientBootstrap] Error in client bootstrap:',
          error instanceof Error ? error.message : String(error));
        // Still mark as bootstrapped even if there's an error to allow the app to function
        setIsBootstrapped(true);
      }
    };

    // Call the bootstrap function (don't return it, that's what caused the error)
    bootstrapClient();

    // Return the cleanup function
    return () => {
      if (typeof restoreConsole === 'function') restoreConsole();
      if (typeof restoreFetch === 'function') restoreFetch();
    };
  }, []);

  // Render children immediately to avoid delaying hydration
  // The bootstrap process happens in the background
  return (
    <>
      <ThemeBootstrap />
      {children}
    </>
  );
} 