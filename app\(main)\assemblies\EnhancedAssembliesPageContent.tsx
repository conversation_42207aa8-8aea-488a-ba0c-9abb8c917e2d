'use client';

import { Clock, Layers, Plus, Eye, Trash2, LayoutGrid, List } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';

import { Button } from '@/app/components/forms/Button';
import { EnhancedBackground } from '@/app/components/layout/EnhancedBackground';
import Header from '@/app/components/layout/Header';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { ViewAssemblyButton } from '@/app/components/modals/ViewAssemblyModal';
import { QuickEditAssemblyAction } from '@/app/components/actions/QuickEditAssemblyAction';
import { AssembliesGrid } from '@/app/components/tables/AssembliesGrid';
import Link from 'next/link';

import { AutoRefreshControl } from '@/app/components/controls/AutoRefreshControl';
import { NewAssemblyButton } from '@/app/components/navigation/NewAssemblyButton';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { LoadingSkeleton } from '@/app/components/data-display/loading';
import { ErrorBanner } from '@/app/components/feedback';
import { DataTableColumn } from '@/app/components/data-display/data-table/types';

// SCHEMA ALIGNMENT: Updated all mapping, filtering, and display logic to use canonical assembly schema from database_schema_updated.md. Legacy/incorrect fields removed or migrated.
// Use only canonical fields: assemblyCode, name, partsRequired: [{ partId, quantityRequired, unitOfMeasure }], etc.
// Remove all legacy/incorrect fields and update all usages and types accordingly.

/**
 * Enhanced Assemblies page content component with StandardizedTable
 */
const EnhancedAssembliesPageContent: React.FC = () => {
    const {
        assemblies,
        isLoading,
        error,
        refreshAssemblies
    } = useAssemblies();

    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

    // Fetch assemblies with parts data
    useEffect(() => {
        const fetchAssemblies = async () => {
            try {
                await refreshAssemblies({ includeParts: true });
                setLastUpdated(new Date());
            } catch (error) {
                console.error("Failed to refresh assemblies:", error);
            }
        };
        fetchAssemblies();
    }, []);

    // Define columns for the StandardizedTable
    const columns = useMemo<DataTableColumn<any>[]>(() => [
        {
            accessorKey: 'name',
            header: 'Assembly Name',
            enableHiding: false, // Always show assembly name
            cell: ({ row }) => (
                <div>
                    <div className="font-medium">{row.original.name}</div>
                    <div className="text-sm text-muted-foreground">{row.original.assemblyCode}</div>
                </div>
            ),
        },
        {
            accessorKey: 'status',
            header: 'Status',
            enableHiding: true, // Allow hiding status
            cell: ({ row }) => {
                const hasValidParts = row.original.partsRequired && Array.isArray(row.original.partsRequired) && row.original.partsRequired.length > 0;
                const status = hasValidParts ? 'Active' : 'Empty';
                return (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        hasValidParts
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                        {status}
                    </span>
                );
            },
        },
        {
            accessorKey: 'partsRequired',
            header: 'Parts',
            cell: ({ row }) => {
                const partsCount = row.original.partsRequired?.length || 0;
                return (
                    <div>
                        <div className="font-medium">{partsCount}</div>
                        <div className="text-sm text-muted-foreground">
                            {partsCount === 0 ? 'No parts defined' : `${partsCount} part${partsCount !== 1 ? 's' : ''}`}
                        </div>
                    </div>
                );
            },
        },
        {
            id: 'actions',
            header: 'Actions',
            enableSorting: false,
            enableHiding: false,
            cell: ({ row }) => (
                <div className="flex items-center gap-1">
                    {/* View Action */}
                    <ViewAssemblyButton
                        assembly={row.original as any}
                        variant="ghost"
                        size="sm"
                    >
                        <Eye className="h-4 w-4" />
                    </ViewAssemblyButton>

                    {/* Edit Action */}
                    <QuickEditAssemblyAction
                        assembly={row.original as any}
                        variant="icon"
                        size="sm"
                        onSuccess={() => refreshAssemblies()}
                    />

                    {/* Delete Action - TODO: Add delete functionality */}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            // TODO: Implement delete functionality
                            console.log('Delete assembly:', row.original._id);
                        }}
                        className="text-destructive hover:text-destructive"
                    >
                        <Trash2 className="h-4 w-4" />
                    </Button>
                </div>
            ),
        },
    ], []);

    // Filter out invalid assemblies
    const validAssemblies = useMemo(() => {
        return assemblies.filter(assembly => {
            if (!assembly || !assembly.name) {
                console.warn('[AssembliesPage] Filtering out invalid assembly:', assembly);
                return false;
            }
            return true;
        });
    }, [assemblies]);

    // Render loading state
    if (isLoading && assemblies.length === 0) {
        return (
            <EnhancedBackground>
                <Header title="Assemblies" />
                <div className="container mx-auto px-4 py-8">
                    <LoadingSkeleton
                        variant="grid"
                        count={8}
                        columns={4}
                    />
                </div>
            </EnhancedBackground>
        );
    }

    // Render error state
    if (error) {
        return (
            <EnhancedBackground>
                <Header title="Assemblies" />
                <div className="container mx-auto px-4 py-8">
                    <ErrorBanner
                        error={error}
                        actions={[
                            {
                                label: "Retry",
                                action: () => refreshAssemblies(),
                                variant: "outline"
                            }
                        ]}
                    />
                </div>
            </EnhancedBackground>
        );
    }

    return (
        <EnhancedBackground patternType="grid" interactiveMode={false}>
            <Header title="Assemblies">
                <div className="flex items-center gap-2">
                    <AutoRefreshControl />
                </div>
            </Header>

            <div className="container mx-auto px-4 py-8">
                {/* StandardizedTable with action button in horizontal layout */}
                <StandardizedTable
                    data={validAssemblies}
                    columns={columns}
                    searchPlaceholder="Search assemblies..."
                    viewOptions={[
                        { id: 'table', label: 'Table', icon: <List className="h-4 w-4" /> },
                        { id: 'grid', label: 'Grid', icon: <LayoutGrid className="h-4 w-4" /> }
                    ]}
                    GridComponent={({ data }) => <AssembliesGrid assemblies={data as any} />}
                    renderActions={() => (
                        <NewAssemblyButton variant="default" />
                    )}
                    enableSorting={true}
                    enableFiltering={true}
                    enablePagination={true}
                    enableGlobalSearch={false} // Using StandardizedTable's search instead
                    enableColumnVisibility={false}
                    mobileDisplayMode="cards"
                    density="normal"
                    initialPagination={{ pageIndex: 0, pageSize: 20 }}
                    pageSizeOptions={[10, 20, 50, 100]}
                    renderEmptyState={() => (
                        <div className="text-center py-8">
                            <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                            <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                                There are no assemblies in the system yet.
                            </p>
                            <Button asChild>
                                <Link href="/assemblies/create">
                                    <Plus size={16} className="mr-2" />
                                    Create Assembly
                                </Link>
                            </Button>
                        </div>
                    )}
                />

                {/* Results count */}
                <div className="mt-4 text-sm text-muted-foreground flex items-center">
                    <Clock size={14} className="mr-1" />
                    <span>Last updated: {lastUpdated ? new Date(lastUpdated).toLocaleTimeString() : 'Never'}</span>
                    <span className="mx-2">•</span>
                    <span>{validAssemblies.length} {validAssemblies.length === 1 ? 'assembly' : 'assemblies'} found</span>
                </div>
            </div>
        </EnhancedBackground>
    );
};

export default EnhancedAssembliesPageContent;
