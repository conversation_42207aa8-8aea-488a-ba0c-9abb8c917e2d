import { NextRequest, NextResponse } from 'next/server';
// Import OPTIMIZED service functions and error handler
import { searchParts, handleMongoDBError } from '@/app/services/part.service';
import withDatabase from '@/app/middlewares/withDatabase';
// Remove direct model/db connection imports and response helpers if service handles response structure

// PERFORMANCE OPTIMIZATION: Reduced limits for search to prevent overloading
const MAX_LIMIT = 50; // Reduced from 100 to 50 for search performance
const DEFAULT_LIMIT = 20; // Explicit default limit for search

/**
 * GET handler for searching parts with pagination
 * This endpoint supports full-text search across all parts
 * @param request - The incoming request with search parameters
 * @returns JSON response with matching parts and pagination data
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/parts/search - Searching parts');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || DEFAULT_LIMIT.toString(), 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Validate pagination parameters
    if (page < 1 || limit < 1) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters', data: null },
        { status: 400 }
      );
    }

    const query = url.searchParams.get('search') || ''; // General search term
    const sortField = url.searchParams.get('sortField') || 'updatedAt';
    const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object (Aligned with New Schema) ---
    const filter: any = {};
    const status = url.searchParams.get('status');
    const isManufactured = url.searchParams.get('is_manufactured');
    const minStock = url.searchParams.get('minStock');
    const maxStock = url.searchParams.get('maxStock');
    const warehouseId = url.searchParams.get('warehouseId'); // NEW: Warehouse filter for transaction form
    // Removed outdated filters: description, supplier, stockStatus, excludeAssemblies, onlyAssemblies, category

    if (status) filter.status = status;
    if (isManufactured !== null) filter.is_manufactured = isManufactured === 'true';

    // V4 Schema: Stock filtering is handled in aggregation pipeline after inventory lookup
    // Remove embedded inventory filters as they no longer exist
    // NOTE: Stock filtering will be implemented in the service layer aggregation pipeline

    // TODO: Implement warehouse and stock filtering in searchParts service aggregation pipeline
    // For now, remove these filters as they reference non-existent embedded inventory fields

    // --- Prepare Options for Service Function ---
    const options = {
      query, // Pass the general search query
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter, // Pass the specific field filters
    };

    console.log(`[API] Calling searchParts service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await searchParts(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service searchParts completed in ${duration}ms`);

    // --- Return Response ---
    // PERFORMANCE OPTIMIZATION: Enhanced response with caching and compression
    const response = NextResponse.json({
      data: result?.parts || [],
      pagination: result?.pagination || {},
      error: null,
      meta: {
        duration,
        query,
        filter,
        sort: options.sort,
        count: result?.parts?.length || 0,
        totalCount: result?.pagination?.totalCount || 0
      }
    });

    // Add performance headers
    response.headers.set('X-Response-Time', `${duration}ms`);
    response.headers.set('X-Total-Count', (result?.pagination?.totalCount || 0).toString());

    // Add caching headers for search results (shorter cache for dynamic content)
    response.headers.set('Cache-Control', 'public, max-age=15, stale-while-revalidate=30');
    response.headers.set('ETag', `"search-${query}-${page}-${limit}-${Date.now()}"`);

    // FIXED: Removed incorrect Content-Encoding header that was causing decoding errors
    // The response is not actually compressed, so we shouldn't set Content-Encoding: gzip

    return response;

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/parts/search (${duration}ms):`, error);
    // Use imported error handler
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

// Export the handler wrapped with database middleware
export const GET = withDatabase(handleGET);
