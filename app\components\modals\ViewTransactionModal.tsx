'use client';

import { BaseViewModal } from './BaseViewModal';
import { BaseViewButton } from './BaseViewButton';
import { TransactionViewContent } from './content/TransactionViewContent';
import { type InventoryTransactionColumnData } from '@/app/components/data-display/data-table';
import { getTransactionTypeIcon, formatTransactionType } from '@/app/utils/transactionUtils';

interface ViewTransactionModalProps {
  transaction: InventoryTransactionColumnData;
  isOpen: boolean;
  onClose: () => void;
  trigger?: React.ReactNode;
}

interface ViewTransactionButtonProps {
  transaction: InventoryTransactionColumnData;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  buttonContent?: React.ReactNode;
  children?: React.ReactNode;
}



/**
 * Modal component for viewing transaction details
 * Uses the standardized BaseViewModal for consistent design
 */
export function ViewTransactionModal({ transaction, isOpen, onClose, trigger }: ViewTransactionModalProps) {
  const icon = getTransactionTypeIcon(transaction.transactionType, 'md');

  // Create a descriptive title
  const formattedType = formatTransactionType(transaction.transactionType);
  const itemName = transaction.itemName || transaction.partNumber || 'Unknown Item';
  const title = `${formattedType} - ${itemName}`;
  const subtitle = `Transaction Details`;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={title}
      subtitle={subtitle}
      icon={icon}
      maxWidth="max-w-4xl"
    >
      <TransactionViewContent transaction={transaction} isInTab={false} />
    </BaseViewModal>
  );
}

/**
 * Button component that opens the ViewTransactionModal
 * Uses the standardized BaseViewButton for consistent design
 */
export function ViewTransactionButton({
  transaction,
  variant = 'outline',
  size = 'sm',
  className,
  buttonContent,
  children
}: ViewTransactionButtonProps) {
  const icon = getTransactionTypeIcon(transaction.transactionType, 'md');

  // Create a descriptive title
  const formattedType = formatTransactionType(transaction.transactionType);
  const itemName = transaction.itemName || transaction.partNumber || 'Unknown Item';
  const title = `${formattedType} - ${itemName}`;
  const subtitle = `Transaction Details`;

  return (
    <BaseViewButton
      title={title}
      subtitle={subtitle}
      icon={icon}
      variant={variant}
      size={size}
      className={className || ''}
      buttonContent={buttonContent || children}
      maxWidth="max-w-4xl"
    >
      <TransactionViewContent transaction={transaction} />
    </BaseViewButton>
  );
}
