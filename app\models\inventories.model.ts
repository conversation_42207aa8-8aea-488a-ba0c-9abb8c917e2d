import mongoose, { Schema, Document, Types } from 'mongoose';

/**
 * NEW INVENTORIES COLLECTION MODEL
 * 
 * This model represents the new normalized inventory architecture where each document
 * represents the quantity of a specific part, in a specific manufacturing state, 
 * at a specific warehouse location.
 * 
 * This replaces the embedded inventory objects in the parts collection and enables:
 * - Multi-warehouse inventory management
 * - Multi-state manufacturing tracking
 * - Atomic inventory operations
 * - Better scalability and performance
 */

// Interface for the Inventories collection document - V4 Schema
export interface IInventories extends Document {
  _id: Types.ObjectId;
  partId: Types.ObjectId;        // Reference to parts._id
  locationId: Types.ObjectId;    // Reference to locations._id (V4 Schema - REQUIRED)
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected'; // Manufacturing state
  quantity: number;              // Current quantity on hand for this combination
  allocatedQuantity?: number;    // Quantity allocated for orders/work orders
  lastUpdated: Date;             // Timestamp of the last update to this stock record

  // Optional metadata fields for enhanced inventory management
  safetyStockLevel?: number;     // Minimum stock level for this part/location/type combination
  maximumStockLevel?: number;    // Maximum stock level for this part/location/type combination
  averageDailyUsage?: number;    // Average daily usage for planning
  abcClassification?: string;    // ABC classification for this specific inventory record
  notes?: string;                // Additional notes for this inventory record

  // REMOVED: Deprecated fields - use locationId and locations collection instead
  // DEPRECATED: warehouseId?: Types.ObjectId;  // Use locationId instead
  // DEPRECATED: locationInWarehouse?: string;  // Location name is now in locations collection

  createdAt: Date;
  updatedAt: Date;
}

// Schema for the new Inventories collection
const InventoriesSchema: Schema<IInventories> = new Schema(
  {
    partId: {
      type: Schema.Types.ObjectId,
      ref: 'Part',
      required: [true, 'Part ID is required'],
      index: true
    },
    locationId: {
      type: Schema.Types.ObjectId,
      ref: 'Location',
      required: [true, 'Location ID is required'],
      index: true
    },
    // REMOVED: warehouseId field - use locationId instead (V4 Schema)
    // DEPRECATED: warehouseId field removed in favor of locationId
    stockType: {
      type: String,
      required: [true, 'Stock type is required'],
      enum: {
        values: ['raw', 'hardening', 'grinding', 'finished', 'rejected'],
        message: 'Stock type must be one of: raw, hardening, grinding, finished, rejected'
      },
      index: true
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [0, 'Quantity cannot be negative'],
      default: 0,
      validate: {
        validator: Number.isInteger,
        message: 'Quantity must be a whole number'
      }
    },
    allocatedQuantity: {
      type: Number,
      min: [0, 'Allocated quantity cannot be negative'],
      default: 0,
      validate: {
        validator: Number.isInteger,
        message: 'Allocated quantity must be a whole number'
      }
    },
    lastUpdated: {
      type: Date,
      required: true,
      default: Date.now,
      index: true
    },
    
    // Optional metadata fields
    safetyStockLevel: {
      type: Number,
      min: [0, 'Safety stock level cannot be negative'],
      default: null,
      validate: {
        validator: function(value: number) {
          return value === null || value === undefined || Number.isInteger(value);
        },
        message: 'Safety stock level must be a whole number'
      }
    },
    maximumStockLevel: {
      type: Number,
      min: [0, 'Maximum stock level cannot be negative'],
      default: null,
      validate: {
        validator: function(value: number) {
          return value === null || value === undefined || Number.isInteger(value);
        },
        message: 'Maximum stock level must be a whole number'
      }
    },
    averageDailyUsage: {
      type: Number,
      min: [0, 'Average daily usage cannot be negative'],
      default: null
    },
    abcClassification: {
      type: String,
      enum: {
        values: ['A', 'B', 'C', null],
        message: 'ABC classification must be A, B, C, or null'
      },
      default: null,
      trim: true,
      uppercase: true
    },
    // REMOVED: locationInWarehouse field - use locations collection instead (V4 Schema)
    // DEPRECATED: locationInWarehouse field removed in favor of locations collection
    notes: {
      type: String,
      trim: true,
      default: null,
      maxlength: [500, 'Notes cannot exceed 500 characters']
    }
  },
  { 
    timestamps: true,
    collection: 'inventories' // Explicit collection name
  }
);

// CRITICAL INDEX: Compound unique index to ensure one record per part/location/stockType combination
InventoriesSchema.index(
  { partId: 1, locationId: 1, stockType: 1 },
  {
    unique: true,
    name: 'inventories_compound_unique_v2',
    background: true,
    comment: 'Ensures unique inventory records per part/location/stockType combination'
  }
);

// REMOVED: Legacy warehouseId index - use locationId instead (V4 Schema)
// DEPRECATED: Legacy index removed in favor of location-based indexing

// Additional performance indexes for common query patterns
InventoriesSchema.index(
  { partId: 1, lastUpdated: -1 }, 
  { 
    name: 'inventories_part_lastUpdated',
    background: true,
    comment: 'Optimizes queries for part inventory history'
  }
);

// New location-based indexes for optimal performance
InventoriesSchema.index(
  { locationId: 1, stockType: 1, quantity: -1 },
  {
    name: 'inventories_location_stockType_quantity',
    background: true,
    comment: 'Optimizes location-level inventory queries'
  }
);

// BACKWARD COMPATIBILITY: Keep warehouse-based index during transition
InventoriesSchema.index(
  { warehouseId: 1, stockType: 1, quantity: -1 },
  {
    name: 'inventories_warehouse_stockType_quantity_legacy',
    background: true,
    comment: 'Legacy warehouse-level inventory queries during migration'
  }
);

InventoriesSchema.index(
  { stockType: 1, quantity: -1 }, 
  { 
    name: 'inventories_stockType_quantity',
    background: true,
    comment: 'Optimizes stock type aggregation queries'
  }
);

// Pre-save middleware to update lastUpdated timestamp
InventoriesSchema.pre('save', function(this: IInventories & Document, next) {
  if (this.isModified('quantity') || this.isNew) {
    this.lastUpdated = new Date();
  }
  next();
});

// Pre-update middleware to update lastUpdated timestamp for updateOne/updateMany operations
InventoriesSchema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function(next) {
  this.set({ lastUpdated: new Date() });
  next();
});

// Static method to get total stock for a part across all warehouses and stock types
InventoriesSchema.statics.getTotalStockForPart = async function(partId: Types.ObjectId) {
  const result = await this.aggregate([
    { $match: { partId: new Types.ObjectId(partId) } },
    { $group: { _id: null, totalStock: { $sum: '$quantity' } } }
  ]);
  return result.length > 0 ? result[0].totalStock : 0;
};

// Static method to get stock breakdown by type for a part
InventoriesSchema.statics.getStockBreakdownForPart = async function(partId: Types.ObjectId) {
  return await this.aggregate([
    { $match: { partId: new Types.ObjectId(partId) } },
    { 
      $group: { 
        _id: '$stockType', 
        totalQuantity: { $sum: '$quantity' },
        warehouseCount: { $addToSet: '$warehouseId' }
      } 
    },
    { 
      $project: {
        stockType: '$_id',
        totalQuantity: 1,
        warehouseCount: { $size: '$warehouseCount' },
        _id: 0
      }
    }
  ]);
};

// Static method to get stock breakdown by warehouse for a part
InventoriesSchema.statics.getStockBreakdownByWarehouse = async function(partId: Types.ObjectId) {
  return await this.aggregate([
    { $match: { partId: new Types.ObjectId(partId) } },
    { 
      $group: { 
        _id: '$warehouseId', 
        totalQuantity: { $sum: '$quantity' },
        stockTypes: { 
          $push: { 
            stockType: '$stockType', 
            quantity: '$quantity' 
          } 
        }
      } 
    },
    {
      $lookup: {
        from: 'warehouses',
        localField: '_id',
        foreignField: '_id',
        as: 'warehouse'
      }
    },
    {
      $project: {
        warehouseId: '$_id',
        warehouseName: { $arrayElemAt: ['$warehouse.name', 0] },
        totalQuantity: 1,
        stockTypes: 1,
        _id: 0
      }
    }
  ]);
};

// Create and export the Inventories model
const Inventories = mongoose.models?.Inventories as mongoose.Model<IInventories> || 
  mongoose.model<IInventories>('Inventories', InventoriesSchema);

export { InventoriesSchema, Inventories };
export default Inventories;
