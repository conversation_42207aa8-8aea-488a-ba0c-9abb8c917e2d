// This is a Server Component by default (no "use client" directive)
import { WorkOrderFormClient } from "./WorkOrderFormClient";
import { WorkOrderFormProps } from "./types";

/**
 * WorkOrderForm component
 * Server component that delegates to client component
 */
export function WorkOrderForm(props: WorkOrderFormProps) {
  return <WorkOrderFormClient {...props} />;
}

export default WorkOrderForm;
