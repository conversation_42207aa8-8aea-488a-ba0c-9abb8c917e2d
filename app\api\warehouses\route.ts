import { NextRequest, NextResponse } from 'next/server';
// Import new service functions
import { createWarehouse, fetchWarehouses, searchWarehouses, CreateWarehouseDto } from '@/app/services/warehouse.service';
// Keep legacy import for backward compatibility during transition
import { handleMongoDBError } from '@/app/services/mongodb';
import { CreateWarehouseRequest } from '@/app/components/forms/WarehouseForm/types';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching warehouses with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with warehouses data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/warehouses - Fetching warehouses');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Check for Generic Search Parameter ---
    const searchQuery = url.searchParams.get('search');

    let result;

    if (searchQuery) {
      // Use search function for generic search across multiple fields
      const searchOptions = {
        query: searchQuery,
        page,
        limit,
        sort: { [sortField]: sortOrder } as Record<string, 1 | -1>,
        filter: {} // Additional filters can be added here if needed
      };

      console.log(`[API] Calling searchWarehouses service with options: ${JSON.stringify(searchOptions)}`);
      result = await searchWarehouses(searchOptions);
    } else {
      // --- Building Filter Object for Specific Field Filters ---
      const filter: any = {};

      // Name filter
      const nameFilter = url.searchParams.get('name');
      if (nameFilter) {
        filter.name = new RegExp(nameFilter, 'i'); // Case-insensitive regex search
      }

      // Location filter
      const locationFilter = url.searchParams.get('location');
      if (locationFilter) {
        filter.location = new RegExp(locationFilter, 'i');
      }

      // Manager filter
      const managerFilter = url.searchParams.get('manager');
      if (managerFilter) {
        filter.manager = new RegExp(managerFilter, 'i');
      }

      // --- Prepare Options for Service Function ---
      const options = {
        page,
        limit,
        sort: { [sortField]: sortOrder } as Record<string, 1 | -1>,
        filter,
      };

      console.log(`[API] Calling fetchWarehouses service with options: ${JSON.stringify(options)}`);
      result = await fetchWarehouses(options);
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchWarehouses completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      success: true,
      data: result?.warehouses,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/warehouses (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new warehouse
 * @param request - The incoming request with warehouse data
 * @returns JSON response with the newly created warehouse
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/warehouses - Creating new warehouse');
    const warehouseData = await request.json() as CreateWarehouseRequest;

    // Basic validation
    if (!warehouseData || typeof warehouseData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid warehouse data provided' }, { status: 400 });
    }

    // Validate required fields based on the warehouse schema
    if (!warehouseData.name) {
      return NextResponse.json({ data: null, error: 'Missing required field: name' }, { status: 400 });
    }

    if (!warehouseData.location) {
      return NextResponse.json({ data: null, error: 'Missing required field: location' }, { status: 400 });
    }

    if (warehouseData.capacity === undefined) {
      return NextResponse.json({ data: null, error: 'Missing required field: capacity' }, { status: 400 });
    }

    if (!warehouseData.manager) {
      return NextResponse.json({ data: null, error: 'Missing required field: manager' }, { status: 400 });
    }

    if (!warehouseData.contact) {
      return NextResponse.json({ data: null, error: 'Missing required field: contact' }, { status: 400 });
    }

    console.log(`[API] Calling createWarehouse service with data: ${JSON.stringify(warehouseData)}`);

    // Transform the request data to match the new service DTO
    const createWarehouseDto: CreateWarehouseDto = {
      location_id: warehouseData.location_id,
      name: warehouseData.name,
      location: warehouseData.location || '',
      capacity: warehouseData.capacity || 0,
      manager: warehouseData.manager || '',
      contact: warehouseData.contact || '',
      isBinTracked: warehouseData.isBinTracked || false
    };

    // Call the new createWarehouse service function
    const savedWarehouse = await createWarehouse(createWarehouseDto);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createWarehouse completed in ${duration}ms`);

    return NextResponse.json({ data: savedWarehouse, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/warehouses (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
