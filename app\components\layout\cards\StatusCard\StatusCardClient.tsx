"use client";

import BaseCard from '@/app/components/layout/cards/BaseCard';
import React from 'react';
import { StatusCardProps, colorClasses } from './types';

/**
 * Client component for StatusCard that displays data with progress bars
 */
const StatusCardClient: React.FC<StatusCardProps> = ({
  title,
  data,
  mainStat,
  color = 'blue',
  icon,
  onClick
}) => {
  const total = Object.values(data).reduce((sum, val) => sum + val, 0);

  // Get the color classes for the selected color
  const selectedColor = colorClasses[color] || colorClasses.blue;

  // Ensure mainStat.value is a valid number
  const displayValue = isNaN(mainStat.value) ? 0 : mainStat.value;

  return (
    <BaseCard
      title={title}
      icon={icon}
      color={color}
      onClick={onClick || (() => {})}
      onViewDetails={onClick || (() => {})}
      viewDetailsText="View Details"
    >
      <div className="mt-2">
        <div className="text-3xl font-bold mb-1 text-foreground">{displayValue}</div>
        <div className="text-sm text-muted-foreground">{mainStat.label}</div>
      </div>

      <div className="mt-6 space-y-3">
        {Object.entries(data).map(([label, value]) => {
          // Ensure value is a valid number
          const safeValue = isNaN(value) ? 0 : value;
          const percentage = total > 0 ? (safeValue / total) * 100 : 0;

          return (
            <div key={label}>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-muted-foreground">{label}</span>
                <span className="font-medium text-foreground">{safeValue}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className={`${selectedColor.bar} h-1.5 rounded-full`}
                  style={{ width: `${percentage}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
    </BaseCard>
  );
};

export default StatusCardClient; 