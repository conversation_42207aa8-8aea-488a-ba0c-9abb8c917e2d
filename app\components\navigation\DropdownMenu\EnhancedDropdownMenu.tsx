"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/app/components/navigation/DropdownMenu';
import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';
import { cn } from '@/app/lib/utils';
import React, { useCallback, useEffect, useRef, useState } from 'react';

export interface EnhancedDropdownMenuProps {
  children: React.ReactNode;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
  className?: string;
  contentClassName?: string;
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  sideOffset?: number;
  alignOffset?: number;
  enableKeyboardNavigation?: boolean;
  closeOnItemSelect?: boolean;
}

/**
 * Enhanced DropdownMenu with improved focus management and keyboard navigation
 * Fixes the critical focus management issues identified in the theme audit
 */
export const EnhancedDropdownMenu: React.FC<EnhancedDropdownMenuProps> = ({
  children,
  onOpenChange,
  open: controlledOpen,
  className,
  contentClassName,
  align = 'end',
  side = 'bottom',
  sideOffset = 4,
  alignOffset = 0,
  enableKeyboardNavigation = true,
  closeOnItemSelect = true,
  ...props
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const { themeClasses } = useEnhancedTheme();
  
  // Use controlled or internal state
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setIsOpen = controlledOpen !== undefined ? (open: boolean) => {
    onOpenChange?.(open);
  } : (open: boolean) => {
    setInternalOpen(open);
    onOpenChange?.(open);
  };
  
  // Refs for managing focus
  const contentRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLElement | null)[]>([]);
  const triggerRef = useRef<HTMLButtonElement>(null);
  
  // Reset focus when dropdown opens/closes
  useEffect(() => {
    if (isOpen) {
      setFocusedIndex(-1);
      // Focus first item after a short delay to ensure content is rendered
      setTimeout(() => {
        if (itemRefs.current[0]) {
          setFocusedIndex(0);
        }
      }, 50);
    } else {
      setFocusedIndex(-1);
    }
  }, [isOpen]);
  
  // Focus management
  useEffect(() => {
    if (enableKeyboardNavigation && isOpen && focusedIndex >= 0 && itemRefs.current[focusedIndex]) {
      itemRefs.current[focusedIndex]?.focus();
    }
  }, [focusedIndex, isOpen, enableKeyboardNavigation]);
  
  // Keyboard navigation handler
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isOpen || !enableKeyboardNavigation) return;
    
    const availableItems = itemRefs.current.filter(Boolean);
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => {
          const nextIndex = prev + 1;
          return nextIndex >= availableItems.length ? 0 : nextIndex;
        });
        break;
        
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => {
          const prevIndex = prev - 1;
          return prevIndex < 0 ? availableItems.length - 1 : prevIndex;
        });
        break;
        
      case 'Home':
        e.preventDefault();
        setFocusedIndex(0);
        break;
        
      case 'End':
        e.preventDefault();
        setFocusedIndex(availableItems.length - 1);
        break;
        
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (focusedIndex >= 0 && availableItems[focusedIndex]) {
          availableItems[focusedIndex]?.click();
          if (closeOnItemSelect) {
            setIsOpen(false);
          }
        }
        break;
        
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        // Return focus to trigger
        setTimeout(() => {
          triggerRef.current?.focus();
        }, 50);
        break;
        
      case 'Tab':
        // Allow tab to close dropdown and move to next element
        setIsOpen(false);
        break;
    }
  }, [isOpen, focusedIndex, enableKeyboardNavigation, closeOnItemSelect, setIsOpen]);
  
  // Add keyboard event listener
  useEffect(() => {
    if (isOpen && enableKeyboardNavigation) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
    return () => {}; // Return empty cleanup function for all code paths
  }, [isOpen, handleKeyDown, enableKeyboardNavigation]);
  
  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && contentRef.current && !contentRef.current.contains(event.target as Node)) {
        const trigger = triggerRef.current;
        if (trigger && !trigger.contains(event.target as Node)) {
          setIsOpen(false);
        }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {}; // Return empty cleanup function for all code paths
  }, [isOpen, setIsOpen]);
  
  // Register item refs
  const registerItemRef = useCallback((index: number) => (element: HTMLElement | null) => {
    itemRefs.current[index] = element;
  }, []);
  
  // Enhanced children with proper ref registration and focus management
  const enhancedChildren = React.Children.map(children, (child, index) => {
    if (React.isValidElement(child)) {
      // Handle DropdownMenuTrigger
      if (child.type === DropdownMenuTrigger) {
        return React.cloneElement(child as React.ReactElement<any>, {
          ref: triggerRef,
          'aria-expanded': isOpen,
          'aria-haspopup': 'menu',
          onKeyDown: (e: React.KeyboardEvent) => {
            // Handle trigger-specific keyboard events
            if (e.key === 'ArrowDown' && !isOpen) {
              e.preventDefault();
              setIsOpen(true);
            }
            // Call original onKeyDown if it exists
            const childProps = child.props as any;
            if (childProps.onKeyDown) {
              childProps.onKeyDown(e);
            }
          },
        });
      }
      
      // Handle DropdownMenuContent
      if (child.type === DropdownMenuContent) {
        const childProps = child.props as any;
        return React.cloneElement(child as React.ReactElement<any>, {
          ref: contentRef,
          className: cn(
            themeClasses.card,
            'focus:outline-none',
            contentClassName,
            childProps.className
          ),
          align,
          side,
          sideOffset,
          alignOffset,
          onEscapeKeyDown: (e: Event) => {
            e.preventDefault();
            setIsOpen(false);
            setTimeout(() => {
              triggerRef.current?.focus();
            }, 50);
          },
          children: React.Children.map(childProps.children, (menuChild, menuIndex) => {
            if (React.isValidElement(menuChild) && menuChild.type === DropdownMenuItem) {
              const menuChildProps = menuChild.props as any;
              return React.cloneElement(menuChild as React.ReactElement<any>, {
                ref: registerItemRef(menuIndex),
                tabIndex: focusedIndex === menuIndex ? 0 : -1,
                'data-focused': focusedIndex === menuIndex,
                className: cn(
                  menuChildProps.className,
                  focusedIndex === menuIndex && 'bg-accent text-accent-foreground',
                  'focus:bg-accent focus:text-accent-foreground focus:outline-none'
                ),
                onFocus: () => setFocusedIndex(menuIndex),
                onMouseEnter: () => setFocusedIndex(menuIndex),
                onSelect: (e: Event) => {
                  // Call original onSelect if it exists
                  if (menuChildProps.onSelect) {
                    menuChildProps.onSelect(e);
                  }
                  
                  // Close dropdown if configured to do so
                  if (closeOnItemSelect && !e.defaultPrevented) {
                    setIsOpen(false);
                  }
                },
              });
            }
            return menuChild;
          }),
        });
      }
    }
    return child;
  });
  
  return (
    <DropdownMenu
      open={isOpen}
      onOpenChange={setIsOpen}
      {...props}
    >
      {enhancedChildren}
    </DropdownMenu>
  );
};

/**
 * Enhanced DropdownMenuItem with better focus management
 */
export interface EnhancedDropdownMenuItemProps extends React.ComponentProps<typeof DropdownMenuItem> {
  onSelect?: (e: Event) => void;
  disabled?: boolean;
}

export const EnhancedDropdownMenuItem = React.forwardRef<
  HTMLDivElement,
  EnhancedDropdownMenuItemProps
>(({ className, onSelect, disabled, children, ...props }, ref) => {
  const { themeClasses } = useEnhancedTheme();
  
  const dropdownProps = {
    ref,
    className: cn(
      themeClasses.interactive,
      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors',
      'focus:bg-accent focus:text-accent-foreground',
      'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      disabled && 'pointer-events-none opacity-50',
      className
    ),
    ...props,
    ...(onSelect && !disabled ? { onSelect } : {})
  };

  return (
    <DropdownMenuItem {...dropdownProps}
    >
      {children}
    </DropdownMenuItem>
  );
});

EnhancedDropdownMenuItem.displayName = 'EnhancedDropdownMenuItem';
