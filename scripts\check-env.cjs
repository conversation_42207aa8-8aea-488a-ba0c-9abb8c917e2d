#!/usr/bin/env node

/**
 * Environment Check Script for Trend IMS
 * Validates environment configuration and port settings
 */

const fs = require('fs');
const path = require('path');

function log(message) {
  console.log(`[EnvCheck] ${message}`);
}

function error(message) {
  console.error(`[EnvCheck Error] ${message}`);
}

function success(message) {
  console.log(`[EnvCheck] ✅ ${message}`);
}

function warning(message) {
  console.warn(`[EnvCheck] ⚠️  ${message}`);
}

function checkEnvironmentFiles() {
  log('Checking environment files...');
  
  const envFiles = ['.env', '.env.local', '.env.example'];
  const existingFiles = [];
  
  envFiles.forEach(file => {
    if (fs.existsSync(file)) {
      existingFiles.push(file);
      success(`Found ${file}`);
    } else {
      warning(`Missing ${file}`);
    }
  });
  
  if (existingFiles.length === 0) {
    error('No environment files found!');
    return false;
  }
  
  return true;
}

function loadEnvironmentVariables() {
  log('Loading environment variables...');
  
  // Load dotenv if available
  try {
    require('dotenv').config();
    success('Environment variables loaded');
  } catch (err) {
    warning('dotenv not available, using process.env only');
  }
}

function validatePortConfiguration() {
  log('Validating port configuration...');
  
  const port = process.env.PORT;
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  if (port) {
    const portNum = parseInt(port, 10);
    
    if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
      error(`Invalid PORT value: ${port}. Must be a number between 1 and 65535.`);
      return false;
    }
    
    success(`PORT is set to ${port}`);
    
    // Check for common restricted ports
    const restrictedPorts = [22, 25, 53, 80, 110, 143, 443, 993, 995];
    if (restrictedPorts.includes(portNum)) {
      warning(`PORT ${port} is a restricted port. Consider using a different port.`);
    }
  } else {
    const defaultPort = nodeEnv === 'production' ? 3000 : 3000;
    warning(`PORT not set. Will use default: ${defaultPort}`);
  }
  
  return true;
}

function validateRequiredVariables() {
  log('Validating required environment variables...');
  
  const requiredVars = [
    'NODE_ENV',
    'MONGODB_URI',
    'NEXTAUTH_SECRET'
  ];
  
  const optionalVars = [
    'PORT',
    'HOST',
    'NEXT_PUBLIC_APP_URL',
    'NEXTAUTH_URL'
  ];
  
  let allValid = true;
  
  // Check required variables
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      success(`${varName} is set`);
    } else {
      error(`Missing required variable: ${varName}`);
      allValid = false;
    }
  });
  
  // Check optional variables
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      success(`${varName} is set: ${process.env[varName]}`);
    } else {
      warning(`Optional variable not set: ${varName}`);
    }
  });
  
  return allValid;
}

function validateURLConsistency() {
  log('Validating URL consistency...');
  
  const port = process.env.PORT || (process.env.NODE_ENV === 'production' ? '3000' : '3000');
  const host = process.env.HOST || 'localhost';
  const expectedBaseUrl = `http://${host}:${port}`;
  
  const urls = {
    'NEXT_PUBLIC_APP_URL': process.env.NEXT_PUBLIC_APP_URL,
    'NEXTAUTH_URL': process.env.NEXTAUTH_URL
  };
  
  let consistent = true;
  
  Object.entries(urls).forEach(([varName, url]) => {
    if (url) {
      if (url === expectedBaseUrl) {
        success(`${varName} matches expected URL: ${url}`);
      } else {
        warning(`${varName} (${url}) doesn't match expected URL (${expectedBaseUrl})`);
        consistent = false;
      }
    }
  });
  
  return consistent;
}

function displayConfiguration() {
  log('Current configuration:');
  
  const config = {
    'NODE_ENV': process.env.NODE_ENV || 'development',
    'PORT': process.env.PORT || 'default',
    'HOST': process.env.HOST || 'localhost',
    'MONGODB_URI': process.env.MONGODB_URI ? '***configured***' : 'not set',
    'NEXT_PUBLIC_APP_URL': process.env.NEXT_PUBLIC_APP_URL || 'not set',
    'NEXTAUTH_URL': process.env.NEXTAUTH_URL || 'not set',
    'NEXTAUTH_SECRET': process.env.NEXTAUTH_SECRET ? '***configured***' : 'not set'
  };
  
  console.log('\n📋 Configuration Summary:');
  Object.entries(config).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  console.log('');
}

function main() {
  log('Starting environment validation...');
  
  let allChecksPass = true;
  
  // Run all checks
  allChecksPass &= checkEnvironmentFiles();
  loadEnvironmentVariables();
  allChecksPass &= validatePortConfiguration();
  allChecksPass &= validateRequiredVariables();
  allChecksPass &= validateURLConsistency();
  
  // Display current configuration
  displayConfiguration();
  
  // Final result
  if (allChecksPass) {
    success('All environment checks passed!');
    log('Your environment is properly configured.');
  } else {
    error('Some environment checks failed!');
    log('Please review the errors above and update your environment configuration.');
    process.exit(1);
  }
}

// Run the main function
main();
