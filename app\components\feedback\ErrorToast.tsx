/**
 * ErrorToast Component
 * Standardized toast notifications for errors using Sonner
 * Provides consistent error toast patterns across the application
 */

"use client";

import React from 'react';
import { toast } from 'sonner';
import { AlertCircle, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { ErrorToastProps, ErrorSeverity } from '@/app/types/error.types';
import { extractErrorMessage, isTestError, getErrorSeverity } from '@/app/utils/error.utils';

/**
 * Show error toast notification
 */
export function showErrorToast({
  error,
  duration = 5000,
  action,
}: ErrorToastProps) {
  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);
  const severity = getErrorSeverity(error);

  // Use appropriate toast method based on severity
  const toastMethod = isTest || severity === ErrorSeverity.WARNING 
    ? toast.warning 
    : toast.error;

  return toastMethod(errorMessage, {
    duration,
    description: isTest ? 'This is a test error for verification purposes.' : undefined,
    action: action ? {
      label: action.label,
      onClick: action.action,
    } : undefined,
    icon: isTest || severity === ErrorSeverity.WARNING 
      ? <AlertTriangle className="h-4 w-4" />
      : <AlertCircle className="h-4 w-4" />,
  });
}

/**
 * Show warning toast notification
 */
export function showWarningToast(
  message: string,
  options: {
    duration?: number;
    description?: string;
    action?: { label: string; action: () => void };
  } = {}
) {
  return toast.warning(message, {
    duration: options.duration || 4000,
    description: options.description,
    action: options.action ? {
      label: options.action.label,
      onClick: options.action.action,
    } : undefined,
    icon: <AlertTriangle className="h-4 w-4" />,
  });
}

/**
 * Show info toast notification
 */
export function showInfoToast(
  message: string,
  options: {
    duration?: number;
    description?: string;
    action?: { label: string; action: () => void };
  } = {}
) {
  return toast.info(message, {
    duration: options.duration || 3000,
    description: options.description,
    action: options.action ? {
      label: options.action.label,
      onClick: options.action.action,
    } : undefined,
    icon: <Info className="h-4 w-4" />,
  });
}

/**
 * Show success toast notification
 */
export function showSuccessToast(
  message: string,
  options: {
    duration?: number;
    description?: string;
    action?: { label: string; action: () => void };
  } = {}
) {
  return toast.success(message, {
    duration: options.duration || 3000,
    description: options.description,
    action: options.action ? {
      label: options.action.label,
      onClick: options.action.action,
    } : undefined,
    icon: <CheckCircle className="h-4 w-4" />,
  });
}

/**
 * Show loading toast notification
 */
export function showLoadingToast(
  message: string,
  options: {
    description?: string;
  } = {}
) {
  return toast.loading(message, {
    description: options.description,
  });
}

/**
 * Dismiss a specific toast
 */
export function dismissToast(toastId: string | number) {
  toast.dismiss(toastId);
}

/**
 * Dismiss all toasts
 */
export function dismissAllToasts() {
  toast.dismiss();
}

/**
 * Promise-based toast for async operations
 */
export function showPromiseToast<T>(
  promise: Promise<T>,
  options: {
    loading: string;
    success: string | ((data: T) => string);
    error: string | ((error: any) => string);
    description?: string;
    duration?: number;
  }
) {
  return toast.promise(promise, {
    loading: options.loading,
    success: options.success,
    error: options.error,
    description: options.description,
    duration: options.duration || 4000,
  });
}

/**
 * Custom toast with retry action for failed operations
 */
export function showRetryToast(
  message: string,
  onRetry: () => void,
  options: {
    duration?: number;
    description?: string;
    retryLabel?: string;
  } = {}
) {
  return toast.error(message, {
    duration: options.duration || 6000,
    description: options.description,
    action: {
      label: options.retryLabel || 'Retry',
      onClick: onRetry,
    },
    icon: <AlertCircle className="h-4 w-4" />,
  });
}

/**
 * Network error toast with specific messaging
 */
export function showNetworkErrorToast(
  onRetry?: () => void,
  options: {
    duration?: number;
    customMessage?: string;
  } = {}
) {
  const message = options.customMessage || 'Network error occurred. Please check your connection.';
  
  return toast.error(message, {
    duration: options.duration || 6000,
    description: 'Your internet connection may be unstable.',
    action: onRetry ? {
      label: 'Retry',
      onClick: onRetry,
    } : undefined,
    icon: <AlertCircle className="h-4 w-4" />,
  });
}

/**
 * Validation error toast for form submissions
 */
export function showValidationErrorToast(
  errors: string[] | string,
  options: {
    duration?: number;
    title?: string;
  } = {}
) {
  const errorList = Array.isArray(errors) ? errors : [errors];
  const title = options.title || 'Validation Error';
  
  if (errorList.length === 1) {
    return toast.error(title, {
      duration: options.duration || 4000,
      description: errorList[0],
      icon: <AlertTriangle className="h-4 w-4" />,
    });
  }

  return toast.error(title, {
    duration: options.duration || 5000,
    description: `${errorList.length} validation errors found`,
    icon: <AlertTriangle className="h-4 w-4" />,
  });
}

/**
 * Permission error toast
 */
export function showPermissionErrorToast(
  action?: string,
  options: {
    duration?: number;
    onLogin?: () => void;
  } = {}
) {
  const message = action 
    ? `You don't have permission to ${action}` 
    : 'You don\'t have permission to perform this action';

  return toast.error(message, {
    duration: options.duration || 5000,
    description: 'Please contact your administrator or log in with appropriate credentials.',
    action: options.onLogin ? {
      label: 'Login',
      onClick: options.onLogin,
    } : undefined,
    icon: <AlertCircle className="h-4 w-4" />,
  });
}

// Export all toast functions
export const ErrorToast = {
  error: showErrorToast,
  warning: showWarningToast,
  info: showInfoToast,
  success: showSuccessToast,
  loading: showLoadingToast,
  promise: showPromiseToast,
  retry: showRetryToast,
  network: showNetworkErrorToast,
  validation: showValidationErrorToast,
  permission: showPermissionErrorToast,
  dismiss: dismissToast,
  dismissAll: dismissAllToasts,
};

export default ErrorToast;
