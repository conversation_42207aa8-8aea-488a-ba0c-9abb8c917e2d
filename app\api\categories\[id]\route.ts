import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new category service
import { 
  getCategoryById, 
  updateCategoryById, 
  deleteCategoryById,
  handleMongoDBError 
} from '@/app/services/category.service';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the _id field
}

/**
 * GET handler for fetching a specific category by its ID
 * @param _request - The incoming request (unused)
 * @param context - Route context including the category ID (Promise in Next.js 15)
 * @returns JSON response with category data or error
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const categoryId = resolvedParams.id; // The MongoDB ObjectId
  try {
    console.log(`[API] GET /api/categories/${categoryId} - Fetching category`);

    // Call the service function to get the category
    const category = await getCategoryById(categoryId);

    const duration = Date.now() - startTime;

    if (!category) {
      console.log(`[API] Category ${categoryId} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Category with ID ${categoryId} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched category ${categoryId} successfully (${duration}ms)`);
    return NextResponse.json({ data: category, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching category ${categoryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific category by its ID
 * @param request - The incoming request with updated category data
 * @param context - Route context including the category ID (Promise in Next.js 15)
 * @returns JSON response with updated category data or error
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const categoryId = resolvedParams.id;
  try {
    console.log(`[API] PUT /api/categories/${categoryId} - Updating category`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Call the service function to update the category
    const updatedCategory = await updateCategoryById(categoryId, updateData);

    const duration = Date.now() - startTime;
    
    if (!updatedCategory) {
      console.log(`[API] Category ${categoryId} not found for update (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Category with ID ${categoryId} not found`, meta: { duration } },
        { status: 404 }
      );
    }
    
    console.log(`[API] Updated category ${categoryId} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedCategory, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating category ${categoryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific category by its ID
 * @param _request - The incoming request (unused)
 * @param context - Route context including the category ID (Promise in Next.js 15)
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const resolvedParams = await params;
  const categoryId = resolvedParams.id;
  try {
    console.log(`[API] DELETE /api/categories/${categoryId} - Deleting category`);

    // Call the service function to delete the category
    const result = await deleteCategoryById(categoryId);

    const duration = Date.now() - startTime;
    console.log(`[API] Deleted category ${categoryId} successfully (${duration}ms)`);
    return NextResponse.json({ 
      data: result, 
      error: null, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting category ${categoryId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
