import mongoose, { Schema, Document, Types, Model } from 'mongoose';

// REMOVED: IStockLevels interface - now in inventories collection (V4 Schema)
// REMOVED: StockLevelsSchema - inventory data is in separate collection



// Interface for the plain Part data object
export interface IPart {
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  // V4 Schema: Core part fields only - inventory data is in separate collection
  supplierId?: Types.ObjectId | null;
  unitOfMeasure: string;
  standardCost: number; // Changed from costPrice to match target schema
  abcClassification?: string | null; // NEW FIELD: ABC classification (A, B, C)
  categoryId?: Types.ObjectId | null;
  createdAt?: Date;
  updatedAt?: Date;

  // REMOVED: All inventory-related virtual fields - use inventories collection instead
  // REMOVED: totalStock, stockLevels, currentStock, primaryWarehouseId, inventory object
}

// Interface for the Part Mongoose Document
export interface IPartDocument extends IPart, Document {
  // Inherits all fields from IPart (partNumber, name, description, etc.)
  // Inherits Mongoose document properties and methods from Document.
  // Schema options like timestamps: true will add createdAt and updatedAt.
  // _id is also automatically added by Mongoose schemas.
}

// Main Part Schema
const PartSchema: Schema<IPartDocument> = new Schema({
  partNumber: { type: String, required: true, unique: true, index: true, trim: true },
  name: { type: String, required: true, trim: true },
  businessName: { type: String, trim: true, default: null, required: false }, // NEW FIELD: Human-readable business name
  description: { type: String, trim: true, default: null, required: false },
  technicalSpecs: { type: String, trim: true, default: null, required: false },
  isManufactured: { type: Boolean, default: false, required: true },
  reorderLevel: { type: Number, default: null, required: false }, // Int32 | Null
  status: {
    type: String,
    enum: ['active', 'inactive', 'obsolete'], // Canonical schema values
    default: 'active',
    required: true,
  },

  // NEW: Part Master Data Planning Parameters
  planningMethod: { type: String, trim: true, default: null, required: false },        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel: { type: Number, default: null, required: false },                  // Safety stock level for this part
  maximumStockLevel: { type: Number, default: null, required: false },                 // Maximum stock level for this part
  leadTimeDays: { type: Number, default: null, required: false },                      // Lead time in days
  averageDailyUsage: { type: Number, default: null, required: false },                 // Average daily usage
  // REMOVED: inventory field - now in separate inventories collection (V4 Schema)
  // DEPRECATED: inventory: { type: InventorySchema, required: false, default: undefined },
  supplierId: { type: Schema.Types.ObjectId, ref: 'Supplier', default: null, required: false },
  unitOfMeasure: { type: String, required: true },
  standardCost: { type: Number, required: true }, // Double - changed from costPrice to match target schema
  abcClassification: { type: String, trim: true, default: null, required: false }, // NEW FIELD: ABC classification (A, B, C)
  categoryId: { type: Schema.Types.ObjectId, ref: 'Category', default: null, required: false },
}, { timestamps: true });

// PERFORMANCE OPTIMIZATION: Add indexes for aggregation pipeline $lookup operations
// These indexes eliminate N+1 queries by optimizing the $lookup stages
// REMOVED: Legacy inventory index - no longer needed with V4 schema
// DEPRECATED: PartSchema.index({ 'inventory.warehouseId': 1 }, { ... });

PartSchema.index({ 'supplierId': 1 }, {
  name: 'supplierId_1',
  background: true,
  sparse: true, // Since supplierId can be null
  comment: 'Index for supplier $lookup in aggregation pipeline'
});

PartSchema.index({ 'categoryId': 1 }, {
  name: 'categoryId_1',
  background: true,
  sparse: true, // Since categoryId can be null
  comment: 'Index for category $lookup in aggregation pipeline'
});

// Compound index for common query patterns (status filtering with sorting)
PartSchema.index({ 'status': 1, 'updatedAt': -1 }, {
  name: 'status_updatedAt_compound',
  background: true,
  comment: 'Compound index for status filtering with updatedAt sorting'
});

// Text index for search functionality
PartSchema.index({
  'name': 'text',
  'businessName': 'text', // NEW FIELD: Include businessName in search
  'description': 'text',
  'partNumber': 'text',
  'technicalSpecs': 'text'
}, {
  name: 'parts_text_search',
  background: true,
  comment: 'Text index for parts search functionality including businessName'
});

// REMOVED: All virtual properties for inventory data
// V4 Schema: Inventory data is fetched separately from inventories collection
// No backward compatibility virtual properties needed

// REMOVED: Virtual properties configuration - no virtuals needed in V4 schema

// Create the model with proper TypeScript typing
const Part: Model<IPartDocument> = mongoose.models?.Part as mongoose.Model<IPartDocument> || mongoose.model<IPartDocument>('Part', PartSchema);

export { PartSchema, Part }; // Export both schema and model as named exports
export default Part;