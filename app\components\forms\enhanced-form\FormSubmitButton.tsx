"use client";

import { Button } from "@/app/components/forms/Button";
import { cn } from "@/app/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { CheckCircle, Loader2 } from "lucide-react";
import React from "react";
import { useFormContext } from "react-hook-form";

export interface FormSubmitButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  isSuccess?: boolean;
  loadingText?: string;
  successText?: string;
  showSuccessAnimation?: boolean;
  disableOnInvalid?: boolean;
  disableOnSubmitting?: boolean;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
}

export const FormSubmitButton = React.forwardRef<HTMLButtonElement, FormSubmitButtonProps>(
  (
    {
      className,
      children,
      isLoading = false,
      isSuccess = false,
      loadingText = "Submitting...",
      successText = "Submitted!",
      showSuccessAnimation = true,
      disableOnInvalid = true,
      disableOnSubmitting = true,
      variant = "default",
      ...props
    },
    ref
  ) => {
    const form = useFormContext();
    const isFormValid = form ? form.formState.isValid : true;
    const isSubmitting = form ? form.formState.isSubmitting : false;
    
    // Determine if button should be disabled
    const isDisabled = 
      (disableOnInvalid && !isFormValid) || 
      (disableOnSubmitting && (isSubmitting || isLoading)) ||
      props.disabled;

    const buttonProps: any = {
      type: "submit",
      variant,
      className: cn(
        "relative",
        isSuccess && "bg-success hover:bg-success/90",
        className
      ),
      ...props
    };

    if (isDisabled !== undefined) {
      buttonProps.disabled = isDisabled;
    }

    return (
      <Button
        ref={ref}
        {...buttonProps}
      >
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center"
            >
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {loadingText}
            </motion.div>
          ) : isSuccess ? (
            <motion.div
              key="success"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              {successText}
            </motion.div>
          ) : (
            <motion.div
              key="default"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Success animation overlay */}
        {isSuccess && showSuccessAnimation && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: [1, 1.5, 0],
              opacity: [0.5, 0.8, 0]
            }}
            transition={{ 
              duration: 1,
              times: [0, 0.5, 1]
            }}
            className="absolute inset-0 bg-success rounded-md"
            style={{ zIndex: -1 }}
          />
        )}
      </Button>
    );
  }
);

FormSubmitButton.displayName = "FormSubmitButton";
