'use client';

import React from 'react';
import { Card, CardContent } from '@/app/components/layout/cards/card';
import { Badge } from '@/app/components/data-display/badge';
import {
  MapPin,
  Package,
  Activity,
  Edit,
  Edit2,
  Trash2
} from 'lucide-react';
import { Button } from '@/app/components/forms/Button';

export interface LocationFormData {
  _id?: string;
  name: string;
  description?: string | null | undefined;
  locationType: string;
  capacity?: {
    maxWeightKg?: number | undefined;
    volumeM3?: number | undefined;
  } | null | undefined;
  isActive?: boolean;
  warehouseId?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface LocationCardProps {
  location: LocationFormData;
  isReadOnly?: boolean;
  onEdit?: (location: LocationFormData) => void;
  onDelete?: (locationId: string) => void;
  children?: React.ReactNode;
}

export function LocationCard({ 
  location, 
  isReadOnly = false, 
  onEdit, 
  onDelete,
  children 
}: LocationCardProps) {
  const isActive = location.isActive !== false; // Default to true if undefined
  
  const handleEdit = () => {
    if (onEdit && !isReadOnly) {
      onEdit(location);
    }
  };

  const handleDelete = () => {
    if (onDelete && !isReadOnly && location._id) {
      onDelete(location._id);
    }
  };

  return (
    <Card className={`relative transition-all duration-200 hover:shadow-md ${
      isActive ? 'border-border' : 'border-muted bg-muted/50'
    }`}>
      <CardContent className="p-4">
        {/* Action icons in top-right corner */}
        {!isReadOnly && (onEdit || onDelete) && (
          <div className="absolute top-2 right-2 flex items-center gap-1">
            {onEdit && (
              <button
                onClick={handleEdit}
                className="p-1 rounded-md hover:bg-muted/80 transition-colors"
                title="Edit location"
              >
                <Edit2 className="h-3 w-3 text-muted-foreground hover:text-foreground" />
              </button>
            )}
            {onDelete && location._id && (
              <button
                onClick={handleDelete}
                className="p-1 rounded-md hover:bg-muted/80 transition-colors"
                title="Delete location"
              >
                <Trash2 className="h-3 w-3 text-muted-foreground hover:text-destructive" />
              </button>
            )}
          </div>
        )}

        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <h4 className="font-medium text-sm">{location.name}</h4>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              variant={isActive ? "default" : "secondary"}
              className="text-xs"
            >
              <Activity className="h-3 w-3 mr-1" />
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>

        {location.description && (
          <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
            {location.description}
          </p>
        )}

        <div className="space-y-2">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Package className="h-3 w-3" />
            <span className="font-medium">Type:</span>
            <span>{location.locationType}</span>
          </div>
          
          {location.capacity && (location.capacity.maxWeightKg || location.capacity.volumeM3) && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Package className="h-3 w-3" />
              <span className="font-medium">Capacity:</span>
              <span>
                {location.capacity.maxWeightKg && `${location.capacity.maxWeightKg}kg`}
                {location.capacity.maxWeightKg && location.capacity.volumeM3 && ' / '}
                {location.capacity.volumeM3 && `${location.capacity.volumeM3}m³`}
              </span>
            </div>
          )}
        </div>

        {/* Action buttons or custom children */}
        {!isReadOnly && (onEdit || onDelete || children) && (
          <div className="flex items-center justify-end gap-2 mt-4 pt-3 border-t border-border">
            {children}
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-7 px-2"
              >
                <Edit className="h-3 w-3 mr-1" />
                Edit
              </Button>
            )}
            {onDelete && location._id && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                className="h-7 px-2 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
