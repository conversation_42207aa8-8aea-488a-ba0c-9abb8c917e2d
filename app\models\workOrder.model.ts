import mongoose, { Schema, Document, Types } from 'mongoose';

/**
 * Interface for Bill of Materials Snapshot items
 * Represents the parts required for a work order at the time of creation
 */
interface IBOMSnapshotItem {
  partId: Types.ObjectId; // Reference to parts._id
  quantity: number; // Quantity required
  unitOfMeasure: string; // Unit of measure (e.g., "pcs", "kg", "m")
}

/**
 * Interface for WorkOrder document in MongoDB (canonical schema)
 */
export interface IWorkOrder extends Document {
  _id: Types.ObjectId;
  woNumber: string; // Unique business identifier
  assemblyId: Types.ObjectId | null; // Reference to assemblies._id
  partIdToManufacture: Types.ObjectId | null; // Reference to parts._id
  productId: Types.ObjectId | null; // Reference to products._id
  quantity: number;
  status: 'planned' | 'released' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Date;
  startDate: Date | null;
  assignedTo: Types.ObjectId | null; // Reference to users._id
  notes: string | null;
  completedAt: Date | null;
  sourceDemand: string | null; // e.g., "StockReplenishment", "CustomerOrder"
  billOfMaterialsSnapshot?: IBOMSnapshotItem[]; // Snapshot of BOM at time of work order creation
  createdAt: Date;
  updatedAt: Date;
}

const BOMSnapshotItemSchema: Schema = new Schema<IBOMSnapshotItem>({
  partId: { type: Schema.Types.ObjectId, ref: 'Part', required: true },
  quantity: { type: Number, required: true, min: 0 },
  unitOfMeasure: { type: String, required: true, trim: true }
}, { _id: false });

const WorkOrderSchema: Schema = new Schema({
  woNumber: {
    type: String,
    required: [true, 'Work order number is required'],
    unique: true,
    trim: true,
    index: true
  },
  assemblyId: {
    type: Schema.Types.ObjectId,
    ref: 'Assembly',
    default: null
  },
  partIdToManufacture: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    default: null
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    default: null
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1'],
    validate: {
      validator: Number.isInteger,
      message: 'Quantity must be a whole number'
    }
  },
  status: {
    type: String,
    required: [true, 'Status is required'],
    enum: {
      values: ['planned', 'released', 'in_progress', 'completed', 'on_hold', 'cancelled', 'closed'],
      message: 'Status must be one of the predefined values'
    },
    default: 'planned',
    index: true
  },
  priority: {
    type: String,
    required: [true, 'Priority is required'],
    enum: {
      values: ['low', 'medium', 'high', 'urgent'],
      message: 'Priority must be one of: low, medium, high, urgent'
    },
    default: 'medium',
    index: true
  },
  dueDate: {
    type: Date,
    required: [true, 'Due date is required'],
    index: true
  },
  startDate: {
    type: Date,
    default: null
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
    index: true
  },
  notes: {
    type: String,
    trim: true,
    default: null
  },
  completedAt: {
    type: Date,
    default: null
  },
  sourceDemand: {
    type: String,
    trim: true,
    default: null
  },
  billOfMaterialsSnapshot: {
    type: [BOMSnapshotItemSchema],
    default: undefined
  }
}, {
  timestamps: true
});

// Add validation to ensure at least one of assemblyId, partIdToManufacture, or productId is provided
WorkOrderSchema.pre('validate', function(this: IWorkOrder & Document, next) {
  const hasAssembly = !!this.assemblyId;
  const hasPart = !!this.partIdToManufacture;
  const hasProduct = !!this.productId;

  if (!hasAssembly && !hasPart && !hasProduct) {
    this.invalidate('assemblyId', 'At least one of assemblyId, partIdToManufacture, or productId must be provided');
  }

  next();
});

// Create the model with proper TypeScript typing
const WorkOrder = mongoose.models?.WorkOrder as mongoose.Model<IWorkOrder> || mongoose.model<IWorkOrder>('WorkOrder', WorkOrderSchema);

export { WorkOrder };
export default WorkOrder;