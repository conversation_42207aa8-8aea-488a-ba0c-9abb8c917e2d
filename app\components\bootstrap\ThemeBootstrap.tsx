"use client";

/**
 * Theme Bootstrap Component
 * Ensures theme is initialized immediately on page load for cross-page integration
 */
import { useEffect } from 'react';
import { initializeTheme, applyThemeToDocument } from '@/app/utils/theme.utils';

export const ThemeBootstrap: React.FC = () => {
  useEffect(() => {
    // Initialize theme immediately on mount
    const theme = initializeTheme();
    applyThemeToDocument(theme);
    
    // Add a data attribute to indicate theme is ready
    document.documentElement.setAttribute('data-theme-ready', 'true');
  }, []);

  return null; // This component doesn't render anything
};

export default ThemeBootstrap;
