/**
 * Simple database connection test script
 */

import mongoose from 'mongoose';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function testConnection() {
  console.log('🔧 Testing database connection...');
  
  const uri = process.env.MONGODB_URI;
  const dbName = process.env.MONGODB_DB_NAME || 'Trend_IMS';
  
  console.log('Environment variables:');
  console.log('- MONGODB_URI:', uri ? 'SET' : 'NOT SET');
  console.log('- MONGODB_DB_NAME:', dbName);
  
  if (!uri) {
    console.error('❌ MONGODB_URI is not set');
    process.exit(1);
  }
  
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(uri, {
      dbName: dbName,
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ Connected to MongoDB successfully');
    
    // Test basic operations
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`📊 Found ${collections.length} collections:`);
    collections.forEach(col => console.log(`  - ${col.name}`));
    
    // Check parts collection
    const partsCount = await mongoose.connection.db.collection('parts').countDocuments();
    console.log(`📦 Parts collection: ${partsCount} documents`);
    
    // Check transactions collection
    const transactionsCount = await mongoose.connection.db.collection('transactions').countDocuments();
    console.log(`📋 Transactions collection: ${transactionsCount} documents`);
    
    await mongoose.disconnect();
    console.log('✅ Database connection test completed successfully');
    
  } catch (error: any) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
}

testConnection().catch(console.error);
