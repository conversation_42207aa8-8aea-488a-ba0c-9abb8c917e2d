import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/**
 * This file contains an intentional error for testing Sentry error tracking.
 * The error is expected and should be ignored in Sentry's dashboard or marked
 * with a low priority. Do not attempt to "fix" this error as it's by design.
 *
 * Purpose: To verify Sentry is properly capturing API route errors.
 */
class SentryExampleAPIError extends Error {
  constructor(message: string | undefined) {
    super(message);
    this.name = "SentryExampleAPIError";
  }
}

// A faulty API route to test Sentry's error monitoring
async function handleGET(request: NextRequest) {
  // Intentionally throw an error to test Sentry's error tracking
  throw new SentryExampleAPIError("This error is raised on the backend called by the example page.");

  // This line is never reached
  return NextResponse.json({ data: "Testing Sentry Error..." });
}

// Apply error handling middleware to ensure JSON responses even for intentional errors
export const GET = withErrorHandling(handleGET, '/api/sentry-example-api');
