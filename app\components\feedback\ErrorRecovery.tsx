/**
 * ErrorRecovery Component
 * Standardized error recovery mechanisms with retry and alternative actions
 * Provides consistent retry UI patterns across the application
 */

"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, ArrowLeft, Home, AlertCircle } from 'lucide-react';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { ErrorRecoveryProps } from '@/app/types/error.types';

/**
 * ErrorRecovery component for standardized error recovery actions
 */
export function ErrorRecovery({
  onRetry,
  onAlternative,
  retryLabel = 'Try Again',
  alternativeLabel = 'Go Back',
  loading = false,
  disabled = false,
}: ErrorRecoveryProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
      {onRetry && (
        <Button
          onClick={onRetry}
          disabled={disabled || loading}
          className="gap-2 min-w-[120px]"
          variant="default"
        >
          <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
          {loading ? 'Retrying...' : retryLabel}
        </Button>
      )}

      {onAlternative && (
        <Button
          onClick={onAlternative}
          disabled={disabled}
          variant="outline"
          className="gap-2 min-w-[120px]"
        >
          <ArrowLeft className="h-4 w-4" />
          {alternativeLabel}
        </Button>
      )}
    </div>
  );
}

/**
 * Enhanced error recovery with more options
 */
export function ErrorRecoveryEnhanced({
  onRetry,
  onAlternative,
  onHome,
  retryLabel = 'Try Again',
  alternativeLabel = 'Go Back',
  homeLabel = 'Go Home',
  loading = false,
  disabled = false,
  error,
  showErrorDetails = false,
}: ErrorRecoveryProps & {
  onHome?: () => void;
  homeLabel?: string;
  error?: string;
  showErrorDetails?: boolean;
}) {
  const [showDetails, setShowDetails] = React.useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center space-y-4"
    >
      {/* Error details toggle */}
      {error && showErrorDetails && (
        <div className="w-full max-w-md">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            <AlertCircle className="h-3 w-3 mr-1" />
            {showDetails ? 'Hide Details' : 'Show Details'}
          </Button>
          
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-2 p-3 bg-muted rounded-md border"
            >
              <pre className="text-xs text-muted-foreground whitespace-pre-wrap break-words">
                {error}
              </pre>
            </motion.div>
          )}
        </div>
      )}

      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
        {onRetry && (
          <Button
            onClick={onRetry}
            disabled={disabled || loading}
            className="gap-2 min-w-[120px]"
            variant="default"
          >
            <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
            {loading ? 'Retrying...' : retryLabel}
          </Button>
        )}

        {onAlternative && (
          <Button
            onClick={onAlternative}
            disabled={disabled}
            variant="outline"
            className="gap-2 min-w-[120px]"
          >
            <ArrowLeft className="h-4 w-4" />
            {alternativeLabel}
          </Button>
        )}

        {onHome && (
          <Button
            onClick={onHome}
            disabled={disabled}
            variant="ghost"
            className="gap-2 min-w-[120px]"
          >
            <Home className="h-4 w-4" />
            {homeLabel}
          </Button>
        )}
      </div>
    </motion.div>
  );
}

/**
 * Compact error recovery for smaller spaces
 */
export function ErrorRecoveryCompact({
  onRetry,
  onAlternative,
  retryLabel = 'Retry',
  alternativeLabel = 'Back',
  loading = false,
  disabled = false,
}: ErrorRecoveryProps) {
  return (
    <div className="flex gap-2 justify-center items-center">
      {onRetry && (
        <Button
          onClick={onRetry}
          disabled={disabled || loading}
          size="sm"
          className="gap-1"
          variant="default"
        >
          <RefreshCw className={cn('h-3 w-3', loading && 'animate-spin')} />
          {retryLabel}
        </Button>
      )}

      {onAlternative && (
        <Button
          onClick={onAlternative}
          disabled={disabled}
          size="sm"
          variant="outline"
          className="gap-1"
        >
          <ArrowLeft className="h-3 w-3" />
          {alternativeLabel}
        </Button>
      )}
    </div>
  );
}

/**
 * Inline error recovery for form fields and small components
 */
export function ErrorRecoveryInline({
  onRetry,
  retryLabel = 'Retry',
  loading = false,
  disabled = false,
}: Pick<ErrorRecoveryProps, 'onRetry' | 'retryLabel' | 'loading' | 'disabled'>) {
  if (!onRetry) {
    return null;
  }

  return (
    <Button
      onClick={onRetry}
      disabled={disabled || loading}
      size="sm"
      variant="ghost"
      className="h-6 px-2 text-xs gap-1 text-muted-foreground hover:text-foreground"
    >
      <RefreshCw className={cn('h-2 w-2', loading && 'animate-spin')} />
      {retryLabel}
    </Button>
  );
}

export default ErrorRecovery;
