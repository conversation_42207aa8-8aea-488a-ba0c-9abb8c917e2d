import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { addUser, fetchUsers, handleMongoDBError } from '@/app/services/mongodb';
import { CreateUserRequestBody } from '@/app/types/form.types';
import bcrypt from 'bcryptjs';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching users with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with users data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/users - Fetching users');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'username'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object ---
    const filter: any = {};

    // Username filter
    const usernameFilter = url.searchParams.get('username');
    if (usernameFilter) {
      filter.username = new RegExp(usernameFilter, 'i'); // Case-insensitive regex search
    }

    // Role filter
    const roleFilter = url.searchParams.get('role');
    if (roleFilter) {
      filter.role = roleFilter;
    }

    // Active status filter
    const isActiveFilter = url.searchParams.get('isActive');
    if (isActiveFilter !== null) {
      filter.isActive = isActiveFilter === 'true';
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling fetchUsers service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchUsers(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchUsers completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.users,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/users (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new user
 * @param request - The incoming request with user data
 * @returns JSON response with the newly created user
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/users - Creating new user');
    const userData = await request.json() as CreateUserRequestBody;

    // Basic validation
    if (!userData || typeof userData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid user data provided' }, { status: 400 });
    }

    // Validate required fields based on the user schema
    if (!userData.username) {
      return NextResponse.json({ data: null, error: 'Missing required field: username' }, { status: 400 });
    }

    if (!userData.email) {
      return NextResponse.json({ data: null, error: 'Missing required field: email' }, { status: 400 });
    }

    if (!userData.firstName) {
      return NextResponse.json({ data: null, error: 'Missing required field: firstName' }, { status: 400 });
    }

    if (!userData.lastName) {
      return NextResponse.json({ data: null, error: 'Missing required field: lastName' }, { status: 400 });
    }

    if (!userData.password) {
      return NextResponse.json({ data: null, error: 'Missing required field: password' }, { status: 400 });
    }

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const password_hash = await bcrypt.hash(userData.password, salt);

    // Prepare user data for service function - transform frontend fields to database fields
    const userDataForService = {
      ...userData,
      first_name: userData.firstName,
      last_name: userData.lastName,
      password_hash,
      // Remove frontend fields and plain text password
      firstName: undefined,
      lastName: undefined,
      password: undefined
    };

    console.log(`[API] Calling addUser service with data: ${JSON.stringify({
      ...userDataForService,
      password_hash: '[REDACTED]'
    })}`);

    // Call the addUser service function
    const savedUser = await addUser(userDataForService);

    const duration = Date.now() - startTime;
    console.log(`[API] Service addUser completed in ${duration}ms`);

    // Remove password_hash from response
    const userResponse = {
      ...savedUser,
      password_hash: undefined
    };

    return NextResponse.json({ data: userResponse, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/users (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
