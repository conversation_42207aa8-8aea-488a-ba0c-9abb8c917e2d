"use client";

import React from 'react';
import { Bell, BarChart2, Users } from 'lucide-react';

const ThemeShowcase: React.FC = () => {
  return (
    <div className="border border-border rounded-lg overflow-hidden bg-card">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
        {/* Button Showcase */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-foreground mb-2">Buttons</h4>

          <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg shadow hover:bg-primary/90 transition-colors">
            Primary Button
          </button>

          <button className="px-4 py-2 bg-secondary text-secondary-foreground border border-border rounded-lg shadow hover:bg-secondary/80 transition-colors">
            Secondary Button
          </button>

          <button className="px-4 py-2 bg-destructive text-destructive-foreground rounded-lg shadow hover:bg-destructive/90 transition-colors">
            Danger Button
          </button>
        </div>

        {/* Card Showcase */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-foreground mb-2">Cards</h4>

          <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
            <div className="flex items-center justify-between mb-2">
              <h5 className="font-medium text-card-foreground">Card Title</h5>
              <span className="text-xs text-muted-foreground">Today</span>
            </div>
            <p className="text-sm text-muted-foreground">
              This is a sample card with some content inside.
            </p>
          </div>

          <div className="bg-accent p-4 rounded-lg shadow-sm border border-border">
            <div className="flex items-center mb-2">
              <Bell size={16} className="text-primary mr-2" />
              <h5 className="font-medium text-accent-foreground">Notification</h5>
            </div>
            <p className="text-sm text-accent-foreground/80">
              You have a new message in your inbox.
            </p>
          </div>
        </div>

        {/* Charts & Components */}
        <div className="flex flex-col space-y-3">
          <h4 className="text-sm font-medium text-foreground mb-2">Components</h4>

          <div className="flex items-center space-x-2 p-2">
            <div className="w-4 h-4 rounded-full bg-primary"></div>
            <div className="w-4 h-4 rounded-full bg-secondary"></div>
            <div className="w-4 h-4 rounded-full bg-accent"></div>
            <div className="w-4 h-4 rounded-full bg-muted"></div>
          </div>

          <div className="bg-card p-3 rounded-lg shadow-sm border border-border">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <BarChart2 size={16} className="text-primary mr-2" />
                <span className="text-sm font-medium text-card-foreground">Analytics</span>
              </div>
              <div className="text-xs font-medium px-2 py-1 rounded-full bg-primary/10 text-primary">
                +24%
              </div>
            </div>
            <div className="flex h-8 space-x-1">
              <div className="w-1/5 bg-primary/20 rounded"></div>
              <div className="w-2/5 bg-primary/40 rounded"></div>
              <div className="w-1/5 bg-primary/60 rounded"></div>
              <div className="w-1/5 bg-primary/80 rounded"></div>
            </div>
          </div>

          <div className="bg-card p-3 rounded-lg shadow-sm border border-border">
            <div className="flex items-center mb-2">
              <Users size={16} className="text-primary mr-2" />
              <span className="text-sm font-medium text-card-foreground">Team</span>
            </div>
            <div className="flex -space-x-2 overflow-hidden">
              <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium text-muted-foreground">
                JD
              </div>
              <div className="w-8 h-8 rounded-full bg-primary/20 border-2 border-background flex items-center justify-center text-xs font-medium text-primary">
                MK
              </div>
              <div className="w-8 h-8 rounded-full bg-secondary border-2 border-background flex items-center justify-center text-xs font-medium text-secondary-foreground">
                TP
              </div>
              <div className="w-8 h-8 rounded-full bg-accent border-2 border-background flex items-center justify-center text-xs font-medium text-accent-foreground">
                +5
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeShowcase;