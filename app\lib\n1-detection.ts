/**
 * N+1 Query Detection and Performance Monitoring
 * 
 * Detects potential N+1 query patterns and monitors database performance
 * to prevent future performance regressions.
 */

import { captureMessage, setTag, startTransaction } from './logging-utils';

interface QueryPattern {
  operation: string;
  collection: string;
  query: any;
  timestamp: number;
  duration: number;
  stackTrace?: string;
}

interface N1Detection {
  pattern: string;
  count: number;
  totalDuration: number;
  avgDuration: number;
  queries: QueryPattern[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
}

class N1QueryDetector {
  private queryHistory: QueryPattern[] = [];
  private detectionWindow = 5000; // 5 seconds
  private maxHistorySize = 1000;
  
  // Thresholds for N+1 detection
  private readonly thresholds = {
    queryCount: 10, // More than 10 similar queries in window
    totalDuration: 1000, // Total duration > 1 second
    avgDuration: 100, // Average duration > 100ms
    criticalCount: 50, // Critical if > 50 queries
    criticalDuration: 5000 // Critical if total > 5 seconds
  };

  /**
   * Record a database query for analysis
   */
  recordQuery(
    operation: string,
    collection: string,
    query: any,
    duration: number,
    stackTrace?: string
  ): void {
    const queryPattern: QueryPattern = {
      operation,
      collection,
      query: this.normalizeQuery(query),
      timestamp: Date.now(),
      duration,
      ...(stackTrace && { stackTrace })
    };

    this.queryHistory.push(queryPattern);
    
    // Cleanup old queries
    this.cleanupHistory();
    
    // Check for N+1 patterns
    this.detectN1Patterns();
  }

  /**
   * Normalize query for pattern matching
   */
  private normalizeQuery(query: any): string {
    if (!query) return 'empty';
    
    try {
      // Convert ObjectIds to placeholder for pattern matching
      const normalized = JSON.stringify(query, (key, value) => {
        if (value && typeof value === 'object' && value.$oid) {
          return 'ObjectId(...)';
        }
        if (value && typeof value === 'object' && value.constructor?.name === 'ObjectId') {
          return 'ObjectId(...)';
        }
        return value;
      });
      
      return normalized;
    } catch (error) {
      return 'unparseable';
    }
  }

  /**
   * Clean up old query history
   */
  private cleanupHistory(): void {
    const cutoff = Date.now() - this.detectionWindow;
    this.queryHistory = this.queryHistory.filter(q => q.timestamp > cutoff);
    
    // Limit history size
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory = this.queryHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Detect N+1 query patterns
   */
  private detectN1Patterns(): void {
    const patterns = new Map<string, QueryPattern[]>();
    
    // Group queries by pattern
    this.queryHistory.forEach(query => {
      const pattern = `${query.operation}:${query.collection}:${query.query}`;
      if (!patterns.has(pattern)) {
        patterns.set(pattern, []);
      }
      patterns.get(pattern)!.push(query);
    });

    // Analyze each pattern for N+1 characteristics
    patterns.forEach((queries, pattern) => {
      if (queries.length >= this.thresholds.queryCount) {
        const detection = this.analyzePattern(pattern, queries);
        if (detection.severity !== 'low') {
          this.reportN1Detection(detection);
        }
      }
    });
  }

  /**
   * Analyze a query pattern for N+1 characteristics
   */
  private analyzePattern(pattern: string, queries: QueryPattern[]): N1Detection {
    const totalDuration = queries.reduce((sum, q) => sum + q.duration, 0);
    const avgDuration = totalDuration / queries.length;
    const count = queries.length;

    let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
    let recommendation = 'Monitor query patterns';

    // Determine severity
    if (count >= this.thresholds.criticalCount || totalDuration >= this.thresholds.criticalDuration) {
      severity = 'critical';
      recommendation = 'URGENT: Replace with aggregation pipeline or batch queries';
    } else if (count >= 30 || totalDuration >= 3000) {
      severity = 'high';
      recommendation = 'High priority: Optimize with aggregation pipeline';
    } else if (count >= 20 || totalDuration >= 2000) {
      severity = 'medium';
      recommendation = 'Medium priority: Consider query optimization';
    }

    return {
      pattern,
      count,
      totalDuration,
      avgDuration,
      queries: queries.slice(-10), // Keep last 10 for analysis
      severity,
      recommendation
    };
  }

  /**
   * Report N+1 detection to monitoring systems
   */
  private reportN1Detection(detection: N1Detection): void {
    const message = `N+1 Query Pattern Detected: ${detection.pattern}`;
    
    // Set Sentry tags
    setTag('n1_detection.pattern', detection.pattern);
    setTag('n1_detection.severity', detection.severity);
    setTag('n1_detection.count', detection.count.toString());
    setTag('n1_detection.total_duration', detection.totalDuration.toString());

    // Capture to Sentry with appropriate level
    const level = detection.severity === 'critical' ? 'error' : 
                  detection.severity === 'high' ? 'warning' : 'info';

    captureMessage(message, level, {
      tags: {
        type: 'n1_detection',
        severity: detection.severity,
        pattern: detection.pattern
      },
      extra: {
        queryCount: detection.count,
        totalDuration: detection.totalDuration,
        avgDuration: detection.avgDuration,
        recommendation: detection.recommendation,
        sampleQueries: detection.queries.slice(0, 3),
        timestamp: new Date().toISOString()
      }
    });

    // Log to console for immediate visibility
    console.warn(`[N+1 Detection] ${message}`, {
      severity: detection.severity,
      count: detection.count,
      totalDuration: `${detection.totalDuration}ms`,
      recommendation: detection.recommendation
    });
  }

  /**
   * Get current detection statistics
   */
  getStats(): {
    totalQueries: number;
    uniquePatterns: number;
    avgDuration: number;
    recentDetections: number;
  } {
    const patterns = new Set(this.queryHistory.map(q => 
      `${q.operation}:${q.collection}:${q.query}`
    ));
    
    const totalDuration = this.queryHistory.reduce((sum, q) => sum + q.duration, 0);
    const avgDuration = this.queryHistory.length > 0 ? totalDuration / this.queryHistory.length : 0;
    
    return {
      totalQueries: this.queryHistory.length,
      uniquePatterns: patterns.size,
      avgDuration,
      recentDetections: this.queryHistory.filter(q => 
        Date.now() - q.timestamp < 60000 // Last minute
      ).length
    };
  }

  /**
   * Reset detection history (useful for testing)
   */
  reset(): void {
    this.queryHistory = [];
  }
}

// Singleton instance
const n1Detector = new N1QueryDetector();

/**
 * Monitor a database operation for N+1 patterns
 */
export function monitorDatabaseOperation<T>(
  operation: string,
  collection: string,
  query: any,
  fn: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  return fn().then(result => {
    const duration = Date.now() - startTime;
    
    // Record the query for N+1 detection
    n1Detector.recordQuery(operation, collection, query, duration);
    
    // Log slow queries
    if (duration > 500) {
      console.warn(`[Slow Query] ${operation} on ${collection} took ${duration}ms`, {
        query: JSON.stringify(query).substring(0, 200),
        duration
      });
    }
    
    return result;
  }).catch(error => {
    const duration = Date.now() - startTime;
    n1Detector.recordQuery(operation, collection, query, duration);
    throw error;
  });
}

/**
 * Create a Sentry transaction for database operations
 */
export function createDatabaseTransaction(
  name: string,
  operation: string,
  collection: string
) {
  return startTransaction(name, 'db.query', {
    tags: {
      'db.operation': operation,
      'db.collection': collection
    }
  });
}

/**
 * Get N+1 detection statistics
 */
export function getN1DetectionStats() {
  return n1Detector.getStats();
}

/**
 * Reset N+1 detection (for testing)
 */
export function resetN1Detection() {
  n1Detector.reset();
}

export default n1Detector;
