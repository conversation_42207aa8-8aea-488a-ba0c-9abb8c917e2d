#!/usr/bin/env node

/**
 * Environment setup script for Trend IMS
 * This script ensures that the necessary environment variables are set up
 * before starting the development server.
 */

const fs = require('fs');
const path = require('path');

const ENV_FILE = path.join(process.cwd(), '.env');
const ENV_EXAMPLE_FILE = path.join(process.cwd(), '.env.example');

function createEnvFile() {
  console.log('🔧 Setting up environment...');

  // Check if .env file exists
  if (fs.existsSync(ENV_FILE)) {
    console.log('✅ .env file already exists');
    return;
  }

  // Check if .env.example exists to copy from
  if (fs.existsSync(ENV_EXAMPLE_FILE)) {
    console.log('📋 Copying .env.example to .env...');
    fs.copyFileSync(ENV_EXAMPLE_FILE, ENV_FILE);
    console.log('✅ .env file created from .env.example');
    console.log('⚠️  Please update the environment variables in .env as needed');
    return;
  }

  // Create a basic .env file with default values
  console.log('📝 Creating basic .env file...');
  // Determine if we're setting up for production or development
  const isProduction = process.env.NODE_ENV === 'production';
  // Use PORT environment variable if set, otherwise use defaults
  const defaultPort = isProduction ? '3001' : '3001';
  const port = process.env.PORT || defaultPort;
  const environment = isProduction ? 'production' : 'development';

  const defaultEnvContent = `# =============================================================================
# TREND IMS - OPTIMIZED ENVIRONMENT CONFIGURATION
# =============================================================================
# Generated by create-env.cjs for ${environment} environment
# Supports conditional environment loading based on NODE_ENV
# =============================================================================

# =============================================================================
# CORE ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=${environment}

# =============================================================================
# MONGODB CONFIGURATION (Conditional)
# =============================================================================
# Primary MongoDB connection string (used for all environments)
# Replace with your actual MongoDB Atlas connection string
MONGODB_URI=mongodb://localhost:27017/trend_ims

# Database name (consistent across environments)
MONGODB_DB_NAME=trend_ims

# =============================================================================
# ENVIRONMENT-SPECIFIC MONGODB SETTINGS
# =============================================================================
# Development: More aggressive timeouts for faster feedback
MONGODB_DEV_CONNECT_TIMEOUT=5000
MONGODB_DEV_SERVER_SELECTION_TIMEOUT=5000
MONGODB_DEV_SOCKET_TIMEOUT=8000
MONGODB_DEV_MAX_POOL_SIZE=5
MONGODB_DEV_MIN_POOL_SIZE=1

# Production: Conservative timeouts for stability
MONGODB_PROD_CONNECT_TIMEOUT=15000
MONGODB_PROD_SERVER_SELECTION_TIMEOUT=15000
MONGODB_PROD_SOCKET_TIMEOUT=30000
MONGODB_PROD_MAX_POOL_SIZE=10
MONGODB_PROD_MIN_POOL_SIZE=2

# =============================================================================
# SERVER CONFIGURATION (Flexible Port Handling)
# =============================================================================
# PORT can be overridden by environment variable at runtime
# Development default: 3001, Production default: 3001
PORT=${port}
HOST=localhost

# =============================================================================
# API URLS (Dynamic Port Configuration)
# =============================================================================
# These URLs will be dynamically constructed based on PORT environment variable
NEXT_PUBLIC_APP_URL=http://localhost:${port}
NEXT_PUBLIC_API_BASE_URL=http://localhost:${port}

# =============================================================================
# AUTHENTICATION (Dynamic Port Configuration)
# =============================================================================
NEXTAUTH_URL=http://localhost:${port}
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# =============================================================================
# BUILD & DEPLOYMENT SETTINGS
# =============================================================================
SKIP_MONGODB_CONNECTION=false

# =============================================================================
# ENVIRONMENT LOGGING & DEBUGGING
# =============================================================================
# Enable detailed environment variable logging
ENABLE_ENV_LOGGING=${isProduction ? 'false' : 'true'}
# Enable MongoDB connection debugging
ENABLE_DB_CONNECTION_DEBUG=${isProduction ? 'false' : 'true'}

# =============================================================================
# OPTIONAL SERVICES (Disabled by default)
# =============================================================================
# Sentry Error Monitoring
# NEXT_PUBLIC_SENTRY_DSN=
# SENTRY_AUTH_TOKEN=

# API Keys and External Services
# Add any additional API keys or service credentials here
`;

  fs.writeFileSync(ENV_FILE, defaultEnvContent);
  console.log('✅ Basic .env file created');
  console.log('⚠️  Please update the environment variables as needed');
}

function validateEnv() {
  console.log('🔍 Validating environment variables...');
  
  // Load environment variables
  require('dotenv').config();
  
  const requiredVars = [
    'NODE_ENV',
    'PORT',
    'MONGODB_URI',
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log('⚠️  Missing required environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
    console.log('Please update your .env file with the missing variables.');
  } else {
    console.log('✅ All required environment variables are set');
  }
}

// Main execution
try {
  createEnvFile();
  validateEnv();
  console.log('🚀 Environment setup complete!');
} catch (error) {
  console.error('❌ Error setting up environment:', error.message);
  process.exit(1);
}
