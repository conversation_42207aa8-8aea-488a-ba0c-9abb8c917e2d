'use client';

import { getApiUrl } from '@/app/utils/apiUtils';
import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  timestamp: string;
  n1Detection: {
    totalQueries: number;
    uniquePatterns: number;
    avgDuration: number;
    recentDetections: number;
  };
  cache: {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{ key: string; hits: number; age: number }>;
  };
  health: {
    status: string;
    checks: {
      n1Detection: string;
      cache: string;
      memory: string;
    };
  };
  thresholds: {
    api: {
      slow: number;
      verySlow: number;
      critical: number;
    };
    database: {
      slow: number;
      verySlow: number;
      critical: number;
    };
  };
}

export default function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl('/api/monitoring/performance'));

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setMetrics(data.data);
      setLastUpdated(new Date());
      setError(null);
    } catch (err: any) {
      console.error('Failed to fetch performance metrics:', err);
      setError(err.message || 'Failed to fetch performance metrics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-success bg-success/10 border-success/20';
      case 'warning': return 'text-warning bg-warning/10 border-warning/20';
      case 'critical': return 'text-destructive bg-destructive/10 border-destructive/20';
      default: return 'text-muted-foreground bg-muted border-border';
    }
  };

  if (loading && !metrics) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">🔄 Performance Monitor</h2>
        <p className="text-gray-600 mb-4">Loading performance metrics...</p>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4 text-red-600">⚠️ Performance Monitor - Error</h2>
        <p className="text-gray-600 mb-4">Failed to load performance metrics</p>
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={fetchMetrics}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!metrics) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            📊 Performance Monitor
          </h2>
          <p className="text-gray-600">
            Real-time performance metrics and N+1 query detection
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className="px-3 py-1 bg-gray-100 border border-gray-200 rounded text-sm">
            Last updated: {lastUpdated?.toLocaleTimeString()}
          </span>
          <button
            onClick={fetchMetrics}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Health Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
          ✅ System Health
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-2">
            <span className="font-medium">N+1 Detection:</span>
            <span className={`px-2 py-1 rounded text-sm border ${getStatusColor(metrics.health.checks.n1Detection)}`}>
              {metrics.health.checks.n1Detection}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">Cache:</span>
            <span className={`px-2 py-1 rounded text-sm border ${getStatusColor(metrics.health.checks.cache)}`}>
              {metrics.health.checks.cache}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">Memory:</span>
            <span className={`px-2 py-1 rounded text-sm border ${getStatusColor(metrics.health.checks.memory)}`}>
              {metrics.health.checks.memory}
            </span>
          </div>
        </div>
      </div>

      {/* N+1 Detection */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-bold mb-2 flex items-center gap-2">
          🗄️ N+1 Query Detection
        </h3>
        <p className="text-gray-600 mb-4">
          Monitoring for inefficient database query patterns
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.n1Detection.totalQueries}
            </div>
            <div className="text-sm text-gray-600">Total Queries</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {metrics.n1Detection.uniquePatterns}
            </div>
            <div className="text-sm text-gray-600">Unique Patterns</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {Math.round(metrics.n1Detection.avgDuration)}ms
            </div>
            <div className="text-sm text-gray-600">Avg Duration</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              metrics.n1Detection.recentDetections > 5 ? 'text-red-600' : 'text-green-600'
            }`}>
              {metrics.n1Detection.recentDetections}
            </div>
            <div className="text-sm text-gray-600">Recent Detections</div>
          </div>
        </div>
      </div>

      {/* Cache Performance */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-bold mb-2 flex items-center gap-2">
          ⚡ Cache Performance
        </h3>
        <p className="text-gray-600 mb-4">
          In-memory cache statistics and hit rates
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {metrics.cache.size} / {metrics.cache.maxSize}
            </div>
            <div className="text-sm text-gray-600">Cache Size</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              metrics.cache.hitRate > 0.7 ? 'text-green-600' :
              metrics.cache.hitRate > 0.5 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {Math.round(metrics.cache.hitRate * 100)}%
            </div>
            <div className="text-sm text-gray-600">Hit Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {metrics.cache.entries.length}
            </div>
            <div className="text-sm text-gray-600">Active Entries</div>
          </div>
        </div>
      </div>

      {/* Performance Thresholds */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-bold mb-2 flex items-center gap-2">
          ⏱️ Performance Thresholds
        </h3>
        <p className="text-gray-600 mb-4">
          Configured performance monitoring thresholds
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">API Response Times</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Slow:</span>
                <span className="text-yellow-600">&gt; {metrics.thresholds.api.slow}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Very Slow:</span>
                <span className="text-orange-600">&gt; {metrics.thresholds.api.verySlow}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Critical:</span>
                <span className="text-red-600">&gt; {metrics.thresholds.api.critical}ms</span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-2">Database Query Times</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Slow:</span>
                <span className="text-yellow-600">&gt; {metrics.thresholds.database.slow}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Very Slow:</span>
                <span className="text-orange-600">&gt; {metrics.thresholds.database.verySlow}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Critical:</span>
                <span className="text-red-600">&gt; {metrics.thresholds.database.critical}ms</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
