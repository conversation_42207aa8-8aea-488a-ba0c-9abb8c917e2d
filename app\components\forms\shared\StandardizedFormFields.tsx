"use client";

import React, { memo, useMemo } from "react";
import { useFormContext, Control } from "react-hook-form";
import { Package, FileText, DollarSign, Hash, Building2, Layers, Settings, Tag } from "lucide-react";

import { EnhancedFormField, EnhancedFormItem, EnhancedFormLabel, EnhancedFormControl, EnhancedFormMessage } from "../enhanced-form/EnhancedFormField";
import { Input } from "@/app/components/forms/Input";
import { Textarea } from "@/app/components/forms/Textarea/Textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { cn } from "@/app/lib/utils";

/**
 * Props for basic information section
 */
export interface BasicInformationSectionProps {
  /**
   * Type of entity (product, assembly, etc.)
   */
  entityType: 'product' | 'assembly';
  
  /**
   * Whether to show the code field
   */
  showCodeField?: boolean;
  
  /**
   * Placeholder for the code field
   */
  codePlaceholder?: string;
  
  /**
   * Label for the code field
   */
  codeLabel?: string;
  
  /**
   * Field name for the code
   */
  codeFieldName?: string;
  
  /**
   * Whether to show the name field
   */
  showNameField?: boolean;
  
  /**
   * Placeholder for the name field
   */
  namePlaceholder?: string;
  
  /**
   * Label for the name field
   */
  nameLabel?: string;
  
  /**
   * Field name for the name
   */
  nameFieldName?: string;
  
  /**
   * Whether to show the description field
   */
  showDescriptionField?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Reusable basic information section for forms
 * Optimized with React.memo and useMemo for better performance
 */
export const BasicInformationSection: React.FC<BasicInformationSectionProps> = memo(({
  entityType,
  showCodeField = true,
  codePlaceholder,
  codeLabel,
  codeFieldName,
  showNameField = true,
  namePlaceholder,
  nameLabel,
  nameFieldName,
  showDescriptionField = true,
  className = "",
}) => {
  const { control } = useFormContext();

  // Memoized default values based on entity type for better performance
  const config = useMemo(() => {
    const defaults = {
      product: {
        codePlaceholder: "PROD-001",
        codeLabel: "Product Code",
        codeFieldName: "productCode",
        namePlaceholder: "Enter product name",
        nameLabel: "Product Name",
        nameFieldName: "name",
      },
      assembly: {
        codePlaceholder: "ASM-001",
        codeLabel: "Assembly Code",
        codeFieldName: "assemblyCode",
        namePlaceholder: "Enter assembly name",
        nameLabel: "Assembly Name",
        nameFieldName: "name",
      }
    };
    return defaults[entityType];
  }, [entityType]);
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2 pb-2 border-b border-border">
        <Package className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-medium">Basic Information</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {showCodeField && (
          <EnhancedFormField
            control={control}
            name={codeFieldName || config.codeFieldName}
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {codeLabel || config.codeLabel}
                </EnhancedFormLabel>
                <EnhancedFormControl>
                  <Input
                    {...field}
                    placeholder={codePlaceholder || config.codePlaceholder}
                    className="font-mono"
                    autoComplete="off"
                  />
                </EnhancedFormControl>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
        )}

        {showNameField && (
          <EnhancedFormField
            control={control}
            name={nameFieldName || config.nameFieldName}
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  {nameLabel || config.nameLabel}
                </EnhancedFormLabel>
                <EnhancedFormControl>
                  <Input
                    {...field}
                    placeholder={namePlaceholder || config.namePlaceholder}
                    autoComplete="off"
                  />
                </EnhancedFormControl>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
        )}
      </div>

      {showDescriptionField && (
        <EnhancedFormField
          control={control}
          name="description"
          render={({ field }) => (
            <EnhancedFormItem>
              <EnhancedFormLabel className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Description
              </EnhancedFormLabel>
              <EnhancedFormControl>
                <Textarea
                  {...field}
                  placeholder={`Enter ${entityType} description`}
                  className="min-h-[80px] resize-none"
                />
              </EnhancedFormControl>
              <EnhancedFormMessage />
            </EnhancedFormItem>
          )}
        />
      )}
    </div>
  );
});

/**
 * Props for category selection field
 */
export interface CategorySelectionFieldProps {
  /**
   * Available categories
   */
  categories?: Array<{
    _id: string;
    name: string;
  }>;
  
  /**
   * Field name for category
   */
  fieldName?: string;
  
  /**
   * Label for the field
   */
  label?: string;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Reusable category selection field
 * Optimized with React.memo for better performance
 */
export const CategorySelectionField: React.FC<CategorySelectionFieldProps> = memo(({
  categories = [],
  fieldName = "categoryId",
  label = "Category",
  placeholder = "Select category",
  className = "",
}) => {
  const { control } = useFormContext();
  
  return (
    <EnhancedFormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <EnhancedFormItem className={className}>
          <EnhancedFormLabel className="flex items-center gap-2">
            <Tag className="h-4 w-4" />
            {label}
          </EnhancedFormLabel>
          <Select onValueChange={field.onChange} value={field.value || ""}>
            <EnhancedFormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </EnhancedFormControl>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category._id} value={category._id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <EnhancedFormMessage />
        </EnhancedFormItem>
      )}
    />
  );
});

/**
 * Props for price field
 */
export interface PriceFieldProps {
  /**
   * Field name for price
   */
  fieldName?: string;
  
  /**
   * Label for the field
   */
  label?: string;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Reusable price field component
 * Optimized with React.memo for better performance
 */
export const PriceField: React.FC<PriceFieldProps> = memo(({
  fieldName = "sellingPrice",
  label = "Selling Price",
  placeholder = "0.00",
  className = "",
}) => {
  const { control } = useFormContext();
  
  return (
    <EnhancedFormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <EnhancedFormItem className={className}>
          <EnhancedFormLabel className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            {label}
          </EnhancedFormLabel>
          <EnhancedFormControl>
            <Input
              {...field}
              type="number"
              step="0.01"
              min="0"
              placeholder={placeholder}
              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
            />
          </EnhancedFormControl>
          <EnhancedFormMessage />
        </EnhancedFormItem>
      )}
    />
  );
});

/**
 * Props for status selection field
 */
export interface StatusSelectionFieldProps {
  /**
   * Available status options
   */
  statusOptions: Array<{
    value: string;
    label: string;
  }>;
  
  /**
   * Field name for status
   */
  fieldName?: string;
  
  /**
   * Label for the field
   */
  label?: string;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Reusable status selection field
 * Optimized with React.memo for better performance
 */
export const StatusSelectionField: React.FC<StatusSelectionFieldProps> = memo(({
  statusOptions,
  fieldName = "status",
  label = "Status",
  placeholder = "Select status",
  className = "",
}) => {
  const { control } = useFormContext();
  
  return (
    <EnhancedFormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <EnhancedFormItem className={className}>
          <EnhancedFormLabel className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            {label}
          </EnhancedFormLabel>
          <Select onValueChange={field.onChange} value={field.value || ""}>
            <EnhancedFormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </EnhancedFormControl>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <EnhancedFormMessage />
        </EnhancedFormItem>
      )}
    />
  );
});
