import mongoose, { Types } from 'mongoose';
import { connectToDatabase } from '@/app/lib/mongodb';
import { Location, ILocation } from '@/app/models/location.model';
import { Warehouse } from '@/app/models/warehouse.model';
import { logOperation } from '@/app/services/logging';

// DTOs for location operations
export interface CreateLocationDto {
  warehouseId: string;
  name: string;
  description?: string | null;
  locationType: 'Bin' | 'Shelf' | 'Floor Area' | 'Staging' | 'Production Area' | 'Quality Control' | 'Shipping' | 'Receiving';
  capacity?: {
    maxWeightKg?: number;
    volumeM3?: number;
  } | null;
  isActive?: boolean;
}

export interface UpdateLocationDto extends Partial<CreateLocationDto> {}

export interface LocationQueryOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
  warehouseId?: string;
  isActive?: boolean;
  locationType?: string;
}

/**
 * Creates a new location within a warehouse
 */
export async function createLocation(locationData: CreateLocationDto): Promise<ILocation> {
  logOperation('CREATE_LOCATION', 'locations', { warehouseId: locationData.warehouseId, name: locationData.name });
  
  try {
    await connectToDatabase();

    // Validate warehouse exists
    if (!Types.ObjectId.isValid(locationData.warehouseId)) {
      throw new Error('Invalid warehouse ID format');
    }

    const warehouse = await Warehouse.findById(locationData.warehouseId);
    if (!warehouse) {
      throw new Error(`Warehouse with ID ${locationData.warehouseId} not found`);
    }

    // Check if location name already exists in this warehouse
    const existingLocation = await Location.findOne({
      warehouseId: new Types.ObjectId(locationData.warehouseId),
      name: locationData.name.toUpperCase()
    });

    if (existingLocation) {
      throw new Error(`Location '${locationData.name}' already exists in warehouse '${warehouse.name}'`);
    }

    const newLocation = new Location({
      ...locationData,
      warehouseId: new Types.ObjectId(locationData.warehouseId),
      name: locationData.name.toUpperCase(),
      isActive: locationData.isActive ?? true
    });

    const savedLocation = await newLocation.save();
    
    logOperation('CREATE_LOCATION_SUCCESS', 'service', { 
      locationId: savedLocation._id,
      warehouseId: locationData.warehouseId,
      name: savedLocation.name
    });

    return savedLocation;

  } catch (error: any) {
    logOperation('CREATE_LOCATION_ERROR', 'service', { 
      warehouseId: locationData.warehouseId,
      name: locationData.name,
      error: error.message 
    });
    throw error;
  }
}

/**
 * Fetches locations with pagination, sorting, and filtering
 */
export async function fetchLocations(options: LocationQueryOptions = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {},
    warehouseId,
    isActive,
    locationType
  } = options;

  logOperation('FETCH_LOCATIONS', 'service', {
    page,
    limit,
    warehouseId,
    isActive,
    locationType
  });

  try {
    await connectToDatabase();

    // Build filter
    const queryFilter: Record<string, any> = { ...filter };
    
    if (warehouseId) {
      if (!Types.ObjectId.isValid(warehouseId)) {
        throw new Error('Invalid warehouse ID format');
      }
      queryFilter.warehouseId = new Types.ObjectId(warehouseId);
    }
    
    if (isActive !== undefined) {
      queryFilter.isActive = isActive;
    }
    
    if (locationType) {
      queryFilter.locationType = locationType;
    }

    const skip = (page - 1) * limit;

    // Execute query with population
    const [locations, total] = await Promise.all([
      Location.find(queryFilter)
        .populate('warehouseId', 'name location_id location')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Location.countDocuments(queryFilter)
    ]);

    const pagination = {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };

    logOperation('FETCH_LOCATIONS_SUCCESS', 'service', {
      count: locations.length,
      total,
      page
    });

    return {
      locations,
      pagination
    };

  } catch (error: any) {
    logOperation('FETCH_LOCATIONS_ERROR', 'service', { error: error.message });
    throw error;
  }
}

/**
 * Gets locations for a specific warehouse
 */
export async function getLocationsByWarehouse(warehouseId: string, activeOnly: boolean = true) {
  logOperation('GET_LOCATIONS_BY_WAREHOUSE', 'service', { warehouseId, activeOnly });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(warehouseId)) {
      throw new Error('Invalid warehouse ID format');
    }

    const filter: Record<string, any> = {
      warehouseId: new Types.ObjectId(warehouseId)
    };

    if (activeOnly) {
      // Handle legacy documents that don't have isActive field
      // Treat missing isActive as true (the default value)
      filter.$or = [
        { isActive: true },
        { isActive: { $exists: false } }
      ];
    }

    const locations = await Location.find(filter)
      .sort({ name: 1 })
      .lean();

    logOperation('GET_LOCATIONS_BY_WAREHOUSE_SUCCESS', 'service', {
      warehouseId,
      count: locations.length
    });

    return locations;

  } catch (error: any) {
    logOperation('GET_LOCATIONS_BY_WAREHOUSE_ERROR', 'service', {
      warehouseId,
      error: error.message
    });
    throw error;
  }
}

/**
 * Gets a single location by ID
 */
export async function getLocationById(locationId: string): Promise<ILocation | null> {
  logOperation('GET_LOCATION_BY_ID', 'service', { locationId });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(locationId)) {
      throw new Error('Invalid location ID format');
    }

    const location = await Location.findById(locationId)
      .populate('warehouseId', 'name location_id location')
      .lean();

    logOperation('GET_LOCATION_BY_ID_SUCCESS', 'service', {
      locationId,
      found: !!location
    });

    return location;

  } catch (error: any) {
    logOperation('GET_LOCATION_BY_ID_ERROR', 'service', {
      locationId,
      error: error.message
    });
    throw error;
  }
}

/**
 * Updates a location
 */
export async function updateLocation(locationId: string, updateData: UpdateLocationDto): Promise<ILocation | null> {
  logOperation('UPDATE_LOCATION', 'service', { locationId, updateData });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(locationId)) {
      throw new Error('Invalid location ID format');
    }

    // If updating name, ensure it's unique within the warehouse
    if (updateData.name) {
      const existingLocation = await Location.findById(locationId);
      if (!existingLocation) {
        throw new Error('Location not found');
      }

      const duplicateLocation = await Location.findOne({
        warehouseId: existingLocation.warehouseId,
        name: updateData.name.toUpperCase(),
        _id: { $ne: locationId }
      });

      if (duplicateLocation) {
        throw new Error(`Location name '${updateData.name}' already exists in this warehouse`);
      }

      updateData.name = updateData.name.toUpperCase();
    }

    const updatedLocation = await Location.findByIdAndUpdate(
      locationId,
      updateData,
      { new: true, runValidators: true }
    ).populate('warehouseId', 'name location_id location');

    logOperation('UPDATE_LOCATION_SUCCESS', 'service', {
      locationId,
      updated: !!updatedLocation
    });

    return updatedLocation;

  } catch (error: any) {
    logOperation('UPDATE_LOCATION_ERROR', 'service', {
      locationId,
      error: error.message
    });
    throw error;
  }
}

/**
 * Soft deletes a location (sets isActive to false)
 */
export async function deactivateLocation(locationId: string): Promise<boolean> {
  logOperation('DEACTIVATE_LOCATION', 'service', { locationId });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(locationId)) {
      throw new Error('Invalid location ID format');
    }

    // TODO: Check if location has inventory before deactivating
    // This should be implemented when inventory service is updated

    const result = await Location.findByIdAndUpdate(
      locationId,
      { isActive: false },
      { new: true }
    );

    const success = !!result;

    logOperation('DEACTIVATE_LOCATION_SUCCESS', 'service', {
      locationId,
      success
    });

    return success;

  } catch (error: any) {
    logOperation('DEACTIVATE_LOCATION_ERROR', 'service', {
      locationId,
      error: error.message
    });
    throw error;
  }
}
