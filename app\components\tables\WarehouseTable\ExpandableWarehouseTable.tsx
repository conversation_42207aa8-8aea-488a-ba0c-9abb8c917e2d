"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Badge } from '@/app/components/data-display/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { 
  ChevronDown, 
  ChevronRight, 
  Building2, 
  MapPin, 
  Package, 
  User, 
  Phone,
  CheckCircle,
  XCircle,
  Weight,
  Box,
  Plus
} from 'lucide-react';
import { WarehouseColumnData } from './types';
import { LocationFormData } from '@/app/components/forms/WarehouseForm/types';
import { LocationCard } from '@/app/components/cards/LocationCard';

interface ExpandableWarehouseTableProps {
  warehouses?: WarehouseColumnData[]; // Optional, will fetch if not provided
  onWarehouseClick?: (warehouse: WarehouseColumnData) => void;
  onWarehouseEdit?: (warehouse: WarehouseColumnData) => void;
  onWarehouseDelete?: (warehouse: WarehouseColumnData) => void;
  isLoading?: boolean;
  error?: string | null;
  searchTerm?: string;
  refreshTrigger?: number;
}

interface WarehouseWithLocations extends WarehouseColumnData {
  locations?: LocationFormData[];
}

export default function ExpandableWarehouseTable({
  warehouses: propWarehouses,
  onWarehouseClick,
  onWarehouseEdit,
  onWarehouseDelete,
  isLoading: propIsLoading = false,
  error: propError = null,
  searchTerm = '',
  refreshTrigger = 0
}: ExpandableWarehouseTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [warehouseLocations, setWarehouseLocations] = useState<Record<string, LocationFormData[]>>({});
  const [loadingLocations, setLoadingLocations] = useState<Set<string>>(new Set());

  // Internal state for fetching warehouses if not provided
  const [warehouses, setWarehouses] = useState<WarehouseColumnData[]>(propWarehouses || []);
  const [isLoading, setIsLoading] = useState(propIsLoading || !propWarehouses);
  const [error, setError] = useState<string | null>(propError);

  // Fetch warehouses if not provided as props
  useEffect(() => {
    if (!propWarehouses) {
      fetchWarehouses();
    } else {
      setWarehouses(propWarehouses);
      setIsLoading(propIsLoading);
      setError(propError);
    }
  }, [propWarehouses, propIsLoading, propError, refreshTrigger]);

  const fetchWarehouses = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/warehouses');
      if (!response.ok) {
        throw new Error('Failed to fetch warehouses');
      }

      const data = await response.json();
      if (data.success && data.data) {
        // Transform API data to display format
        const transformedWarehouses = data.data.map((warehouse: any) => ({
          ...warehouse,
          capacityFormatted: warehouse.capacity ? `${warehouse.capacity.toLocaleString()} units` : 'N/A',
          status: warehouse.isActive !== undefined ? (warehouse.isActive ? 'active' : 'inactive') : 'active',
          createdAtFormatted: warehouse.createdAt ? new Date(warehouse.createdAt).toLocaleDateString() : 'N/A',
          updatedAtFormatted: warehouse.updatedAt ? new Date(warehouse.updatedAt).toLocaleDateString() : 'N/A',
        }));

        setWarehouses(transformedWarehouses);
      } else {
        throw new Error(data.message || 'Failed to fetch warehouses');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch warehouses';
      setError(errorMessage);
      console.error('Error fetching warehouses:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRow = async (warehouseId: string) => {
    const newExpanded = new Set(expandedRows);
    
    if (newExpanded.has(warehouseId)) {
      newExpanded.delete(warehouseId);
    } else {
      newExpanded.add(warehouseId);
      
      // Load locations if not already loaded
      if (!warehouseLocations[warehouseId]) {
        await loadWarehouseLocations(warehouseId);
      }
    }
    
    setExpandedRows(newExpanded);
  };

  const loadWarehouseLocations = async (warehouseId: string) => {
    setLoadingLocations(prev => new Set(prev).add(warehouseId));
    
    try {
      const response = await fetch(`/api/warehouses/${warehouseId}/locations`);
      if (response.ok) {
        const data = await response.json();
        setWarehouseLocations(prev => ({
          ...prev,
          [warehouseId]: data.data || []
        }));
      } else {
        console.error('Failed to load warehouse locations');
        setWarehouseLocations(prev => ({
          ...prev,
          [warehouseId]: []
        }));
      }
    } catch (error) {
      console.error('Error loading warehouse locations:', error);
      setWarehouseLocations(prev => ({
        ...prev,
        [warehouseId]: []
      }));
    } finally {
      setLoadingLocations(prev => {
        const newSet = new Set(prev);
        newSet.delete(warehouseId);
        return newSet;
      });
    }
  };

  // Filter warehouses based on search term
  const filteredWarehouses = warehouses.filter(warehouse => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      (warehouse.name || '').toLowerCase().includes(searchLower) ||
      (warehouse.location_id || '').toLowerCase().includes(searchLower) ||
      (warehouse.location || '').toLowerCase().includes(searchLower) ||
      (warehouse.manager || '').toLowerCase().includes(searchLower)
    );
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        <span className="ml-2">Loading warehouses...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading warehouses: {error}
      </div>
    );
  }

  if (filteredWarehouses.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {searchTerm ? `No warehouses found matching "${searchTerm}"` : 'No warehouses found'}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filteredWarehouses.map((warehouse) => (
        <Card key={warehouse._id} className="overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleRow(warehouse._id)}
                  className="h-8 w-8 p-0"
                >
                  {expandedRows.has(warehouse._id) ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
                <div>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Building2 className="h-5 w-5" />
                    <span>{warehouse.name}</span>
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {warehouse.location_id} • {warehouse.location}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge variant={warehouse.status === 'active' ? 'default' : 'secondary'}>
                  {warehouse.status}
                </Badge>
                <Badge variant={warehouse.isBinTracked ? 'default' : 'outline'}>
                  {warehouse.isBinTracked ? 'Bin Tracked' : 'Basic'}
                </Badge>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="flex items-center space-x-2 text-sm">
                <Package className="h-4 w-4 text-muted-foreground" />
                <span>Capacity: {warehouse.capacityFormatted}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>Manager: {warehouse.manager}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>Contact: {warehouse.contact}</span>
              </div>
            </div>
          </CardHeader>

          {expandedRows.has(warehouse._id) && (
            <CardContent className="pt-0 border-t">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span>Warehouse Locations</span>
                  </h4>
                  {loadingLocations.has(warehouse._id) && (
                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                  )}
                </div>

                {(() => {
                  const locations = warehouseLocations[warehouse._id];
                  return locations ? (
                    locations.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {locations.map((location) => (
                          <LocationCard
                            key={location._id}
                            location={location}
                            isReadOnly={true}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6 text-muted-foreground">
                        <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No locations configured for this warehouse</p>
                        <p className="text-xs mt-1">
                          Edit the warehouse to add specific locations
                        </p>
                      </div>
                    )
                  ) : (
                    <div className="text-center py-6">
                      <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
                      <p className="text-sm text-muted-foreground mt-2">Loading locations...</p>
                    </div>
                  );
                })()}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-2 pt-4 border-t">
                  {onWarehouseClick && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onWarehouseClick(warehouse)}
                    >
                      View Details
                    </Button>
                  )}
                  {onWarehouseEdit && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onWarehouseEdit(warehouse)}
                    >
                      Edit Warehouse
                    </Button>
                  )}
                  {onWarehouseDelete && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onWarehouseDelete(warehouse)}
                      className="text-red-500 hover:text-red-700"
                    >
                      Delete
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  );
}
