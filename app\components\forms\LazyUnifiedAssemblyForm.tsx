'use client';

import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import { LoadingOverlay } from '@/app/components/data-display/loading';
import dynamic from 'next/dynamic';

// Loading component using standardized LoadingOverlay
const loading = () => <LoadingOverlay message="Loading Assembly Form..." />;

// Dynamically import the UnifiedAssemblyForm component
const DynamicUnifiedAssemblyForm = dynamic(
  () => import('./UnifiedAssemblyForm').then(mod => ({ default: mod.UnifiedAssemblyForm })),
  {
    loading,
    ssr: false,
  }
);

// Props interface
interface LazyUnifiedAssemblyFormProps {
  isOpen: boolean;
  onClose: () => void;
  assemblyId?: string | undefined;
  mode?: 'create' | 'edit';
  onSuccess?: () => void;
}

/**
 * Lazy-loaded wrapper for UnifiedAssemblyForm
 * This component dynamically imports the actual form component only when needed
 */
export function LazyUnifiedAssemblyForm({ isOpen, onClose, assemblyId, mode = 'create', onSuccess }: LazyUnifiedAssemblyFormProps) {
  // Only render the form when isOpen is true
  if (!isOpen) return null;
  
  return (
    <AssemblyFormProvider assemblyId={assemblyId}>
      <DynamicUnifiedAssemblyForm
        isOpen={isOpen}
        onClose={onClose}
        assemblyId={assemblyId}
        mode={mode}
        onSuccess={onSuccess || (() => {})}
      />
    </AssemblyFormProvider>
  );
}
