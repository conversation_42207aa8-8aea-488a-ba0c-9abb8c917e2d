// File: app/api/parts/route.ts

import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { handleMongoDBError } from '@/app/services/mongodb'; // fetchParts and addPart removed
import { createPart, CreatePartDto, getAllParts } from '@/app/services/part.service';
import { z } from 'zod';
import withDatabase from '@/app/middlewares/withDatabase';

// PERFORMANCE OPTIMIZATION: Set MAX_LIMIT to accommodate all parts in database
const MAX_LIMIT = 150; // Increased to accommodate all 112+ parts in database
const DEFAULT_LIMIT = 20; // Explicit default limit

// Helper function to generate request ID using crypto for better uniqueness
function generateRequestId(): string {
  // Use crypto.randomUUID if available, fallback to timestamp-based ID
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for environments without crypto.randomUUID
  const timestamp = Date.now().toString(36);
  const counter = (globalThis as any).__requestIdCounter = ((globalThis as any).__requestIdCounter || 0) + 1;
  return `req-${timestamp}-${counter.toString(36)}`;
}

// Zod schema for strict validation of CreatePartDto
const CreatePartValidationSchema = z.object({
  partNumber: z.string().trim().min(1, { message: 'Part number is required.' }),
  name: z.string().trim().min(1, { message: 'Part name is required.' }),
  businessName: z.string().trim().nullable().optional(), // NEW FIELD: Human-readable business name
  description: z.string().trim().nullable().optional(),
  technicalSpecs: z.string().trim().nullable().optional(),
  isManufactured: z.boolean(),
  reorderLevel: z.number().int().min(0).nullable().optional(),
  status: z.enum(['active', 'inactive', 'obsolete']),

  // NEW: Part Master Data Planning Parameters
  planningMethod: z.string().trim().nullable().optional(),                    // Planning method (MRP, EOQ, etc.)
  safetyStockLevel: z.number().int().min(0).nullable().optional(),            // Safety stock level for this part
  maximumStockLevel: z.number().int().min(0).nullable().optional(),           // Maximum stock level for this part
  leadTimeDays: z.number().int().min(0).nullable().optional(),                // Lead time in days
  averageDailyUsage: z.number().min(0).nullable().optional(),                 // Average daily usage

  // REMOVED: inventory object - V4 Schema uses separate inventories collection
  // inventory: z.object({ ... }) - Use /api/inventories endpoints instead

  supplierId: z.string().trim().min(1).nullable().optional(), // Further ObjectId validation happens in service/model
  unitOfMeasure: z.string().trim().min(1, { message: 'Unit of measure is required.' }),
  standardCost: z.number().min(0, { message: 'Standard cost cannot be negative.' }), // Changed from costPrice to match target schema
  abcClassification: z.string().trim().nullable().optional(), // NEW FIELD: ABC classification (A, B, C)
  categoryId: z.string().trim().min(1).nullable().optional(), // Further ObjectId validation happens in service/model
});

async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  const url = new URL(request.url); // Move outside try block for error handler access

  try {
    console.log('[API] GET /api/parts - Fetching parts with dual-service architecture');

    // Extract request context for dual-service architecture
    const userId = request.headers.get('x-user-id') || undefined;
    const requestId = request.headers.get('x-request-id') || generateRequestId();

    // PERFORMANCE OPTIMIZATION: Enhanced pagination with explicit defaults
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = Math.min(
      parseInt(url.searchParams.get('limit') || DEFAULT_LIMIT.toString(), 10),
      MAX_LIMIT
    );

    // Validate pagination parameters
    if (page < 1) {
      return NextResponse.json(
        { error: 'Page must be greater than 0', data: null },
        { status: 400 }
      );
    }
    if (limit < 1) {
      return NextResponse.json(
        { error: 'Limit must be greater than 0', data: null },
        { status: 400 }
      );
    }

    // Sorting
    const sortField = url.searchParams.get('sortField') || 'updatedAt';
    const sortOrder = (url.searchParams.get('sortOrder') === 'asc') ? 1 : -1;

    // Filters
    const filter: any = {};
    if (url.searchParams.get('status')) {
      filter.status = url.searchParams.get('status');
    }
    if (url.searchParams.get('isManufactured') !== null) {
      filter.isManufactured =
        url.searchParams.get('isManufactured') === 'true';
    }
    if (url.searchParams.get('categoryId')) {
      const cat = url.searchParams.get('categoryId')!;
      try {
        filter.categoryId = new mongoose.Types.ObjectId(cat);
      } catch (err) {
        console.error(`Invalid categoryId: ${cat}`, err);
        filter.categoryId = cat;
      }
    }
    // Stock range - support both new stockLevels.finished and legacy currentStock
    if (
      url.searchParams.has('minStock') ||
      url.searchParams.has('maxStock')
    ) {
      // Use finished stock as the primary stock level for filtering
      filter['inventory.stockLevels.finished'] = {};
      if (url.searchParams.get('minStock')) {
        filter['inventory.stockLevels.finished'].$gte = parseInt(
          url.searchParams.get('minStock')!,
          10
        );
      }
      if (url.searchParams.get('maxStock')) {
        filter['inventory.stockLevels.finished'].$lte = parseInt(
          url.searchParams.get('maxStock')!,
          10
        );
      }
    }

    // Pack options and call service
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      // includeSubParts is not handled by getAllParts directly; if needed, requires specific logic.
      // includeInventory is implicitly true as inventory is embedded in the Part model.
    };
    console.log('[API] getAllParts options:', options);

    // Use unified service with inventories collection integration
    const serviceOptions = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      status: filter.status,
      search: url.searchParams.get('search') || undefined,
    };

    const result = await getAllParts(serviceOptions);

    const duration = Date.now() - startTime;
    console.log(`[API] GET completed in ${duration}ms`);

    // PERFORMANCE OPTIMIZATION: Add caching and compression headers
    const response = NextResponse.json({
      data: result?.parts || [],
      pagination: {
        totalCount: result?.pagination?.totalCount || 0,
        page,
        limit,
        totalPages: Math.ceil((result?.pagination?.totalCount || 0) / limit),
        hasMore: (page * limit) < (result?.pagination?.totalCount || 0)
      },
      error: null,
      meta: {
        duration,
        count: result?.parts?.length || 0,
        totalCount: result?.pagination?.totalCount || 0,
        page,
        limit,
        service: 'unified-architecture'
      },
    });

    // Add performance headers
    response.headers.set('X-Response-Time', `${duration}ms`);
    response.headers.set('X-Total-Count', (result?.pagination?.totalCount || 0).toString());

    // Add caching headers for better performance (cache for 30 seconds)
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');
    response.headers.set('ETag', `"parts-${page}-${limit}-${Date.now()}"`);

    // FIXED: Removed incorrect Content-Encoding header that was causing decoding errors
    // The response is not actually compressed, so we shouldn't set Content-Encoding: gzip

    return response;
  } catch (error: any) {
    const duration = Date.now() - startTime;

    // PRODUCTION FIX: Enhanced error logging for debugging
    console.error(`[API] GET /api/parts error (${duration}ms):`, {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      duration,
      originalError: error.originalError?.message,
      requestParams: {
        page: url.searchParams.get('page'),
        limit: url.searchParams.get('limit'),
        search: url.searchParams.get('search'),
        status: url.searchParams.get('status')
      }
    });

    // Determine if this is a timeout or aggregation failure
    let errorMessage = 'Failed to fetch parts';
    let statusCode = 500;

    if (error.message.includes('timeout') || error.message.includes('maxTimeMS')) {
      errorMessage = 'Database query timed out. The system may be experiencing high load. Please try again.';
      statusCode = 504;
    } else if (error.message.includes('Aggregation pipeline failed')) {
      errorMessage = 'Complex data processing failed. Basic part data may be available with limited functionality.';
      statusCode = 503;
    } else if (error.message.includes('Both optimized aggregation and simple fallback failed')) {
      errorMessage = 'Database is currently unavailable. Please try again later.';
      statusCode = 503;
    }

    const { message: mongoMessage, status: mongoStatus } = handleMongoDBError(error);

    return NextResponse.json(
      {
        data: null,
        error: errorMessage || mongoMessage,
        meta: {
          duration,
          errorType: error.name || 'UnknownError',
          isTimeout: error.message.includes('timeout') || error.message.includes('maxTimeMS'),
          isAggregationFailure: error.message.includes('Aggregation pipeline failed'),
          timestamp: new Date().toISOString()
        }
      },
      { status: statusCode || mongoStatus }
    );
  }
}

async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] POST /api/parts - Creating new part');
    const requestBody = await request.json();

    const validationResult = CreatePartValidationSchema.safeParse(requestBody);

    if (!validationResult.success) {
      const duration = Date.now() - startTime;
      console.error(`[API] POST /api/parts - Validation Error (${duration}ms):`, validationResult.error.flatten());
      return NextResponse.json(
        {
          data: null,
          error: 'Invalid part data provided.',
          errorDetails: validationResult.error.flatten(), // Provides detailed validation errors
          meta: { duration },
        },
        { status: 400 }
      );
    }

    // Use the validated data, which conforms to CreatePartDto
    const partData = validationResult.data;

    console.log('[API] Calling createPart service with validated data:', partData);
    const savedPart = await createPart(partData as CreatePartDto); // Cast is safe here due to Zod validation

    const duration = Date.now() - startTime;
    console.log(`[API] POST /api/parts - Part created successfully in ${duration}ms`, { partId: savedPart._id });

    return NextResponse.json(
      { data: savedPart, error: null, meta: { duration } },
      { status: 201 }
    );
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] POST /api/parts - Error (${duration}ms):`, error);
    
    let errorMessage = error.message || 'An unexpected error occurred.';
    let errorStatus = error.status || 500; 

    // If it's a known error structure from handleMongoDBError or similar custom error
    if (!(error.status && error.message && typeof error.status === 'number')) { 
        const parsedError = handleMongoDBError(error); // Assumes handleMongoDBError is robust
        errorMessage = parsedError.message;
        errorStatus = parsedError.status;
    }

    return NextResponse.json(
      { data: null, error: errorMessage, meta: { duration } },
      { status: errorStatus }
    );
  }
}

export const GET  = withDatabase(handleGET);
export const POST = withDatabase(handlePOST);