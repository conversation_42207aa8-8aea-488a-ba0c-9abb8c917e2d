'use client';

import React from 'react';
import { UnifiedCard, UnifiedCardProps } from '../UnifiedCard';
import { getAccessibilityAttributes, shouldReduceAnimations } from '@/app/utils/accessibility';

interface AccessibleUnifiedCardProps extends UnifiedCardProps {
  /** Accessible label for screen readers */
  accessibleLabel?: string;
  /** Role for the card (default: 'region' for content cards, 'button' for interactive cards) */
  role?: string;
  /** Whether this card represents a landmark region */
  isLandmark?: boolean;
  /** Accessible description for complex cards */
  accessibleDescription?: string;
  /** Whether to announce changes to screen readers */
  announceChanges?: boolean;
  /** Custom keyboard navigation handler */
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

/**
 * Accessible wrapper for UnifiedCard that ensures WCAG 2.1 AA compliance
 * Provides enhanced keyboard navigation, screen reader support, and focus management
 */
export const AccessibleUnifiedCard = React.forwardRef<HTMLDivElement, AccessibleUnifiedCardProps>(
  ({
    accessibleLabel,
    role,
    isLandmark = false,
    accessibleDescription,
    announceChanges = false,
    onKeyDown,
    onClick,
    title,
    variant = 'default',
    animate = true,
    children,
    ...props
  }, ref) => {
    // Use React's useId() hook for stable server/client ID generation
    // This prevents hydration mismatches that occurred with Math.random() based IDs
    const baseId = React.useId();
    const cardId = `unified-card-${baseId}`;
    const descriptionId = `card-description-${baseId}`;
    
    // Determine appropriate role based on interactivity and variant
    const cardRole = React.useMemo(() => {
      if (role) return role;
      if (onClick || variant === 'action' || variant === 'interactive') return 'button';
      if (isLandmark) return 'region';
      return 'article'; // Default for content cards
    }, [role, onClick, variant, isLandmark]);

    // Check if animations should be reduced for accessibility
    const shouldReduceMotion = shouldReduceAnimations();
    const effectiveAnimate = animate && !shouldReduceMotion;

    // Generate accessibility attributes
    const accessibilityAttributes = getAccessibilityAttributes({
      role: cardRole,
      expanded: false, // Could be enhanced based on card state
      selected: false, // Could be enhanced based on card state
      disabled: false, // Could be enhanced based on props
      required: false,
      invalid: false,
      describedBy: accessibleDescription ? descriptionId : undefined,
      labelledBy: title ? `${cardId}-title` : undefined,
      label: accessibleLabel || title || undefined
    });

    // Enhanced keyboard navigation
    const handleKeyDown = (event: React.KeyboardEvent) => {
      // Call custom handler first
      if (onKeyDown) {
        onKeyDown(event);
      }

      // Handle standard keyboard interactions for interactive cards
      if (onClick && (cardRole === 'button' || variant === 'action' || variant === 'interactive')) {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
          onClick(event as any);
        }
      }

      // Handle escape key for dismissible cards
      if (event.key === 'Escape' && onClick) {
        // Could be enhanced to handle dismissible behavior
      }
    };

    // Enhanced click handler with accessibility considerations
    const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
      if (onClick) {
        onClick(event as any);
        
        // Announce action to screen readers if needed
        if (announceChanges && accessibleLabel) {
          // This would typically use a live region or aria-live
          console.log(`Action performed: ${accessibleLabel}`);
        }
      }
    };

    return (
      <>
        <UnifiedCard
          ref={ref}
          id={cardId}
          role={cardRole}
          tabIndex={onClick ? 0 : undefined}
          title={title || ''}
          variant={variant}
          animate={effectiveAnimate}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          {...accessibilityAttributes}
          {...props}
        >
          {/* Hidden title for screen readers if different from visible title */}
          {title && (
            <span id={`${cardId}-title`} className="sr-only">
              {accessibleLabel || title}
            </span>
          )}
          
          {children}
          
          {/* Hidden description for screen readers */}
          {accessibleDescription && (
            <span id={descriptionId} className="sr-only">
              {accessibleDescription}
            </span>
          )}
        </UnifiedCard>
        
        {/* Live region for announcements */}
        {announceChanges && (
          <div
            aria-live="polite"
            aria-atomic="true"
            className="sr-only"
            id={`${cardId}-announcements`}
          />
        )}
      </>
    );
  }
);

AccessibleUnifiedCard.displayName = "AccessibleUnifiedCard";
