# Utility Functions

This directory contains utility functions used throughout the application.

## Table of Contents

- [Debounce](#debounce)
- [Throttle](#throttle)
- [Debounced Search](#debounced-search)
- [Class Name Utility](#class-name-utility)

## Debounce

The `debounce` utility creates a debounced version of a function that delays its execution until after a specified wait time has elapsed since the last time the debounced function was called.

### Features

- Configurable wait time
- Optional immediate execution on first call
- Cancellation of pending executions
- Flushing (immediate execution of pending calls)
- Maximum wait time option
- Trailing and leading edge execution options

### Usage

```typescript
import { debounce } from '@/app/lib/utils';

// Basic usage
const debouncedSearch = debounce(searchFunction, 300);

// With immediate execution
const debouncedSave = debounce(saveFunction, 500, { immediate: true });

// With maximum wait time
const debouncedUpdate = debounce(updateFunction, 300, { maxWait: 1000 });

// Cancel a pending execution
debouncedSearch.cancel();

// Force immediate execution of a pending call
debouncedSave.flush();

// Check if there is a pending execution
if (debouncedUpdate.pending()) {
  console.log('Update is pending');
}
```

## Throttle

The `throttle` utility creates a throttled version of a function that only executes at most once per every specified wait time.

### Features

- Configurable wait time
- Optional leading and trailing edge execution
- Cancellation of pending executions

### Usage

```typescript
import { throttle } from '@/app/lib/utils';

// Basic usage
const throttledScroll = throttle(handleScroll, 100);

// With leading: false option (only trailing edge)
const throttledResize = throttle(handleResize, 200, { leading: false });

// With trailing: false option (only leading edge)
const throttledClick = throttle(handleClick, 300, { trailing: false });

// Cancel a pending execution
throttledScroll.cancel();
```

## Debounced Search

The `createDebouncedSearch` utility creates a specialized debounced function for search inputs that combines debouncing with a minimum query length check.

### Features

- Configurable wait time
- Minimum query length threshold
- Callbacks for empty and too-short queries
- All debounce options available

### Usage

```typescript
import { createDebouncedSearch } from '@/app/lib/utils';

// Basic usage
const debouncedSearch = createDebouncedSearch(
  async (query) => {
    const results = await api.search(query);
    setSearchResults(results);
  }
);

// With custom options
const debouncedSearch = createDebouncedSearch(
  async (query) => {
    const results = await api.search(query);
    setSearchResults(results);
  },
  {
    wait: 500,
    minLength: 3,
    onEmpty: () => setSearchResults([]),
    onTooShort: (query) => setMessage(`Type at least 3 characters (${query.length}/3)`)
  }
);

// Use it in a component
const handleSearchChange = (e) => {
  const query = e.target.value;
  setSearchQuery(query);
  debouncedSearch(query);
};

// Clean up on unmount
useEffect(() => {
  return () => {
    debouncedSearch.cancel();
  };
}, [debouncedSearch]);
```

## Class Name Utility

The `cn` utility is a wrapper around `clsx` and `tailwind-merge` that makes it easy to conditionally apply Tailwind CSS classes.

### Usage

```typescript
import { cn } from '@/app/lib/utils';

// Basic usage
<div className={cn(
  'base-class',
  isActive && 'active-class',
  variant === 'primary' ? 'primary-class' : 'secondary-class'
)} />
```
