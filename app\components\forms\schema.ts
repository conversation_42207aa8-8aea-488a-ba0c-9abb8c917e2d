import { z } from "zod";

// Define the status options for assemblies
export const assemblyStatusOptions = [
  "active",
  "pending_review",
  "design_phase",
  "design_complete",
  "obsolete"
] as const;

// Define the unit of measure options
export const unitOfMeasureOptions = [
  "ea",
  "ft",
  "m",
  "kg",
  "g",
  "lb",
  "oz",
  "l",
  "ml",
  "mm",
  "cm",
  "in"
] as const;

// Schema for a part requirement within an assembly with hierarchical support
export const partRequirementSchema: z.ZodSchema = z.lazy(() => z.object({
  partId: z.string().min(1, "Part ID is required"),
  quantityRequired: z.number().min(0.001, "Quantity must be greater than 0"),
  unitOfMeasure: z.enum(unitOfMeasureOptions).default("ea"),

  // Additional fields for UI display/management
  name: z.string().optional(),
  description: z.string().optional(),
  partNumber: z.string().optional(),
  currentStock: z.number().optional(),
  isAssembly: z.boolean().optional(),
  stockStatus: z.enum(["sufficient", "insufficient", "unknown"]).optional(),

  // Hierarchical support
  children: z.array(partRequirementSchema).optional().default([]),
}));

// Schema for the assembly form
export const assemblyFormSchema = z.object({
  assemblyCode: z.string().min(1, "Assembly code is required"),
  name: z.string().min(1, "Assembly name is required"),
  description: z.string().optional(),
  status: z.enum(assemblyStatusOptions),
  version: z.number().int().positive(),
  isTopLevel: z.boolean(),
  manufacturingInstructions: z.string().nullable(),
  estimatedBuildTime: z.string().nullable(),
  productId: z.string().nullable(),
  parentId: z.string().nullable(),
  partsRequired: z.array(partRequirementSchema).min(1, "Assembly must have at least one part"),
});

// Optional fields schema for validation during form entry (before submission)
export const partialAssemblyFormSchema = assemblyFormSchema.partial({
  partsRequired: true,
}).extend({
  partsRequired: z.array(partRequirementSchema).optional(),
});

// TypeScript types derived from the schemas
export type PartRequirement = z.infer<typeof partRequirementSchema>;
export type AssemblyFormValues = z.infer<typeof assemblyFormSchema>;
export type PartialAssemblyFormValues = z.infer<typeof partialAssemblyFormSchema>;

// Default values for new assemblies
export const defaultValues: PartialAssemblyFormValues = {
  assemblyCode: "",
  name: "",
  description: "",
  status: "active",
  productId: null,
  parentId: null,
  isTopLevel: true,
  version: 1,
  manufacturingInstructions: null,
  estimatedBuildTime: null,
  partsRequired: []
};

// Function to check if stock is sufficient for a part requirement
export const checkStockStatus = (part: PartRequirement): "sufficient" | "insufficient" | "unknown" => {
  if (part.currentStock === undefined || part.currentStock === null) {
    return "unknown";
  }
  return part.currentStock >= part.quantityRequired ? "sufficient" : "insufficient";
};

// Part search result interface
export interface PartSearchResult {
  _id: string;
  partNumber?: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string;
  inventory?: {
    currentStock: number;
  };
  reorder_level?: number;
  isAssembly?: boolean;
  category?: string;
  unitOfMeasure?: string;
  technical_specs?: string;
}