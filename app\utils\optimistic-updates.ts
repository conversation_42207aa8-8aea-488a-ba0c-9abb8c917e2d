/**
 * Utility functions for optimistic UI updates
 */

/**
 * Creates an optimistic update for an array of items
 * @param items The current array of items
 * @param updatedItem The updated item
 * @param idField The field to use as the ID (default: '_id')
 * @returns The updated array of items
 */
export function optimisticUpdate<T extends Record<string, any>>(
  items: T[],
  updatedItem: T,
  idField: keyof T = '_id' as keyof T
): T[] {
  return items.map(item => 
    item[idField] === updatedItem[idField] ? { ...item, ...updatedItem } : item
  );
}

/**
 * Creates an optimistic update for an array of items using ID and partial data
 * @param items The current array of items
 * @param itemId The ID of the item to update
 * @param partialData The partial data to update
 * @param idField The field to use as the ID (default: '_id')
 * @returns The updated array of items
 */
export function optimisticUpdateById<T extends Record<string, any>>(
  items: T[],
  itemId: string | number,
  partialData: Partial<T>,
  idField: keyof T = '_id' as keyof T
): T[] {
  return items.map(item => 
    String(item[idField]) === String(itemId) ? { ...item, ...partialData } : item
  );
}

/**
 * Creates an optimistic delete for an array of items
 * @param items The current array of items
 * @param itemId The ID of the item to delete
 * @param idField The field to use as the ID (default: '_id')
 * @returns The updated array of items
 */
export function optimisticDelete<T extends Record<string, any>>(
  items: T[],
  itemId: string | number,
  idField: keyof T = '_id' as keyof T
): T[] {
  return items.filter(item => item[idField] !== itemId);
}

/**
 * Creates an optimistic add for an array of items
 * @param items The current array of items
 * @param newItem The new item to add
 * @returns The updated array of items
 */
export function optimisticAdd<T>(items: T[], newItem: T): T[] {
  return [...items, newItem];
}
