"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import React from 'react';
import { Assembly } from '../tables/AssembliesTable/types';

interface AssemblyStatusProps {
  assemblies: Assembly[];
}

const AssemblyStatus: React.FC<AssemblyStatusProps> = ({ assemblies }) => {
  const { theme } = useTheme();

  // Group assemblies by status
  const statusGroups = assemblies.reduce((acc, assembly) => {
    const status = assembly.status || 'active';
    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(assembly);
    return acc;
  }, {} as Record<string, Assembly[]>);

  // Get count of assemblies in each status
  const statusCounts = Object.entries(statusGroups).reduce((acc, [status, items]) => {
    acc[status] = items.length;
    return acc;
  }, {} as Record<string, number>);

  // Status color mapping using semantic colors
  const statusColors: Record<string, string> = {
    'active': 'bg-success/10 text-success',
    'pending_review': 'bg-info/10 text-info',
    'obsolete': 'bg-destructive/10 text-destructive',
    'in_production': 'bg-warning/10 text-warning',
  };

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-background rounded-3xl p-6 relative overflow-hidden shadow-md dark:shadow-gray-900/30"
    >
      <div className="flex justify-between items-start">
        <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary">Assembly Status</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-8 h-8 bg-gray-200 dark:bg-sidebar rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={16} className="text-gray-600 dark:text-text-secondary" />
        </motion.div>
      </div>

      <div className="mt-4">
        <p className="text-lg text-gray-700 dark:text-text-secondary">Final assemblies in progress</p>
      </div>

      {/* Assembly status summary */}
      <div className="mt-4 grid grid-cols-2 gap-3">
        {Object.entries(statusCounts).map(([status, count]) => (
          <div key={status} className="bg-gray-50 dark:bg-sidebar p-3 rounded-lg">
            <div className="text-2xl font-semibold text-gray-800 dark:text-text-primary">{count}</div>
            <div className="text-sm text-gray-500 dark:text-text-secondary capitalize">{status.replace('_', ' ')}</div>
          </div>
        ))}
      </div>

      {/* Assembly list - showing only the top assemblies for each status */}
      <div className="mt-6 space-y-4 max-h-96 overflow-y-auto">
        {Object.entries(statusGroups).map(([status, items]) => (
          <React.Fragment key={status}>
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-foreground capitalize">{status.replace('_', ' ')}</h4>
              <span className={`text-xs px-2 py-1 rounded-full ${statusColors[status] || statusColors.active}`}>
                {items.length} assemblies
              </span>
            </div>

            {/* Show only the first 3 assemblies from each status group */}
            {items.slice(0, 3).map((assembly, index) => (
              <motion.div
                key={assembly._id}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 + (index * 0.05) }}
                className="bg-gray-50 dark:bg-sidebar rounded-xl p-3"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-text-primary">{assembly.name}</h4>
                    <p className="text-xs text-gray-500 dark:text-text-secondary">
                      {assembly.partsRequired?.length || 0} parts • ID: {assembly.assemblyCode}
                    </p>
                  </div>
                  <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-xs font-semibold px-2 py-0.5 rounded-full">
                    In Progress
                  </div>
                </div>
              </motion.div>
            ))}

            {/* Show "View more" if there are more than 3 assemblies */}
            {items.length > 3 && (
              <motion.div
                whileHover={{ backgroundColor: theme.isDark ? 'rgba(75, 85, 99, 0.3)' : 'rgba(243, 244, 246, 1)' }}
                className="text-center p-2 text-sm text-blue-600 dark:text-blue-400 cursor-pointer rounded"
              >
                View {items.length - 3} more {status.replace('_', ' ')} assemblies
              </motion.div>
            )}
          </React.Fragment>
        ))}
      </div>
    </motion.div>
  );
};

export default AssemblyStatus;