import { checkDatabaseHealth, getConnectionState } from '@/app/lib/mongodb';
import withDatabase from '@/app/middlewares/withDatabase';
import { NextRequest, NextResponse } from 'next/server';
import pkg from '../../../package.json';
import { env } from '../../utils/env';

/**
 * GET handler for retrieving application status information
 * Supports query parameters for additional monitoring data:
 * - ?metrics=true - Include recent monitoring metrics
 * - ?stats=true - Include database statistics
 * @param request - The incoming request
 * @returns JSON response with status information
 */
async function handleGET(request: NextRequest) {
  try {
    console.log('[API] GET /api/status - Checking application status');

    // Parse query parameters
    const url = new URL(request.url);
    const includeMetrics = url.searchParams.get('metrics') === 'true';
    const includeStats = url.searchParams.get('stats') === 'true';

    // Check database health
    const dbHealth = await checkDatabaseHealth();

    // Get connection state
    const connectionState = getConnectionState();

    // Get application version from package.json
    const appVersion = pkg.version || 'unknown';

    // Prepare status response
    const status: any = {
      status: dbHealth.status === 'healthy' ? 'ok' : 'degraded',
      timestamp: new Date().toISOString(),
      application: {
        name: pkg.name || 'trend-ims',
        version: appVersion,
        environment: env.NODE_ENV || 'development',
        uptime: typeof process !== 'undefined' ? process.uptime() : 0
      },
      database: {
        status: dbHealth.status,
        message: dbHealth.message,
        connectionState: connectionState.state,
        readyState: connectionState.readyState
      }
    };

    // Include additional metrics if requested (merged from db-status)
    if (includeMetrics) {
      try {
        const { getRecentMetrics } = await import('@/app/lib/mongodb-monitoring');
        status.metrics = getRecentMetrics();
      } catch (error) {
        console.warn('Failed to load monitoring metrics:', error);
        status.metrics = null;
      }
    }

    // Include database stats if requested (merged from db-status)
    if (includeStats) {
      try {
        const { getDatabaseStats } = await import('@/app/lib/mongodb-monitoring');
        status.stats = await getDatabaseStats();
      } catch (error) {
        console.warn('Failed to load database stats:', error);
        status.stats = null;
      }
    }

    // Use appropriate status code based on database health
    const statusCode = dbHealth.status === 'healthy' ? 200 :
                      dbHealth.status === 'warning' ? 200 : 503;

    return NextResponse.json(status, { status: statusCode });
  } catch (error) {
    console.error('[API] Error in GET /api/status:',
      error instanceof Error ? error.message : String(error));

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Wrap the handler with the withDatabase middleware to ensure connection is established
export const GET = withDatabase(handleGET); 