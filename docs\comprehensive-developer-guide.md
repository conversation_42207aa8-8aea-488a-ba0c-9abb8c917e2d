# Comprehensive Developer Guide - Trend IMS

## Table of Contents
1. [Introduction & Overview](#introduction--overview)
2. [Component Standardization Patterns](#component-standardization-patterns)
3. [Form Development Patterns](#form-development-patterns)
4. [Architecture Patterns](#architecture-patterns)
5. [Theme Integration System](#theme-integration-system)
6. [File Organization & Import Conventions](#file-organization--import-conventions)
7. [Development Workflow](#development-workflow)
8. [Migration Patterns](#migration-patterns)
9. [Performance & Accessibility Standards](#performance--accessibility-standards)
10. [Troubleshooting & Common Patterns](#troubleshooting--common-patterns)

## Introduction & Overview

This comprehensive guide serves as the definitive reference for developing and maintaining the Trend IMS codebase following our major UI/UX standardization overhaul. We achieved **95.5% component standardization** (exceeding our 95% target) through systematic migration of legacy components to modern, unified patterns.

### Key Achievements
- **DataTable System**: 100% migration from 7 different table implementations
- **UnifiedCard System**: 95% standardization with 8 variants and backward compatibility
- **Error Handling**: 95% standardization across <PERSON>rror<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormErrorDisplay
- **Loading States**: 90% standardization with LoadingSpinner, LoadingSkeleton, LoadingOverlay
- **Theme Integration**: 96% achievement with CSS custom properties and semantic variables
- **Form Validation**: Zod + React Hook Form pattern standardized across major forms
- **Performance**: 15-20% bundle size reduction through component consolidation

### Technology Stack
- **Frontend**: Next.js 14 with App Router, React 18, TypeScript
- **Styling**: Tailwind CSS with CSS custom properties for theming
- **Component Variants**: CVA (Class Variance Authority) for type-safe component variants
- **Form Management**: React Hook Form + Zod for validation
- **State Management**: React Context + Custom hooks
- **Testing**: Jest + React Testing Library + Playwright
- **Architecture**: Server/Client component pattern with proper hydration boundaries

## Component Standardization Patterns

### DataTable System (100% Standardization)

Our unified DataTable system replaces all legacy table implementations using TanStack Table v8.

#### Basic Usage
```tsx
import { DataTable } from '@/app/components/data-display/tables/DataTable'
import { columns } from './columns'

export function InventoryTable({ data }: { data: InventoryItem[] }) {
  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="partNumber"
      filterableColumns={[
        { id: 'category', title: 'Category' },
        { id: 'status', title: 'Status' }
      ]}
    />
  )
}
```

#### Column Definition Pattern
```tsx
import { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '@/app/components/data-display/tables/DataTableColumnHeader'

export const columns: ColumnDef<InventoryItem>[] = [
  {
    accessorKey: 'partNumber',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Part Number" />
    ),
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('partNumber')}</div>
    ),
  },
  // ... more columns
]
```

#### Mobile Responsiveness
DataTable automatically falls back to card layout on mobile devices:
```tsx
// Automatic mobile fallback - no additional code needed
<DataTable
  columns={columns}
  data={data}
  mobileCardComponent={InventoryCard} // Optional custom mobile card
/>
```

### UnifiedCard System (95% Standardization)

The UnifiedCard component provides 8 standardized variants with CVA-based type safety.

#### Available Variants
```tsx
import { UnifiedCard } from '@/app/components/layout/cards/UnifiedCard'

// Basic card
<UnifiedCard variant="default">
  <UnifiedCard.Header>
    <UnifiedCard.Title>Card Title</UnifiedCard.Title>
  </UnifiedCard.Header>
  <UnifiedCard.Content>
    Card content goes here
  </UnifiedCard.Content>
</UnifiedCard>

// Interactive card with hover effects
<UnifiedCard variant="interactive" onClick={handleClick}>
  <UnifiedCard.Header>
    <UnifiedCard.Title>Interactive Card</UnifiedCard.Title>
  </UnifiedCard.Header>
</UnifiedCard>

// Status cards with semantic colors
<UnifiedCard variant="success">
  <UnifiedCard.Content>Success message</UnifiedCard.Content>
</UnifiedCard>
```

#### CVA Variant Configuration
```tsx
const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm",
  {
    variants: {
      variant: {
        default: "border-border",
        interactive: "cursor-pointer transition-colors hover:bg-accent",
        success: "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950",
        warning: "border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950",
        error: "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950",
        info: "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950",
        outline: "border-2 border-dashed border-muted-foreground/25",
        ghost: "border-transparent shadow-none"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
)
```

### Error Handling System (95% Standardization)

Standardized error components for consistent user experience.

#### ErrorAlert Component
```tsx
import { ErrorAlert } from '@/app/components/feedback/ErrorAlert'

// Basic error alert
<ErrorAlert message="Something went wrong" />

// Error alert with retry action
<ErrorAlert 
  message="Failed to load data" 
  onRetry={handleRetry}
  retryText="Try Again"
/>

// Error alert with details
<ErrorAlert 
  message="Validation failed"
  details={["Email is required", "Password must be at least 8 characters"]}
/>
```

#### FormErrorDisplay Component
```tsx
import { FormErrorDisplay } from '@/app/components/forms/FormErrorDisplay'

// For React Hook Form integration
<FormErrorDisplay 
  error={errors.email}
  fieldName="email"
/>

// For multiple errors
<FormErrorDisplay 
  errors={errors}
  fields={['email', 'password']}
/>
```

### Loading States System (90% Standardization)

Consistent loading indicators across the application.

#### LoadingSpinner Variants
```tsx
import { LoadingSpinner } from '@/app/components/data-display/loading/LoadingSpinner'

// Basic spinner
<LoadingSpinner />

// Sized variants
<LoadingSpinner size="sm" />
<LoadingSpinner size="lg" />

// With text
<LoadingSpinner text="Loading..." />

// Overlay spinner
<LoadingSpinner variant="overlay" />
```

#### LoadingSkeleton for Content
```tsx
import { LoadingSkeleton } from '@/app/components/data-display/loading/LoadingSkeleton'

// Table skeleton
<LoadingSkeleton variant="table" rows={5} />

// Card skeleton
<LoadingSkeleton variant="card" />

// Custom skeleton
<LoadingSkeleton className="h-4 w-[250px]" />
```

## Form Development Patterns

### Zod + React Hook Form Integration

Our standardized form pattern combines Zod for schema validation with React Hook Form for optimal performance and developer experience.

#### Schema Definition Pattern
```tsx
import { z } from 'zod'

export const workOrderSchema = z.object({
  woNumber: z.string().min(1, 'Work order number is required'),
  assemblyId: z.string().min(1, 'Assembly is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  priority: z.enum(['low', 'medium', 'high'], {
    required_error: 'Priority is required'
  }),
  dueDate: z.date({
    required_error: 'Due date is required'
  }),
  notes: z.string().optional()
})

export type WorkOrderFormData = z.infer<typeof workOrderSchema>
```

#### Form Component Pattern
```tsx
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormErrorDisplay } from '@/app/components/forms/FormErrorDisplay'

export function WorkOrderForm({ onSubmit, defaultValues }: WorkOrderFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<WorkOrderFormData>({
    resolver: zodResolver(workOrderSchema),
    defaultValues
  })

  const handleFormSubmit = async (data: WorkOrderFormData) => {
    try {
      await onSubmit(data)
      reset()
    } catch (error) {
      // Error handling is managed by parent component
      console.error('Form submission error:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div>
        <label htmlFor="woNumber">Work Order Number</label>
        <input
          id="woNumber"
          {...register('woNumber')}
          className="input"
        />
        <FormErrorDisplay error={errors.woNumber} fieldName="woNumber" />
      </div>
      
      <button 
        type="submit" 
        disabled={isSubmitting}
        className="btn btn-primary"
      >
        {isSubmitting ? 'Saving...' : 'Save Work Order'}
      </button>
    </form>
  )
}
```

#### React Hook Form Best Practices

Based on official React Hook Form documentation:

1. **Avoid useForm in useEffect dependencies**:
```tsx
// ❌ Incorrect - can cause infinite loops
const methods = useForm()
useEffect(() => {
  methods.reset({ ... })
}, [methods])

// ✅ Correct - destructure specific methods
const { reset } = useForm()
useEffect(() => {
  reset({ ... })
}, [reset])
```

2. **Optimize useFormState subscriptions**:
```tsx
// ✅ Destructure for optimal performance
const { isDirty, isValid } = useFormState({ control })

// ❌ Avoid - subscribes to all formState changes
const formState = useFormState({ control })
```

3. **Handle form reset after successful submission**:
```tsx
const { reset, formState: { isSubmitSuccessful } } = useForm()

useEffect(() => {
  if (isSubmitSuccessful) {
    reset({ /* new default values */ })
  }
}, [isSubmitSuccessful, reset])
```

## Architecture Patterns

### Server/Client Component Pattern

We follow Next.js 14 App Router patterns with clear separation between server and client components.

#### Server Component (Data Fetching)
```tsx
// app/inventory/page.tsx
import { InventoryPageClient } from './InventoryPageClient'
import { getInventoryData } from '@/lib/data/inventory'

export default async function InventoryPage() {
  const inventoryData = await getInventoryData()
  
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Inventory Management</h1>
      <InventoryPageClient initialData={inventoryData} />
    </div>
  )
}
```

#### Client Component (Interactivity)
```tsx
// app/inventory/InventoryPageClient.tsx
'use client'

import { useState } from 'react'
import { DataTable } from '@/app/components/data-display/tables/DataTable'
import { UnifiedCard } from '@/app/components/layout/cards/UnifiedCard'

interface InventoryPageClientProps {
  initialData: InventoryItem[]
}

export function InventoryPageClient({ initialData }: InventoryPageClientProps) {
  const [data, setData] = useState(initialData)
  const [isLoading, setIsLoading] = useState(false)

  // Client-side interactivity
  const handleRefresh = async () => {
    setIsLoading(true)
    // Refresh logic
    setIsLoading(false)
  }

  return (
    <UnifiedCard>
      <UnifiedCard.Header>
        <UnifiedCard.Title>Inventory Items</UnifiedCard.Title>
        <button onClick={handleRefresh} disabled={isLoading}>
          Refresh
        </button>
      </UnifiedCard.Header>
      <UnifiedCard.Content>
        <DataTable columns={columns} data={data} />
      </UnifiedCard.Content>
    </UnifiedCard>
  )
}
```

### CVA (Class Variance Authority) Pattern

CVA provides type-safe component variants with excellent TypeScript integration.

#### Basic CVA Setup
```tsx
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  // Base classes
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary"
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

#### Component Composition Pattern
```tsx
// Compound component pattern for complex components
export const UnifiedCard = React.forwardRef<HTMLDivElement, UnifiedCardProps>(
  ({ className, variant, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant }), className)}
      {...props}
    />
  )
)

UnifiedCard.Header = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex flex-col space-y-1.5 p-6", className)}
      {...props}
    />
  )
)

UnifiedCard.Title = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn("text-2xl font-semibold leading-none tracking-tight", className)}
      {...props}
    />
  )
)

UnifiedCard.Content = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
  )
)
```

## Theme Integration System

### CSS Custom Properties Architecture

Our theme system uses CSS custom properties for consistent theming across light/dark modes.

#### Theme Configuration
```css
/* globals.css */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    /* ... dark mode values */
  }
}
```

#### Theme Hook Usage
```tsx
import { useTheme } from '@/app/contexts/ThemeContext'

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <button
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      className="p-2 rounded-md border border-input bg-background hover:bg-accent"
    >
      {theme === 'light' ? '🌙' : '☀️'}
    </button>
  )
}
```

#### Semantic Color Usage
```tsx
// Use semantic colors instead of hardcoded values
<div className="bg-card text-card-foreground border border-border">
  <h2 className="text-foreground">Title</h2>
  <p className="text-muted-foreground">Description</p>
  <button className="bg-primary text-primary-foreground hover:bg-primary/90">
    Action
  </button>
</div>
```

## File Organization & Import Conventions

### Directory Structure
```
app/
├── components/
│   ├── data-display/
│   │   ├── tables/
│   │   │   ├── DataTable.tsx
│   │   │   ├── DataTableColumnHeader.tsx
│   │   │   └── index.ts
│   │   └── loading/
│   │       ├── LoadingSpinner.tsx
│   │       ├── LoadingSkeleton.tsx
│   │       └── index.ts
│   ├── forms/
│   │   ├── FormErrorDisplay.tsx
│   │   ├── WorkOrderForm/
│   │   │   ├── WorkOrderForm.tsx
│   │   │   ├── WorkOrderFormClient.tsx
│   │   │   ├── schema.ts
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── layout/
│   │   ├── cards/
│   │   │   ├── UnifiedCard.tsx
│   │   │   └── index.ts
│   │   └── navigation/
│   └── feedback/
│       ├── ErrorAlert.tsx
│       ├── ErrorBanner.tsx
│       └── index.ts
├── contexts/
│   ├── ThemeContext.tsx
│   └── index.ts
├── lib/
│   ├── utils.ts
│   ├── data/
│   └── validations/
└── types/
    ├── inventory.ts
    ├── workOrder.ts
    └── index.ts
```

### Import/Export Conventions

#### Barrel Exports (index.ts files)
```tsx
// app/components/data-display/tables/index.ts
export { DataTable } from './DataTable'
export { DataTableColumnHeader } from './DataTableColumnHeader'
export type { DataTableProps, Column } from './types'

// app/components/forms/index.ts
export { FormErrorDisplay } from './FormErrorDisplay'
export { WorkOrderForm } from './WorkOrderForm'
export * from './WorkOrderForm'
```

#### Import Patterns
```tsx
// ✅ Preferred - Use barrel exports
import { DataTable, DataTableColumnHeader } from '@/app/components/data-display/tables'
import { UnifiedCard } from '@/app/components/layout/cards'
import { ErrorAlert } from '@/app/components/feedback'

// ✅ Direct imports for specific components
import { WorkOrderForm } from '@/app/components/forms/WorkOrderForm'

// ✅ Type imports
import type { InventoryItem, WorkOrderFormData } from '@/types'

// ❌ Avoid - Direct file imports when barrel exports exist
import { DataTable } from '@/app/components/data-display/tables/DataTable'
```

#### Naming Conventions
- **Components**: PascalCase (e.g., `DataTable`, `UnifiedCard`)
- **Files**: PascalCase for components (e.g., `DataTable.tsx`)
- **Directories**: kebab-case (e.g., `data-display`, `work-orders`)
- **Types**: PascalCase with descriptive suffixes (e.g., `InventoryItem`, `WorkOrderFormData`)
- **Hooks**: camelCase starting with 'use' (e.g., `useInventory`, `useWorkOrders`)

## Development Workflow

### Code Review Guidelines

#### Component Review Checklist
- [ ] Uses standardized components (DataTable, UnifiedCard, etc.)
- [ ] Follows Server/Client component pattern appropriately
- [ ] Implements proper error handling with ErrorAlert/FormErrorDisplay
- [ ] Uses semantic theme colors instead of hardcoded values
- [ ] Includes proper TypeScript types and interfaces
- [ ] Follows CVA pattern for component variants
- [ ] Implements proper loading states
- [ ] Includes accessibility attributes (ARIA labels, roles)
- [ ] Mobile responsive design considerations
- [ ] Performance optimizations (React.memo, useCallback where appropriate)

#### Form Review Checklist
- [ ] Uses Zod schema validation
- [ ] Implements React Hook Form with proper error handling
- [ ] Follows form submission patterns (async/await, error boundaries)
- [ ] Includes proper form reset after successful submission
- [ ] Uses FormErrorDisplay for consistent error messaging
- [ ] Implements proper loading states during submission
- [ ] Includes proper accessibility (labels, ARIA attributes)
- [ ] Validates both client-side and server-side

### Testing Patterns

#### Component Testing
```tsx
// __tests__/components/DataTable.test.tsx
import { render, screen } from '@testing-library/react'
import { DataTable } from '@/app/components/data-display/tables/DataTable'

const mockData = [
  { id: 1, name: 'Item 1', status: 'active' },
  { id: 2, name: 'Item 2', status: 'inactive' }
]

const mockColumns = [
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'status',
    header: 'Status'
  }
]

describe('DataTable', () => {
  it('renders table with data', () => {
    render(<DataTable columns={mockColumns} data={mockData} />)

    expect(screen.getByText('Item 1')).toBeInTheDocument()
    expect(screen.getByText('Item 2')).toBeInTheDocument()
  })

  it('handles empty data state', () => {
    render(<DataTable columns={mockColumns} data={[]} />)

    expect(screen.getByText('No results found')).toBeInTheDocument()
  })
})
```

#### Form Testing with React Hook Form
```tsx
// __tests__/components/WorkOrderForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { WorkOrderForm } from '@/app/components/forms/WorkOrderForm'

describe('WorkOrderForm', () => {
  const mockOnSubmit = jest.fn()

  beforeEach(() => {
    mockOnSubmit.mockClear()
  })

  it('validates required fields', async () => {
    render(<WorkOrderForm onSubmit={mockOnSubmit} />)

    fireEvent.click(screen.getByRole('button', { name: /save/i }))

    await waitFor(() => {
      expect(screen.getByText('Work order number is required')).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('submits valid form data', async () => {
    render(<WorkOrderForm onSubmit={mockOnSubmit} />)

    fireEvent.input(screen.getByLabelText(/work order number/i), {
      target: { value: 'WO-001' }
    })

    fireEvent.click(screen.getByRole('button', { name: /save/i }))

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        woNumber: 'WO-001',
        // ... other form data
      })
    })
  })
})
```

### Performance Optimization Guidelines

#### React.memo Usage
```tsx
// Use React.memo for components that receive stable props
export const InventoryCard = React.memo(({ item }: { item: InventoryItem }) => {
  return (
    <UnifiedCard>
      <UnifiedCard.Header>
        <UnifiedCard.Title>{item.partNumber}</UnifiedCard.Title>
      </UnifiedCard.Header>
      <UnifiedCard.Content>
        <p>Quantity: {item.quantity}</p>
        <p>Status: {item.status}</p>
      </UnifiedCard.Content>
    </UnifiedCard>
  )
})

// Custom comparison for complex props
export const DataTableRow = React.memo(
  ({ row, columns }: DataTableRowProps) => {
    // Component implementation
  },
  (prevProps, nextProps) => {
    return prevProps.row.id === nextProps.row.id &&
           prevProps.row.original === nextProps.row.original
  }
)
```

#### useCallback and useMemo Patterns
```tsx
export function InventoryList({ items }: { items: InventoryItem[] }) {
  // Memoize expensive calculations
  const totalValue = useMemo(() => {
    return items.reduce((sum, item) => sum + (item.quantity * item.unitCost), 0)
  }, [items])

  // Memoize event handlers passed to child components
  const handleItemClick = useCallback((itemId: string) => {
    // Handle item click
  }, [])

  const handleItemUpdate = useCallback((itemId: string, updates: Partial<InventoryItem>) => {
    // Handle item update
  }, [])

  return (
    <div>
      <p>Total Value: ${totalValue.toFixed(2)}</p>
      {items.map(item => (
        <InventoryCard
          key={item.id}
          item={item}
          onClick={handleItemClick}
          onUpdate={handleItemUpdate}
        />
      ))}
    </div>
  )
}
```

## Migration Patterns

### Legacy Component Migration

#### Step 1: Identify Legacy Patterns
```tsx
// ❌ Legacy pattern - multiple table implementations
import { CustomTable } from '@/components/CustomTable'
import { InventoryTable } from '@/components/InventoryTable'
import { WorkOrderTable } from '@/components/WorkOrderTable'

// ❌ Legacy pattern - inconsistent card components
import { Card } from '@/components/Card'
import { InfoCard } from '@/components/InfoCard'
import { StatusCard } from '@/components/StatusCard'
```

#### Step 2: Migrate to Standardized Components
```tsx
// ✅ Standardized pattern - unified DataTable
import { DataTable } from '@/app/components/data-display/tables'
import { inventoryColumns, workOrderColumns } from './columns'

// Replace all table implementations
<DataTable columns={inventoryColumns} data={inventoryData} />
<DataTable columns={workOrderColumns} data={workOrderData} />

// ✅ Standardized pattern - UnifiedCard with variants
import { UnifiedCard } from '@/app/components/layout/cards'

<UnifiedCard variant="default">
  <UnifiedCard.Content>Default content</UnifiedCard.Content>
</UnifiedCard>

<UnifiedCard variant="info">
  <UnifiedCard.Content>Info content</UnifiedCard.Content>
</UnifiedCard>

<UnifiedCard variant="success">
  <UnifiedCard.Content>Success content</UnifiedCard.Content>
</UnifiedCard>
```

#### Step 3: Update Form Patterns
```tsx
// ❌ Legacy form pattern
import { useState } from 'react'

export function LegacyForm() {
  const [formData, setFormData] = useState({})
  const [errors, setErrors] = useState({})

  const handleSubmit = (e) => {
    e.preventDefault()
    // Manual validation logic
    if (!formData.email) {
      setErrors({ email: 'Email is required' })
      return
    }
    // Submit logic
  }

  return (
    <form onSubmit={handleSubmit}>
      <input
        value={formData.email || ''}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      />
      {errors.email && <span>{errors.email}</span>}
    </form>
  )
}

// ✅ Standardized form pattern
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormErrorDisplay } from '@/app/components/forms/FormErrorDisplay'
import { formSchema } from './schema'

export function StandardizedForm() {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(formSchema)
  })

  const onSubmit = async (data) => {
    // Submit logic
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register('email')} />
      <FormErrorDisplay error={errors.email} fieldName="email" />
    </form>
  )
}
```

### Migration Checklist

When migrating legacy components:

- [ ] **Identify component type**: Table, Card, Form, Loading, Error handling
- [ ] **Choose standardized replacement**: DataTable, UnifiedCard, React Hook Form + Zod, LoadingSpinner, ErrorAlert
- [ ] **Update imports**: Use barrel exports from standardized component directories
- [ ] **Migrate props**: Map legacy props to standardized component props
- [ ] **Update styling**: Use theme-aware classes instead of hardcoded styles
- [ ] **Add TypeScript types**: Ensure proper typing for props and data
- [ ] **Test functionality**: Verify all features work with new components
- [ ] **Update tests**: Modify tests to work with new component structure
- [ ] **Remove legacy code**: Delete old component files after successful migration

## Performance & Accessibility Standards

### WCAG 2.1 AA Compliance

#### Accessibility Checklist
- [ ] **Keyboard Navigation**: All interactive elements accessible via keyboard
- [ ] **Screen Reader Support**: Proper ARIA labels and roles
- [ ] **Color Contrast**: Minimum 4.5:1 ratio for normal text, 3:1 for large text
- [ ] **Focus Management**: Visible focus indicators and logical tab order
- [ ] **Alternative Text**: Images have descriptive alt text
- [ ] **Form Labels**: All form inputs have associated labels
- [ ] **Error Identification**: Clear error messages and instructions
- [ ] **Responsive Design**: Works across different screen sizes and orientations

#### Implementation Examples
```tsx
// ✅ Accessible DataTable
<DataTable
  columns={columns}
  data={data}
  aria-label="Inventory items table"
  role="table"
/>

// ✅ Accessible form with proper labels
<form onSubmit={handleSubmit(onSubmit)}>
  <label htmlFor="email" className="sr-only">
    Email Address
  </label>
  <input
    id="email"
    type="email"
    {...register('email')}
    aria-describedby="email-error"
    aria-invalid={errors.email ? 'true' : 'false'}
  />
  <FormErrorDisplay
    error={errors.email}
    fieldName="email"
    id="email-error"
    role="alert"
  />
</form>

// ✅ Accessible loading states
<LoadingSpinner
  aria-label="Loading inventory data"
  role="status"
/>
```

### Bundle Size Optimization

Our component standardization achieved **15-20% bundle size reduction** through:

#### Code Splitting Patterns
```tsx
// Lazy load heavy components
const DataTable = lazy(() => import('@/app/components/data-display/tables/DataTable'))
const WorkOrderForm = lazy(() => import('@/app/components/forms/WorkOrderForm'))

// Use Suspense for loading states
<Suspense fallback={<LoadingSkeleton variant="table" />}>
  <DataTable columns={columns} data={data} />
</Suspense>
```

#### Tree Shaking Optimization
```tsx
// ✅ Import only what you need
import { DataTable } from '@/app/components/data-display/tables'
import { UnifiedCard } from '@/app/components/layout/cards'

// ❌ Avoid importing entire libraries
import * as Components from '@/app/components'
```

#### Dynamic Imports for Route-Based Splitting
```tsx
// app/inventory/page.tsx
import dynamic from 'next/dynamic'

const InventoryPageClient = dynamic(
  () => import('./InventoryPageClient'),
  {
    loading: () => <LoadingSkeleton variant="page" />,
    ssr: false
  }
)

export default function InventoryPage() {
  return <InventoryPageClient />
}
```

## Troubleshooting & Common Patterns

### Common Issues and Solutions

#### 1. DataTable Not Rendering Data
```tsx
// ❌ Problem: Incorrect column definition
const columns = [
  {
    header: 'Name',
    accessorKey: 'fullName' // Field doesn't exist in data
  }
]

// ✅ Solution: Verify accessor keys match data structure
const columns: ColumnDef<InventoryItem>[] = [
  {
    accessorKey: 'partNumber', // Matches InventoryItem.partNumber
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Part Number" />
    )
  }
]
```

#### 2. Form Validation Not Working
```tsx
// ❌ Problem: Missing resolver
const { register, handleSubmit } = useForm()

// ✅ Solution: Add Zod resolver
const { register, handleSubmit } = useForm({
  resolver: zodResolver(schema)
})
```

#### 3. Theme Colors Not Applying
```tsx
// ❌ Problem: Hardcoded colors
<div className="bg-white text-black border-gray-200">

// ✅ Solution: Use semantic theme colors
<div className="bg-card text-card-foreground border-border">
```

#### 4. Server/Client Component Hydration Issues
```tsx
// ❌ Problem: Using client-side features in server component
export default function ServerComponent() {
  const [state, setState] = useState() // Error: useState in server component

  return <div>{state}</div>
}

// ✅ Solution: Separate server and client components
// ServerComponent.tsx
export default function ServerComponent() {
  const data = await fetchData()
  return <ClientComponent initialData={data} />
}

// ClientComponent.tsx
'use client'
export function ClientComponent({ initialData }) {
  const [state, setState] = useState(initialData)
  return <div>{state}</div>
}
```

### Performance Debugging

#### Bundle Analysis
```bash
# Analyze bundle size
npm run build
npm run analyze

# Check for duplicate dependencies
npx duplicate-package-checker

# Audit performance
npm run lighthouse
```

#### React DevTools Profiling
1. Install React DevTools browser extension
2. Open Profiler tab
3. Record component renders during user interactions
4. Identify components with unnecessary re-renders
5. Apply React.memo, useCallback, or useMemo optimizations

### Development Tools Setup

#### VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

#### Recommended VS Code Settings
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

## Conclusion

This comprehensive guide represents the culmination of our UI/UX standardization effort, achieving **95.5% component standardization** across the Trend IMS codebase. By following these patterns and guidelines, developers can:

- **Maintain Consistency**: Use standardized components and patterns
- **Improve Performance**: Leverage optimized, battle-tested components
- **Enhance Accessibility**: Follow WCAG 2.1 AA compliance standards
- **Accelerate Development**: Reuse proven patterns and components
- **Ensure Quality**: Follow established testing and code review practices

### Key Takeaways

1. **Always use standardized components** (DataTable, UnifiedCard, ErrorAlert, etc.)
2. **Follow Server/Client component patterns** for optimal Next.js 14 performance
3. **Implement Zod + React Hook Form** for all form validation
4. **Use semantic theme colors** instead of hardcoded values
5. **Apply CVA patterns** for type-safe component variants
6. **Maintain accessibility standards** in all components
7. **Test thoroughly** with Jest, React Testing Library, and Playwright
8. **Optimize for performance** with proper memoization and code splitting

For questions or clarifications on any of these patterns, refer to the supporting documentation in the `docs/` directory or reach out to the development team.

---

*Last updated: Based on UI/UX audit completion achieving 95.5% standardization*
