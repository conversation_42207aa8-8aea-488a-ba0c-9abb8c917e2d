"use client";

import { Badge } from "@/app/components/data-display/badge";
import { motion } from 'framer-motion';
import { AlertTriangle, Clock, Package, Truck } from 'lucide-react';
import React from 'react';
import LogisticsMap from '../../../app/components/charts/LogisticsMap';
import Header from '../../../app/components/layout/Header';
import { Product } from '../../../app/types';
import { useAppContext } from '../../contexts/AppContext';
import { useTheme } from '../../contexts/ThemeContext';

const Logistics: React.FC = () => {
  const { logisticsInfo, products } = useAppContext();
  const { theme } = useTheme();

  // Ensure logisticsInfo exists and provide defaults
  const safeLogisticsInfo = logisticsInfo || {
    inTransit: 0,
    delivered: 0,
    delayedItems: [],
    delayDuration: 'N/A',
    status: 'Data unavailable'
  };

  // Get all products that are in transit or delayed
  const delayedItems: Product[] = safeLogisticsInfo.delayedItems || [];
  const inTransitCount = safeLogisticsInfo.inTransit || 0;
  const deliveredCount = safeLogisticsInfo.delivered || 0;

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Logistics Dashboard" />

      <div className="px-8 pb-8">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12">
            <LogisticsMap logisticsInfo={safeLogisticsInfo as any} />
          </div>

          <div className="col-span-12 md:col-span-6">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-card rounded-3xl p-6 shadow-md dark:shadow-gray-900/30"
            >
              <h3 className="text-xl font-medium text-foreground mb-4">Delayed Shipments</h3>

              {delayedItems.length > 0 ? (
                <div className="space-y-4">
                  {delayedItems.map((item, index) => (
                    <motion.div
                      key={item.id}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 * index }}
                      className="bg-muted rounded-xl p-4 flex items-start"
                    >
                      <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg mr-4">
                        <AlertTriangle size={20} className="text-orange-600 dark:text-orange-500" />
                      </div>

                      <div className="flex-1">
                        <h4 className="font-medium text-foreground">{item.name}</h4>
                        <p className="text-sm text-muted-foreground">ID: {item.id}</p>
                        <div className="flex items-center mt-2">
                          <Clock size={14} className="text-muted-foreground mr-1" />
                          <span className="text-xs text-muted-foreground">Delayed by {safeLogisticsInfo.delayDuration}</span>
                        </div>
                      </div>

                      <Badge variant="destructive">Delayed</Badge>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No delayed shipments.</p>
              )}
            </motion.div>
          </div>

          <div className="col-span-12 md:col-span-6">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white dark:bg-card rounded-3xl p-6 shadow-md dark:shadow-gray-900/30"
            >
              <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary mb-4">Shipping Overview</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4">
                  <div className="flex items-center">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg mr-4">
                      <Truck size={20} className="text-blue-500 dark:text-blue-400" />
                    </div>
                    <div>
                      <h4 className="text-2xl font-semibold text-gray-900 dark:text-text-primary">{inTransitCount}</h4>
                      <p className="text-sm text-gray-500 dark:text-text-secondary">In Transit</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4">
                  <div className="flex items-center">
                    <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg mr-4">
                      <Package size={20} className="text-green-500 dark:text-green-400" />
                    </div>
                    <div>
                      <h4 className="text-2xl font-semibold text-gray-900 dark:text-text-primary">{deliveredCount}</h4>
                      <p className="text-sm text-gray-500 dark:text-text-secondary">Delivered</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-sidebar rounded-xl p-4">
                  <div className="flex items-center">
                    <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg mr-4">
                      <AlertTriangle size={20} className="text-red-500 dark:text-red-400" />
                    </div>
                    <div>
                      <h4 className="text-2xl font-semibold text-gray-900 dark:text-text-primary">{delayedItems.length}</h4>
                      <p className="text-sm text-gray-500 dark:text-text-secondary">Delayed</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Logistics;