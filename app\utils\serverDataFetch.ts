/**
 * Server-side data fetching utilities
 * Use these in server components instead of calling internal API routes
 * This prevents the "Unexpected token '<'" error during SSR/SSG
 */

import { connectToDatabase } from '@/app/lib/mongodb';
import { Assembly, Part } from '@/app/models';
import { Product } from '@/app/models/product.model';

/**
 * Fetch parts directly from database (for server components)
 */
export async function getPartsServerSide(options: {
  page?: number;
  limit?: number;
  search?: string;
} = {}) {
  try {
    const { page = 1, limit = 20, search } = options;
    
    await connectToDatabase();
    
    let query = {};
    if (search) {
      query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { partNumber: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      };
    }
    
    const skip = (page - 1) * limit;
    
    const [parts, total] = await Promise.all([
      Part.find(query)
        .populate('supplier', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Part.countDocuments(query)
    ]);
    
    return {
      success: true,
      data: parts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('[ServerDataFetch] Error fetching parts:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch parts',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    };
  }
}

/**
 * Fetch assemblies directly from database (for server components)
 */
export async function getAssembliesServerSide(options: {
  page?: number;
  limit?: number;
  search?: string;
} = {}) {
  try {
    const { page = 1, limit = 20, search } = options;

    await connectToDatabase();

    // Use any type to avoid complex TypeScript issues with Mongoose models
    const AssemblyModel = Assembly as any;

    let query = {};
    if (search) {
      query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { assemblyCode: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      };
    }

    const skip = (page - 1) * limit;

    const [assemblies, total] = await Promise.all([
      AssemblyModel.find(query)
        .populate('parts.part', 'name partNumber')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      AssemblyModel.countDocuments(query)
    ]);

    return {
      success: true,
      data: assemblies,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('[ServerDataFetch] Error fetching assemblies:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch assemblies',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    };
  }
}

/**
 * Fetch products directly from database (for server components)
 */
export async function getProductsServerSide(options: {
  page?: number;
  limit?: number;
  search?: string;
} = {}) {
  try {
    const { page = 1, limit = 20, search } = options;

    await connectToDatabase();

    // Use any type to avoid complex TypeScript issues with Mongoose models
    const ProductModel = Product as any;

    let query = {};
    if (search) {
      query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { sku: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      };
    }

    const skip = (page - 1) * limit;

    const [products, total] = await Promise.all([
      ProductModel.find(query)
        .populate('category', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      ProductModel.countDocuments(query)
    ]);

    return {
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('[ServerDataFetch] Error fetching products:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch products',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    };
  }
}

/**
 * Get dashboard statistics directly from database (for server components)
 */
export async function getDashboardStatsServerSide() {
  try {
    await connectToDatabase();

    // Use any type to avoid complex TypeScript issues with Mongoose models
    const PartModel = Part as any;
    const AssemblyModel = Assembly as any;
    const ProductModel = Product as any;

    const [partsCount, assembliesCount, productsCount] = await Promise.all([
      PartModel.countDocuments(),
      AssemblyModel.countDocuments(),
      ProductModel.countDocuments()
    ]);
    
    return {
      success: true,
      data: {
        partsCount,
        assembliesCount,
        productsCount,
        totalItems: partsCount + assembliesCount + productsCount
      }
    };
  } catch (error) {
    console.error('[ServerDataFetch] Error fetching dashboard stats:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch dashboard stats',
      data: {
        partsCount: 0,
        assembliesCount: 0,
        productsCount: 0,
        totalItems: 0
      }
    };
  }
}
