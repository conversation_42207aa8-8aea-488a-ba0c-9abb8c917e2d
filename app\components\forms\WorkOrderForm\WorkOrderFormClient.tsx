"use client";

import { useTheme } from "@/app/contexts/ThemeContext";
import { cn } from "@/app/lib/utils";
import { Assembly, Product } from "@/app/types/inventory";
import { User } from "@/app/types/user";
import { getApiUrl } from '@/app/utils/env';
import { safeFetch } from '@/app/utils/safeFetch';
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { AlertTriangle, CalendarIcon, Loader2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { WorkOrderFormData, WorkOrderFormProps, workOrderFormSchema } from "./types";

import { Alert, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { FormErrorDisplay } from "@/app/components/feedback";
import { Textarea } from "@/app/components/forms";
import { Button } from "@/app/components/forms/Button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/app/components/forms/Form";
import { Input } from "@/app/components/forms/Input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/app/components/forms/Select";
import { Calendar } from "@/app/components/layout/calendar/calendar";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/app/components/layout/cards/card";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/app/components/navigation/popover";
import { PartSearch, PartSearchResult } from "@/app/components/search/PartSearch";
import { BatchesTable } from "@/app/components/tables/BatchesTable";

/**
 * WorkOrderFormClient component
 * Client component for creating and editing work orders
 */
export function WorkOrderFormClient({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error = null,
  isEditing = false
}: WorkOrderFormProps) {
  const { theme } = useTheme();
  const router = useRouter();
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);
  const [batchesError, setBatchesError] = useState<string | null>(null);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<WorkOrderFormData>({
    resolver: zodResolver(workOrderFormSchema) as any,
    defaultValues: {
      _id: initialData?._id,
      woNumber: initialData?.woNumber,
      assemblyId: initialData?.assemblyId || null,
      partIdToManufacture: initialData?.partIdToManufacture || null,
      productId: initialData?.productId || null,
      quantity: initialData?.quantity || 1,
      status: (initialData?.status as any) || "pending",
      priority: (initialData?.priority as any) || "medium",
      dueDate: initialData?.dueDate ? new Date(initialData.dueDate) : null,
      assignedTo: initialData?.assignedTo || "",
      notes: initialData?.notes || "",
      completedAt: initialData?.completedAt ? new Date(initialData.completedAt) : null
    }
  });

  // Fetch assemblies, products, and users for dropdowns
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true);
      setDataError(null);

      try {
        // Fetch assemblies
        const assembliesResult = await safeFetch(getApiUrl("/api/assemblies"));
        if (!assembliesResult.success) {
          throw new Error("Error fetching assemblies: " + assembliesResult.error);
        }
        const assembliesData = assembliesResult.data as { data?: Assembly[]; success?: boolean; message?: string };
        setAssemblies(assembliesData.data || []);

        // Fetch products
        const productsResult = await safeFetch(getApiUrl("/api/products"));
        if (!productsResult.success) {
          throw new Error("Error fetching products: " + productsResult.error);
        }
        const productsData = productsResult.data as { data?: Product[]; success?: boolean; message?: string };
        setProducts(productsData.data || []);

        // Fetch users
        const usersResult = await safeFetch(getApiUrl("/api/users"));
        if (!usersResult.success) {
          throw new Error("Error fetching users: " + usersResult.error);
        }
        const usersData = usersResult.data as { data?: User[]; success?: boolean; message?: string };
        setUsers(usersData.data || []);
      } catch (err: any) {
        setDataError(err.message || "Failed to fetch form data");
        console.error("Error fetching form data:", err);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchData();
  }, []);

  // Handle form submission
  const handleSubmit = (data: WorkOrderFormData) => {
    if (onSubmit) {
      onSubmit(data);
    }
  };

  // Handle part selection from search
  const handlePartSelect = (part: PartSearchResult) => {
    form.setValue("partIdToManufacture", part._id);
    // Clear other fields to ensure only one is selected
    form.setValue("assemblyId", null);
    form.setValue("productId", null);
  };

  // Handle form cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Check if at least one of assemblyId, partIdToManufacture, or productId is selected
  const hasTarget = form.watch("assemblyId") || form.watch("partIdToManufacture") || form.watch("productId");

  return (
    <Card className={cn(
      "w-full",
      theme.isLight ? "bg-white" : "bg-[var(--T-bg-card)]"
    )}>
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Work Order" : "Create Work Order"}</CardTitle>
      </CardHeader>
      <CardContent>
        {(isLoading || isLoadingData) && (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading...</span>
          </div>
        )}

        {(error || dataError) && (
          <FormErrorDisplay
            error={error || dataError}
            field="Work Order Form"
            className="mb-4"
          />
        )}

        {!isLoadingData && !dataError && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit as any)} className="space-y-6">
              {/* Work Order Number (read-only for editing) */}
              {isEditing && (
                <FormField
                  control={form.control as any}
                  name="woNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Order Number</FormLabel>
                      <FormControl>
                        <Input {...field} disabled value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Assembly Selection */}
              <FormField
                control={form.control as any}
                name="assemblyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assembly</FormLabel>
                    <Select
                      disabled={isLoading}
                      onValueChange={(value) => {
                        field.onChange(value === "none" ? null : value);
                        // Clear other fields to ensure only one is selected
                        form.setValue("partIdToManufacture", null);
                        form.setValue("productId", null);
                      }}
                      value={field.value || "none"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an assembly" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {assemblies.map((assembly) => (
                          <SelectItem key={String(assembly._id)} value={String(assembly._id)}>
                            {assembly.name} ({assembly.assemblyCode})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Part Selection */}
              <FormField
                control={form.control as any}
                name="partIdToManufacture"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Part to Manufacture</FormLabel>
                    <PartSearch
                      onPartSelect={handlePartSelect}
                      selectedPartId={field.value || ""}
                      placeholder="Search for a part..."
                      disabled={isLoading}
                      label="Part to Manufacture"
                      showStockLevels={true}
                      minQueryLength={3}
                      debounceTime={300}
                      maxResults={10}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Product Selection */}
              <FormField
                control={form.control as any}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product</FormLabel>
                    <Select
                      disabled={isLoading}
                      onValueChange={(value) => {
                        field.onChange(value === "none" ? null : value);
                        // Clear other fields to ensure only one is selected
                        form.setValue("assemblyId", null);
                        form.setValue("partIdToManufacture", null);
                      }}
                      value={field.value || "none"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {products.map((product) => (
                          <SelectItem key={String(product._id)} value={String(product._id)}>
                            {product.name} ({product.productCode})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Target Validation Warning */}
              {!hasTarget && (
                <Alert variant="warning" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Required</AlertTitle>
                  <AlertDescription>
                    At least one of Assembly, Part, or Product must be selected.
                  </AlertDescription>
                </Alert>
              )}

              {/* Quantity */}
              <FormField
                control={form.control as any}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control as any}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      disabled={isLoading}
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority */}
              <FormField
                control={form.control as any}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select
                      disabled={isLoading}
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Due Date */}
              <FormField
                control={form.control as any}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                            disabled={isLoading}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Assigned To */}
              <FormField
                control={form.control as any}
                name="assignedTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned To</FormLabel>
                    <Select
                      disabled={isLoading}
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a user" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {users.map((user) => (
                          <SelectItem key={String(user._id)} value={String(user._id)}>
                            {user.fullName || user.username}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes */}
              <FormField
                control={form.control as any}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter notes about this work order..."
                        className="resize-y"
                        {...field}
                        value={field.value || ""}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        )}
      </CardContent>
      {/* Associated Batches Section - Only show for existing work orders */}
      {isEditing && initialData?.woNumber && (
        <div className="mt-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Associated Batches</h3>
            <Button
              onClick={() => router.push(`/batch-tracking?workOrderId=${initialData._id}&woNumber=${initialData.woNumber}`)}
              size="sm"
              className="bg-primary hover:bg-primary/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Batch
            </Button>
          </div>

          <BatchesTable
            woNumber={initialData.woNumber}
            simple={false}
            onBatchClick={(batch) => router.push(`/batch-tracking/${batch._id}`)}
            onBatchCreate={() => router.push(`/batch-tracking?workOrderId=${initialData._id}&woNumber=${initialData.woNumber}`)}
          />
        </div>
      )}

      <CardFooter className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading || isLoadingData}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={form.handleSubmit(handleSubmit as any)}
          disabled={isLoading || isLoadingData || !form.formState.isValid}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {isEditing ? "Updating..." : "Creating..."}
            </>
          ) : (
            isEditing ? "Update Work Order" : "Create Work Order"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
