/**
 * API Response Types for Trend IMS
 * 
 * This file contains standardized type definitions for API responses,
 * error handling, and response transformation utilities.
 */

// ============================================================================
// Base API Response Interfaces
// ============================================================================

/**
 * Standard API response structure for successful responses
 */
export interface ApiResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  metadata?: any;
  pagination?: PaginationInfo;
}

/**
 * Standard API response structure for error responses
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any[];
  };
}

/**
 * Union type for all possible API responses
 */
export type StandardApiResponse<T = any> = ApiResponse<T> | ApiErrorResponse;

/**
 * Legacy API response format (for backward compatibility)
 */
export interface LegacyApiResponse<T = any> {
  data: T | null;
  error: string | null;
  pagination?: any;
  meta?: any;
}

/**
 * Pagination information structure
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// ============================================================================
// Specific Response Types
// ============================================================================

/**
 * Analytics API response type
 */
export interface AnalyticsResponse extends LegacyApiResponse<any> {
  data: {
    inventoryTrends?: any[];
    stockLevels?: any[];
    categoryDistribution?: any[];
    inventoryValueByCategory?: any[];
    dashboardSummary?: any;
    trends?: any[]; // Legacy field
  } | null;
}

/**
 * Assemblies API response type
 */
export interface AssembliesResponse extends LegacyApiResponse<any[]> {}

/**
 * Parts API response type
 */
export interface PartsResponse extends LegacyApiResponse<any[]> {}

/**
 * Suppliers API response type
 */
export interface SuppliersResponse extends LegacyApiResponse<any[]> {}

/**
 * Users API response type
 */
export interface UsersResponse extends LegacyApiResponse<any[]> {}

/**
 * Work Orders API response type
 */
export interface WorkOrdersResponse extends LegacyApiResponse<any[]> {}

/**
 * Reports API response type
 */
export interface ReportsResponse extends LegacyApiResponse<any[]> {}

/**
 * Settings API response type
 */
export interface SettingsApiResponse extends LegacyApiResponse<any[]> {}

/**
 * Assembly Create response type
 */
export interface AssemblyCreateResponse extends LegacyApiResponse<{ name?: string }> {}

/**
 * Work Order response type
 */
export interface WorkOrderResponse extends LegacyApiResponse<{ woNumber?: string }> {}

// ============================================================================
// Type Guards and Utility Functions
// ============================================================================

/**
 * Type guard to check if a response is an error response
 */
export function hasApiError<T>(response: StandardApiResponse<T> | LegacyApiResponse<T>): response is ApiErrorResponse | (LegacyApiResponse<T> & { error: string }) {
  if ('success' in response) {
    return response.success === false;
  }
  // Legacy format check
  return response.error !== null && response.error !== undefined;
}

/**
 * Extract error message from API response
 */
export function extractApiError<T>(response: StandardApiResponse<T> | LegacyApiResponse<T>): string | null {
  if ('success' in response && response.success === false) {
    return response.error.message;
  }
  // Legacy format
  if ('error' in response && response.error) {
    return response.error;
  }
  return null;
}

// ============================================================================
// Response Transformation Functions
// ============================================================================

/**
 * Transform any response to standard API response format
 */
export function asApiResponse<T>(response: any): StandardApiResponse<T> {
  // If already in standard format, return as-is
  if (response && typeof response === 'object' && 'success' in response) {
    return response as StandardApiResponse<T>;
  }
  
  // Transform legacy format to standard format
  if (response && typeof response === 'object') {
    if (response.error) {
      return {
        success: false,
        error: {
          code: 'API_ERROR',
          message: response.error,
          details: []
        }
      } as ApiErrorResponse;
    }
    
    return {
      success: true,
      data: response.data || response,
      pagination: response.pagination,
      metadata: response.meta
    } as ApiResponse<T>;
  }
  
  // Fallback for unexpected formats
  return {
    success: true,
    data: response
  } as ApiResponse<T>;
}

/**
 * Transform response to analytics format
 */
export function asAnalyticsResponse(response: any): AnalyticsResponse {
  return response as AnalyticsResponse;
}

/**
 * Transform response to assemblies format
 */
export function asAssembliesResponse(response: any): AssembliesResponse {
  return response as AssembliesResponse;
}

/**
 * Transform response to parts format
 */
export function asPartsResponse(response: any): PartsResponse {
  return response as PartsResponse;
}

/**
 * Transform response to suppliers format
 */
export function asSuppliersResponse(response: any): SuppliersResponse {
  return response as SuppliersResponse;
}

/**
 * Transform response to users format
 */
export function asUsersResponse(response: any): UsersResponse {
  return response as UsersResponse;
}

/**
 * Transform response to work orders format
 */
export function asWorkOrdersResponse(response: any): WorkOrdersResponse {
  return response as WorkOrdersResponse;
}

/**
 * Transform response to reports format
 */
export function asReportsResponse(response: any): ReportsResponse {
  return response as ReportsResponse;
}
