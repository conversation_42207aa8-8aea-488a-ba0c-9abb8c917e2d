'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';

// Define types for UI preferences
export interface AssemblyUIPreferences {
  viewMode: 'grid' | 'table';
  skipDeleteConfirmation: boolean;
  sortBy: string;
  filterStatus: string;
  searchQuery: string;
}

// Default preferences
const defaultPreferences: AssemblyUIPreferences = {
  viewMode: 'grid',
  skipDeleteConfirmation: false,
  sortBy: 'name',
  filterStatus: '',
  searchQuery: '',
};

/**
 * Hook for managing assembly UI preferences with localStorage persistence
 */
export function useAssemblyUIPreferences() {
  // Initialize with default preferences
  const [preferences, setPreferences] = useState<AssemblyUIPreferences>(defaultPreferences);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Load preferences from localStorage on mount
  useEffect(() => {
    if (isLoaded) return; // Only load once
    
    try {
      const storedPreferences = localStorage.getItem('assemblyUIPreferences');
      if (storedPreferences) {
        const parsedPreferences = JSON.parse(storedPreferences);
        setPreferences(prev => ({
          ...prev,
          ...parsedPreferences,
        }));
      }
    } catch (error) {
      console.error('Failed to load assembly UI preferences:', error);
    } finally {
      setIsLoaded(true);
    }
  }, [isLoaded]);
  
  // Save preferences to localStorage whenever they change
  useEffect(() => {
    if (!isLoaded || isUpdating) return; // Don't save during initial load or while updating
    
    try {
      localStorage.setItem('assemblyUIPreferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Failed to save assembly UI preferences:', error);
    }
  }, [preferences, isLoaded, isUpdating]);
  
  // Function to update a single preference
  const updatePreference = useCallback(<K extends keyof AssemblyUIPreferences>(
    key: K, 
    value: AssemblyUIPreferences[K]
  ) => {
    setIsUpdating(true); // Prevent save loop
    setPreferences(prev => {
      // Skip update if value hasn't changed
      if (prev[key] === value) return prev;
      return { ...prev, [key]: value };
    });
    // Allow saving again on next render
    setTimeout(() => setIsUpdating(false), 0);
  }, []);
  
  // Function to update multiple preferences
  const updatePreferences = useCallback((newPreferences: Partial<AssemblyUIPreferences>) => {
    setIsUpdating(true); // Prevent save loop
    setPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));
    // Allow saving again on next render
    setTimeout(() => setIsUpdating(false), 0);
  }, []);
  
  // Function to reset preferences to defaults
  const resetPreferences = useCallback(() => {
    setIsUpdating(true);
    setPreferences(defaultPreferences);
    setTimeout(() => setIsUpdating(false), 0);
  }, []);
  
  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(() => ({
    preferences,
    updatePreference,
    updatePreferences,
    resetPreferences,
    isLoaded
  }), [preferences, updatePreference, updatePreferences, resetPreferences, isLoaded]);
} 