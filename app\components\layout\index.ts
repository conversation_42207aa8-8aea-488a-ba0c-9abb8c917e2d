// Layout Components - Centralized exports

// Card components
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle, cardVariants } from './cards/card';

// Unified Card Components (Recommended)
export { UnifiedCard } from './cards/UnifiedCard';
export type { UnifiedCardProps } from './cards/UnifiedCard';

// Compatibility Wrappers
export {
  BaseCardCompat,
  ActionCardCompat,
  StatusCardCompat,
  EnhancedCard
} from './cards/card-compat';
export type {
  BaseCardCompatProps,
  ActionCardCompatProps,
  StatusCardCompatProps,
  EnhancedCardProps
} from './cards/card-compat';

// Legacy card components (deprecated - use UnifiedCard instead)
export { default as ActionCard } from './cards/ActionCard';
export { default as BaseCard } from './cards/BaseCard';
export { default as StatusCard } from './cards/StatusCard';

// Calendar components
export { Calendar } from './calendar/calendar';
export { default as CalendarComponent } from './calendar/CalendarComponent';
export { DatePicker } from './calendar/date-picker';

// Existing layout components
export { EnhancedBackground } from './EnhancedBackground';
export { default as Header } from './Header';
export { default as HeaderRightControls } from './HeaderRightControls';
export { default as Sidebar } from './Sidebar';

