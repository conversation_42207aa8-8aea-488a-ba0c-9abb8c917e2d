'use client';

import {
  FileText,
  Package,
  User,
  Warehouse,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';
import { format } from 'date-fns';

import { Badge } from '@/app/components/data-display/badge';
import { StandardizedViewContent, StandardizedSection } from './StandardizedViewContent';
import { type InventoryTransactionColumnData } from '@/app/components/data-display/data-table';
import {
  getTransactionTypeIcon,
  getTransactionTypeBadgeVariant,
  getQuantityChangeDisplay,
  getReferenceTypeInfo,
  formatTransactionDate,
  getStockMovementDescription,
  getWarehouseLocationDisplay,
  formatTransactionType
} from '@/app/utils/transactionUtils';

interface TransactionViewContentProps {
  transaction: InventoryTransactionColumnData;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Get the appropriate icon and color for quantity change (local override for specific styling)
 */
function getLocalQuantityChangeDisplay(quantity: number) {
  if (quantity > 0) {
    return {
      icon: <TrendingUp className="h-4 w-4 text-green-600" />,
      color: 'text-green-600',
      prefix: '+'
    };
  } else if (quantity < 0) {
    return {
      icon: <TrendingDown className="h-4 w-4 text-red-600" />,
      color: 'text-red-600',
      prefix: ''
    };
  } else {
    return {
      icon: <BarChart3 className="h-4 w-4 text-gray-600" />,
      color: 'text-gray-600',
      prefix: ''
    };
  }
}

export function TransactionViewContent({ transaction, isInTab = false }: TransactionViewContentProps) {
  const quantityDisplay = getLocalQuantityChangeDisplay(transaction.quantity);
  const stockMovementDescription = getStockMovementDescription(transaction);
  const warehouseLocationData = getWarehouseLocationDisplay(transaction);
  const formattedTransactionType = formatTransactionType(transaction.transactionType);

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <div className="space-y-3">
        {/* Transaction Overview - Compact Header */}
        <div className="bg-card border border-border rounded-lg shadow-sm">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getTransactionTypeIcon(transaction.transactionType)}
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    {formattedTransactionType}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {formatTransactionDate(transaction.transactionDate)}
                  </p>
                </div>
              </div>
              <Badge variant={getTransactionTypeBadgeVariant(transaction.transactionType)}>
                {formattedTransactionType}
              </Badge>
            </div>
          </div>

          {/* Stock Movement Description - Consolidated into Location Details section */}
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <p className="text-sm text-muted-foreground">Transaction ID</p>
                <p className="font-mono text-sm">{transaction.transactionId || transaction._id}</p>
              </div>
              <div className="flex items-center gap-2">
                {quantityDisplay.icon}
                <div>
                  <p className="text-sm text-muted-foreground">Quantity Change</p>
                  <span className={`font-bold ${quantityDisplay.color}`}>
                    {quantityDisplay.prefix}{transaction.quantity} units
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Item & Warehouse Information - Combined Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          <StandardizedSection
            title="Item Details"
            icon={<Package className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Part Number</p>
                <p className="font-mono text-sm">{transaction.partNumber || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Part Name</p>
                <p className="font-medium text-foreground">{transaction.itemName || 'Unknown Part'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Business Name</p>
                <p className="font-medium text-foreground">{transaction.businessName || transaction.itemName || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Item Type</p>
                <Badge variant="outline">{transaction.itemType || 'Part'}</Badge>
              </div>
            </div>
          </StandardizedSection>

          <StandardizedSection
            title="Location Details"
            icon={<Warehouse className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-3">
            {/* Stock Movement Summary */}
            <div className="p-3 bg-muted/30 rounded-lg border border-border">
              <div className="flex items-start gap-2">
                <TrendingUp className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-foreground">Stock Movement</p>
                  <p className="text-sm text-muted-foreground">{stockMovementDescription}</p>
                </div>
              </div>
            </div>
              {/* Source Location (for transfers and outbound transactions) */}
              {warehouseLocationData.source && warehouseLocationData.source.warehouse && (
                <div className="p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                  <p className="text-xs font-medium text-red-700 dark:text-red-300 mb-1">From Location</p>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-foreground">{warehouseLocationData.source.warehouse}</p>
                    <p className="text-xs text-muted-foreground">{warehouseLocationData.source.location}</p>
                    {warehouseLocationData.source.stockType && (
                      <Badge variant="outline" className="text-xs">{warehouseLocationData.source.stockType}</Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Destination Location (for transfers and inbound transactions) */}
              {warehouseLocationData.destination && warehouseLocationData.destination.warehouse && (
                <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <p className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">To Location</p>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-foreground">{warehouseLocationData.destination.warehouse}</p>
                    <p className="text-xs text-muted-foreground">{warehouseLocationData.destination.location}</p>
                    {warehouseLocationData.destination.stockType && (
                      <Badge variant="outline" className="text-xs">{warehouseLocationData.destination.stockType}</Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Fallback for legacy data */}
              {!warehouseLocationData.source?.warehouse && !warehouseLocationData.destination?.warehouse && (
                <div className="space-y-2">
                  <div>
                    <p className="text-sm text-muted-foreground">Warehouse</p>
                    <p className="font-medium text-foreground">{transaction.warehouseName || 'Unknown Warehouse'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Location</p>
                    <p className="font-mono text-sm">{transaction.warehouseLocationId || transaction.warehouseId || 'Unknown Location'}</p>
                  </div>
                </div>
              )}
            </div>
          </StandardizedSection>
        </div>

        {/* Stock Levels - Compact */}
        <StandardizedSection
          title="Stock Levels"
          icon={<BarChart3 className="h-4 w-4" />}
          compact={true}
        >
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Previous Stock</p>
              <p className="font-semibold text-lg">{transaction.previousStock || 'N/A'}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">New Stock</p>
              <p className="font-semibold text-lg">{transaction.newStock || 'N/A'}</p>
            </div>
          </div>
        </StandardizedSection>

        {/* Reference & User Information - Combined Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          {/* Reference Information */}
          {(transaction.referenceNumber || transaction.referenceType) && (
            <StandardizedSection
              title="Reference Information"
              icon={<FileText className="h-4 w-4" />}
              className="h-fit"
              compact={true}
            >
              <div className="space-y-2">
                {transaction.referenceType && (
                  <div>
                    <p className="text-sm text-muted-foreground">Reference Type</p>
                    <Badge variant="outline">{transaction.referenceType}</Badge>
                  </div>
                )}
                {transaction.referenceNumber && (
                  <div>
                    <p className="text-sm text-muted-foreground">Reference Number</p>
                    <p className="font-mono text-sm">{transaction.referenceNumber}</p>
                  </div>
                )}
                {transaction.reference && (
                  <div>
                    <p className="text-sm text-muted-foreground">Full Reference</p>
                    <p className="text-sm">{transaction.reference}</p>
                  </div>
                )}
              </div>
            </StandardizedSection>
          )}

          {/* User Information */}
          <StandardizedSection
            title="User Information"
            icon={<User className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">User Name</p>
                <p className="font-medium text-foreground">{transaction.userName || 'Unknown User'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">User ID</p>
                <p className="font-mono text-sm">{transaction.userId || 'N/A'}</p>
              </div>
            </div>
          </StandardizedSection>
        </div>

        {/* Notes */}
        {transaction.notes && transaction.notes !== '-' && (
          <StandardizedSection
            title="Notes"
            icon={<FileText className="h-4 w-4" />}
            compact={true}
          >
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{transaction.notes}</p>
            </div>
          </StandardizedSection>
        )}
      </div>
    </StandardizedViewContent>
  );
}
