'use client';

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { ProductFormModal } from '@/app/components/forms/ProductFormModal';
import { Product } from '@/app/components/tables/ProductsTable/types';
import { cn } from '@/app/lib/utils';
import { PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface QuickEditProductActionProps {
  product: Product;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Quick edit product action component
 * Opens the ProductFormModal in edit mode with the product data loaded
 */
export function QuickEditProductAction({
  product,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: QuickEditProductActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Open the modal for editing
  const handleOpenModal = () => {
    console.log('Opening quick edit modal for product:', product._id);
    setIsModalOpen(true);
  };

  // Close the modal
  const handleCloseModal = () => {
    console.log('Closing quick edit modal for product:', product._id);
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
    router.refresh();
  };

  // Render button based on variant
  const renderButton = () => {
    const buttonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        handleOpenModal();
      },
      id: id || `quick-edit-product-${product._id}`,
    };

    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            className={cn("flex-shrink-0", className)}
            {...buttonProps}
          >
            <PencilIcon size={16} className="mr-2" />
            Edit Product
          </Button>
        );

      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <PencilIcon size={16} className="mr-2" />
            Edit Product
          </Button>
        );

      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn("h-8 w-8 p-0", className)}
                  {...buttonProps}
                  style={{ position: 'relative', zIndex: 30 }}
                >
                  <span className="sr-only">Edit Product</span>
                  <PencilIcon size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit Product</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {isModalOpen && (
        <ProductFormModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          productId={product._id}
          mode="edit"
          onSuccess={handleCloseModal}
        />
      )}
    </>
  );
}
