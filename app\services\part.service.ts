import mongoose, { Types } from 'mongoose';
// import { InjectModel } from '@nestjs/mongoose'; // Example if using NestJS
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import Part, { IPart, IPartDocument } from '../models/part.model';
import Inventories from '../models/inventories.model';
import Warehouse from '../models/warehouse.model';
import { deletePart } from './mongodb'; // Import deletePart from mongodb.ts
// PERFORMANCE OPTIMIZATION: Import caching utilities
import { generatePartsKey, generateSearchKey, invalidatePattern, withCache } from '../lib/cache';
// PERFORMANCE MONITORING: Import N+1 detection utilities
import { monitorDatabaseOperation } from '../lib/n1-detection';

// PERFORMANCE OPTIMIZATION: Enhanced logger with performance tracking
const logOperation = (operation: string, entity: string, details?: any, duration?: number) => {
  const timestamp = new Date().toISOString();
  const performanceInfo = duration ? ` (${duration}ms)` : '';
  console.log(`[PartService][${timestamp}] ${operation} on ${entity}${performanceInfo}${details ? ': ' + JSON.stringify(details) : ''}`);

  // Log slow operations for monitoring
  if (duration && duration > 500) {
    console.warn(`[PartService][SLOW QUERY] ${operation} on ${entity} took ${duration}ms`);
    setTag('performance.slow_query', 'true');
    setTag('performance.operation', operation);
    setTag('performance.duration', duration.toString());
  }
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[PartService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'part');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// import { PartNotFoundException, DuplicatePartNumberException, InvalidStockOperationException } from '../common/exceptions/part.exceptions.ts'; // Custom exceptions to be defined

// Local IPart interface removed, using IPart and IPartDocument from part.model.ts directly.

// REMOVED: Legacy aggregation pipeline for embedded inventory structure
// V4 Schema: Parts and inventories are now separate collections
// No need for backward compatibility reconstruction






// V4 SCHEMA ALIGNMENT: Part service only manages part master data
// Inventory data is managed separately via inventory.service.ts
export interface CreatePartDto {
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete'; // Aligned with part.model.ts

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  // V4 Schema: Removed inventory field - use inventories collection instead
  supplierId?: string | null; // Will be converted to ObjectId
  unitOfMeasure: string;
  standardCost: number; // Changed from costPrice to match target schema
  abcClassification?: string | null; // NEW FIELD: ABC classification (A, B, C)
  categoryId?: string | null; // Will be converted to ObjectId
}

// UpdatePartDto allows partial updates to allowed fields.
// V4 Schema: No inventory fields - use inventory.service.ts for stock management
export interface UpdatePartDto {
  name?: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured?: boolean;
  reorderLevel?: number | null;
  status?: 'active' | 'inactive' | 'obsolete';

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  // V4 Schema: Removed inventory field - use inventories collection instead
  supplierId?: string | null;
  unitOfMeasure?: string;
  standardCost?: number; // Changed from costPrice to match target schema
  abcClassification?: string | null; // NEW FIELD: ABC classification (A, B, C)
  categoryId?: string | null;
}

// This structure uses exported functions rather than a class, 
// aligning with the existing code snippet provided for this file.
// If a class-based service is preferred, that can be adjusted.

/**
 * Creates a new part.
 */
export async function createPart(partData: CreatePartDto): Promise<IPartDocument> {
  logOperation('CREATE', 'service', { partNumber: partData.partNumber });
  await connectToDatabase();
  try {
    const existingPart = await Part.findOne({ partNumber: partData.partNumber }).exec();
    if (existingPart) {
      throw new Error(`Part with number ${partData.partNumber} already exists.`); // Consider custom error
    }

    // V4 Schema: Create part without embedded inventory - use inventories collection instead
    const partToSave = new Part({
      partNumber: partData.partNumber,
      name: partData.name,
      businessName: partData.businessName || null,
      description: partData.description || null,
      technicalSpecs: partData.technicalSpecs || null,
      isManufactured: partData.isManufactured || false,
      reorderLevel: partData.reorderLevel || null,
      status: partData.status || 'active',

      // NEW: Part Master Data Planning Parameters
      planningMethod: partData.planningMethod || null,
      safetyStockLevel: partData.safetyStockLevel || null,
      maximumStockLevel: partData.maximumStockLevel || null,
      leadTimeDays: partData.leadTimeDays || null,
      averageDailyUsage: partData.averageDailyUsage || null,

      supplierId: partData.supplierId ? new Types.ObjectId(partData.supplierId) : null,
      unitOfMeasure: partData.unitOfMeasure,
      standardCost: partData.standardCost, // Changed from costPrice to match target schema
      abcClassification: partData.abcClassification || null, // NEW FIELD: ABC classification
      categoryId: partData.categoryId ? new Types.ObjectId(partData.categoryId) : null,
    });

    const savedPart = await partToSave.save();

    // REMOVED: V4 Schema inventory creation - use inventory.service.ts instead

    logOperation('CREATE_SUCCESS', 'service', { partId: savedPart._id, partNumber: savedPart.partNumber });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when new part is created
    invalidatePattern('parts:');
    invalidatePattern('search:');

    return savedPart as IPartDocument;
  } catch (error: any) {
    logOperation('CREATE_ERROR', 'service', { partNumber: partData.partNumber, error: error.message });
    if (error.message.includes('already exists')) {
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate duplicate error for consistent handling
        throw new Error(errDetails.message || `Part with number ${partData.partNumber} already exists.`);
    }
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create part');
  }
}

/**
 * Retrieves a part by its MongoDB ObjectId.
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 */
export async function getPartById(id: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_ID', 'service', { id });
  await connectToDatabase();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }
  try {
    // V4 SCHEMA AGGREGATION PIPELINE: Includes inventories collection lookup for stock data
    const pipeline = [
      // Match specific part by ID
      { $match: { _id: new Types.ObjectId(id) } },

      // V4 Schema: Lookup inventories collection for stock data
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },

      // Calculate aggregated stock data
      {
        $addFields: {
          currentStock: {
            $sum: {
              $map: {
                input: '$inventoryRecords',
                as: 'inv',
                in: '$$inv.quantity'
              }
            }
          },
          stockLevels: {
            $reduce: {
              input: '$inventoryRecords',
              initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              in: {
                raw: { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, { $add: ['$$value.raw', '$$this.quantity'] }, '$$value.raw'] },
                hardening: { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, { $add: ['$$value.hardening', '$$this.quantity'] }, '$$value.hardening'] },
                grinding: { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, { $add: ['$$value.grinding', '$$this.quantity'] }, '$$value.grinding'] },
                finished: { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, { $add: ['$$value.finished', '$$this.quantity'] }, '$$value.finished'] },
                rejected: { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, { $add: ['$$value.rejected', '$$this.quantity'] }, '$$value.rejected'] }
              }
            }
          }
        }
      },

      // Optimized lookups with minimal projections
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [{ $project: { name: 1, contactPerson: 1 } }]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [{ $project: { name: 1 } }]
        }
      },

      // Simplified transformation
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // Explicit projection to ensure businessName is included
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          currentStock: 1,
          stockLevels: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    const parts = await Part.aggregate(pipeline).exec() as IPart[];
    const part = parts.length > 0 ? parts[0] : null;

    if (!part) {
      logOperation('GET_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    logOperation('GET_BY_ID_SUCCESS', 'service', { id });
    return part as IPartDocument;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', 'service', { id, error: error.message });
    handleMongoDBError(error);
    return null; // Or rethrow, depending on desired error handling strategy
  }
}

/**
 * Retrieves a part by its unique partNumber.
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 */
export async function getPartByPartNumberService(partNumber: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_PARTNUMBER', 'service', { partNumber });
  await connectToDatabase();
  try {
    // V4 SCHEMA AGGREGATION PIPELINE: Includes inventories collection lookup for stock data
    const pipeline = [
      // Match specific part by partNumber
      { $match: { partNumber } },

      // V4 Schema: Lookup inventories collection for stock data
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },

      // Calculate aggregated stock data
      {
        $addFields: {
          currentStock: {
            $sum: {
              $map: {
                input: '$inventoryRecords',
                as: 'inv',
                in: '$$inv.quantity'
              }
            }
          },
          stockLevels: {
            $reduce: {
              input: '$inventoryRecords',
              initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              in: {
                raw: { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, { $add: ['$$value.raw', '$$this.quantity'] }, '$$value.raw'] },
                hardening: { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, { $add: ['$$value.hardening', '$$this.quantity'] }, '$$value.hardening'] },
                grinding: { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, { $add: ['$$value.grinding', '$$this.quantity'] }, '$$value.grinding'] },
                finished: { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, { $add: ['$$value.finished', '$$this.quantity'] }, '$$value.finished'] },
                rejected: { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, { $add: ['$$value.rejected', '$$this.quantity'] }, '$$value.rejected'] }
              }
            }
          }
        }
      },

      // Optimized lookups with minimal projections
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [{ $project: { name: 1, contactPerson: 1 } }]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [{ $project: { name: 1 } }]
        }
      },

      // Simplified transformation
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // Explicit projection to ensure businessName is included
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          currentStock: 1,
          stockLevels: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    const parts = await Part.aggregate(pipeline).exec() as IPart[];
    const part = parts.length > 0 ? parts[0] : null;

    if (!part) {
      logOperation('GET_BY_PARTNUMBER_NOT_FOUND', 'service', { partNumber });
      return null;
    }
    logOperation('GET_BY_PARTNUMBER_SUCCESS', 'service', { partNumber });
    return part as IPartDocument;
  } catch (error: any) {
    logOperation('GET_BY_PARTNUMBER_ERROR', 'service', { partNumber, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Retrieves all parts, possibly with filters and pagination.
 * UNIFIED: Uses aggregation pipeline with inventories collection integration
 * PERFORMANCE: Includes caching for frequently accessed data
 * ENHANCED: Production-ready error handling with fallback mechanisms
 */
export async function getAllParts(options: any = {}): Promise<{ parts: IPart[], pagination: any }> {
  const {
    page = 1,
    limit = 50, // Balanced default for good UX and performance
    sort = { updatedAt: -1 },
    filter = {},
    status,
    search
  } = options;

  const startTime = Date.now();
  logOperation('GET_ALL', 'service', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter), status, search });

  // PERFORMANCE OPTIMIZATION: Generate cache key for this request
  const cacheKey = generatePartsKey(page, limit, filter, sort);

  // Try to get from cache first (cache for 30 seconds for frequently accessed data)
  return withCache(cacheKey, async () => {
    await connectToDatabase();

    // PRODUCTION FIX: Enhanced error handling with fallback mechanisms
    try {
      const skip = (page - 1) * limit;

      // Build match stage with enhanced filtering
      const matchStage: any = {};
      if (filter.status || status) matchStage.status = filter.status || status;
      if (filter.isManufactured !== undefined) matchStage.isManufactured = filter.isManufactured;
      if (filter.categoryId) matchStage.categoryId = new Types.ObjectId(filter.categoryId);
      if (filter.supplierId) matchStage.supplierId = new Types.ObjectId(filter.supplierId);

      // Add search functionality
      if (search) {
        matchStage.$or = [
          { partNumber: { $regex: search, $options: 'i' } },
          { name: { $regex: search, $options: 'i' } },
          { businessName: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // PRODUCTION FIX: Try optimized aggregation first, with fallback on failure
      try {
        return await executeOptimizedAggregation(matchStage, sort, skip, limit, startTime);
      } catch (aggregationError: any) {
        console.error('[PRODUCTION ERROR] Optimized aggregation failed, attempting fallback:', {
          error: aggregationError.message,
          stack: aggregationError.stack,
          duration: Date.now() - startTime,
          matchStage,
          options: { page, limit, sort }
        });

        logOperation('GET_ALL_FALLBACK', 'service', {
          error: aggregationError.message,
          duration: Date.now() - startTime
        });

        // Fallback to simpler query without complex aggregations
        return await executeSimpleFallback(matchStage, sort, skip, limit, startTime);
      }

      // This section is now handled by executeOptimizedAggregation function
      throw new Error('This code path should not be reached - aggregation moved to helper functions');
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.error('[PRODUCTION ERROR] getAllParts failed completely:', {
        error: error.message,
        stack: error.stack,
        duration,
        options: { page, limit, sort }
      });

      logOperation('GET_ALL_ERROR', 'service', {
        error: error.message,
        duration
      });

      const errDetails = handleMongoDBError(error);
      throw new Error(errDetails.message || 'Failed to fetch all parts');
    }
  }, 30000); // Cache for 30 seconds
}

/**
 * Updates an existing part.
 */
export async function updatePartService(id: string, updateData: UpdatePartDto): Promise<IPartDocument | null> {
  logOperation('UPDATE', 'service', { partId: id, updateData });
  await connectToDatabase();

  if (!mongoose.Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_INVALID_ID', 'service', { partId: id });
    throw new Error('Invalid Part ID format'); // Consider custom error
  }

  try {
    // Prepare a mutable copy of updateData for modifications
    const updatePayload: any = { ...updateData };

    // Convert string IDs to ObjectIds for other references if they exist in updateData
    if (updatePayload.supplierId && typeof updatePayload.supplierId === 'string') {
      updatePayload.supplierId = new Types.ObjectId(updatePayload.supplierId);
    }
    if (updatePayload.categoryId && typeof updatePayload.categoryId === 'string') {
      updatePayload.categoryId = new Types.ObjectId(updatePayload.categoryId);
    }
    
    // V4 Schema: No inventory updates in part service - use inventory.service.ts instead

    const updatedPart = await Part.findByIdAndUpdate(id, updatePayload, { new: true, runValidators: true }).exec();
    if (!updatedPart) {
      logOperation('UPDATE_NOT_FOUND', 'service', { partId: id });
      throw new Error(`Part not found with id ${id}, unable to update.`);
    }

    // REMOVED: V4 Schema inventory updates - use inventory.service.ts instead

    logOperation('UPDATE_SUCCESS', 'service', { partId: id, updatedFields: Object.keys(updateData) });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when part is updated
    invalidatePattern('parts:');
    invalidatePattern('search:');

    return updatedPart as IPartDocument | null;
  } catch (error: any) {
    logOperation('UPDATE_ERROR', 'service', { partId: id, error: error.message });
    // Example of specific error handling, adapt as needed
    if (error.code === 11000 || (error.message && error.message.includes('duplicate key'))) { 
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate specific error for handler
        throw new Error(errDetails.message || 'A data conflict occurred: a unique identifier may already exist.');
    }
    const errDetails = handleMongoDBError(error); // General handler
    throw new Error(errDetails.message || `Failed to update part ${id}`);
  }
}

/**
 * Deletes a part by its ObjectId, utilizing the comprehensive deletePart logic from mongodb.ts.
 */
export async function deletePartService(id: string): Promise<void> {
  logOperation('SERVICE_DELETE_PART_START', 'service', { partId: id });
  await connectToDatabase(); // Ensure connection

  if (!Types.ObjectId.isValid(id)) {
    logOperation('SERVICE_DELETE_PART_INVALID_ID', 'service', { partId: id });
    const err = new Error('Invalid Part ID format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    // Call the centralized deletePart function from mongodb.ts
    const result = await deletePart(id);

    if (!result.success) {
      logOperation('SERVICE_DELETE_PART_FAILED_FROM_DB', 'service', { partId: id, message: result.message, statusCode: result.statusCode });
      const error = new Error(result.message);
      (error as any).statusCode = result.statusCode || 500;
      throw error;
    }

    logOperation('SERVICE_DELETE_PART_SUCCESS', 'service', { partId: id });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when part is deleted
    invalidatePattern('parts:');
    invalidatePattern('search:');

    // On successful deletion, no specific document is returned by deletePart from mongodb.ts
  } catch (error: any) {
    if (error.statusCode) {
        logOperation('SERVICE_DELETE_PART_ERROR_WITH_STATUS', 'service', { partId: id, message: error.message, statusCode: error.statusCode });
        throw error; // Re-throw the error with its existing statusCode
    }
    
    logOperation('SERVICE_DELETE_PART_ERROR_UNHANDLED', 'service', { partId: id, originalError: error.message });
    // Using the local handleMongoDBError for now
    const errDetails = handleMongoDBError(error);
    const newError = new Error(errDetails.message || `Failed to delete part ${id}`);
    (newError as any).statusCode = errDetails.status || 500;
    throw newError;
  }
}

// REMOVED: adjustStockLevelByDelta function - deprecated in V4 Schema
// Use InventoryService and StockMovementService for stock adjustments

// REMOVED: updatePartInventoryInV4Schema function
// V4 Schema: Inventory updates are handled by inventory.service.ts
// Parts service only manages part master data




/**
 * Checks if a part is below its reorder level.
 * V4 Schema: Uses inventory service to get stock data
 */
export async function checkReorderPoint(partId: string): Promise<boolean> {
  logOperation('CHECK_REORDER_POINT', 'service', { partId });

  try {
    await connectToDatabase();

    // Get part details to check reorder level
    const part = await getPartById(partId);
    if (!part || part.reorderLevel === null || part.reorderLevel === undefined) {
      logOperation('CHECK_REORDER_POINT_NOT_APPLICABLE', 'service', { partId, reorderLevel: part?.reorderLevel });
      return false;
    }

    // V4 Schema: Use inventory service to get stock summary
    const { InventoriesService } = await import('./inventory.service');
    const stockSummary = await InventoriesService.getPartStockSummary(partId);

    const totalStock = stockSummary.length > 0 ? stockSummary[0]?.totalStock || 0 : 0;
    const needsReorder = totalStock <= part.reorderLevel;

    logOperation('CHECK_REORDER_POINT_RESULT', 'service', {
      partId,
      totalStock,
      reorderLevel: part.reorderLevel,
      needsReorder
    });

    return needsReorder;
  } catch (error: any) {
    logOperation('CHECK_REORDER_POINT_ERROR', 'service', { partId, error: error.message });
    throw error;
  }
}

/**
 * Search for parts with text search and filtering options
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 * PERFORMANCE: Includes caching for search results
 */
export async function searchParts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 50, // Consistent with getAllParts default
    sort = { updatedAt: -1 },
    filter = {}
  } = options;

  logOperation('SEARCH', 'service', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  // PERFORMANCE OPTIMIZATION: Generate cache key for search results
  const cacheKey = generateSearchKey(query, page, limit, filter);

  // Cache search results for 15 seconds (shorter than regular queries due to dynamic nature)
  return withCache(cacheKey, async () => {

    // Declare variables outside try block for use in catch block
    const skip = (page - 1) * limit;
    let searchFilter: any = { ...filter };

  try {
    await connectToDatabase();

    // PERFORMANCE OPTIMIZATION: Use text search index when available
    if (query) {
      // Use text search index for better performance on longer queries
      if (query.length > 2) {
        searchFilter.$text = { $search: query };
      } else {
        // Fallback to regex for short queries
        searchFilter.$or = [
          { partNumber: { $regex: query, $options: 'i' } },
          { name: { $regex: query, $options: 'i' } },
          { businessName: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } }
        ];
      }
    }

    // V4 SCHEMA SEARCH AGGREGATION PIPELINE: Optimized with performance improvements
    const pipeline = [
      // 1. EARLY FILTERING: Apply search filters first to reduce dataset size
      { $match: searchFilter },

      // V4 Schema: OPTIMIZED lookup with performance improvements
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords',
          // PERFORMANCE: Filter out zero quantities during lookup
          pipeline: [
            {
              $match: {
                $expr: { $ne: ['$quantity', 0] }
              }
            }
          ]
        }
      },

      // Calculate aggregated stock data
      {
        $addFields: {
          currentStock: {
            $sum: {
              $map: {
                input: '$inventoryRecords',
                as: 'inv',
                in: '$$inv.quantity'
              }
            }
          },
          stockLevels: {
            $reduce: {
              input: '$inventoryRecords',
              initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              in: {
                raw: { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, { $add: ['$$value.raw', '$$this.quantity'] }, '$$value.raw'] },
                hardening: { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, { $add: ['$$value.hardening', '$$this.quantity'] }, '$$value.hardening'] },
                grinding: { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, { $add: ['$$value.grinding', '$$this.quantity'] }, '$$value.grinding'] },
                finished: { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, { $add: ['$$value.finished', '$$this.quantity'] }, '$$value.finished'] },
                rejected: { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, { $add: ['$$value.rejected', '$$this.quantity'] }, '$$value.rejected'] }
              }
            }
          }
        }
      },

      // 3. OPTIMIZED LOOKUPS: Use pipeline projections to minimize data transfer
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [
            { $project: { name: 1, contactPerson: 1 } }
          ]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [
            { $project: { name: 1 } }
          ]
        }
      },

      // 4. SORTING: Apply sorting before pagination for consistent results
      { $sort: sort },

      // 5. PAGINATION: Apply after all transformations
      { $skip: skip },
      { $limit: limit },

      // 6. SIMPLIFIED TRANSFORMATION: Use simpler field mapping
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // 7. EXPLICIT PROJECTION: Ensure all required fields are included, especially businessName
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName, default to null if missing
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          currentStock: 1,
          stockLevels: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    // PERFORMANCE MONITORING: Monitor search aggregation pipeline execution
    console.log('[SEARCH DEBUG] Executing aggregation pipeline:', JSON.stringify(pipeline, null, 2));

    const [parts, totalCount] = await Promise.all([
      monitorDatabaseOperation('aggregate', 'parts', pipeline, () =>
        Part.aggregate(pipeline).exec() as Promise<IPart[]>
      ),
      monitorDatabaseOperation('countDocuments', 'parts', searchFilter, () =>
        Part.countDocuments(searchFilter)
      )
    ]);

    console.log('[SEARCH DEBUG] Aggregation results:', {
      partsCount: parts.length,
      firstPart: parts[0] ? {
        _id: (parts[0] as any)._id,
        name: parts[0].name,
        currentStock: (parts[0] as any).currentStock,
        inventoryRecords: (parts[0] as any).inventoryRecords?.length || 0,
        stockLevels: (parts[0] as any).stockLevels
      } : null
    });

    const pagination = {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
    };

    console.log('[SEARCH DEBUG] Returning successful aggregation results:', {
      query,
      count: parts.length,
      pagination,
      sampleStockData: parts.slice(0, 3).map(p => ({
        name: p.name,
        currentStock: (p as any).currentStock,
        stockLevels: (p as any).stockLevels
      }))
    });

    logOperation('SEARCH_SUCCESS', 'service', { query, count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    console.error('[SEARCH ERROR] Complex aggregation failed, attempting fallback:', {
      error: error.message,
      stack: error.stack,
      query,
      filter: JSON.stringify(filter)
    });

    // FALLBACK: Try simple search with actual stock calculation
    try {
      const fallbackParts = await Part.find(searchFilter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      const totalCount = await Part.countDocuments(searchFilter);

      // Calculate actual stock levels for each part using inventory aggregation
      const partsWithRealStock = await Promise.all(
        fallbackParts.map(async (part: any) => {
          try {
            // Get inventory data for this specific part
            const inventoryData = await Inventories.aggregate([
              { $match: { partId: part._id } },
              {
                $group: {
                  _id: null,
                  totalStock: { $sum: '$quantity' },
                  stockLevels: {
                    $push: {
                      stockType: '$stockType',
                      quantity: '$quantity'
                    }
                  }
                }
              }
            ]).exec();

            let stockLevels = { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 };
            let totalStock = 0;

            if (inventoryData.length > 0) {
              totalStock = inventoryData[0].totalStock || 0;

              // Calculate stock levels by type
              inventoryData[0].stockLevels?.forEach((stock: any) => {
                if (stock.stockType in stockLevels) {
                  stockLevels[stock.stockType as keyof typeof stockLevels] += stock.quantity || 0;
                }
              });
            }

            return {
              ...part,
              currentStock: totalStock,
              stockLevels,
              inventory: {
                stockLevels,
                totalStock,
                currentStock: totalStock
              }
            };
          } catch (stockError) {
            console.warn(`Failed to calculate stock for part ${part._id}:`, stockError);
            // Fallback to 0 for this specific part if stock calculation fails
            return {
              ...part,
              currentStock: 0,
              stockLevels: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              inventory: {
                stockLevels: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
                totalStock: 0,
                currentStock: 0
              }
            };
          }
        })
      );

      const pagination = {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      };

      console.log('[SEARCH FALLBACK] Simple search with real stock calculation completed:', {
        query,
        count: partsWithRealStock.length,
        note: 'Using fallback query with actual stock levels calculated'
      });

      logOperation('SEARCH_FALLBACK_SUCCESS', 'service', { query, count: partsWithRealStock.length, pagination });
      return { parts: partsWithRealStock, pagination };

    } catch (fallbackError: any) {
      console.error('[SEARCH FALLBACK ERROR] Both complex and simple search failed:', {
        originalError: error.message,
        fallbackError: fallbackError.message,
        query
      });

      logOperation('SEARCH_ERROR', 'service', { query, error: error.message, fallbackError: fallbackError.message });
      const errDetails = handleMongoDBError(error);
      throw new Error(errDetails.message || 'Failed to search parts');
    }
  }
  }, 15000); // Cache for 15 seconds
}

// Unit Test Considerations:
// - Mock connectToDatabase, Part model methods (findOne, findById, save, etc.)
// - Test successful CRUD operations.
// - Test error handling: duplicate partNumber, validation errors, part not found, invalid IDs.
// - Test business logic: stock updates, reorder point checks.
// - Test pagination and filtering in getAllParts and searchParts.

// Export an instance of the service if not using DI framework like NestJS, or export the class itself.
// export const partService = new PartService();

/**
 * PRODUCTION FIX: Optimized aggregation execution with enhanced error handling
 * This function attempts to execute the complex aggregation pipeline with proper timeout and error handling
 */
async function executeOptimizedAggregation(
  matchStage: any,
  sort: any,
  skip: number,
  limit: number,
  startTime: number
): Promise<{ parts: IPart[], pagination: any }> {
  console.log('[PRODUCTION DEBUG] Starting optimized aggregation pipeline');

  try {
    // V4 SCHEMA AGGREGATION PIPELINE: Optimized with proper indexing hints and timeout
    const pipeline = [
      // 1. EARLY FILTERING: Apply filters first to reduce dataset size
      ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),

      // V4 Schema: OPTIMIZED lookup with index hint for better performance
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords',
          // PERFORMANCE: Use the compound index for optimal lookup performance
          pipeline: [
            {
              $match: {
                $expr: { $ne: ['$quantity', 0] } // Only include non-zero quantities
              }
            },
            // PRODUCTION FIX: Limit inventory records per part to prevent memory issues
            { $limit: 1000 }
          ]
        }
      },

      // Calculate aggregated stock data with error handling
      {
        $addFields: {
          currentStock: {
            $sum: {
              $map: {
                input: '$inventoryRecords',
                as: 'inv',
                in: { $ifNull: ['$$inv.quantity', 0] } // Handle null quantities
              }
            }
          },
          stockLevels: {
            $reduce: {
              input: '$inventoryRecords',
              initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              in: {
                raw: { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, { $add: ['$$value.raw', { $ifNull: ['$$this.quantity', 0] }] }, '$$value.raw'] },
                hardening: { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, { $add: ['$$value.hardening', { $ifNull: ['$$this.quantity', 0] }] }, '$$value.hardening'] },
                grinding: { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, { $add: ['$$value.grinding', { $ifNull: ['$$this.quantity', 0] }] }, '$$value.grinding'] },
                finished: { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, { $add: ['$$value.finished', { $ifNull: ['$$this.quantity', 0] }] }, '$$value.finished'] },
                rejected: { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, { $add: ['$$value.rejected', { $ifNull: ['$$this.quantity', 0] }] }, '$$value.rejected'] }
              }
            }
          }
        }
      },

      // 3. OPTIMIZED LOOKUPS: Use pipeline projections to minimize data transfer
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [
            { $project: { name: 1, contactPerson: 1 } }
          ]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [
            { $project: { name: 1 } }
          ]
        }
      },

      // 4. SORTING: Apply sorting before pagination for consistent results
      { $sort: sort },

      // 5. PAGINATION: Apply after all transformations
      { $skip: skip },
      { $limit: limit },

      // 6. SIMPLIFIED TRANSFORMATION: Use simpler field mapping
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          // Ensure businessName field is always present, even if it doesn't exist in the document
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // 7. EXPLICIT PROJECTION: Ensure all required fields are included, especially businessName
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName, default to null if missing
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          currentStock: 1,
          stockLevels: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    console.log('[PRODUCTION DEBUG] Executing aggregation pipeline with', pipeline.length, 'stages');

    // PRODUCTION FIX: Add explicit timeout and enhanced monitoring
    const aggregationPromise = Part.aggregate(pipeline, { maxTimeMS: 30000 })
      .exec() as Promise<IPart[]>;

    const countPromise = Object.keys(matchStage).length > 0
      ? Part.countDocuments(matchStage, { maxTimeMS: 10000 }) // 10 second timeout for count
      : Part.countDocuments({}, { maxTimeMS: 10000 });

    // PERFORMANCE MONITORING: Monitor aggregation pipeline execution with timeout
    const [parts, totalCount] = await Promise.all([
      monitorDatabaseOperation('aggregate', 'parts', pipeline, () => aggregationPromise),
      monitorDatabaseOperation('countDocuments', 'parts', matchStage, () => countPromise)
    ]);

    const duration = Date.now() - startTime;
    console.log('[PRODUCTION DEBUG] Optimized aggregation completed successfully in', duration, 'ms');

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: Math.ceil(skip / limit) + 1,
      limit,
    };

    logOperation('GET_ALL_SUCCESS', 'service', { count: parts.length, pagination, duration });
    return { parts, pagination };

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error('[PRODUCTION ERROR] Optimized aggregation failed:', {
      error: error.message,
      duration,
      errorCode: error.code,
      errorName: error.name
    });

    // Re-throw with enhanced error information for fallback handling
    const enhancedError = new Error(`Aggregation pipeline failed: ${error.message}`);
    (enhancedError as any).originalError = error;
    (enhancedError as any).duration = duration;
    throw enhancedError;
  }
}

/**
 * PRODUCTION FIX: Simple fallback query when complex aggregation fails
 * This function provides a basic parts list without complex inventory calculations
 */
async function executeSimpleFallback(
  matchStage: any,
  sort: any,
  skip: number,
  limit: number,
  startTime: number
): Promise<{ parts: IPart[], pagination: any }> {
  console.log('[PRODUCTION DEBUG] Executing simple fallback query');

  try {
    // Simple query without complex aggregations
    const partsQuery = Part.find(matchStage, {}, { maxTimeMS: 15000 })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(); // 15 second timeout for fallback

    const countQuery = Object.keys(matchStage).length > 0
      ? Part.countDocuments(matchStage, { maxTimeMS: 5000 })
      : Part.countDocuments({}, { maxTimeMS: 5000 });

    const [parts, totalCount] = await Promise.all([partsQuery, countQuery]);

    // Add basic stock information (set to 0 since we can't calculate it)
    const partsWithBasicStock = parts.map((part: any) => ({
      ...part,
      currentStock: 0,
      stockLevels: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
      supplier: null,
      category: null,
      businessName: part.businessName || null
    }));

    const duration = Date.now() - startTime;
    console.log('[PRODUCTION DEBUG] Simple fallback completed in', duration, 'ms');

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: Math.ceil(skip / limit) + 1,
      limit,
    };

    logOperation('GET_ALL_FALLBACK_SUCCESS', 'service', {
      count: partsWithBasicStock.length,
      pagination,
      duration,
      note: 'Using fallback query - stock levels set to 0'
    });

    return { parts: partsWithBasicStock as IPart[], pagination };

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error('[PRODUCTION ERROR] Simple fallback also failed:', {
      error: error.message,
      duration,
      errorCode: error.code,
      errorName: error.name
    });

    logOperation('GET_ALL_FALLBACK_ERROR', 'service', {
      error: error.message,
      duration
    });

    // If even the simple fallback fails, throw a comprehensive error
    throw new Error(`Both optimized aggregation and simple fallback failed. Database may be unavailable. Error: ${error.message}`);
  }
}