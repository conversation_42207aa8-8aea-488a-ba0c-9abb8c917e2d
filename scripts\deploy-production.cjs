#!/usr/bin/env node

/**
 * Production Deployment Script for Trend IMS
 * Handles environment setup and port configuration for production deployment
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const DEFAULT_PRODUCTION_PORT = 3000;
const BUILD_DIR = '.next';

function log(message) {
  console.log(`[Deploy] ${message}`);
}

function error(message) {
  console.error(`[Deploy Error] ${message}`);
}

function checkPrerequisites() {
  log('Checking prerequisites...');
  
  // Check if package.json exists
  if (!fs.existsSync('package.json')) {
    error('package.json not found. Please run this script from the project root.');
    process.exit(1);
  }
  
  // Check if Next.js is installed
  try {
    execSync('npx next --version', { stdio: 'pipe' });
    log('✅ Next.js is available');
  } catch (err) {
    error('Next.js not found. Please install dependencies first: npm install');
    process.exit(1);
  }
}

function setupProductionEnvironment() {
  log('Setting up production environment...');
  
  // Set NODE_ENV to production
  process.env.NODE_ENV = 'production';
  
  // Set default port if not specified
  if (!process.env.PORT) {
    process.env.PORT = DEFAULT_PRODUCTION_PORT.toString();
    log(`Using default production port: ${DEFAULT_PRODUCTION_PORT}`);
  } else {
    log(`Using specified port: ${process.env.PORT}`);
  }
  
  // Run environment setup script
  try {
    execSync('node scripts/create-env.cjs', { stdio: 'inherit' });
    log('✅ Environment setup complete');
  } catch (err) {
    error('Failed to setup environment');
    process.exit(1);
  }
}

function buildApplication() {
  log('Building application for production...');
  
  // Clean previous build
  if (fs.existsSync(BUILD_DIR)) {
    log('Cleaning previous build...');
    execSync(`rm -rf ${BUILD_DIR}`, { stdio: 'inherit' });
  }
  
  // Build the application
  try {
    execSync('npm run build', { stdio: 'inherit' });
    log('✅ Build completed successfully');
  } catch (err) {
    error('Build failed');
    process.exit(1);
  }
}

function startProductionServer() {
  const port = process.env.PORT || DEFAULT_PRODUCTION_PORT;
  
  log(`Starting production server on port ${port}...`);
  log(`Application will be available at: http://localhost:${port}`);
  log('Press Ctrl+C to stop the server');
  
  try {
    // Start the production server
    execSync(`npm run start`, { stdio: 'inherit' });
  } catch (err) {
    error('Failed to start production server');
    process.exit(1);
  }
}

function displayHelp() {
  console.log(`
Trend IMS Production Deployment Script

Usage:
  node scripts/deploy-production.cjs [options]

Options:
  --port <number>    Specify the port to run on (default: 3000)
  --build-only       Only build the application, don't start the server
  --help             Show this help message

Environment Variables:
  PORT               Port to run the server on
  NODE_ENV           Environment (automatically set to 'production')

Examples:
  node scripts/deploy-production.cjs
  PORT=8080 node scripts/deploy-production.cjs
  node scripts/deploy-production.cjs --port 8080
  node scripts/deploy-production.cjs --build-only
`);
}

function main() {
  const args = process.argv.slice(2);
  
  // Parse command line arguments
  let buildOnly = false;
  let customPort = null;
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--help':
        displayHelp();
        process.exit(0);
        break;
      case '--build-only':
        buildOnly = true;
        break;
      case '--port':
        if (i + 1 < args.length) {
          customPort = args[i + 1];
          i++; // Skip next argument
        } else {
          error('--port requires a port number');
          process.exit(1);
        }
        break;
      default:
        error(`Unknown option: ${args[i]}`);
        displayHelp();
        process.exit(1);
    }
  }
  
  // Set custom port if specified
  if (customPort) {
    process.env.PORT = customPort;
  }
  
  log('Starting production deployment...');
  
  checkPrerequisites();
  setupProductionEnvironment();
  buildApplication();
  
  if (!buildOnly) {
    startProductionServer();
  } else {
    log('✅ Build completed. Use "npm run start" to start the production server.');
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  log('Deployment script terminated by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Deployment script terminated');
  process.exit(0);
});

// Run the main function
main();
