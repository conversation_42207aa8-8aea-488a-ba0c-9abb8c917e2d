import mongoose, { Types } from 'mongoose';
import Inventories, { IInventories } from '../models/inventories.model';
import connectToDatabase from '../lib/mongodb';

/**
 * Inventory Aggregation Utilities for v3 Schema
 * 
 * This module provides helper functions to aggregate inventory data from the new
 * normalized `inventories` collection, replacing legacy embedded inventory fields.
 */

// Logger function for tracking inventory operations
const logInventoryOperation = (operation: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoryAggregation][${timestamp}] ${operation}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Get total current stock for a part across all warehouses and stock types
 * This replaces the legacy `current_stock` field
 */
export async function getTotalCurrentStock(partId: string | Types.ObjectId): Promise<number> {
  try {
    await connectToDatabase();
    const objectId = typeof partId === 'string' ? new Types.ObjectId(partId) : partId;
    
    logInventoryOperation('GET_TOTAL_CURRENT_STOCK', { partId: objectId.toString() });
    
    const result = await Inventories.aggregate([
      { $match: { partId: objectId } },
      { $group: { _id: null, totalStock: { $sum: '$quantity' } } }
    ]);
    
    const totalStock = result.length > 0 ? result[0].totalStock : 0;
    logInventoryOperation('GET_TOTAL_CURRENT_STOCK_SUCCESS', { partId: objectId.toString(), totalStock });
    
    return totalStock;
  } catch (error: any) {
    logInventoryOperation('GET_TOTAL_CURRENT_STOCK_ERROR', { partId, error: error.message });
    console.error('[InventoryAggregation] Error getting total current stock:', error);
    return 0; // Return 0 on error to prevent breaking functionality
  }
}

/**
 * Get available stock for a part (finished goods only)
 * This is useful for BOM calculations where only finished parts can be used
 */
export async function getAvailableStock(partId: string | Types.ObjectId): Promise<number> {
  try {
    await connectToDatabase();
    const objectId = typeof partId === 'string' ? new Types.ObjectId(partId) : partId;
    
    logInventoryOperation('GET_AVAILABLE_STOCK', { partId: objectId.toString() });
    
    const result = await Inventories.aggregate([
      { 
        $match: { 
          partId: objectId,
          stockType: 'finished' // Only count finished goods as available
        } 
      },
      { $group: { _id: null, availableStock: { $sum: '$quantity' } } }
    ]);
    
    const availableStock = result.length > 0 ? result[0].availableStock : 0;
    logInventoryOperation('GET_AVAILABLE_STOCK_SUCCESS', { partId: objectId.toString(), availableStock });
    
    return availableStock;
  } catch (error: any) {
    logInventoryOperation('GET_AVAILABLE_STOCK_ERROR', { partId, error: error.message });
    console.error('[InventoryAggregation] Error getting available stock:', error);
    return 0; // Return 0 on error to prevent breaking functionality
  }
}

/**
 * Get stock breakdown by type for a part
 * Returns an object with stock quantities for each stock type
 */
export async function getStockBreakdownByType(partId: string | Types.ObjectId): Promise<Record<string, number>> {
  try {
    await connectToDatabase();
    const objectId = typeof partId === 'string' ? new Types.ObjectId(partId) : partId;

    logInventoryOperation('GET_STOCK_BREAKDOWN_BY_TYPE', { partId: objectId.toString() });
    
    const result = await Inventories.aggregate([
      { $match: { partId: objectId } },
      { 
        $group: { 
          _id: '$stockType', 
          totalQuantity: { $sum: '$quantity' }
        } 
      }
    ]);
    
    // Convert array to object with default values
    const breakdown: Record<string, number> = {
      raw: 0,
      hardening: 0,
      grinding: 0,
      finished: 0,
      rejected: 0
    };
    
    result.forEach(item => {
      breakdown[item._id] = item.totalQuantity;
    });
    
    logInventoryOperation('GET_STOCK_BREAKDOWN_BY_TYPE_SUCCESS', { partId: objectId.toString(), breakdown });
    
    return breakdown;
  } catch (error: any) {
    logInventoryOperation('GET_STOCK_BREAKDOWN_BY_TYPE_ERROR', { partId, error: error.message });
    console.error('[InventoryAggregation] Error getting stock breakdown by type:', error);
    return { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 };
  }
}

/**
 * Get inventory data for multiple parts at once
 * This is optimized for BOM calculations where we need stock for many parts
 */
export async function getBulkInventoryData(partIds: (string | Types.ObjectId)[]): Promise<Map<string, number>> {
  try {
    await connectToDatabase();
    const objectIds = partIds.map(id => typeof id === 'string' ? new Types.ObjectId(id) : id);
    
    logInventoryOperation('GET_BULK_INVENTORY_DATA', { partCount: partIds.length });
    
    const result = await Inventories.aggregate([
      { $match: { partId: { $in: objectIds } } },
      { 
        $group: { 
          _id: '$partId', 
          totalStock: { $sum: '$quantity' }
        } 
      }
    ]);
    
    // Create a map for fast lookup
    const inventoryMap = new Map<string, number>();
    
    // Initialize all parts with 0 stock
    partIds.forEach(partId => {
      const idString = typeof partId === 'string' ? partId : partId.toString();
      inventoryMap.set(idString, 0);
    });
    
    // Update with actual stock values
    result.forEach(item => {
      inventoryMap.set(item._id.toString(), item.totalStock);
    });
    
    logInventoryOperation('GET_BULK_INVENTORY_DATA_SUCCESS', { 
      partCount: partIds.length, 
      foundCount: result.length 
    });
    
    return inventoryMap;
  } catch (error: any) {
    logInventoryOperation('GET_BULK_INVENTORY_DATA_ERROR', { partIds, error: error.message });
    console.error('[InventoryAggregation] Error getting bulk inventory data:', error);
    
    // Return empty map on error
    const inventoryMap = new Map<string, number>();
    partIds.forEach(partId => {
      const idString = typeof partId === 'string' ? partId : partId.toString();
      inventoryMap.set(idString, 0);
    });
    return inventoryMap;
  }
}

/**
 * Get inventory data with part information for display purposes
 * This replaces legacy queries that joined inventory data with parts
 */
export async function getInventoryWithPartInfo(options: {
  page?: number;
  limit?: number;
  sort?: any;
  filter?: any;
} = {}) {
  try {
    await connectToDatabase();
    
    const {
      page = 1,
      limit = 20,
      sort = { 'part.name': 1 },
      filter = {}
    } = options;
    
    const skip = (page - 1) * limit;
    
    logInventoryOperation('GET_INVENTORY_WITH_PART_INFO', { page, limit, filter });
    
    // Build aggregation pipeline
    const pipeline = [
      // Group by partId to get total stock per part
      {
        $group: {
          _id: '$partId',
          totalStock: { $sum: '$quantity' },
          stockBreakdown: {
            $push: {
              stockType: '$stockType',
              quantity: '$quantity',
              warehouseId: '$warehouseId'
            }
          },
          lastUpdated: { $max: '$lastUpdated' }
        }
      },
      // Lookup part information
      {
        $lookup: {
          from: 'parts',
          localField: '_id',
          foreignField: '_id',
          as: 'part'
        }
      },
      // Unwind part array (should be single element)
      {
        $unwind: {
          path: '$part',
          preserveNullAndEmptyArrays: true
        }
      },
      // Apply filters if any
      ...(Object.keys(filter).length > 0 ? [{ $match: filter }] : []),
      // Sort
      { $sort: sort },
      // Pagination
      { $skip: skip },
      { $limit: limit },
      // Project final structure
      {
        $project: {
          _id: 1,
          partId: '$_id',
          part: 1,
          totalStock: 1,
          stockBreakdown: 1,
          lastUpdated: 1
        }
      }
    ];
    
    const [inventoryRecords, totalCountResult] = await Promise.all([
      Inventories.aggregate(pipeline),
      Inventories.aggregate([
        {
          $group: {
            _id: '$partId'
          }
        },
        {
          $count: 'total'
        }
      ])
    ]);
    
    const totalCount = totalCountResult.length > 0 ? totalCountResult[0].total : 0;
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logInventoryOperation('GET_INVENTORY_WITH_PART_INFO_SUCCESS', { 
      count: inventoryRecords.length, 
      pagination 
    });
    
    return { inventoryRecords, pagination };
  } catch (error: any) {
    logInventoryOperation('GET_INVENTORY_WITH_PART_INFO_ERROR', { error: error.message });
    console.error('[InventoryAggregation] Error getting inventory with part info:', error);
    throw error;
  }
}

/**
 * Check if a part has sufficient stock for a given quantity
 * This is useful for BOM validation and order processing
 */
export async function hasInsufficientStock(partId: string | Types.ObjectId, requiredQuantity: number): Promise<boolean> {
  try {
    const availableStock = await getAvailableStock(partId);
    return availableStock < requiredQuantity;
  } catch (error: any) {
    console.error('[InventoryAggregation] Error checking stock sufficiency:', error);
    return true; // Assume insufficient stock on error for safety
  }
}

/**
 * Get low stock parts based on safety stock levels
 * This replaces legacy low stock queries
 */
export async function getLowStockParts(options: {
  page?: number;
  limit?: number;
  sort?: any;
} = {}) {
  try {
    await connectToDatabase();
    
    const {
      page = 1,
      limit = 20,
      sort = { totalStock: 1 }
    } = options;
    
    const skip = (page - 1) * limit;
    
    logInventoryOperation('GET_LOW_STOCK_PARTS', { page, limit });
    
    const pipeline = [
      // Group by partId to get total stock
      {
        $group: {
          _id: '$partId',
          totalStock: { $sum: '$quantity' },
          safetyStockLevel: { $max: '$safetyStockLevel' }
        }
      },
      // Only include parts where stock is below safety level
      {
        $match: {
          $expr: {
            $and: [
              { $ne: ['$safetyStockLevel', null] },
              { $lte: ['$totalStock', '$safetyStockLevel'] }
            ]
          }
        }
      },
      // Lookup part information
      {
        $lookup: {
          from: 'parts',
          localField: '_id',
          foreignField: '_id',
          as: 'part'
        }
      },
      {
        $unwind: {
          path: '$part',
          preserveNullAndEmptyArrays: true
        }
      },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const [lowStockParts, totalCountResult] = await Promise.all([
      Inventories.aggregate(pipeline),
      Inventories.aggregate([
        {
          $group: {
            _id: '$partId',
            totalStock: { $sum: '$quantity' },
            safetyStockLevel: { $max: '$safetyStockLevel' }
          }
        },
        {
          $match: {
            $expr: {
              $and: [
                { $ne: ['$safetyStockLevel', null] },
                { $lte: ['$totalStock', '$safetyStockLevel'] }
              ]
            }
          }
        },
        {
          $count: 'total'
        }
      ])
    ]);
    
    const totalCount = totalCountResult.length > 0 ? totalCountResult[0].total : 0;
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logInventoryOperation('GET_LOW_STOCK_PARTS_SUCCESS', { 
      count: lowStockParts.length, 
      pagination 
    });
    
    return { lowStockParts, pagination };
  } catch (error: any) {
    logInventoryOperation('GET_LOW_STOCK_PARTS_ERROR', { error: error.message });
    console.error('[InventoryAggregation] Error getting low stock parts:', error);
    throw error;
  }
}
