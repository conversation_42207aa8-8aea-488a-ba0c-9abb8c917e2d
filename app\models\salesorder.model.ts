import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for items within the sales order
interface ISOItemCore {
  item_id: Types.ObjectId; // refPath: 'items.item_type'
  item_type: 'Product' | 'Assembly' | 'Service';
  description?: string | null;
  quantity_ordered: number;
  unit_price: mongoose.Types.Decimal128;
  discount_percentage?: mongoose.Types.Decimal128 | null;
  discount_amount?: mongoose.Types.Decimal128 | null;
  tax_rate_percentage?: mongoose.Types.Decimal128 | null;
  tax_amount_item?: mongoose.Types.Decimal128 | null;
  total_price: mongoose.Types.Decimal128; // (quantity * unit_price) - discount_amount + tax_amount_item
  quantity_shipped?: number;
  notes?: string | null;
}
export interface ISOItem extends ISOItemCore, Document {}

// Interface for attachments (reusable structure)
interface ISOAttachmentCore {
  file_name: string;
  url: string;
  uploaded_at?: Date;
}
export interface ISOAttachment extends ISOAttachmentCore, Document {}

// Interface for history (reusable structure)
interface ISOHistoryCore {
  event: string;
  user_id?: Types.ObjectId | null; // ref: 'User'
  timestamp?: Date;
  details?: string | null;
}
export interface ISOHistory extends ISOHistoryCore, Document {}

// Define interface for Sales Order document
export interface ISalesOrder extends Document {
  _id: Types.ObjectId;
  so_number: string; // Unique business identifier
  customer_id: Types.ObjectId; // ref: 'Customer'
  order_date: Date;
  status: 'draft' | 'pending_approval' | 'approved' | 'processing' | 'partially_shipped' | 'fully_shipped' | 'completed' | 'cancelled' | 'on_hold';
  items: ISOItem[];
  sub_total: mongoose.Types.Decimal128; // Sum of (item.quantity_ordered * item.unit_price)
  discount_total?: mongoose.Types.Decimal128 | null; // Sum of item.discount_amount
  tax_total?: mongoose.Types.Decimal128 | null; // Sum of item.tax_amount_item
  shipping_cost?: mongoose.Types.Decimal128 | null;
  total_amount: mongoose.Types.Decimal128; // sub_total - discount_total + tax_total + shipping_cost
  shipping_address_id?: Types.ObjectId | null; // ref: 'Customer.shipping_addresses._id' (optional)
  shipping_address_details?: string | null; // Denormalized or custom
  billing_address_details?: string | null; // Denormalized or custom
  notes?: string | null;
  sales_rep_id?: Types.ObjectId | null; // ref: 'User'
  expected_shipment_date?: Date | null;
  actual_shipment_date?: Date | null;
  payment_status?: 'pending' | 'partially_paid' | 'fully_paid' | 'refunded';
  payment_method?: string | null;
  transaction_id?: string | null;
  attachments?: ISOAttachment[];
  history?: ISOHistory[];
  tags?: string[] | null;
  createdAt: Date;
  updatedAt: Date;
}

// Sub-schema for sales order items
const SOItemSchema: Schema<ISOItem> = new Schema({
  item_id: { type: Schema.Types.ObjectId, required: true, refPath: 'items.item_type' },
  item_type: { type: String, required: true, enum: ['Product', 'Assembly', 'Service'] },
  description: { type: String, trim: true, default: null },
  quantity_ordered: { type: Number, required: true, min: 1, validate: Number.isInteger },
  unit_price: { type: Schema.Types.Decimal128, required: true },
  discount_percentage: { type: Schema.Types.Decimal128, default: null },
  discount_amount: { type: Schema.Types.Decimal128, default: null },
  tax_rate_percentage: { type: Schema.Types.Decimal128, default: null },
  tax_amount_item: { type: Schema.Types.Decimal128, default: null },
  total_price: { type: Schema.Types.Decimal128, required: true }, // This will be calculated
  quantity_shipped: { type: Number, default: 0, min: 0, validate: Number.isInteger },
  notes: { type: String, trim: true, default: null }
}, { _id: false });

// Sub-schema for attachments
const SOAttachmentSchema: Schema<ISOAttachment> = new Schema({
  file_name: { type: String, required: true, trim: true },
  url: { type: String, required: true, trim: true },
  uploaded_at: { type: Date, default: Date.now }
}, { _id: false });

// Sub-schema for history
const SOHistorySchema: Schema<ISOHistory> = new Schema({
  event: { type: String, required: true, trim: true },
  user_id: { type: Schema.Types.ObjectId, ref: 'User', default: null },
  timestamp: { type: Date, default: Date.now },
  details: { type: String, trim: true, default: null }
}, { _id: false });

// Define schema for Sales Order model
const SalesOrderSchema: Schema<ISalesOrder> = new Schema(
  {
    so_number: { type: String, required: true, unique: true, trim: true, index: true },
    customer_id: { type: Schema.Types.ObjectId, ref: 'Customer', required: true, index: true },
    order_date: { type: Date, required: true, default: Date.now },
    status: {
      type: String,
      required: true,
      enum: ['draft', 'pending_approval', 'approved', 'processing', 'partially_shipped', 'fully_shipped', 'completed', 'cancelled', 'on_hold'],
      default: 'draft',
      index: true
    },
    items: { type: [SOItemSchema], required: true, validate: (v: any[]) => Array.isArray(v) && v.length > 0 },
    sub_total: { type: Schema.Types.Decimal128, required: true, default: Types.Decimal128.fromString('0') },
    discount_total: { type: Schema.Types.Decimal128, default: Types.Decimal128.fromString('0') },
    tax_total: { type: Schema.Types.Decimal128, default: Types.Decimal128.fromString('0') },
    shipping_cost: { type: Schema.Types.Decimal128, default: Types.Decimal128.fromString('0') },
    total_amount: { type: Schema.Types.Decimal128, required: true, default: Types.Decimal128.fromString('0') },
    shipping_address_id: { type: Schema.Types.ObjectId, ref: 'Customer.shipping_addresses._id', default: null }, // Note: Direct ref like this might be tricky
    shipping_address_details: { type: String, trim: true, default: null },
    billing_address_details: { type: String, trim: true, default: null },
    notes: { type: String, trim: true, default: null },
    sales_rep_id: { type: Schema.Types.ObjectId, ref: 'User', default: null, index: true },
    expected_shipment_date: { type: Date, default: null },
    actual_shipment_date: { type: Date, default: null },
    payment_status: {
      type: String,
      enum: ['pending', 'partially_paid', 'fully_paid', 'refunded'],
      default: 'pending',
      index: true
    },
    payment_method: { type: String, trim: true, default: null },
    transaction_id: { type: String, trim: true, default: null },
    attachments: { type: [SOAttachmentSchema], default: [] },
    history: { type: [SOHistorySchema], default: [] },
    tags: { type: [String], default: null, index: true }
  },
  { timestamps: true }
);

// Pre-save hook to calculate item totals and order totals
SalesOrderSchema.pre('save', function(this: ISalesOrder & Document, next) {
  if (this.isModified('items') || this.isNew || this.isModified('shipping_cost') || this.isModified('tax_total') || this.isModified('discount_total')) {
    let calculatedSubTotal = 0;
    let calculatedDiscountTotal = 0;
    let calculatedTaxTotal = 0;

    this.items.forEach(item => {
      const quantity = item.quantity_ordered || 0;
      const unitPrice = item.unit_price ? parseFloat(item.unit_price.toString()) : 0;
      
      let itemSubTotal = quantity * unitPrice;

      const discountPerc = item.discount_percentage ? parseFloat(item.discount_percentage.toString()) : null;
      let discountAmt = item.discount_amount ? parseFloat(item.discount_amount.toString()) : 0;

      if (discountPerc !== null && discountPerc > 0 && item.discount_amount === null) {
        discountAmt = (itemSubTotal * discountPerc) / 100;
        item.discount_amount = Types.Decimal128.fromString(discountAmt.toFixed(2));
      }
      
      const priceAfterDiscount = itemSubTotal - discountAmt;

      const taxRatePerc = item.tax_rate_percentage ? parseFloat(item.tax_rate_percentage.toString()) : null;
      let taxAmtItem = item.tax_amount_item ? parseFloat(item.tax_amount_item.toString()) : 0;

      if (taxRatePerc !== null && taxRatePerc > 0 && item.tax_amount_item === null) {
         taxAmtItem = (priceAfterDiscount * taxRatePerc) / 100;
         item.tax_amount_item = Types.Decimal128.fromString(taxAmtItem.toFixed(2));
      }
      
      item.total_price = Types.Decimal128.fromString((priceAfterDiscount + taxAmtItem).toFixed(2));

      calculatedSubTotal += (quantity * unitPrice); 
      calculatedDiscountTotal += discountAmt;
      calculatedTaxTotal += taxAmtItem;
    });

    this.sub_total = Types.Decimal128.fromString(calculatedSubTotal.toFixed(2));
    this.discount_total = Types.Decimal128.fromString(calculatedDiscountTotal.toFixed(2)); 
    this.tax_total = Types.Decimal128.fromString(calculatedTaxTotal.toFixed(2)); 
    
    const shipping = this.shipping_cost ? parseFloat(this.shipping_cost.toString()) : 0;
    
    this.total_amount = Types.Decimal128.fromString((
      calculatedSubTotal - 
      calculatedDiscountTotal + 
      calculatedTaxTotal + 
      shipping
    ).toFixed(2));
  }
  next();
});

// Create and export Sales Order model
let SalesOrder;

try {
  SalesOrder = mongoose.models?.SalesOrder || mongoose.model<ISalesOrder>('SalesOrder', SalesOrderSchema);
} catch (error) {
  // If there's an error during model creation, create it fresh
  SalesOrder = mongoose.model<ISalesOrder>('SalesOrder', SalesOrderSchema);
}

export { SalesOrder };
export default SalesOrder;