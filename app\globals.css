/* React Complex Tree styles - must be imported first */
@import 'react-complex-tree/lib/style-modern.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Brand colors */
  --primary-yellow: #FFEB3B;
  --primary-orange: #FF9800;
  --primary-black: #212121;
  --primary-blue: #1274F3;
  --primary-pink: #EC3A76;
  --primary-mint: #4BFFB2;

  /* Light theme - Standard Tailwind CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 221 83% 53%;
  --radius: 0.5rem;

  /* Status colors - Light theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 221 83% 53%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Light theme */
  --chart-1: 217 91% 60%;  /* #0088FE */
  --chart-2: 168 76% 42%;  /* #00C49F */
  --chart-3: 45 93% 58%;   /* #FFBB28 */
  --chart-4: 20 100% 62%;  /* #FF8042 */
  --chart-5: 248 53% 58%;  /* #8884D8 */
  --chart-6: 158 58% 62%;  /* #82CA9D */
  --chart-7: 43 96% 67%;   /* #FFC658 */
  --chart-8: 188 69% 69%;  /* #8DD1E1 */

  /* Additional semantic colors for components */
  --purple: 262 83% 58%;
  --purple-foreground: 210 40% 98%;
  --orange: 25 95% 53%;
  --orange-foreground: 210 40% 98%;
  --blue: 217 91% 60%;
  --blue-foreground: 210 40% 98%;
  --green: 142 76% 36%;
  --green-foreground: 355 7% 97%;
  --red: 0 84% 60%;
  --red-foreground: 210 40% 98%;
  --yellow: 45 93% 58%;
  --yellow-foreground: 48 96% 89%;
  --gray: 215 16% 47%;
  --gray-foreground: 210 40% 98%;

  /* React Complex Tree - Light theme */
  --rct-color-tree-bg: hsl(var(--background));
  --rct-color-tree-focus-outline: hsl(var(--ring));
  --rct-color-focustree-item-selected-bg: hsl(var(--accent));
  --rct-color-focustree-item-selected-text: hsl(var(--accent-foreground));
  --rct-color-focustree-item-focused-border: hsl(var(--ring));
  --rct-color-focustree-item-draggingover-bg: hsl(var(--muted));
  --rct-color-focustree-item-draggingover-color: hsl(var(--muted-foreground));
  --rct-color-nonfocustree-item-selected-bg: hsl(var(--muted));
  --rct-color-nonfocustree-item-selected-text: hsl(var(--muted-foreground));
  --rct-color-nonfocustree-item-focused-border: hsl(var(--border));
  --rct-color-search-highlight-bg: hsl(var(--primary) / 0.3);
  --rct-color-drag-between-line-bg: hsl(var(--primary));
  --rct-color-arrow: hsl(var(--foreground));
  --rct-item-height: 40px;
  --rct-item-padding: 12px;
  --rct-radius: var(--radius);

  /* Enhanced React Complex Tree variables for better integration */
  --rct-color-item-hover-bg: hsl(var(--muted) / 0.5);
  --rct-color-item-text: hsl(var(--foreground));
  --rct-color-item-secondary-text: hsl(var(--muted-foreground));
  --rct-color-expand-button: hsl(var(--muted-foreground));
  --rct-color-expand-button-hover: hsl(var(--foreground));
  --rct-indent-size: 24px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.5s ease, color 0.5s ease;
}

.dark {
  /* Dark theme - Standard Tailwind CSS variables */
  --background: 0 0% 12%;
  --foreground: 0 0% 94%;
  --muted: 0 0% 15%;
  --muted-foreground: 0 0% 63%;
  --popover: 0 0% 15%;
  --popover-foreground: 0 0% 94%;
  --border: 0 0% 18%;
  --input: 0 0% 18%;
  --card: 0 0% 20%;
  --card-foreground: 0 0% 94%;
  --primary: 221 83% 53%;
  --primary-foreground: 0 0% 7%;
  --secondary: 0 0% 15%;
  --secondary-foreground: 0 0% 94%;
  --accent: 0 0% 15%;
  --accent-foreground: 0 0% 94%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 0 0% 94%;
  --ring: 0 0% 27%;

  /* Status colors - Dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Dark theme */
  --chart-1: 217 91% 70%;  /* Lighter blue for dark mode */
  --chart-2: 168 76% 52%;  /* Lighter green for dark mode */
  --chart-3: 45 93% 68%;   /* Lighter yellow for dark mode */
  --chart-4: 20 100% 72%;  /* Lighter orange for dark mode */
  --chart-5: 248 53% 68%;  /* Lighter purple for dark mode */
  --chart-6: 158 58% 72%;  /* Lighter mint for dark mode */
  --chart-7: 43 96% 77%;   /* Lighter gold for dark mode */
  --chart-8: 188 69% 79%;  /* Lighter cyan for dark mode */

  /* Additional semantic colors for components - Dark theme */
  --purple: 262 83% 68%;
  --purple-foreground: 0 0% 7%;
  --orange: 25 95% 63%;
  --orange-foreground: 0 0% 7%;
  --blue: 217 91% 70%;
  --blue-foreground: 0 0% 7%;
  --green: 142 70% 45%;
  --green-foreground: 355 7% 97%;
  --red: 0 84% 65%;
  --red-foreground: 0 0% 94%;
  --yellow: 45 93% 68%;
  --yellow-foreground: 48 96% 89%;
  --gray: 215 16% 57%;
  --gray-foreground: 0 0% 7%;

  /* React Complex Tree - Dark theme */
  --rct-color-tree-bg: hsl(var(--background));
  --rct-color-tree-focus-outline: hsl(var(--ring));
  --rct-color-focustree-item-selected-bg: hsl(var(--accent));
  --rct-color-focustree-item-selected-text: hsl(var(--accent-foreground));
  --rct-color-focustree-item-focused-border: hsl(var(--ring));
  --rct-color-focustree-item-draggingover-bg: hsl(var(--muted));
  --rct-color-focustree-item-draggingover-color: hsl(var(--muted-foreground));
  --rct-color-nonfocustree-item-selected-bg: hsl(var(--muted));
  --rct-color-nonfocustree-item-selected-text: hsl(var(--muted-foreground));
  --rct-color-nonfocustree-item-focused-border: hsl(var(--border));
  --rct-color-search-highlight-bg: hsl(var(--primary) / 0.4);
  --rct-color-drag-between-line-bg: hsl(var(--primary));
  --rct-color-arrow: hsl(var(--foreground));

  /* Enhanced React Complex Tree variables for dark theme */
  --rct-color-item-hover-bg: hsl(var(--muted) / 0.7);
  --rct-color-item-text: hsl(var(--foreground));
  --rct-color-item-secondary-text: hsl(var(--muted-foreground));
  --rct-color-expand-button: hsl(var(--muted-foreground));
  --rct-color-expand-button-hover: hsl(var(--foreground));
}

.dark body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  background-attachment: fixed;
}

/* Glass morphism */
.glass {
  background: hsl(var(--card) / 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--border) / 0.05);
}

.dark .glass {
  background: hsl(var(--card) / 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--border) / 0.2);
}

/* Floating animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px 0;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: transparent;
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Custom sidebar scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border) / 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border) / 0.7);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.9);
}

/* Legacy color mappings - these will be removed as components are updated */
.dark .bg-gray-900 {
  background-color: hsl(var(--background));
}

.dark .bg-gray-800 {
  background-color: hsl(var(--card));
}

.dark .bg-gray-700 {
  background-color: hsl(var(--muted));
}

.dark .border-gray-700 {
  border-color: hsl(var(--border));
}

.dark .text-gray-400 {
  color: hsl(var(--muted-foreground));
}

.dark .text-gray-500 {
  color: hsl(var(--muted-foreground));
}

.dark .text-gray-600 {
  color: hsl(var(--muted-foreground));
}

.dark .text-gray-300 {
  color: hsl(var(--foreground));
}

/* Gradient backgrounds */
.dark .bg-gradient-primary {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
}

.dark .bg-gradient-accent {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--card)) 100%);
}

/* Improved gradient for dark theme */
.dark .bg-gradient-dark {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
}

/* Card styling */
.card {
  @apply rounded-xl overflow-hidden transition-all duration-300;
}

.dark .card {
  background-color: hsl(var(--card));
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsl(var(--border));
  box-shadow: 0 10px 15px -3px hsl(var(--foreground) / 0.3);
  color: hsl(var(--foreground));
}

.card-hover {
  @apply transition-all duration-300;
}

.card-hover:hover {
  transform: translateY(-5px);
}

.dark .card-hover:hover {
  background-color: hsl(var(--muted-foreground));
}

/* Button styling */
.btn {
  @apply rounded-xl py-2 px-4 transition-all duration-300 transform hover:translate-y-[-2px] focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* Enhanced button styles for better visibility */
.dark .btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  box-shadow: 0 4px 10px hsl(var(--foreground) / 0.25);
}

.dark .btn-primary:hover {
  background-color: hsl(var(--primary));
  box-shadow: 0 6px 15px hsl(var(--foreground) / 0.3);
}

.dark .btn-primary:focus {
  box-shadow: 0 0 0 3px hsl(var(--ring));
}

.dark .btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  box-shadow: 0 4px 10px hsl(var(--muted-foreground) / 0.35);
}

.dark .btn-secondary:hover {
  background-color: hsl(var(--secondary));
  box-shadow: 0 6px 15px hsl(var(--muted-foreground) / 0.45);
}

.dark .btn-secondary:focus {
  box-shadow: 0 0 0 3px hsl(var(--ring));
}

.dark .btn-accent {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  box-shadow: 0 4px 10px hsl(var(--muted-foreground) / 0.35);
}

.dark .btn-accent:hover {
  background-color: hsl(var(--accent));
  box-shadow: 0 6px 15px hsl(var(--muted-foreground) / 0.45);
}

.dark .btn-accent:focus {
  box-shadow: 0 0 0 3px hsl(var(--ring));
}

/* Form controls with better focus states */
.dark input, .dark textarea, .dark select {
  border-color: hsl(var(--border));
  background-color: hsl(var(--input));
  color: hsl(var(--foreground));
}

.dark input::placeholder, .dark textarea::placeholder {
  color: hsl(var(--muted-foreground));
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring));
  outline: none;
}

.dark input:disabled, .dark textarea:disabled, .dark select:disabled {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  cursor: not-allowed;
}

/* Improved form controls for dark theme */
.dark .form-input,
.dark .form-select,
.dark .form-textarea {
  background-color: hsl(var(--input));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.dark .form-input:focus,
.dark .form-select:focus,
.dark .form-textarea:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* Interactive elements with improved hover states */
.dark button:not([disabled]):hover,
.dark .btn:not([disabled]):hover,
.dark .clickable:hover {
  filter: brightness(1.15);
  transform: translateY(-2px);
}

/* Add focus outlines for keyboard navigation */
.dark *:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 3px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Additional dark theme card elements */
.dark .card-background,
.dark .content-card {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
}

/* Ensure main content areas use the right background */
.dark .content-area,
.dark .main-content,
.dark .dashboard-content {
  background-color: hsl(var(--background));
}

/* Ensure buttons have the right hover state */
.dark .btn:hover,
.dark .button:hover,
.dark button:hover {
  background-color: hsl(var(--muted));
}

/* Apply theme colors to page containers */
.dark [class*="page-container"],
.dark [class*="content-container"] {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Apply updated colors to all cards and panels */
.dark [class*="card"],
.dark [class*="panel"],
.dark [class*="Box"],
.dark [class*="Container"] {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Table styling is now handled by component-level styling in table.tsx */
/* This ensures better theme compliance and removes unprofessional background overrides */

/* ============================================================================
   React Complex Tree Custom Styling
   ============================================================================ */

/* Tree container styling - fixed border overlapping issues */
.rct-tree-container {
  background-color: var(--rct-color-tree-bg);
  border-radius: var(--rct-radius);
  border: 1px solid hsl(var(--border));
  overflow: visible; /* Changed from hidden to visible to prevent border clipping */
  padding: 4px; /* Added padding to prevent border overlap with content */
  position: relative; /* Ensure proper positioning context */
}

/* Tree item styling - fixed overlapping margins */
.rct-tree-item {
  min-height: var(--rct-item-height);
  padding: 0 var(--rct-item-padding);
  display: flex;
  align-items: center;
  color: var(--rct-color-item-text);
  transition: all 0.15s ease-in-out;
  border-radius: calc(var(--rct-radius) - 2px);
  margin: 2px 4px 2px 2px; /* Fixed margins to prevent negative values */
  position: relative; /* Ensure proper positioning */
}

.rct-tree-item:hover {
  background-color: var(--rct-color-item-hover-bg);
}

/* Tree item button hover states */
.rct-tree-item-button:hover:not(.rct-tree-item-button-selected) {
  background-color: var(--rct-color-item-hover-bg) !important;
}

/* Fix for all tree item buttons to prevent negative margins */
.rct-tree-item-button {
  margin: 1px 0px 1px 0px !important; /* Remove negative left margin */
  border-radius: calc(var(--rct-radius) - 2px) !important;
  transition: all 0.15s ease-in-out !important;
}

/* Tree item button styling - targeting the actual button elements */
.rct-tree-item-button-selected,
.rct-tree-item-button-focused,
.rct-tree-item-button-selected.rct-tree-item-button-focused {
  background-color: var(--rct-color-focustree-item-selected-bg) !important;
  color: var(--rct-color-focustree-item-selected-text) !important;
}

/* Fixed border overlapping issues - refined approach */
.rct-tree-item-button-focused {
  /* Use box-shadow instead of outline to avoid overlapping issues */
  box-shadow: 0 0 0 2px var(--rct-color-focustree-item-focused-border) !important;
  border: 1px solid var(--rct-color-focustree-item-focused-border) !important;
  margin: 2px !important; /* Consistent margins on all sides */
  position: relative !important;
  z-index: 2 !important; /* Higher z-index to ensure proper layering */
  border-radius: calc(var(--rct-radius) - 1px) !important;
  transition: all 0.15s ease-in-out !important;
  /* Ensure no outline conflicts */
  outline: none !important;
}

/* Legacy selectors for backward compatibility */
.rct-tree-item[data-rct-item-selected="true"] {
  background-color: var(--rct-color-focustree-item-selected-bg);
  color: var(--rct-color-focustree-item-selected-text);
}

/* Removed conflicting focused item rule - handled by .rct-tree-item-button-focused instead */

/* Tree item content */
.rct-tree-item-title-container {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}

.rct-tree-item-title {
  flex: 1;
  min-width: 0;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Expand/collapse arrow styling */
.rct-tree-item-arrow {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: var(--rct-color-expand-button);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rct-tree-item-arrow:hover {
  color: var(--rct-color-expand-button-hover);
  transform: scale(1.1);
}

.rct-tree-item-arrow[data-rct-expanded="true"] {
  transform: rotate(90deg);
}

/* Search highlight styling */
.rct-tree-search-highlight {
  background-color: var(--rct-color-search-highlight-bg);
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

/* Drag and drop styling */
.rct-tree-item[data-rct-dragging-over="true"] {
  background-color: var(--rct-color-focustree-item-draggingover-bg);
  color: var(--rct-color-focustree-item-draggingover-color);
}

.rct-tree-drag-between-line {
  height: 2px;
  background-color: var(--rct-color-drag-between-line-bg);
  border-radius: 1px;
}

/* Tree indentation - improved hierarchical structure without border conflicts */
.rct-tree-item[data-rct-depth="1"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 1);
  border-left: 2px solid transparent; /* Add visual separator without conflicts */
}
.rct-tree-item[data-rct-depth="2"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 2);
  border-left: 2px solid transparent;
}
.rct-tree-item[data-rct-depth="3"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 3);
  border-left: 2px solid transparent;
}
.rct-tree-item[data-rct-depth="4"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 4);
  border-left: 2px solid transparent;
}
.rct-tree-item[data-rct-depth="5"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 5);
  border-left: 2px solid transparent;
}
.rct-tree-item[data-rct-depth="6"] {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 6);
  border-left: 2px solid transparent;
}

/* BOM Viewer specific indentation - handles custom button structure */
/* Target buttons by their position in the tree hierarchy */
.rct-tree-item-button {
  position: relative;
  transition: all 0.15s ease-in-out;
}

/* Level 0 (root level) - default padding for top-level items */
[role="tree"] > [role="treeitem"] .rct-tree-item-button {
  padding-left: var(--rct-item-padding);
}

/* Level 1 (child items) - add indentation for nested items */
[role="tree"] [role="group"] .rct-tree-item-button {
  padding-left: calc(var(--rct-item-padding) + var(--rct-indent-size) * 1.5) !important;
  position: relative;
}

/* Add visual hierarchy indicator for child items */
[role="tree"] [role="group"] .rct-tree-item-button::before {
  content: '';
  position: absolute;
  left: calc(var(--rct-item-padding) + 8px);
  top: 50%;
  width: 12px;
  height: 1px;
  background-color: var(--rct-color-arrow);
  opacity: 0.5;
  transform: translateY(-50%);
}

/* Add vertical line for better hierarchy visualization */
[role="tree"] [role="group"] .rct-tree-item-button::after {
  content: '';
  position: absolute;
  left: calc(var(--rct-item-padding) + 8px);
  top: -50%;
  width: 1px;
  height: 100%;
  background-color: var(--rct-color-arrow);
  opacity: 0.3;
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
  :root {
    --rct-item-height: 44px;
    --rct-item-padding: 16px;
    --rct-indent-size: 20px;
  }

  .rct-tree-item {
    min-height: var(--rct-item-height);
    padding: 0 var(--rct-item-padding);
  }

  .rct-tree-item-title {
    font-size: 0.9rem;
  }

  .rct-tree-item-arrow {
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }
}

@media (max-width: 480px) {
  :root {
    --rct-item-height: 48px;
    --rct-item-padding: 12px;
    --rct-indent-size: 16px;
  }

  .rct-tree-item-title {
    font-size: 0.85rem;
  }
}

/* Ensure consistent styling for charts and data visualization */
.dark .recharts-cartesian-grid-horizontal line,
.dark .recharts-cartesian-grid-vertical line {
  stroke: hsl(var(--border));
}

.dark .recharts-tooltip-wrapper {
  background-color: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Ensure consistent styling for modals and dialogs */
.dark .modal,
.dark .dialog,
.dark [role="dialog"] {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
  box-shadow: 0 20px 25px -5px hsl(var(--foreground) / 0.5), 0 10px 10px -5px hsl(var(--foreground) / 0.2);
}

/* Theme-consistent gradients using brand colors */
.theme-gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r;
}

.dark .theme-gradient-text {
  background: linear-gradient(to right, var(--primary-blue), hsl(var(--primary) / 0.9), hsl(var(--accent) / 0.9));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-gradient-text {
  background: linear-gradient(to right, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.8), hsl(var(--accent) / 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-gradient-bg {
  background: linear-gradient(to right, var(--tw-gradient-stops));
}

.dark .theme-gradient-bg {
  background: linear-gradient(to right, hsl(var(--primary) / 0.3), hsl(var(--accent) / 0.3));
}

.theme-gradient-bg {
  background: linear-gradient(to right, hsl(var(--primary) / 0.1), hsl(var(--accent) / 0.1));
}

/* ========================================
   THEME VARIANTS - TweakCN Integration
   ======================================== */

/*
   Note: The default theme classes preserve your current beloved theme configuration
   Additional theme variants provide new options while keeping your preferred styling
*/

/* Default Theme - Light Mode (preserves current configuration) */
.theme-default-light {
  /* Inherits from :root - no changes needed */
}

/* Default Theme - Dark Mode (preserves your beloved dark theme) */
.theme-default-dark {
  /* Inherits from .dark - no changes needed */
}

/* ============================================================================
   React Complex Tree Theme Variant Customizations
   ============================================================================ */

/* Default theme React Complex Tree styling */
.theme-default-light .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(var(--accent));
  --rct-color-focustree-item-selected-text: hsl(var(--accent-foreground));
  --rct-color-search-highlight-bg: hsl(var(--primary) / 0.3);
}

.theme-default-dark .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(var(--accent));
  --rct-color-focustree-item-selected-text: hsl(var(--accent-foreground));
  --rct-color-search-highlight-bg: hsl(var(--primary) / 0.4);
}

/* Blue theme React Complex Tree styling */
.theme-blue-light .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(207 90% 95%);
  --rct-color-focustree-item-selected-text: hsl(207 90% 20%);
  --rct-color-search-highlight-bg: hsl(207 90% 85%);
}

.theme-blue-dark .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(207 90% 15%);
  --rct-color-focustree-item-selected-text: hsl(207 90% 85%);
  --rct-color-search-highlight-bg: hsl(207 90% 25%);
}

/* Green theme React Complex Tree styling */
.theme-green-light .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(142 70% 95%);
  --rct-color-focustree-item-selected-text: hsl(142 70% 20%);
  --rct-color-search-highlight-bg: hsl(142 70% 85%);
}

.theme-green-dark .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(142 70% 15%);
  --rct-color-focustree-item-selected-text: hsl(142 70% 85%);
  --rct-color-search-highlight-bg: hsl(142 70% 25%);
}

/* Purple theme React Complex Tree styling */
.theme-purple-light .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(262 80% 95%);
  --rct-color-focustree-item-selected-text: hsl(262 80% 20%);
  --rct-color-search-highlight-bg: hsl(262 80% 85%);
}

.theme-purple-dark .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(262 80% 15%);
  --rct-color-focustree-item-selected-text: hsl(262 80% 85%);
  --rct-color-search-highlight-bg: hsl(262 80% 25%);
}

/* Orange theme React Complex Tree styling */
.theme-orange-light .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(25 95% 95%);
  --rct-color-focustree-item-selected-text: hsl(25 95% 20%);
  --rct-color-search-highlight-bg: hsl(25 95% 85%);
}

.theme-orange-dark .rct-tree-container {
  --rct-color-focustree-item-selected-bg: hsl(25 95% 15%);
  --rct-color-focustree-item-selected-text: hsl(25 95% 85%);
  --rct-color-search-highlight-bg: hsl(25 95% 25%);
}

/* Blue Theme - Light Mode (Ocean Blue) */
.theme-blue-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 199 89% 48%; /* Ocean blue primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 199 95% 95%; /* Light blue secondary */
  --secondary-foreground: 199 89% 20%;
  --accent: 199 100% 97%; /* Very light blue accent */
  --accent-foreground: 199 89% 20%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 199 89% 48%;
  --radius: 0.5rem;

  /* Status colors - Blue theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 199 89% 48%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Blue theme */
  --chart-1: 199 89% 48%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Blue Theme - Dark Mode (Deep Ocean) */
.theme-blue-dark {
  --background: 199 100% 3%; /* Deep ocean background */
  --foreground: 199 20% 94%;
  --muted: 199 50% 8%;
  --muted-foreground: 199 20% 63%;
  --popover: 199 50% 8%;
  --popover-foreground: 199 20% 94%;
  --border: 199 30% 18%;
  --input: 199 30% 18%;
  --card: 199 50% 6%;
  --card-foreground: 199 20% 94%;
  --primary: 199 89% 60%; /* Brighter blue for dark mode */
  --primary-foreground: 199 100% 3%;
  --secondary: 199 50% 8%;
  --secondary-foreground: 199 20% 94%;
  --accent: 199 20% 94%;
  --accent-foreground: 199 100% 3%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 199 20% 94%;
  --ring: 199 30% 18%;

  /* Status colors - Blue dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 199 20% 88%;
  --info-foreground: 199 100% 3%;

  /* Chart colors - Blue dark theme */
  --chart-1: 199 89% 70%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Green Theme - Light Mode (Forest Green) */
.theme-green-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 142 76% 36%; /* Forest green primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 142 84% 95%; /* Light green secondary */
  --secondary-foreground: 142 76% 20%;
  --accent: 142 100% 97%; /* Very light green accent */
  --accent-foreground: 142 76% 20%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 142 76% 36%;
  --radius: 0.5rem;

  /* Status colors - Green theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 221 83% 53%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Green theme */
  --chart-1: 142 76% 36%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Green Theme - Dark Mode (Deep Forest) */
.theme-green-dark {
  --background: 142 100% 2%; /* Deep forest background */
  --foreground: 142 20% 94%;
  --muted: 142 50% 6%;
  --muted-foreground: 142 20% 63%;
  --popover: 142 50% 6%;
  --popover-foreground: 142 20% 94%;
  --border: 142 30% 15%;
  --input: 142 30% 15%;
  --card: 142 50% 4%;
  --card-foreground: 142 20% 94%;
  --primary: 142 76% 50%; /* Brighter green for dark mode */
  --primary-foreground: 142 100% 2%;
  --secondary: 142 50% 6%;
  --secondary-foreground: 142 20% 94%;
  --accent: 142 20% 94%;
  --accent-foreground: 142 100% 2%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 142 20% 94%;
  --ring: 142 30% 15%;

  /* Status colors - Green dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 142 20% 88%;
  --info-foreground: 142 100% 2%;

  /* Chart colors - Green dark theme */
  --chart-1: 142 76% 60%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Purple Theme - Light Mode (Royal Purple) */
.theme-purple-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 262 83% 58%; /* Royal purple primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 262 100% 96%; /* Light purple secondary */
  --secondary-foreground: 262 83% 25%;
  --accent: 262 100% 98%; /* Very light purple accent */
  --accent-foreground: 262 83% 25%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 262 83% 58%;
  --radius: 0.5rem;

  /* Status colors - Purple theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 262 83% 58%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Purple theme */
  --chart-1: 262 83% 58%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Purple Theme - Dark Mode (Mystical Purple) */
.theme-purple-dark {
  --background: 262 100% 4%; /* Deep mystical background */
  --foreground: 262 20% 94%;
  --muted: 262 50% 8%;
  --muted-foreground: 262 20% 63%;
  --popover: 262 50% 8%;
  --popover-foreground: 262 20% 94%;
  --border: 262 30% 18%;
  --input: 262 30% 18%;
  --card: 262 50% 6%;
  --card-foreground: 262 20% 94%;
  --primary: 262 83% 70%; /* Brighter purple for dark mode */
  --primary-foreground: 262 100% 4%;
  --secondary: 262 50% 8%;
  --secondary-foreground: 262 20% 94%;
  --accent: 262 20% 94%;
  --accent-foreground: 262 100% 4%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 262 20% 94%;
  --ring: 262 30% 18%;

  /* Status colors - Purple dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 262 20% 88%;
  --info-foreground: 262 100% 4%;

  /* Chart colors - Purple dark theme */
  --chart-1: 262 83% 75%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Orange Theme - Light Mode (Sunset Orange) */
.theme-orange-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 25 95% 53%; /* Sunset orange primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 25 100% 94%; /* Light orange secondary */
  --secondary-foreground: 25 95% 25%;
  --accent: 25 100% 97%; /* Very light orange accent */
  --accent-foreground: 25 95% 25%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 25 95% 53%;
  --radius: 0.5rem;

  /* Status colors - Orange theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 221 83% 53%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Orange theme */
  --chart-1: 25 95% 53%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Orange Theme - Dark Mode (Warm Sunset) */
.theme-orange-dark {
  --background: 25 100% 4%; /* Deep warm background */
  --foreground: 25 20% 94%;
  --muted: 25 50% 8%;
  --muted-foreground: 25 20% 63%;
  --popover: 25 50% 8%;
  --popover-foreground: 25 20% 94%;
  --border: 25 30% 18%;
  --input: 25 30% 18%;
  --card: 25 50% 6%;
  --card-foreground: 25 20% 94%;
  --primary: 25 95% 65%; /* Brighter orange for dark mode */
  --primary-foreground: 25 100% 4%;
  --secondary: 25 50% 8%;
  --secondary-foreground: 25 20% 94%;
  --accent: 25 20% 94%;
  --accent-foreground: 25 100% 4%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 25 20% 94%;
  --ring: 25 30% 18%;

  /* Status colors - Orange dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 25 20% 88%;
  --info-foreground: 25 100% 4%;

  /* Chart colors - Orange dark theme */
  --chart-1: 25 95% 70%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Rose Theme - Light Mode (Rose Garden) */
.theme-rose-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;
  --primary: 343 75% 59%; /* Rose primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 343 100% 96%; /* Light rose secondary */
  --secondary-foreground: 343 75% 25%;
  --accent: 343 100% 98%; /* Very light rose accent */
  --accent-foreground: 343 75% 25%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 343 75% 59%;
  --radius: 0.5rem;

  /* Status colors - Rose theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 221 83% 53%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Rose theme */
  --chart-1: 343 75% 59%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Rose Theme - Dark Mode (Romantic Rose) */
.theme-rose-dark {
  --background: 343 100% 3%; /* Deep romantic background */
  --foreground: 343 20% 94%;
  --muted: 343 50% 6%;
  --muted-foreground: 343 20% 63%;
  --popover: 343 50% 6%;
  --popover-foreground: 343 20% 94%;
  --border: 343 30% 15%;
  --input: 343 30% 15%;
  --card: 343 50% 5%;
  --card-foreground: 343 20% 94%;
  --primary: 343 75% 70%; /* Brighter rose for dark mode */
  --primary-foreground: 343 100% 3%;
  --secondary: 343 50% 6%;
  --secondary-foreground: 343 20% 94%;
  --accent: 343 20% 94%;
  --accent-foreground: 343 100% 3%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 343 20% 94%;
  --ring: 343 30% 15%;

  /* Status colors - Rose dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 343 20% 88%;
  --info-foreground: 343 100% 3%;

  /* Chart colors - Rose dark theme */
  --chart-1: 343 75% 75%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Slate Theme - Light Mode (Minimal Slate) */
.theme-slate-light {
  --background: 0 0% 100%;
  --foreground: 215 28% 17%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215 16% 47%;
  --popover: 0 0% 100%;
  --popover-foreground: 215 28% 17%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --card: 0 0% 100%;
  --card-foreground: 215 28% 17%;
  --primary: 215 16% 47%; /* Slate primary */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%; /* Light slate secondary */
  --secondary-foreground: 215 28% 17%;
  --accent: 210 40% 98%; /* Very light slate accent */
  --accent-foreground: 215 28% 17%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 215 16% 47%;
  --radius: 0.5rem;

  /* Status colors - Slate theme */
  --success: 142 76% 36%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 215 16% 47%;
  --info-foreground: 210 40% 98%;

  /* Chart colors - Slate theme */
  --chart-1: 215 16% 47%;
  --chart-2: 168 76% 42%;
  --chart-3: 45 93% 58%;
  --chart-4: 20 100% 62%;
  --chart-5: 248 53% 58%;
  --chart-6: 158 58% 62%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Slate Theme - Dark Mode (Sophisticated Slate) */
.theme-slate-dark {
  --background: 222.2 84% 4.9%; /* Deep sophisticated background */
  --foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --primary: 215 20.2% 65.1%; /* Brighter slate for dark mode */
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --accent: 210 40% 98%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 210 40% 98%;
  --ring: 217.2 32.6% 17.5%;

  /* Status colors - Slate dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 215 20.2% 88%;
  --info-foreground: 222.2 84% 4.9%;

  /* Chart colors - Slate dark theme */
  --chart-1: 215 20.2% 75%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* GitHub Neutral Theme - Dark Mode */
.theme-github-dark {
  --background: 210 15% 18%;
  --foreground: 0 0% 98%;
  --muted: 210 15% 25%;
  --muted-foreground: 210 10% 70%;
  --popover: 210 15% 15%;
  --popover-foreground: 0 0% 98%;
  --border: 210 15% 30%;
  --input: 210 15% 25%;
  --card: 210 15% 15%;
  --card-foreground: 0 0% 98%;
  --primary: 207 94% 66%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 15% 30%;
  --secondary-foreground: 0 0% 98%;
  --accent: 210 15% 25%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --ring: 207 94% 66%;
  --radius: 0.5rem;

  /* Status colors - GitHub dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - GitHub dark theme */
  --chart-1: 217 91% 70%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Linear Purple Theme - Dark Mode */
.theme-linear-dark {
  --background: 0 0% 10%;
  --foreground: 0 0% 98%;
  --muted: 0 0% 17%;
  --muted-foreground: 0 0% 60%;
  --popover: 0 0% 17%;
  --popover-foreground: 0 0% 98%;
  --border: 0 0% 23%;
  --input: 0 0% 23%;
  --card: 0 0% 17%;
  --card-foreground: 0 0% 98%;
  --primary: 200 100% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 17%;
  --secondary-foreground: 0 0% 98%;
  --accent: 0 0% 17%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 70% 57%;
  --destructive-foreground: 0 0% 100%;
  --ring: 200 100% 40%;

  /* Status colors - Linear dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Linear dark theme */
  --chart-1: 200 100% 50%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Vercel Minimal Theme - Dark Mode */
.theme-vercel-dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --muted: 0 0% 7%;
  --muted-foreground: 0 0% 53%;
  --popover: 0 0% 7%;
  --popover-foreground: 0 0% 100%;
  --border: 0 0% 20%;
  --input: 0 0% 20%;
  --card: 0 0% 7%;
  --card-foreground: 0 0% 100%;
  --primary: 0 0% 100%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 7%;
  --secondary-foreground: 0 0% 100%;
  --accent: 0 0% 7%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 0 0% 100%;
  --ring: 0 0% 20%;

  /* Status colors - Vercel dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Vercel dark theme */
  --chart-1: 217 91% 70%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Enterprise Green Theme - Dark Mode */
.theme-enterprise-dark {
  --background: 142 100% 2%;
  --foreground: 142 20% 94%;
  --muted: 142 50% 6%;
  --muted-foreground: 142 20% 63%;
  --popover: 142 50% 6%;
  --popover-foreground: 142 20% 94%;
  --border: 142 30% 15%;
  --input: 142 30% 15%;
  --card: 142 50% 4%;
  --card-foreground: 142 20% 94%;
  --primary: 142 76% 50%;
  --primary-foreground: 142 100% 2%;
  --secondary: 142 50% 6%;
  --secondary-foreground: 142 20% 94%;
  --accent: 142 20% 94%;
  --accent-foreground: 142 100% 2%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 142 20% 94%;
  --ring: 142 30% 15%;

  /* Status colors - Enterprise dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 142 20% 88%;
  --info-foreground: 142 100% 2%;

  /* Chart colors - Enterprise dark theme */
  --chart-1: 142 76% 60%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* Professional Navy Theme - Dark Mode */
.theme-navy-dark {
  --background: 210 40% 10%;
  --foreground: 210 40% 98%;
  --muted: 210 40% 15%;
  --muted-foreground: 210 40% 63%;
  --popover: 210 40% 15%;
  --popover-foreground: 210 40% 98%;
  --border: 210 40% 27%;
  --input: 210 40% 27%;
  --card: 210 40% 20%;
  --card-foreground: 210 40% 98%;
  --primary: 210 83% 53%;
  --primary-foreground: 210 40% 7%;
  --secondary: 210 40% 15%;
  --secondary-foreground: 210 40% 98%;
  --accent: 210 40% 100%;
  --accent-foreground: 210 40% 7%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 210 40% 98%;
  --ring: 210 40% 27%;

  /* Status colors - Navy dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Navy dark theme */
  --chart-1: 217 91% 70%;
  --chart-2: 168 76% 52%;
  --chart-3: 45 93% 68%;
  --chart-4: 20 100% 72%;
  --chart-5: 248 53% 68%;
  --chart-6: 158 58% 72%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* ========================================
   SEMANTIC COLOR UTILITIES
   ======================================== */

/* Ensure all semantic color classes work with theme variants */
.bg-success { background-color: hsl(var(--success)); }
.text-success { color: hsl(var(--success)); }
.text-success-foreground { color: hsl(var(--success-foreground)); }
.border-success { border-color: hsl(var(--success)); }

.bg-warning { background-color: hsl(var(--warning)); }
.text-warning { color: hsl(var(--warning)); }
.text-warning-foreground { color: hsl(var(--warning-foreground)); }
.border-warning { border-color: hsl(var(--warning)); }

.bg-info { background-color: hsl(var(--info)); }
.text-info { color: hsl(var(--info)); }
.text-info-foreground { color: hsl(var(--info-foreground)); }
.border-info { border-color: hsl(var(--info)); }

/* Chart color utilities */
.bg-chart-1 { background-color: hsl(var(--chart-1)); }
.bg-chart-2 { background-color: hsl(var(--chart-2)); }
.bg-chart-3 { background-color: hsl(var(--chart-3)); }
.bg-chart-4 { background-color: hsl(var(--chart-4)); }
.bg-chart-5 { background-color: hsl(var(--chart-5)); }
.bg-chart-6 { background-color: hsl(var(--chart-6)); }
.bg-chart-7 { background-color: hsl(var(--chart-7)); }
.bg-chart-8 { background-color: hsl(var(--chart-8)); }

/* GitHub Neutral Theme - Light Mode */
.theme-github-light {
  --background: 0 0% 100%;
  --foreground: 213 27% 20%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 213 27% 20%;
  --primary: 212 100% 43%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 213 27% 20%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 213 27% 20%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 212 100% 43%;

  /* Status colors - GitHub light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - GitHub light theme */
  --chart-1: 212 100% 43%;
  --chart-2: 142 70% 45%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Linear Purple Theme - Light Mode */
.theme-linear-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --primary: 200 100% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 200 100% 40%;

  /* Status colors - Linear light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Linear light theme */
  --chart-1: 200 100% 40%;
  --chart-2: 142 70% 45%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Vercel Minimal Theme - Light Mode */
.theme-vercel-light {
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 40%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 0%;
  --accent: 0 0% 96%;
  --accent-foreground: 0 0% 0%;
  --destructive: 0 100% 45%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 91%;
  --input: 0 0% 91%;
  --ring: 0 0% 0%;

  /* Status colors - Vercel light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Vercel light theme */
  --chart-1: 0 0% 0%;
  --chart-2: 142 70% 45%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Enterprise Green Theme - Light Mode */
.theme-enterprise-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --primary: 142 70% 45%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 142 70% 45%;

  /* Status colors - Enterprise light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Enterprise light theme */
  --chart-1: 142 70% 45%;
  --chart-2: 168 76% 42%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Professional Navy Theme - Light Mode */
.theme-navy-light {
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;
  --primary: 210 40% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 210 40% 40%;

  /* Status colors - Navy light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Navy light theme */
  --chart-1: 210 40% 40%;
  --chart-2: 142 70% 45%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Modern Theme - Light Mode */
.theme-modern-light {
  --background: 0 0% 98%;
  --foreground: 0 0% 10%;
  --muted: 0 0% 95%;
  --muted-foreground: 0 0% 40%;
  --popover: 0 0% 98%;
  --popover-foreground: 0 0% 10%;
  --primary: 200 100% 40%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 95%;
  --secondary-foreground: 0 0% 10%;
  --accent: 0 0% 95%;
  --accent-foreground: 0 0% 10%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 88%;
  --input: 0 0% 88%;
  --ring: 200 100% 40%;

  /* Status colors - Modern light theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Modern light theme */
  --chart-1: 200 100% 40%;
  --chart-2: 142 70% 45%;
  --chart-3: 38 92% 50%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --chart-6: 200 80% 60%;
  --chart-7: 43 96% 67%;
  --chart-8: 188 69% 69%;
}

/* Modern Theme - Dark Mode */
.theme-modern-dark {
  --background: 0 0% 6%;
  --foreground: 0 0% 98%;
  --muted: 0 0% 12%;
  --muted-foreground: 0 0% 60%;
  --popover: 0 0% 12%;
  --popover-foreground: 0 0% 98%;
  --primary: 200 100% 50%;
  --primary-foreground: 0 0% 6%;
  --secondary: 0 0% 12%;
  --secondary-foreground: 0 0% 98%;
  --accent: 0 0% 12%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84% 65%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 18%;
  --input: 0 0% 18%;
  --ring: 200 100% 50%;

  /* Status colors - Modern dark theme */
  --success: 142 70% 45%;
  --success-foreground: 355 7% 97%;
  --warning: 38 92% 50%;
  --warning-foreground: 48 96% 89%;
  --info: 0 0% 88%;
  --info-foreground: 0 0% 7%;

  /* Chart colors - Modern dark theme */
  --chart-1: 200 100% 50%;
  --chart-2: 142 70% 55%;
  --chart-3: 38 92% 60%;
  --chart-4: 280 65% 70%;
  --chart-5: 340 75% 65%;
  --chart-6: 200 80% 70%;
  --chart-7: 43 96% 77%;
  --chart-8: 188 69% 79%;
}

/* ========================================
   END THEME VARIANTS
   ======================================== */

.dark .theme-gradient-border {
  background: linear-gradient(to right, hsl(var(--primary) / 0.5), hsl(var(--accent) / 0.5));
}

.theme-gradient-border {
  background: linear-gradient(to right, hsl(var(--primary) / 0.2), hsl(var(--accent) / 0.2));
}

/* Light mode specific card styles */
.light-mode-card {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 2px 0 hsl(var(--foreground) / 0.05);
}

.light-mode-form {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 4px 6px -1px hsl(var(--foreground) / 0.1), 0 2px 4px -1px hsl(var(--foreground) / 0.06);
}

/* ===== CONSOLIDATED STYLES FROM app/styles/globals.css ===== */

/* Add custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Dark mode adjustments */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}
