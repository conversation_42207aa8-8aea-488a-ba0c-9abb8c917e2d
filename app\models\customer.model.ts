import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for Shipping Address sub-document
export interface IShippingAddress {
  address_name: string;
  street: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
  is_default: boolean;
}

// Interface for Customer document
export interface ICustomer extends Document {
  _id: Types.ObjectId;
  customer_id: string; // Unique business identifier
  name: string;
  contact_person?: string | null;
  email: string; // Unique
  phone?: string | null;
  address: string; // Primary/Billing address as a single string
  shipping_addresses?: IShippingAddress[];
  payment_terms?: string | null;
  credit_limit?: number | null; // Mongoose Number for Double
  tax_id?: string | null;
  account_status: 'active' | 'inactive' | 'on_hold';
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Schema for Shipping Address sub-document
const ShippingAddressSchema = new Schema<IShippingAddress>({
  address_name: { type: String, required: true, trim: true },
  street: { type: String, required: true, trim: true },
  city: { type: String, required: true, trim: true },
  state_province: { type: String, required: true, trim: true },
  postal_code: { type: String, required: true, trim: true },
  country: { type: String, required: true, trim: true },
  is_default: { type: Boolean, default: false }
}, { _id: false });

// Schema for Customer model
const CustomerSchema: Schema<ICustomer> = new Schema(
  {
    customer_id: {
      type: String,
      required: [true, 'Customer ID is required'],
      unique: true,
      index: true,
      trim: true
    },
    name: {
      type: String,
      required: [true, 'Customer name is required'],
      trim: true
    },
    contact_person: {
      type: String,
      default: null,
      trim: true
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email address'
      ]
    },
    phone: {
      type: String,
      default: null,
      trim: true
    },
    address: { // Primary/Billing address
      type: String,
      required: [true, 'Primary address is required'],
      trim: true
    },
    shipping_addresses: {
      type: [ShippingAddressSchema],
      default: []
    },
    payment_terms: {
      type: String,
      default: null,
      trim: true
    },
    credit_limit: { // Mongoose Number for Double
      type: Number,
      default: null,
      min: [0, 'Credit limit cannot be negative']
    },
    tax_id: {
        type: String,
        default: null,
        trim: true
    },
    account_status: {
      type: String,
      required: [true, 'Account status is required'],
      enum: {
        values: ['active', 'inactive', 'on_hold'],
        message: 'Account status must be one of: active, inactive, on_hold'
      },
      default: 'active'
    },
    notes: {
      type: String,
      default: null,
      trim: true
    }
  },
  { timestamps: true }
);

// Add indexes
CustomerSchema.index({ name: 1 });
CustomerSchema.index({ account_status: 1 });
CustomerSchema.index({ email: 1 }); // Already unique, but good for queries

// Create and export Customer model
let Customer;

try {
  Customer = mongoose.models?.Customer || mongoose.model<ICustomer>('Customer', CustomerSchema);
} catch (error) {
  // If there's an error during model creation, create it fresh
  Customer = mongoose.model<ICustomer>('Customer', CustomerSchema);
}

export { Customer, ShippingAddressSchema as CustomerShippingAddressSchema }; // Export sub-schema if needed elsewhere
export default Customer;