import mongoose, { Document, Schema, Types } from 'mongoose';
import { ASSEMBLY_STATUS, ASSEMBLY_STATUS_VALUES, type AssemblyStatus } from '../types/enums';

// Interface for Parts Required in an Assembly (Canonical)
interface IAssemblyPartRequired {
  partId: Types.ObjectId; // Reference to parts._id
  quantityRequired: number; // Canonical name: quantityRequired (not quantity)
  unitOfMeasure?: string | null; // Canonical: optional
  children?: IAssemblyPartRequired[]; // Support for hierarchical part structures
}

// Interface for Assembly document, aligned with the canonical schema
interface IAssembly extends Document {
  _id: Types.ObjectId;
  assemblyCode: string;      // Unique business code for the assembly, indexed
  name: string;              // Name of the assembly
  productId?: Types.ObjectId | null; // Reference to products._id if this assembly is sold as a specific product
  parentId?: Types.ObjectId | null; // Reference to another assemblies._id if this is a sub-assembly
  isTopLevel: boolean;       // Indicates if this is a top-level assembly, not part of another assembly
  partsRequired: IAssemblyPartRequired[]; // List of component parts and their quantities
  status: AssemblyStatus; // Canonical enum values from centralized enums
  version: number;           // Canonical: Version number as Int32
  manufacturingInstructions?: string | null; // Link to or text of manufacturing SOPs
  estimatedBuildTime?: string | null; // e.g., "1.5 hours", "30 minutes"
  createdAt: Date;           // Timestamp of assembly definition creation
  updatedAt: Date;           // Timestamp of last assembly definition update
}

// Schema for Parts Required (Canonical) - Using recursive definition
const AssemblyPartRequiredSchema: Schema<IAssemblyPartRequired> = new Schema({
  partId: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    required: true,
  },
  quantityRequired: { // Canonical name: quantityRequired
    type: Number,
    required: true,
    min: [0.001, 'Quantity required must be greater than 0'], // Allow decimal quantities
  },
  unitOfMeasure: { // Canonical: optional
    type: String,
    trim: true,
    default: null,
  },
}, { _id: false });

// Set up recursive reference for children after schema creation
AssemblyPartRequiredSchema.add({
  children: [AssemblyPartRequiredSchema]
});



// Mongoose Schema for Assembly, aligned with the canonical schema
const AssemblySchema: Schema<IAssembly> = new Schema({
  assemblyCode: {
    type: String,
    required: [true, 'Assembly code is required.'],
    unique: true,
    index: true,
    trim: true,
  },
  name: {
    type: String,
    required: [true, 'Assembly name is required.'],
    trim: true,
    index: true,
  },
  productId: { // Canonical: Reference to products._id if this assembly is sold as a specific product
    type: Schema.Types.ObjectId,
    ref: 'Product',
    default: null,
  },
  parentId: { // Canonical: Reference to another assemblies._id if this is a sub-assembly
    type: Schema.Types.ObjectId,
    ref: 'Assembly',
    default: null,
  },
  isTopLevel: { // Canonical: Boolean indicating if this is a top-level assembly
    type: Boolean,
    required: true,
    default: true,
    index: true,
  },
  partsRequired: { // Canonical: List of component parts and their quantities
    type: [AssemblyPartRequiredSchema],
    default: [],
  },
  status: { // Canonical enum values from centralized enums
    type: String,
    required: [true, 'Status is required.'],
    enum: {
      values: ASSEMBLY_STATUS_VALUES,
      message: `Status must be one of: ${ASSEMBLY_STATUS_VALUES.join(', ')}.`,
    },
    default: ASSEMBLY_STATUS.PENDING_REVIEW,
    index: true,
  },
  version: { // Canonical: Version number as Int32
    type: Number,
    required: true,
    default: 1,
    min: [1, 'Version must be at least 1'],
    validate: {
      validator: Number.isInteger,
      message: 'Version must be an integer'
    }
  },
  manufacturingInstructions: { // Canonical: Link to or text of manufacturing SOPs
    type: String,
    default: null,
    trim: true,
  },
  estimatedBuildTime: { // Canonical: e.g., "1.5 hours", "30 minutes"
    type: String,
    default: null,
    trim: true,
  },
}, {
  timestamps: true, // Automatically manages createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Add index for better query performance

AssemblySchema.index({ parentId: 1 });

// Create the Assembly model with proper TypeScript typing
const Assembly = mongoose.models?.Assembly as mongoose.Model<IAssembly> || mongoose.model<IAssembly>('Assembly', AssemblySchema);

// Export the model as default
export default Assembly;

// Export types with proper type exports for isolatedModules
export type { IAssembly, IAssemblyPartRequired };
