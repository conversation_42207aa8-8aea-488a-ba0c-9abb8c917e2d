"use client";

import { cn } from "@/app/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import React from "react";
import { Skeleton } from "../skeleton";

/**
 * Loading skeleton variants for different content types
 */
const skeletonVariants = cva(
  "space-y-4",
  {
    variants: {
      variant: {
        default: "",
        card: "p-6 border rounded-lg bg-card",
        list: "space-y-3",
        grid: "grid gap-4",
        form: "space-y-6",
        table: "space-y-2",
      },
      size: {
        sm: "text-sm",
        md: "",
        lg: "text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

export interface LoadingSkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {
  /**
   * Number of skeleton items to render
   */
  count?: number;
  
  /**
   * Grid columns for grid variant
   */
  columns?: number;
  
  /**
   * Whether to show header skeleton
   */
  showHeader?: boolean;
  
  /**
   * Whether to show footer skeleton
   */
  showFooter?: boolean;
  
  /**
   * Custom height for skeleton items
   */
  itemHeight?: string;
}

/**
 * Versatile loading skeleton component for different content layouts
 */
export const LoadingSkeleton = React.forwardRef<HTMLDivElement, LoadingSkeletonProps>(
  (
    {
      className,
      variant,
      size,
      count = 3,
      columns = 3,
      showHeader = false,
      showFooter = false,
      itemHeight,
      ...props
    },
    ref
  ) => {
    const gridCols = {
      1: "grid-cols-1",
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
      5: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",
      6: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6",
    }[Math.min(6, Math.max(1, columns))] || "grid-cols-3";

    return (
      <div
        ref={ref}
        className={cn(
          skeletonVariants({ variant, size }),
          variant === "grid" && gridCols,
          className
        )}
        {...props}
      >
        {/* Header skeleton */}
        {showHeader && (
          <div className="space-y-2">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        )}

        {/* Content skeletons */}
        {Array.from({ length: count }).map((_, index) => (
          <SkeletonItem
            key={index}
            variant={variant}
            itemHeight={itemHeight || "h-20"}
            index={index}
          />
        ))}

        {/* Footer skeleton */}
        {showFooter && (
          <div className="flex justify-between items-center pt-4 border-t">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-32" />
          </div>
        )}
      </div>
    );
  }
);

LoadingSkeleton.displayName = "LoadingSkeleton";

/**
 * Individual skeleton item component
 */
interface SkeletonItemProps {
  variant?: VariantProps<typeof skeletonVariants>["variant"];
  itemHeight?: string;
  index: number;
}

const SkeletonItem: React.FC<SkeletonItemProps> = ({ variant, itemHeight, index }) => {
  const defaultHeight = itemHeight || "h-20";

  switch (variant) {
    case "card":
      return (
        <div className="p-4 border rounded-lg space-y-3">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
          <Skeleton className={cn("w-full", defaultHeight)} />
          <div className="flex justify-between">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
        </div>
      );

    case "list":
      return (
        <div className="flex items-center space-x-3 p-3 border rounded">
          <Skeleton className="h-8 w-8 rounded" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-2/3" />
          </div>
          <Skeleton className="h-6 w-12" />
        </div>
      );

    case "grid":
      return (
        <div className="space-y-3">
          <Skeleton className={cn("w-full", defaultHeight)} />
          <div className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      );

    case "form":
      return (
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full" />
          {index % 3 === 0 && <Skeleton className="h-3 w-48" />}
        </div>
      );

    case "table":
      return (
        <div className="flex items-center space-x-4 p-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 flex-1" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-12" />
        </div>
      );

    default:
      return <Skeleton className={cn("w-full", defaultHeight)} />;
  }
};

/**
 * Specialized skeleton components for common use cases
 */

/**
 * Page loading skeleton with header and content
 */
export const PageLoadingSkeleton: React.FC<{
  title?: string;
  showBreadcrumb?: boolean;
  contentType?: "grid" | "list" | "form";
  className?: string;
}> = ({ title, showBreadcrumb = false, contentType = "grid", className }) => (
  <div className={cn("container mx-auto py-8 px-4 space-y-6", className)}>
    {/* Breadcrumb */}
    {showBreadcrumb && (
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-16" />
        <span className="text-muted-foreground">/</span>
        <Skeleton className="h-4 w-20" />
      </div>
    )}
    
    {/* Page header */}
    <div className="flex justify-between items-center">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      <Skeleton className="h-10 w-32" />
    </div>

    {/* Content */}
    <LoadingSkeleton
      variant={contentType}
      count={contentType === "grid" ? 8 : 5}
      columns={contentType === "grid" ? 4 : 1}
    />
  </div>
);

/**
 * Chart loading skeleton
 */
export const ChartLoadingSkeleton: React.FC<{
  title?: string;
  height?: string;
  showLegend?: boolean;
  className?: string;
}> = ({ title, height = "h-[400px]", showLegend = true, className }) => (
  <div className={cn("bg-card rounded-lg border p-6 space-y-4", className)}>
    {/* Chart header */}
    <div className="flex justify-between items-start">
      <div className="space-y-2">
        <Skeleton className="h-6 w-40" />
        {title && <Skeleton className="h-4 w-24" />}
      </div>
      <Skeleton className="h-8 w-8" />
    </div>

    {/* Chart area */}
    <Skeleton className={cn("w-full", height)} />

    {/* Legend */}
    {showLegend && (
      <div className="flex justify-center space-x-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-2">
            <Skeleton className="h-3 w-3 rounded-full" />
            <Skeleton className="h-3 w-16" />
          </div>
        ))}
      </div>
    )}
  </div>
);

/**
 * Form loading skeleton
 */
export const FormLoadingSkeleton: React.FC<{
  fields?: number;
  showSubmit?: boolean;
  className?: string;
}> = ({ fields = 5, showSubmit = true, className }) => (
  <div className={cn("max-w-2xl mx-auto space-y-6", className)}>
    {/* Form header */}
    <div className="space-y-2">
      <Skeleton className="h-8 w-64" />
      <Skeleton className="h-4 w-96" />
    </div>

    {/* Form fields */}
    <LoadingSkeleton variant="form" count={fields} />

    {/* Submit area */}
    {showSubmit && (
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-32" />
      </div>
    )}
  </div>
);
