'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { ChartLoadingSkeleton } from '@/app/components/data-display/loading';

// Define the types from the original component
interface CapacityData {
  department: string;
  currentCapacity: number;
  maxCapacity: number;
  bottleneck: boolean;
}

interface ProductionCapacityProps {
  capacityData: CapacityData[];
  forecastAccuracy: number;
  productionTrend: 'increasing' | 'decreasing' | 'stable';
  bottleneckImpact: number;
}

// Loading component using standardized ChartLoadingSkeleton
const loading = () => <ChartLoadingSkeleton title="Production Capacity" height="h-[400px]" />;

// Dynamically import the ProductionCapacity component
const DynamicProductionCapacity = dynamic(
  () => import('./ProductionCapacity'),
  {
    loading,
    ssr: false,
  }
);

/**
 * Lazy-loaded wrapper for ProductionCapacity
 * This component dynamically imports the actual chart component only when needed
 */
export function LazyProductionCapacity(props: ProductionCapacityProps) {
  return (
    <Suspense fallback={<ChartLoadingSkeleton title="Production Capacity" height="h-[400px]" />}>
      <DynamicProductionCapacity {...props} />
    </Suspense>
  );
}
