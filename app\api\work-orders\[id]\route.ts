import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { getWorkOrder, updateWorkOrder, deleteWorkOrder, handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the woNumber field
}

/**
 * GET handler for fetching a specific work order by its woNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the work order number
 * @returns JSON response with work order data or error
 */
export async function GET(
  _request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: woNumber } = await context.params; // The unique string identifier (woNumber)
  try {
    console.log(`[API] GET /api/work-orders/${woNumber} - Fetching work order`);

    // Call the service function to get the work order
    const workOrder = await getWorkOrder(woNumber);

    const duration = Date.now() - startTime;

    if (!workOrder) {
      console.log(`[API] Work Order ${woNumber} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Work Order with number ${woNumber} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched work order ${woNumber} successfully (${duration}ms)`);
    return NextResponse.json({ data: workOrder, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching work order ${woNumber} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific work order by its woNumber
 * @param request - The incoming request with updated work order data
 * @param params - Route parameters including the work order number
 * @returns JSON response with updated work order data or error
 */
export async function PUT(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: woNumber } = await context.params;
  try {
    console.log(`[API] PUT /api/work-orders/${woNumber} - Updating work order`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Call the service function to update the work order
    const updatedWorkOrder = await updateWorkOrder(woNumber, updateData);

    const duration = Date.now() - startTime;

    console.log(`[API] Updated work order ${woNumber} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedWorkOrder, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating work order ${woNumber} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific work order by its woNumber
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the work order number
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: woNumber } = await context.params;
  try {
    console.log(`[API] DELETE /api/work-orders/${woNumber} - Deleting work order`);

    // Call the service function to delete the work order
    const result = await deleteWorkOrder(woNumber);

    const duration = Date.now() - startTime;

    console.log(`[API] Deleted work order ${woNumber} successfully (${duration}ms)`);
    return NextResponse.json({ data: result, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting work order ${woNumber} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
