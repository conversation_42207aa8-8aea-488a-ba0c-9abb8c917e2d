'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { ChartLoadingSkeleton } from '@/app/components/data-display/loading';
import { WorkOrder, Product } from '@/app/types';

// Loading component using standardized ChartLoadingSkeleton
const loading = () => <ChartLoadingSkeleton title="Production Planning" height="h-[400px]" />;

// Dynamically import the ProductionPlanning component
const DynamicProductionPlanning = dynamic(
  () => import('./ProductionPlanning'),
  {
    loading,
    ssr: false,
  }
);

// Props interface
interface LazyProductionPlanningProps {
  workOrders: WorkOrder[];
  products: Product[];
}

/**
 * Lazy-loaded wrapper for ProductionPlanning
 * This component dynamically imports the actual chart component only when needed
 */
export function LazyProductionPlanning({ workOrders, products }: LazyProductionPlanningProps) {
  return (
    <Suspense fallback={<ChartLoadingSkeleton title="Production Planning" height="h-[400px]" />}>
      <DynamicProductionPlanning workOrders={workOrders} products={products} />
    </Suspense>
  );
}
