import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/app/lib/mongodb';
import Part from '@/app/models/part.model';
import Inventories from '@/app/models/inventories.model';
import Location from '@/app/models/location.model';
import { Types } from 'mongoose';

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();
    
    const { partId, locationId, stockType } = await request.json();
    
    if (!partId || !locationId || !stockType) {
      return NextResponse.json({
        success: false,
        error: 'partId, locationId, and stockType are required'
      }, { status: 400 });
    }

    // Get the part to check its current stock
    const part = await Part.findById(partId);
    if (!part) {
      return NextResponse.json({
        success: false,
        error: 'Part not found'
      }, { status: 404 });
    }

    // Check if inventory record already exists
    const existingInventory = await Inventories.findOne({
      partId: new Types.ObjectId(partId),
      locationId: new Types.ObjectId(locationId),
      stockType
    });

    if (existingInventory) {
      return NextResponse.json({
        success: true,
        message: 'Inventory record already exists',
        inventory: existingInventory
      });
    }

    // Get the quantity from the part's stock levels (using type assertion since this is a debug route)
    const quantity = (part as any).stockLevels?.[stockType] || 0;

    // Create new inventory record
    const newInventory = new Inventories({
      partId: new Types.ObjectId(partId),
      locationId: new Types.ObjectId(locationId),
      stockType,
      quantity,
      lastUpdated: new Date()
    });

    await newInventory.save();

    return NextResponse.json({
      success: true,
      message: 'Inventory record created successfully',
      inventory: newInventory
    });

  } catch (error: any) {
    console.error('Error syncing inventory:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const partId = searchParams.get('partId');
    
    if (!partId) {
      return NextResponse.json({
        success: false,
        error: 'partId is required'
      }, { status: 400 });
    }

    // Get all inventory records for this part
    const inventories = await Inventories.find({
      partId: new Types.ObjectId(partId)
    }).populate('locationId', 'name warehouseId');

    // Get all available locations
    const locations = await Location.find({}).populate('warehouseId', 'name');

    // Get the part details
    const part = await Part.findById(partId);

    return NextResponse.json({
      success: true,
      data: {
        part: {
          _id: part?._id,
          partNumber: part?.partNumber,
          name: part?.name,
          currentStock: (part as any)?.currentStock,
          stockLevels: (part as any)?.stockLevels
        },
        inventories,
        availableLocations: locations
      }
    });

  } catch (error: any) {
    console.error('Error getting inventory data:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
