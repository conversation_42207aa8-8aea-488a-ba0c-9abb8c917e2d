// app/api/analytics/route.ts
import { NextRequest } from 'next/server'
import {
  generateInventoryTrends,
  generateStockLevels,
  generateCategoryDistribution,
  generateInventoryValueByCategory,
  generateDashboardAnalytics,
  getAnalyticsTypes
} from '@/app/services/analytics'
import { successResponse, errorResponse } from '@/app/lib/api-response'
import { logApiRequest, logError } from '@/app/services/logging'
import { standardizeAnalyticsResponse } from '@/app/lib/analytics-helpers'

/**
 * GET /api/analytics
 *
 * - No ?type & no ?dashboard=true  → returns your list of types
 * - ?dashboard=true               → combined dashboard (summary + all series)
 * - ?type=inventory-trends        → that one series + summary
 * - ?type=stock-levels            → that one series + summary
 * - etc.
 */
export async function GET(request: NextRequest) {
  const t0 = Date.now()
  const { searchParams } = request.nextUrl
  const type      = searchParams.get('type')           // e.g. "stock‐levels"
  const dashboard = searchParams.get('dashboard') === 'true'

  // Declare variables for request parameters in a scope accessible by the catch block
  let timeRange: string = 'month'; // Default value
  let categoryFilter: string | null = null;
  let dateRange: { startDate: string, endDate: string } | null = null;

  try {
    await logApiRequest('GET', '/api/analytics', { type, dashboard }, true)

    // 1) If nobody asked for data → just list the available types
    if (!dashboard && !type) {
      const types = getAnalyticsTypes()
      return successResponse(
        types,
        'Available analytics types retrieved successfully',
        { duration: Date.now() - t0 }
      )
    }

    // 2) Parse filters
    timeRange      = searchParams.get('timeRange') || 'month'
    categoryFilter = searchParams.get('category')   || null
    if (searchParams.has('startDate') && searchParams.has('endDate')) {
      dateRange = {
        startDate: searchParams.get('startDate')!,
        endDate:   searchParams.get('endDate')!
      }
    }

    // 3) Always fetch a summary (so your UI can do analyticsData.summary.totalValue.toFixed(2) safely)
    const summary = await generateDashboardAnalytics()

    // 4) Dashboard view (all four series at once)
    if (dashboard) {
      const stockData     = await generateStockLevels({ categoryFilter, dateRange })
      const trendData     = await generateInventoryTrends({ timeRange, categoryFilter, dateRange })
      const catDistData   = await generateCategoryDistribution({ dateRange })
      const invValueData  = await generateInventoryValueByCategory({})

      const payload = {
        summary,
        stockLevels:          stockData.weeklyData,
        inventoryTrends:      trendData.trends,
        categoryDistribution: catDistData.distribution,
        inventoryValue:       invValueData.valueByCategory, // adjust to whatever your service returns
        generatedAt:          new Date().toISOString()
      }

      return successResponse(
        payload,
        'Dashboard analytics data retrieved successfully',
        { duration: Date.now() - t0 }
      )
    }

    // 5) Single‐type view
    let rawData: Record<string, any> = {
      summary,
      generatedAt: new Date().toISOString()
    }
    let message = ''

    switch (type) {
      case 'inventory-trends': {
        const d = await generateInventoryTrends({ timeRange, categoryFilter, dateRange })
        rawData.inventoryTrends = d.trends
        message = 'Inventory trends data generated successfully'
        break
      }

      case 'stock-levels': {
        const d = await generateStockLevels({ categoryFilter, dateRange })
        rawData.stockLevels = d.weeklyData
        // Merge the stock-levels specific summary with the dashboard summary
        // to ensure we have totalValue and other required fields
        rawData.summary = { ...rawData.summary, ...d.summary }
        message = 'Stock levels data generated successfully'
        break
      }

      case 'category-distribution': {
        const d = await generateCategoryDistribution({ dateRange })
        rawData.categoryDistribution = d.distribution
        message = 'Category distribution data generated successfully'
        break
      }

      case 'inventory-value': {
        const d = await generateInventoryValueByCategory({})
        rawData.inventoryValue = d.valueByCategory
        message = 'Inventory value data generated successfully'
        break
      }

      default: {
        // Unknown type → fall back to listing
        const types = getAnalyticsTypes()
        return successResponse(
          types,
          'Available analytics types retrieved successfully',
          { duration: Date.now() - t0 }
        )
      }
    }

    // Standardize the response format to ensure consistent structure
    const standardizedPayload = standardizeAnalyticsResponse(rawData)
    
    return successResponse(
      standardizedPayload,
      message,
      { duration: Date.now() - t0 }
    )

  } catch (err) {
    const dur = Date.now() - t0
    // Enhanced error logging
    let errorDetails = 'Unknown error structure';
    if (err instanceof Error) {
      errorDetails = `Message: ${err.message}, Stack: ${err.stack}`;
    } else {
      try {
        errorDetails = `Raw error object: ${JSON.stringify(err)}`;
      } catch (stringifyError) {
        errorDetails = 'Failed to stringify raw error object.';
      }
    }
    const requestParams = { type, dashboard, timeRange, categoryFilter, dateRange: dateRange ? JSON.stringify(dateRange) : null };
    
    console.error('Analytics API Error Caught. Details:', {
      errorMessage: err instanceof Error ? err.message : 'N/A',
      errorStack: err instanceof Error ? err.stack : 'N/A',
      rawErrorString: errorDetails,
      requestParameters: requestParams,
      durationMs: dur
    });

    await logError(
      'API', 
      `Error in GET /api/analytics. Params: ${JSON.stringify(requestParams)}. Details: ${errorDetails}`, 
      err
    );
    return errorResponse(
      'ANALYTICS_ERROR',
      err instanceof Error ? err.message : 'An unknown error occurred',
      [],
      500
    )
  }
}