import mongoose from 'mongoose';
import connectToDatabase from '../lib/mongodb';
import { Assembly, PurchaseOrder, Transaction, WorkOrder } from '../models';
import { Part } from '../models/part.model';
import { logError, logReportGeneration } from './logging';

// Type definitions for aggregation results
interface StockAggregationResult {
  _id: string | null;
  totalStock: number;
}

interface StockStatusAggregationResult {
  _id: string;
  partId: string;
  partNumber: string;
  name: string;
  totalStock: number;
  reorderLevel?: number;
}

/**
 * Generate inventory status report
 * @param options - Report options including filters and date range
 * @returns Inventory status report data
 */
export async function generateInventoryReport(options: any = {}) {
  const {
    lowStockOnly = false,
    categoryFilter = null,
    dateRange = null,
  } = options;

  // Log report generation
  await logReportGeneration('inventory', { lowStockOnly, categoryFilter, dateRange }, true);

  try {
    // Connect to database and get db instance
    const { db } = await connectToDatabase();

    // Build filter based on options
    const filter: any = {};

    // Add low stock filter if requested
    if (lowStockOnly) {
      filter.$expr = {
        $lt: [
          { $ifNull: ['$inventory.currentStock', 0] },
          { $ifNull: ['$reorderLevel', 0] }
        ]
      };
    }

    // Add category filter if provided
    if (categoryFilter) {
      filter.categoryId = categoryFilter;
    }

    // Query parts with inventory information
    const parts = await Part.find(filter)
      .select('_id name description status inventory reorderLevel isManufactured')
      .lean();

    // Calculate inventory metrics using v4 schema
    const totalItems = parts.length;

    // V4 Schema: Calculate total stock from inventories collection
    const stockAggregation = await db.collection('inventories').aggregate([
      {
        $group: {
          _id: null,
          totalStock: { $sum: '$quantity' }
        }
      }
    ]).toArray() as StockAggregationResult[];
    const totalStock = stockAggregation.length > 0 ? stockAggregation[0]?.totalStock || 0 : 0;

    // V4 Schema: Calculate low stock and out of stock items
    const stockStatusAggregation = await db.collection('inventories').aggregate([
      {
        $group: {
          _id: '$partId',
          totalStock: { $sum: '$quantity' }
        }
      },
      {
        $lookup: {
          from: 'parts',
          localField: '_id',
          foreignField: '_id',
          as: 'part'
        }
      },
      {
        $unwind: '$part'
      },
      {
        $project: {
          partId: '$_id',
          partNumber: '$part.partNumber',
          name: '$part.name',
          totalStock: 1,
          reorderLevel: '$part.reorderLevel'
        }
      }
    ]).toArray() as StockStatusAggregationResult[];

    const lowStockItems = stockStatusAggregation.filter(item =>
      item.totalStock > 0 && item.reorderLevel && item.totalStock <= item.reorderLevel
    );
    const outOfStockItems = stockStatusAggregation.filter(item => item.totalStock === 0);

    // Get recent transactions if date range provided
    let recentTransactions = [];
    if (dateRange) {
      const { startDate, endDate } = dateRange;
      recentTransactions = await (Transaction.find as any)({
        transactionDate: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      })
      .sort({ transactionDate: -1 })
      .limit(100)
      .lean();
    }

    return {
      summary: {
        totalItems,
        totalStock,
        lowStockCount: lowStockItems.length,
        outOfStockCount: outOfStockItems.length,
      },
      lowStockItems,
      outOfStockItems,
      recentTransactions,
      generatedAt: new Date()
    };
  } catch (error) {
    await logError('reports', 'Error generating inventory report', error);
    throw error;
  }
}

/**
 * Generate production status report
 * @param options - Report options including filters and date range
 * @returns Production status report data
 */
export async function generateProductionReport(options: any = {}) {
  const {
    statusFilter = null,
    dateRange = null,
  } = options;

  // Log report generation
  await logReportGeneration('production', { statusFilter, dateRange }, true);

  try {
    // Connect to database
    await connectToDatabase();

    // Build filter based on options
    const filter: any = {};

    // Add status filter if provided
    if (statusFilter) {
      filter.status = statusFilter;
    }

    // Add date range filter if provided
    if (dateRange) {
      const { startDate, endDate } = dateRange;
      filter.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Query work orders
    const workOrders = await (WorkOrder.find as any)(filter)
      .sort({ dueDate: 1 })
      .lean();

    // Calculate production metrics
    const totalWorkOrders = workOrders.length;
    const pendingWorkOrders = workOrders.filter((wo: any) => wo.status === 'pending');
    const inProgressWorkOrders = workOrders.filter((wo: any) => wo.status === 'in_progress');
    const completedWorkOrders = workOrders.filter((wo: any) => wo.status === 'completed');
    const overdueWorkOrders = workOrders.filter((wo: any) =>
      wo.status !== 'completed' &&
      wo.dueDate &&
      new Date(wo.dueDate) < new Date()
    );

    return {
      summary: {
        totalWorkOrders,
        pendingCount: pendingWorkOrders.length,
        inProgressCount: inProgressWorkOrders.length,
        completedCount: completedWorkOrders.length,
        overdueCount: overdueWorkOrders.length,
      },
      pendingWorkOrders,
      inProgressWorkOrders,
      overdueWorkOrders,
      generatedAt: new Date()
    };
  } catch (error) {
    await logError('reports', 'Error generating production report', error);
    throw error;
  }
}

/**
 * Generate procurement report
 * @param options - Report options including filters and date range
 * @returns Procurement report data
 */
export async function generateProcurementReport(options: any = {}) {
  const {
    statusFilter = null,
    dateRange = null,
  } = options;

  // Log report generation
  await logReportGeneration('procurement', { statusFilter, dateRange }, false); // Temporarily disable DB logging for testing

  try {
    // Connect to database
    await connectToDatabase();

    // Build filter based on options
    const filter: any = {};

    // Add status filter if provided
    if (statusFilter) {
      filter.status = statusFilter;
    }

    // Add date range filter if provided
    if (dateRange) {
      const { startDate, endDate } = dateRange;
      filter.orderDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Query purchase orders
    const purchaseOrders = await (PurchaseOrder.find as any)(filter)
      .sort({ orderDate: -1 })
      .lean();

    // Calculate procurement metrics
    const totalPOs = purchaseOrders.length;
    const pendingPOs = purchaseOrders.filter((po: any) => po.status === 'pending');
    const receivedPOs = purchaseOrders.filter((po: any) => po.status === 'received');
    const totalSpend = purchaseOrders.reduce((sum: any, po: any) => sum + (po.totalAmount || 0), 0);

    return {
      summary: {
        totalPOs,
        pendingCount: pendingPOs.length,
        receivedCount: receivedPOs.length,
        totalSpend,
      },
      pendingPOs,
      recentPOs: purchaseOrders.slice(0, 10), // Most recent 10 POs
      generatedAt: new Date()
    };
  } catch (error) {
    await logError('reports', 'Error generating procurement report', error);
    throw error;
  }
}

/**
 * Generate assembly status report
 * @param options - Report options including filters
 * @returns Assembly status report data
 */
export async function generateAssemblyReport(options: any = {}) {
  const {
    productFilter = null,
  } = options;

  // Log report generation
  await logReportGeneration('assembly', { productFilter }, true);

  try {
    // Connect to database
    await connectToDatabase();

    // Build filter based on options
    const filter: any = {};

    // Add product filter if provided
    if (productFilter) {
      filter.productId = new mongoose.Types.ObjectId(productFilter);
    }

    // Query assemblies
    const assemblies = await (Assembly.find as any)(filter)
      .lean();

    // Get all part IDs used in assemblies
    const partIds = new Set<string>();
    assemblies.forEach((assembly: any) => {
      assembly.partsRequired.forEach((part: any) => {
        partIds.add(part.partId);
      });
    });

    // Get inventory status for all parts used in assemblies
    const partsInventory = await Part.find({ _id: { $in: Array.from(partIds) } })
      .select('_id name inventory')
      .lean();

    // Create a map for quick lookup
    const partsMap = new Map();
    partsInventory.forEach(part => {
      partsMap.set(part._id, part);
    });

    // Analyze assemblies for completability based on inventory
    const assemblyStatus = assemblies.map((assembly: any) => {
      const missingParts: any[] = [];
      let canComplete = true;

      assembly.partsRequired.forEach((requiredPart: any) => {
        const part = partsMap.get(requiredPart.partId);
        if (!part || (part.inventory?.currentStock || 0) < requiredPart.quantity) {
          canComplete = false;
          missingParts.push({
            partId: requiredPart.partId,
            required: requiredPart.quantity,
            available: part ? (part.inventory?.currentStock || 0) : 0,
            shortfall: requiredPart.quantity - (part ? (part.inventory?.currentStock || 0) : 0)
          });
        }
      });

      return {
        assemblyId: assembly._id,
        assemblyCode: assembly.assemblyCode,
        name: assembly.name,
        canComplete,
        missingParts: missingParts.length > 0 ? missingParts : null
      };
    });

    return {
      summary: {
        totalAssemblies: assemblies.length,
        completableCount: assemblyStatus.filter((a: any) => a.canComplete).length,
        incompleteCount: assemblyStatus.filter((a: any) => !a.canComplete).length,
      },
      assemblyStatus,
      generatedAt: new Date()
    };
  } catch (error) {
    await logError('reports', 'Error generating assembly report', error);
    throw error;
  }
}

/**
 * Get available report types
 * @returns List of available report types and their descriptions
 */
export function getReportTypes() {
  return [
    {
      id: 'inventory',
      name: 'Inventory Status',
      description: 'Current inventory levels, low stock items, and recent transactions',
      endpoint: '/api/reports/inventory'
    },
    {
      id: 'production',
      name: 'Production Status',
      description: 'Work order status, pending production, and overdue items',
      endpoint: '/api/reports/production'
    },
    {
      id: 'procurement',
      name: 'Procurement',
      description: 'Purchase order status, pending deliveries, and spending analysis',
      endpoint: '/api/reports/procurement'
    },
    {
      id: 'assembly',
      name: 'Assembly Status',
      description: 'Assembly completability based on current inventory levels',
      endpoint: '/api/reports/assembly'
    }
  ];
}
