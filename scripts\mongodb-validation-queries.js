#!/usr/bin/env node

/**
 * MONGODB VALIDATION QUERIES FOR INVENTORY MIGRATION
 * 
 * This script provides comprehensive MongoDB queries to validate data
 * consistency before and after the inventory schema migration.
 * 
 * Features:
 * - Pre-migration data validation
 * - Post-migration integrity checks
 * - Cross-schema comparison queries
 * - Performance benchmarking queries
 * - Data consistency verification
 */

const mongoose = require('mongoose');

const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS',
  SAMPLE_SIZE: 100
};

// MongoDB validation queries class
class MongoDBValidationQueries {
  constructor() {
    this.db = null;
  }

  async connect() {
    await mongoose.connect(CONFIG.MONGODB_URI);
    this.db = mongoose.connection.db;
    console.log('Connected to MongoDB for validation');
  }

  async disconnect() {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }

  /**
   * PRE-MIGRATION VALIDATION QUERIES
   */
  async validatePreMigration() {
    console.log('\n=== PRE-MIGRATION VALIDATION ===');

    // Query 1: Count parts with inventory data
    const partsWithInventory = await this.db.collection('parts').countDocuments({
      'inventory.stockLevels': { $exists: true }
    });
    console.log(`Parts with inventory data: ${partsWithInventory}`);

    // Query 2: Validate inventory structure consistency
    const structureValidation = await this.db.collection('parts').aggregate([
      { $match: { 'inventory.stockLevels': { $exists: true } } },
      {
        $project: {
          partNumber: 1,
          hasWarehouseId: { $type: '$inventory.warehouseId' },
          hasStockLevels: { $type: '$inventory.stockLevels' },
          stockLevelsKeys: { $objectToArray: '$inventory.stockLevels' },
          totalStock: {
            $add: [
              { $ifNull: ['$inventory.stockLevels.raw', 0] },
              { $ifNull: ['$inventory.stockLevels.hardening', 0] },
              { $ifNull: ['$inventory.stockLevels.grinding', 0] },
              { $ifNull: ['$inventory.stockLevels.finished', 0] },
              { $ifNull: ['$inventory.stockLevels.rejected', 0] }
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          totalParts: { $sum: 1 },
          partsWithWarehouseId: {
            $sum: { $cond: [{ $eq: ['$hasWarehouseId', 'objectId'] }, 1, 0] }
          },
          partsWithValidStockLevels: {
            $sum: { $cond: [{ $eq: ['$hasStockLevels', 'object'] }, 1, 0] }
          },
          totalStockAcrossAllParts: { $sum: '$totalStock' },
          avgStockPerPart: { $avg: '$totalStock' }
        }
      }
    ]).toArray();

    console.log('Structure validation:', structureValidation[0]);

    // Query 3: Check for data anomalies
    const anomalies = await this.db.collection('parts').aggregate([
      { $match: { 'inventory.stockLevels': { $exists: true } } },
      {
        $project: {
          partNumber: 1,
          negativeStock: {
            $or: [
              { $lt: ['$inventory.stockLevels.raw', 0] },
              { $lt: ['$inventory.stockLevels.hardening', 0] },
              { $lt: ['$inventory.stockLevels.grinding', 0] },
              { $lt: ['$inventory.stockLevels.finished', 0] },
              { $lt: ['$inventory.stockLevels.rejected', 0] }
            ]
          },
          missingWarehouse: { $eq: ['$inventory.warehouseId', null] },
          invalidStockTypes: {
            $ne: [
              { $size: { $objectToArray: '$inventory.stockLevels' } },
              5
            ]
          }
        }
      },
      {
        $match: {
          $or: [
            { negativeStock: true },
            { missingWarehouse: true },
            { invalidStockTypes: true }
          ]
        }
      }
    ]).toArray();

    console.log(`Data anomalies found: ${anomalies.length}`);
    if (anomalies.length > 0) {
      console.log('Sample anomalies:', anomalies.slice(0, 5));
    }

    // Query 4: Warehouse distribution
    const warehouseDistribution = await this.db.collection('parts').aggregate([
      { $match: { 'inventory.warehouseId': { $exists: true } } },
      {
        $group: {
          _id: '$inventory.warehouseId',
          partCount: { $sum: 1 },
          totalStock: {
            $sum: {
              $add: [
                { $ifNull: ['$inventory.stockLevels.raw', 0] },
                { $ifNull: ['$inventory.stockLevels.hardening', 0] },
                { $ifNull: ['$inventory.stockLevels.grinding', 0] },
                { $ifNull: ['$inventory.stockLevels.finished', 0] },
                { $ifNull: ['$inventory.stockLevels.rejected', 0] }
              ]
            }
          }
        }
      },
      { $sort: { partCount: -1 } }
    ]).toArray();

    console.log('Warehouse distribution:', warehouseDistribution);

    return {
      partsWithInventory,
      structureValidation: structureValidation[0],
      anomalies: anomalies.length,
      warehouseDistribution
    };
  }

  /**
   * POST-MIGRATION VALIDATION QUERIES
   */
  async validatePostMigration() {
    console.log('\n=== POST-MIGRATION VALIDATION ===');

    // Query 1: Count inventory records created
    const inventoryRecords = await this.db.collection('inventories').countDocuments();
    console.log(`Inventory records created: ${inventoryRecords}`);

    // Query 2: Validate inventory collection structure
    const inventoryStructure = await this.db.collection('inventories').aggregate([
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          uniqueParts: { $addToSet: '$partId' },
          uniqueWarehouses: { $addToSet: '$warehouseId' },
          stockTypes: { $addToSet: '$stockType' },
          totalQuantity: { $sum: '$quantity' },
          avgQuantity: { $avg: '$quantity' }
        }
      },
      {
        $project: {
          totalRecords: 1,
          uniquePartsCount: { $size: '$uniqueParts' },
          uniqueWarehousesCount: { $size: '$uniqueWarehouses' },
          stockTypes: 1,
          totalQuantity: 1,
          avgQuantity: 1
        }
      }
    ]).toArray();

    console.log('Inventory structure:', inventoryStructure[0]);

    // Query 3: Cross-validate stock totals between old and new schema
    const stockComparison = await this.db.collection('parts').aggregate([
      {
        $match: { 'inventory.stockLevels': { $exists: true } }
      },
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'newInventory'
        }
      },
      {
        $project: {
          partNumber: 1,
          oldTotalStock: {
            $add: [
              { $ifNull: ['$inventory.stockLevels.raw', 0] },
              { $ifNull: ['$inventory.stockLevels.hardening', 0] },
              { $ifNull: ['$inventory.stockLevels.grinding', 0] },
              { $ifNull: ['$inventory.stockLevels.finished', 0] },
              { $ifNull: ['$inventory.stockLevels.rejected', 0] }
            ]
          },
          newTotalStock: { $sum: '$newInventory.quantity' },
          recordCount: { $size: '$newInventory' }
        }
      },
      {
        $match: {
          $expr: { $ne: ['$oldTotalStock', '$newTotalStock'] }
        }
      }
    ]).toArray();

    console.log(`Stock mismatches found: ${stockComparison.length}`);
    if (stockComparison.length > 0) {
      console.log('Sample mismatches:', stockComparison.slice(0, 5));
    }

    // Query 4: Check for orphaned inventory records
    const orphanedRecords = await this.db.collection('inventories').aggregate([
      {
        $lookup: {
          from: 'parts',
          localField: 'partId',
          foreignField: '_id',
          as: 'part'
        }
      },
      {
        $match: { 'part.0': { $exists: false } }
      },
      {
        $count: 'orphaned'
      }
    ]).toArray();

    const orphanedCount = orphanedRecords.length > 0 ? orphanedRecords[0].orphaned : 0;
    console.log(`Orphaned inventory records: ${orphanedCount}`);

    // Query 5: Validate indexes
    const inventoryIndexes = await this.db.collection('inventories').indexes();
    const hasCompoundIndex = inventoryIndexes.some(idx => 
      idx.key && idx.key.partId && idx.key.warehouseId && idx.key.stockType
    );
    console.log(`Required compound index exists: ${hasCompoundIndex}`);

    return {
      inventoryRecords,
      inventoryStructure: inventoryStructure[0],
      stockMismatches: stockComparison.length,
      orphanedRecords: orphanedCount,
      hasRequiredIndexes: hasCompoundIndex,
      isValid: stockComparison.length === 0 && orphanedCount === 0 && hasCompoundIndex
    };
  }

  /**
   * PERFORMANCE COMPARISON QUERIES
   */
  async performanceComparison() {
    console.log('\n=== PERFORMANCE COMPARISON ===');

    // Test 1: Simple part lookup (embedded vs aggregation)
    console.log('Testing part lookup performance...');
    
    const samplePartIds = await this.db.collection('parts')
      .find({}, { _id: 1 })
      .limit(10)
      .toArray();

    // Embedded lookup (legacy)
    const embeddedStart = Date.now();
    for (const part of samplePartIds) {
      await this.db.collection('parts').findOne({ _id: part._id });
    }
    const embeddedTime = Date.now() - embeddedStart;

    // Aggregation lookup (new)
    const aggregationStart = Date.now();
    for (const part of samplePartIds) {
      await this.db.collection('parts').aggregate([
        { $match: { _id: part._id } },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'partId',
            as: 'inventoryRecords'
          }
        }
      ]).toArray();
    }
    const aggregationTime = Date.now() - aggregationStart;

    console.log(`Embedded lookup: ${embeddedTime}ms`);
    console.log(`Aggregation lookup: ${aggregationTime}ms`);
    console.log(`Performance ratio: ${(aggregationTime / embeddedTime).toFixed(2)}x`);

    // Test 2: Bulk operations
    console.log('\nTesting bulk operations...');
    
    const bulkEmbeddedStart = Date.now();
    await this.db.collection('parts').find({
      'inventory.stockLevels': { $exists: true }
    }).limit(100).toArray();
    const bulkEmbeddedTime = Date.now() - bulkEmbeddedStart;

    const bulkAggregationStart = Date.now();
    await this.db.collection('parts').aggregate([
      { $match: { 'inventory.stockLevels': { $exists: true } } },
      { $limit: 100 },
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      }
    ]).toArray();
    const bulkAggregationTime = Date.now() - bulkAggregationStart;

    console.log(`Bulk embedded: ${bulkEmbeddedTime}ms`);
    console.log(`Bulk aggregation: ${bulkAggregationTime}ms`);
    console.log(`Bulk performance ratio: ${(bulkAggregationTime / bulkEmbeddedTime).toFixed(2)}x`);

    return {
      singleLookup: {
        embedded: embeddedTime,
        aggregation: aggregationTime,
        ratio: aggregationTime / embeddedTime
      },
      bulkLookup: {
        embedded: bulkEmbeddedTime,
        aggregation: bulkAggregationTime,
        ratio: bulkAggregationTime / bulkEmbeddedTime
      }
    };
  }

  /**
   * DATA CONSISTENCY VERIFICATION
   */
  async verifyDataConsistency() {
    console.log('\n=== DATA CONSISTENCY VERIFICATION ===');

    // Query 1: Verify all parts have corresponding inventory records
    const partsWithoutInventory = await this.db.collection('parts').aggregate([
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },
      {
        $match: {
          'inventory.stockLevels': { $exists: true },
          'inventoryRecords.0': { $exists: false }
        }
      },
      {
        $project: { partNumber: 1, name: 1 }
      }
    ]).toArray();

    console.log(`Parts missing inventory records: ${partsWithoutInventory.length}`);

    // Query 2: Verify stock level consistency
    const stockLevelConsistency = await this.db.collection('parts').aggregate([
      {
        $match: { 'inventory.stockLevels': { $exists: true } }
      },
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },
      {
        $project: {
          partNumber: 1,
          oldFinished: '$inventory.stockLevels.finished',
          newFinished: {
            $sum: {
              $map: {
                input: {
                  $filter: {
                    input: '$inventoryRecords',
                    cond: { $eq: ['$$this.stockType', 'finished'] }
                  }
                },
                as: 'record',
                in: '$$record.quantity'
              }
            }
          }
        }
      },
      {
        $match: {
          $expr: { $ne: ['$oldFinished', '$newFinished'] }
        }
      }
    ]).toArray();

    console.log(`Finished stock inconsistencies: ${stockLevelConsistency.length}`);

    // Query 3: Verify warehouse consistency
    const warehouseConsistency = await this.db.collection('parts').aggregate([
      {
        $match: { 'inventory.warehouseId': { $exists: true } }
      },
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },
      {
        $project: {
          partNumber: 1,
          oldWarehouseId: '$inventory.warehouseId',
          newWarehouseIds: '$inventoryRecords.warehouseId'
        }
      },
      {
        $match: {
          $expr: {
            $not: {
              $in: ['$oldWarehouseId', '$newWarehouseIds']
            }
          }
        }
      }
    ]).toArray();

    console.log(`Warehouse inconsistencies: ${warehouseConsistency.length}`);

    return {
      partsWithoutInventory: partsWithoutInventory.length,
      stockLevelInconsistencies: stockLevelConsistency.length,
      warehouseInconsistencies: warehouseConsistency.length,
      isConsistent: partsWithoutInventory.length === 0 && 
                   stockLevelConsistency.length === 0 && 
                   warehouseConsistency.length === 0
    };
  }

  /**
   * Run all validation queries
   */
  async runAllValidations() {
    try {
      await this.connect();

      const results = {
        preMigration: await this.validatePreMigration(),
        postMigration: await this.validatePostMigration(),
        performance: await this.performanceComparison(),
        consistency: await this.verifyDataConsistency()
      };

      console.log('\n=== VALIDATION SUMMARY ===');
      console.log('Pre-migration parts:', results.preMigration.partsWithInventory);
      console.log('Post-migration records:', results.postMigration.inventoryRecords);
      console.log('Data consistency:', results.consistency.isConsistent ? 'PASS' : 'FAIL');
      console.log('Performance impact:', `${results.performance.singleLookup.ratio.toFixed(2)}x`);

      return results;
    } finally {
      await this.disconnect();
    }
  }
}

// Main execution
async function main() {
  const validator = new MongoDBValidationQueries();
  
  try {
    const results = await validator.runAllValidations();
    
    // Write results to file
    const fs = require('fs');
    const timestamp = new Date().toISOString().split('T')[0];
    const resultsFile = `validation-results-${timestamp}.json`;
    
    fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
    console.log(`\nResults saved to: ${resultsFile}`);
    
    // Exit with appropriate code
    const isValid = results.postMigration.isValid && results.consistency.isConsistent;
    process.exit(isValid ? 0 : 1);
    
  } catch (error) {
    console.error('Validation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { MongoDBValidationQueries };
