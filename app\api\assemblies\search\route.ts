import { NextRequest } from 'next/server';
import { searchAssemblies, handleMongoDBError } from '@/app/services/assembly.service';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

// Maximum allowed limit per request
const MAX_LIMIT = 100;
const ROUTE_PATH = '/api/assemblies/search';

/**
 * GET handler for searching assemblies with advanced filtering
 * @param request - The incoming request
 * @returns JSON response with filtered assemblies
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);
  
  console.log('[API] GET /api/assemblies/search - Searching assemblies');
  const url = new URL(request.url);

  // --- Extract Search Parameters ---
  // Main search query - will be used for text search across multiple fields
  const query = url.searchParams.get('q') || '';
  
  // --- Pagination Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit
  
  // --- Sorting Parameters ---
  const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
  const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;
  
  // --- Advanced Filter Parameters ---
  const filter: any = {};
  
  // Status filter (active, inactive, prototype, obsolete)
  const status = url.searchParams.get('status');
  if (status) filter.status = status;
  
  // Type filter (standard, custom, kit)
  const type = url.searchParams.get('type');
  if (type) filter.type = type;
  
  // Assembly ID exact match
  const assemblyId = url.searchParams.get('assembly_id');
  if (assemblyId) filter.assembly_id = assemblyId;
  
  // Date range filters for createdAt
  const createdAfter = url.searchParams.get('createdAfter');
  const createdBefore = url.searchParams.get('createdBefore');
  
  if (createdAfter || createdBefore) {
    filter.createdAt = {};
    
    if (createdAfter) {
      filter.createdAt.$gte = new Date(createdAfter);
    }
    
    if (createdBefore) {
      filter.createdAt.$lte = new Date(createdBefore);
    }
  }
  
  // Manufacturing lead time range
  const minLeadTime = url.searchParams.get('minLeadTime');
  const maxLeadTime = url.searchParams.get('maxLeadTime');
  
  if (minLeadTime || maxLeadTime) {
    filter.manufacturing_lead_time = {};
    
    if (minLeadTime) {
      filter.manufacturing_lead_time.$gte = parseInt(minLeadTime, 10);
    }
    
    if (maxLeadTime) {
      filter.manufacturing_lead_time.$lte = parseInt(maxLeadTime, 10);
    }
  }
  
  // Cost range
  const minCost = url.searchParams.get('minCost');
  const maxCost = url.searchParams.get('maxCost');
  
  if (minCost || maxCost) {
    filter.cost = {};
    
    if (minCost) {
      filter.cost.$gte = parseFloat(minCost);
    }
    
    if (maxCost) {
      filter.cost.$lte = parseFloat(maxCost);
    }
  }
  
  // Check for components - can filter to only include assemblies with components
  const hasComponents = url.searchParams.get('hasComponents');
  if (hasComponents === 'true') {
    filter['partsRequired.0'] = { $exists: true };
  } else if (hasComponents === 'false') {
    filter.partsRequired = { $size: 0 };
  }
  
  console.log(`[API] Searching with params: query=${query}, filters=${JSON.stringify(filter)}`);
  
  // Prepare search options
  const searchOptions = {
    query,
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter
  };
  
  // Call service layer to perform search
  const result = await searchAssemblies(searchOptions);
  
  const duration = Date.now() - startTime;
  console.log(`[API] Search completed in ${duration}ms, found ${result.assemblies.length} results`);
  
  // Return results with pagination metadata
  return successResponse(
    result.assemblies,
    `Found ${result.pagination.totalCount} assemblies matching criteria`,
    {
      duration,
      pagination: result.pagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(handleGET, ROUTE_PATH); 