import { checkDatabaseHealth, forceReconnect } from '@/app/lib/mongodb';
import { logError } from '@/app/services/logging';

/**
 * Monitors the health of the database connection
 * This can be called on a timer in development or in a background process in production
 */
export async function monitorDatabaseConnection() {
  try {
    console.log('[Monitor] Checking database connection health...');
    const healthResult = await checkDatabaseHealth();
    
    if (healthResult.status === 'healthy') {
      console.log('[Monitor] Database connection is healthy');
      return true;
    } else if (healthResult.status === 'warning') {
      console.warn('[Monitor] Database connection warning:', healthResult.message);
      
      // Attempt to ping again after a short delay
      await new Promise(resolve => setTimeout(resolve, 500));
      const retryHealth = await checkDatabaseHealth();
      
      if (retryHealth.status !== 'healthy') {
        console.warn('[Monitor] Database connection still in warning state, reconnecting...');
        await forceReconnect();
      }
      
      return retryHealth.status === 'healthy';
    } else {
      // Connection is in error state
      console.error('[Monitor] Database connection error:', healthResult.message);
      
      // Try to reconnect
      try {
        console.log('[Monitor] Attempting to reconnect to database...');
        await forceReconnect();
        
        // Check if reconnection was successful
        const reconnectHealth = await checkDatabaseHealth();
        console.log('[Monitor] Reconnection result:', reconnectHealth.status);
        
        return reconnectHealth.status === 'healthy';
      } catch (reconnectError) {
        console.error('[Monitor] Failed to reconnect to database:', 
          reconnectError instanceof Error ? reconnectError.message : String(reconnectError));
        
        // Log the error
        await logError(
          'database', 
          'Failed to reconnect to database during health monitoring', 
          reconnectError
        );
        
        return false;
      }
    }
  } catch (error) {
    console.error('[Monitor] Error monitoring database connection:', 
      error instanceof Error ? error.message : String(error));
    
    // Log the error
    await logError(
      'database', 
      'Error monitoring database connection', 
      error
    );
    
    return false;
  }
}

/**
 * Start periodic monitoring of database connection
 * This should be called during application startup in development
 * 
 * @param {number} intervalMs - Interval between checks in milliseconds (default: 30000 - 30 seconds)
 * @returns {Function} - Function to stop monitoring
 */
export function startDatabaseMonitoring(intervalMs = 30000) {
  console.log(`[Monitor] Starting database monitoring with interval of ${intervalMs}ms`);
  
  // Run initial check
  monitorDatabaseConnection().catch(error => {
    console.error('[Monitor] Initial database check failed:', 
      error instanceof Error ? error.message : String(error));
  });
  
  // Set up interval for recurring checks
  const intervalId = setInterval(() => {
    monitorDatabaseConnection().catch(error => {
      console.error('[Monitor] Periodic database check failed:', 
        error instanceof Error ? error.message : String(error));
    });
  }, intervalMs);
  
  // Return function to stop monitoring
  return () => {
    console.log('[Monitor] Stopping database monitoring');
    clearInterval(intervalId);
  };
} 