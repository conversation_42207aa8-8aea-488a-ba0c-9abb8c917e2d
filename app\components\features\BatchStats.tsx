"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/app/components/layout/cards/card';
import { Badge } from '@/app/components/data-display/badge';
import { Layers, Package, Activity, Clock, CheckCircle, Pause } from 'lucide-react';
import React from 'react';

/**
 * Interface for batch statistics
 */
export interface BatchStatsData {
  totalBatches: number;
  activeBatches: number;
  completedBatches: number;
  pendingBatches: number;
  onHoldBatches: number;
  totalQuantityPlanned: number;
  totalQuantityProduced: number;
}

/**
 * Props for BatchStats component
 */
interface BatchStatsProps {
  stats: BatchStatsData;
  isLoading?: boolean;
  className?: string;
}

/**
 * BatchStats component displays key batch metrics in cards
 * Following the same pattern as WarehouseStats
 */
export function BatchStats({ stats, isLoading = false, className = "" }: BatchStatsProps) {
  const statCards = [
    {
      title: "Total Batches",
      value: stats.totalBatches,
      icon: Layers,
      description: "All production batches",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      title: "Active Batches",
      value: stats.activeBatches,
      icon: Activity,
      description: "Currently in progress",
      color: "text-green-600 dark:text-green-400"
    },
    {
      title: "Completed Batches",
      value: stats.completedBatches,
      icon: CheckCircle,
      description: "Successfully finished",
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      title: "Pending Batches",
      value: stats.pendingBatches,
      icon: Clock,
      description: "Awaiting production start",
      color: "text-orange-600 dark:text-orange-400"
    }
  ];

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded w-24"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-16 mb-1"></div>
              <div className="h-3 bg-muted rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {statCards.map((card, index) => {
        const Icon = card.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
              {card.title === "Active Batches" && stats.onHoldBatches > 0 && (
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    <Pause className="h-3 w-3 mr-1" />
                    {stats.onHoldBatches} on hold
                  </Badge>
                </div>
              )}
              {card.title === "Total Batches" && stats.totalQuantityPlanned > 0 && (
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    <Package className="h-3 w-3 mr-1" />
                    {stats.totalQuantityPlanned.toLocaleString()} planned
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
