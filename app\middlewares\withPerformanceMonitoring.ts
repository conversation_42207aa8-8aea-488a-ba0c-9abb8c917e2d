import { captureMessage, setTag, startTransaction } from '@/app/lib/logging-utils';
import { getN1DetectionStats } from '@/app/lib/n1-detection';
import { logApiPerformance } from '@/app/services/logging';
import { NextRequest, NextResponse } from 'next/server';

// PERFORMANCE OPTIMIZATION: Stricter thresholds for better monitoring
const SLOW_THRESHOLD = 500; // 500ms (reduced from 1000ms)
const VERY_SLOW_THRESHOLD = 1000; // 1 second (reduced from 3000ms)
const CRITICAL_THRESHOLD = 2000; // 2 seconds (new critical threshold)

/**
 * Middleware that adds performance monitoring to API routes
 * @param handler - The API route handler
 * @param routePath - The API route path (for performance logging and tagging)
 * @returns A wrapped handler with performance monitoring
 */
export default function withPerformanceMonitoring(
  handler: (request: NextRequest) => Promise<NextResponse>,
  routePath: string
) {
  return async function performanceHandler(request: NextRequest) {
    const startTime = Date.now();

    // PERFORMANCE OPTIMIZATION: Start Sentry transaction for detailed monitoring
    const transaction = startTransaction(`API ${routePath}`, 'http.server', {
      tags: {
        'http.method': request.method,
        'http.route': routePath
      }
    });

    try {
      // Execute the original handler
      const response = await handler(request);

      // Calculate request duration
      const duration = Date.now() - startTime;
    
      // Set performance tags
      setTag('performance.route', routePath);
      setTag('performance.duration', duration.toString());

      // PERFORMANCE OPTIMIZATION: Enhanced performance classification with stricter thresholds
      let performanceCategory = 'normal';
      let alertLevel: 'info' | 'warning' | 'error' = 'info';

      if (duration > CRITICAL_THRESHOLD) {
        performanceCategory = 'critical';
        alertLevel = 'error';
        setTag('performance.category', 'critical');

        // Capture critical performance issue
        captureMessage(`Critical API Performance: ${routePath} took ${duration}ms`, 'error', {
          tags: {
            type: 'performance_critical',
            route: routePath,
            method: request.method
          },
          extra: {
            duration,
            threshold: CRITICAL_THRESHOLD,
            url: request.url,
            userAgent: request.headers.get('user-agent'),
            n1Stats: getN1DetectionStats()
          }
        });
      } else if (duration > VERY_SLOW_THRESHOLD) {
        performanceCategory = 'very-slow';
        alertLevel = 'warning';
        setTag('performance.category', 'very-slow');

        // Capture slow performance warning
        captureMessage(`Slow API Performance: ${routePath} took ${duration}ms`, 'warning', {
          tags: {
            type: 'performance_slow',
            route: routePath,
            method: request.method
          },
          extra: {
            duration,
            threshold: VERY_SLOW_THRESHOLD,
            n1Stats: getN1DetectionStats()
          }
        });
      } else if (duration > SLOW_THRESHOLD) {
        performanceCategory = 'slow';
        alertLevel = 'warning';
        setTag('performance.category', 'slow');
      } else {
        setTag('performance.category', 'normal');
      }
    
      // Log performance data with enhanced context
      await logApiPerformance(routePath, duration, performanceCategory, {
        method: request.method,
        query: Object.fromEntries(new URL(request.url).searchParams.entries()),
        alertLevel,
        n1Stats: getN1DetectionStats()
      });

      // Add enhanced performance headers to response
      response.headers.set('X-Response-Time', `${duration}ms`);
      response.headers.set('X-Performance-Category', performanceCategory);
      response.headers.set('X-Performance-Threshold', SLOW_THRESHOLD.toString());

      // End transaction successfully
      transaction?.finish();

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error with performance context
      setTag('performance.error', 'true');
      setTag('performance.duration', duration.toString());

      // End transaction with error
      transaction?.finish();

      throw error;
    }
  };
}