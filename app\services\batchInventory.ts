import Batch, { IBatch } from '@/app/models/batch.model';
import { IInventoryTransaction, InventoryTransaction } from '@/app/models/inventorytransaction.model';
import Part, { IPart } from '@/app/models/part.model';
import { Types } from 'mongoose';

/**
 * Get inventory summary for a specific batch
 * @param batchId - The ID of the batch
 * @returns Inventory summary for the batch
 */
export async function getBatchInventorySummary(batchId: string) {
  try {
    // Validate that the ID is a valid ObjectId
    if (!Types.ObjectId.isValid(batchId)) {
      throw new Error('Invalid batch ID format');
    }

    // Get the batch details
    const batch = await (Batch.findById as any)(batchId)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced status')
      .lean() as IBatch;

    if (!batch) {
      throw new Error(`Batch with ID ${batchId} not found`);
    }

    // Get all transactions related to this batch by its batchCode
    const transactions = await (InventoryTransaction.find as any)({ referenceNumber: batch.batchCode })
      .select('partId transactionType quantity previousStock newStock transactionDate')
      .sort({ transactionDate: 1 })
      .lean() as IInventoryTransaction[];

    // Get part details if this batch is for a part
    let part = null;
    if (batch.partId) {
      part = await Part.findById(batch.partId)
        .select('_id name description inventory')
        .lean<IPart>();
    }

    // Calculate inventory metrics
    const inventoryMetrics = {
      totalStockIn: 0,
      totalStockOut: 0,
      netChange: 0,
      lastTransactionDate: null as Date | null,
      currentStock: 0, // V4 Schema: Use inventories collection instead of embedded inventory
      transactionCount: transactions.length
    };

    // Process transactions to calculate metrics
    transactions.forEach((transaction: IInventoryTransaction) => {
      // Map new transaction types to batch operations
      if (transaction.transactionType === 'process_move') {
        inventoryMetrics.totalStockIn += transaction.quantity;
        inventoryMetrics.netChange += transaction.quantity;
      } else if (transaction.transactionType === 'sales_shipment') {
        inventoryMetrics.totalStockOut += Math.abs(transaction.quantity);
        // For sales_shipment, quantity represents outbound movement
        inventoryMetrics.netChange -= transaction.quantity;
      } else if (transaction.transactionType === 'adjustment') {
        inventoryMetrics.netChange += transaction.quantity;
      }
      // Other transaction types relevant to batches might need to be handled here too.

      // Update last transaction date
      if (!inventoryMetrics.lastTransactionDate || 
          (transaction.transactionDate && transaction.transactionDate > inventoryMetrics.lastTransactionDate)) {
        inventoryMetrics.lastTransactionDate = transaction.transactionDate;
      }
    });

    return {
      batch,
      part,
      inventoryMetrics,
      transactions: transactions.slice(0, 5) // Return only the 5 most recent transactions
    };
  } catch (error: any) {
    console.error(`Error getting batch inventory summary: ${error.message}`);
    throw error;
  }
}

/**
 * Get inventory transactions for a specific batch with pagination
 * @param batchId - The ID of the batch
 * @param options - Options for pagination and sorting
 * @returns Transactions for the batch with pagination
 */
export async function getBatchTransactions(batchId: string, options: any = {}) {
  try {
    const {
      page = 1,
      limit = 20,
      sort = { transactionDate: -1 } // Default sort by date descending
    } = options;

    // Validate that the ID is a valid ObjectId
    if (!Types.ObjectId.isValid(batchId)) {
      throw new Error('Invalid batch ID format');
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Find the batch to get its batchCode
    const batch = await (Batch.findById as any)(batchId).select('batchCode').lean() as IBatch;
    if (!batch) {
      throw new Error(`Batch with ID ${batchId} not found for fetching transactions`);
    }

    // Find transactions for this batch using its batchCode
    const transactions = await (InventoryTransaction.find as any)({ referenceNumber: batch.batchCode })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('partId transactionType quantity previousStock newStock transactionDate referenceNumber notes createdAt')
      .populate({
        path: 'partId',
        select: 'partNumber name'
      })
      .lean() as IInventoryTransaction[];

    // Get total count for pagination (batch already fetched above)
    const totalCount = await InventoryTransaction.countDocuments({ referenceNumber: batch.batchCode });

    return {
      transactions,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    console.error(`Error getting batch transactions: ${error.message}`);
    throw error;
  }
}
