import { NextRequest, NextResponse } from 'next/server';
import { getActiveSuppliers, handleMongoDBError } from '@/app/services/supplier.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching active suppliers with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with active suppliers data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/suppliers/active - Fetching active suppliers');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object (other than is_active which is handled by the service) ---
    const filter: any = {};
    const nameFilter = url.searchParams.get('name');
    if (nameFilter) {
      filter.name = new RegExp(nameFilter, 'i'); // Case-insensitive regex search
    }

    // Filter by specialty if provided
    const specialty = url.searchParams.get('specialty');
    if (specialty) {
      filter.specialty = { $in: [specialty] };
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling getActiveSuppliers service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await getActiveSuppliers(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service getActiveSuppliers completed in ${duration}ms with ${result.suppliers.length} results`);

    // --- Return Response ---
    return NextResponse.json({
      data: result.suppliers,
      pagination: result.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/suppliers/active (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
} 