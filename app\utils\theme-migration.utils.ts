"use client";

import { StatusType } from '@/app/hooks/useEnhancedTheme';

/**
 * Mapping of hardcoded color classes to semantic CSS variables
 * Used for automated migration of legacy color implementations
 */
export const COLOR_MIGRATIONS: Record<string, string> = {
  // Gray scale mappings - light mode
  'text-gray-50': 'text-background',
  'text-gray-100': 'text-muted',
  'text-gray-200': 'text-muted',
  'text-gray-300': 'text-muted-foreground',
  'text-gray-400': 'text-muted-foreground',
  'text-gray-500': 'text-muted-foreground',
  'text-gray-600': 'text-foreground',
  'text-gray-700': 'text-foreground',
  'text-gray-800': 'text-foreground',
  'text-gray-900': 'text-foreground',
  
  // Gray background mappings
  'bg-gray-50': 'bg-muted',
  'bg-gray-100': 'bg-muted',
  'bg-gray-200': 'bg-muted',
  'bg-gray-300': 'bg-accent',
  'bg-gray-400': 'bg-accent',
  'bg-gray-500': 'bg-accent',
  'bg-gray-600': 'bg-card',
  'bg-gray-700': 'bg-card',
  'bg-gray-800': 'bg-card',
  'bg-gray-900': 'bg-background',
  
  // Gray border mappings
  'border-gray-200': 'border-border',
  'border-gray-300': 'border-border',
  'border-gray-400': 'border-input',
  'border-gray-500': 'border-input',
  
  // Status color mappings - Red (Error/Destructive)
  'text-red-50': 'text-destructive-foreground',
  'text-red-100': 'text-destructive-foreground',
  'text-red-500': 'text-destructive',
  'text-red-600': 'text-destructive',
  'text-red-700': 'text-destructive',
  'text-red-800': 'text-destructive',
  'text-red-900': 'text-destructive',
  'bg-red-50': 'bg-destructive/5',
  'bg-red-100': 'bg-destructive/10',
  'bg-red-200': 'bg-destructive/20',
  'bg-red-500': 'bg-destructive',
  'bg-red-600': 'bg-destructive',
  'border-red-200': 'border-destructive/20',
  'border-red-300': 'border-destructive/30',
  'border-red-500': 'border-destructive',
  
  // Status color mappings - Blue (Info)
  'text-blue-50': 'text-info-foreground',
  'text-blue-100': 'text-info-foreground',
  'text-blue-500': 'text-info',
  'text-blue-700': 'text-info',
  'text-blue-800': 'text-info',
  'text-blue-900': 'text-info',
  'bg-blue-50': 'bg-info/5',
  'bg-blue-100': 'bg-info/10',
  'bg-blue-200': 'bg-info/20',
  'bg-blue-500': 'bg-info',
  'border-blue-200': 'border-info/20',
  'border-blue-300': 'border-info/30',
  'border-blue-500': 'border-info',
  
  // Status color mappings - Yellow (Warning)
  'text-yellow-50': 'text-warning-foreground',
  'text-yellow-100': 'text-warning-foreground',
  'text-yellow-500': 'text-warning',
  'text-yellow-600': 'text-warning',
  'text-yellow-700': 'text-warning',
  'text-yellow-800': 'text-warning',
  'text-yellow-900': 'text-warning',
  'bg-yellow-50': 'bg-warning/5',
  'bg-yellow-100': 'bg-warning/10',
  'bg-yellow-200': 'bg-warning/20',
  'bg-yellow-500': 'bg-warning',
  'bg-yellow-600': 'bg-warning',
  'border-yellow-200': 'border-warning/20',
  'border-yellow-300': 'border-warning/30',
  'border-yellow-500': 'border-warning',
  
  // Status color mappings - Green (Success)
  'text-green-50': 'text-success-foreground',
  'text-green-100': 'text-success-foreground',
  'text-green-500': 'text-success',
  'text-green-600': 'text-success',
  'text-green-700': 'text-success',
  'text-green-800': 'text-success',
  'text-green-900': 'text-success',
  'bg-green-50': 'bg-success/5',
  'bg-green-100': 'bg-success/10',
  'bg-green-200': 'bg-success/20',
  'bg-green-500': 'bg-success',
  'bg-green-600': 'bg-success',
  'border-green-200': 'border-success/20',
  'border-green-300': 'border-success/30',
  'border-green-500': 'border-success',
  
  // Primary color mappings
  'text-blue-600': 'text-primary',
  'bg-blue-600': 'bg-primary',
  'border-blue-600': 'border-primary',
  
  // White/Black mappings
  'text-white': 'text-primary-foreground',
  'text-black': 'text-foreground',
  'bg-white': 'bg-background',
  'bg-black': 'bg-foreground',
  'border-white': 'border-background',
  'border-black': 'border-foreground',
};

/**
 * Dark mode specific color mappings
 * These override the default mappings when in dark mode
 */
export const DARK_MODE_MIGRATIONS: Record<string, string> = {
  'text-gray-400': 'text-muted-foreground',
  'text-gray-600': 'text-muted-foreground',
  'text-gray-300': 'text-foreground',
  'hover:text-gray-300': 'hover:text-foreground',
  'hover:text-gray-600': 'hover:text-foreground',
  'bg-dark-800': 'bg-card',
  'bg-dark-700': 'bg-muted',
  'border-dark-border-subtle': 'border-border',
  'border-dark-border': 'border-border',
};

/**
 * Migrate a single color class to its semantic equivalent
 * @param className The hardcoded color class to migrate
 * @param isDarkMode Whether to use dark mode specific mappings
 * @returns The semantic CSS variable class
 */
export const migrateColorClass = (className: string, isDarkMode = false): string => {
  // Check dark mode specific mappings first
  if (isDarkMode && DARK_MODE_MIGRATIONS[className]) {
    return DARK_MODE_MIGRATIONS[className];
  }
  
  // Fall back to general mappings
  return COLOR_MIGRATIONS[className] || className;
};

/**
 * Migrate multiple color classes in a className string
 * @param classNames Space-separated class names
 * @param isDarkMode Whether to use dark mode specific mappings
 * @returns Migrated class names
 */
export const migrateColorClasses = (classNames: string, isDarkMode = false): string => {
  return classNames
    .split(' ')
    .map(className => migrateColorClass(className.trim(), isDarkMode))
    .join(' ');
};

/**
 * Get standardized status color classes
 * @param status The status type
 * @returns Complete status color class string
 */
export const getStatusClasses = (status: StatusType): string => {
  const statusMap = {
    success: 'bg-success/10 text-success border-success/20',
    warning: 'bg-warning/10 text-warning border-warning/20',
    info: 'bg-info/10 text-info border-info/20',
    error: 'bg-destructive/10 text-destructive border-destructive/20',
    pending: 'bg-muted/10 text-muted-foreground border-muted/20',
    'in-progress': 'bg-info/10 text-info border-info/20',
  };
  
  return statusMap[status] || statusMap.info;
};

/**
 * Get individual status color components
 * @param status The status type
 * @returns Object with background, text, and border classes
 */
export const getStatusColorComponents = (status: StatusType) => {
  const componentMap = {
    success: {
      background: 'bg-success/10',
      text: 'text-success',
      border: 'border-success/20',
      solid: 'bg-success',
      solidText: 'text-success-foreground',
    },
    warning: {
      background: 'bg-warning/10',
      text: 'text-warning',
      border: 'border-warning/20',
      solid: 'bg-warning',
      solidText: 'text-warning-foreground',
    },
    info: {
      background: 'bg-info/10',
      text: 'text-info',
      border: 'border-info/20',
      solid: 'bg-info',
      solidText: 'text-info-foreground',
    },
    error: {
      background: 'bg-destructive/10',
      text: 'text-destructive',
      border: 'border-destructive/20',
      solid: 'bg-destructive',
      solidText: 'text-destructive-foreground',
    },
    pending: {
      background: 'bg-muted/10',
      text: 'text-muted-foreground',
      border: 'border-muted/20',
      solid: 'bg-muted',
      solidText: 'text-muted-foreground',
    },
    'in-progress': {
      background: 'bg-info/10',
      text: 'text-info',
      border: 'border-info/20',
      solid: 'bg-info',
      solidText: 'text-info-foreground',
    },
  };
  
  return componentMap[status] || componentMap.info;
};

/**
 * Convert manual theme mode checking to CSS variables
 * @param lightClass Class for light mode
 * @param darkClass Class for dark mode
 * @returns Semantic CSS variable class
 */
export const convertThemeModeClasses = (lightClass: string, darkClass: string): string => {
  // Common patterns and their semantic equivalents
  const patterns = [
    {
      light: 'bg-white',
      dark: 'bg-gray-800',
      semantic: 'bg-card',
    },
    {
      light: 'bg-gray-100',
      dark: 'bg-gray-800',
      semantic: 'bg-muted',
    },
    {
      light: 'text-gray-900',
      dark: 'text-white',
      semantic: 'text-foreground',
    },
    {
      light: 'text-gray-600',
      dark: 'text-gray-300',
      semantic: 'text-muted-foreground',
    },
    {
      light: 'border-gray-200',
      dark: 'border-gray-700',
      semantic: 'border-border',
    },
  ];
  
  // Find matching pattern
  const pattern = patterns.find(p => p.light === lightClass && p.dark === darkClass);
  if (pattern) {
    return pattern.semantic;
  }
  
  // Fall back to migrating individual classes
  const migratedLight = migrateColorClass(lightClass, false);
  const migratedDark = migrateColorClass(darkClass, true);
  
  // If both migrate to the same semantic class, return that
  if (migratedLight === migratedDark) {
    return migratedLight;
  }
  
  // Otherwise, return the light mode migration as default
  return migratedLight;
};

/**
 * Utility to check if a class name is a hardcoded color
 * @param className The class name to check
 * @returns True if it's a hardcoded color class
 */
export const isHardcodedColorClass = (className: string): boolean => {
  const colorPrefixes = [
    'text-gray-', 'bg-gray-', 'border-gray-',
    'text-red-', 'bg-red-', 'border-red-',
    'text-blue-', 'bg-blue-', 'border-blue-',
    'text-green-', 'bg-green-', 'border-green-',
    'text-yellow-', 'bg-yellow-', 'border-yellow-',
    'text-purple-', 'bg-purple-', 'border-purple-',
    'text-pink-', 'bg-pink-', 'border-pink-',
    'text-indigo-', 'bg-indigo-', 'border-indigo-',
    'bg-dark-', 'text-dark-', 'border-dark-',
  ];
  
  return colorPrefixes.some(prefix => className.startsWith(prefix));
};
