import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility for merging Tailwind CSS classes
 * @param inputs - The class values to merge
 * @returns Merged class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Enhanced debounce utility function
 *
 * This function creates a debounced version of the provided function that delays
 * its execution until after the specified wait time has elapsed since the last time
 * the debounced function was called.
 *
 * Features:
 * - Configurable wait time
 * - Optional immediate execution on first call
 * - Cancellation of pending executions
 * - Flushing (immediate execution of pending calls)
 * - Maximum wait time option
 * - Trailing and leading edge execution options
 *
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to delay (default: 300)
 * @param options - Configuration options
 * @returns A debounced function with cancel and flush methods
 *
 * @example
 * // Basic usage
 * const debouncedSearch = debounce(searchFunction, 300);
 *
 * // With immediate execution
 * const debouncedSave = debounce(saveFunction, 500, { immediate: true });
 *
 * // With maximum wait time
 * const debouncedUpdate = debounce(updateFunction, 300, { maxWait: 1000 });
 *
 * // Cancel a pending execution
 * debouncedSearch.cancel();
 *
 * // Force immediate execution of a pending call
 * debouncedSave.flush();
 */
export interface DebounceOptions {
  /** Whether to execute the function on the leading edge of the timeout */
  immediate?: boolean;
  /** Maximum time the function is allowed to be delayed before it's executed */
  maxWait?: number;
  /** Whether to execute the function on the trailing edge of the timeout */
  trailing?: boolean;
}

export interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): ReturnType<T> | undefined;
  /** Cancel any pending executions */
  cancel: () => void;
  /** Immediately execute any pending calls */
  flush: () => ReturnType<T> | undefined;
  /** Check if there is a pending execution */
  pending: () => boolean;
}

/**
 * Throttle utility function
 *
 * This function creates a throttled version of the provided function that only
 * executes at most once per every `wait` milliseconds. The throttled function
 * comes with a cancel method to cancel delayed invocations.
 *
 * @param func - The function to throttle
 * @param wait - The number of milliseconds to throttle (default: 300)
 * @param options - Configuration options
 * @returns A throttled function with cancel method
 *
 * @example
 * // Basic usage
 * const throttledScroll = throttle(handleScroll, 100);
 *
 * // With leading: false option (only trailing edge)
 * const throttledResize = throttle(handleResize, 200, { leading: false });
 *
 * // With trailing: false option (only leading edge)
 * const throttledClick = throttle(handleClick, 300, { trailing: false });
 */
export interface ThrottleOptions {
  /** Whether to execute on the leading edge of the timeout (default: true) */
  leading?: boolean;
  /** Whether to execute on the trailing edge of the timeout (default: true) */
  trailing?: boolean;
}

export interface ThrottledFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): ReturnType<T> | undefined;
  /** Cancel any pending executions */
  cancel: () => void;
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait = 300,
  options: ThrottleOptions = {}
): ThrottledFunction<T> {
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;
  let result: ReturnType<T> | undefined;
  let timerId: NodeJS.Timeout | null = null;
  let lastCallTime = 0;

  // Set default options
  const { leading = true, trailing = true } = options;

  // Function to invoke the original function
  const invokeFunc = () => {
    const args = lastArgs;
    const thisArg = lastThis;

    // Reset the last args and this
    lastArgs = null;
    lastThis = null;

    // Execute the function if we have arguments
    if (args) {
      result = func.apply(thisArg, args);
    }

    return result;
  };

  // Function to execute after the wait time (trailing edge)
  const trailingEdge = () => {
    timerId = null;

    // Only execute if we have args and trailing is true
    if (trailing && lastArgs) {
      return invokeFunc();
    }

    return result;
  };

  // The throttled function
  function throttled(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const time = Date.now();
    const isInvoking = !lastCallTime || (time - lastCallTime >= wait);

    // Save the args and this context
    lastArgs = args;
    lastThis = this;

    // If we should invoke, do it
    if (isInvoking) {
      lastCallTime = time;

      // If leading is false, don't execute on the first call
      if (!leading && timerId === null) {
        timerId = setTimeout(trailingEdge, wait);
        return result;
      }

      // Execute the function
      const invokeResult = invokeFunc();

      // Set up the trailing edge timer if needed
      if (trailing) {
        timerId = setTimeout(trailingEdge, wait);
      }

      return invokeResult;
    }

    // If we're not invoking and there's no timer, set one up for the trailing edge
    if (trailing && timerId === null) {
      timerId = setTimeout(trailingEdge, wait - (time - lastCallTime));
    }

    return result;
  }

  // Add cancel method
  throttled.cancel = function() {
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }

    lastCallTime = 0;
    lastArgs = null;
    lastThis = null;
  };

  return throttled;
}

/**
 * Creates a debounced version of a function that delays its execution.
 *
 * The debounced function will only be called after a certain period of inactivity.
 * It supports immediate invocation, maximum wait time, and cancellation.
 *
 * @template T - The type of the function to debounce.
 * @param {T} func - The function to debounce.
 * @param {number} [wait=300] - The debounce delay in milliseconds.
 * @param {DebounceOptions} [options={}] - Options to configure debounce behavior.
 * @returns {DebouncedFunction<T>} The debounced function, equipped with `cancel`, `flush`, and `pending` methods.
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait = 300,
  options: DebounceOptions = {}
): DebouncedFunction<T> {
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;
  let result: ReturnType<T> | undefined;
  let timerId: NodeJS.Timeout | null = null;
  let lastCallTime: number | null = null;
  let lastInvokeTime = 0;

  // Set default options
  const { immediate = false, maxWait = 0, trailing = true } = options;

  // Calculate the next time the function should be executed
  const calculateNextInvokeTime = () => {
    if (timerId === null) {
      return 0;
    }

    if (maxWait && lastCallTime) {
      // Calculate time until maxWait is reached
      return Math.min(lastCallTime + wait, lastInvokeTime + maxWait);
    }

    // Regular debounce behavior
    return lastCallTime ? lastCallTime + wait : 0;
  };

  // Function to invoke the original function
  const invokeFunc = (time: number) => {
    const args = lastArgs;
    const thisArg = lastThis;

    // Reset the last args and this
    lastArgs = null;
    lastThis = null;
    lastInvokeTime = time;

    // Execute the function if we have arguments
    if (args) {
      result = func.apply(thisArg, args);
    }

    return result;
  };

  // Function to start the timer
  const startTimer = (pendingFunc: () => void, delay: number) => {
    timerId = setTimeout(pendingFunc, delay);
  };

  // Function to determine if we should invoke immediately
  const shouldInvoke = (time: number) => {
    // If this is the first call, we should invoke if immediate is true
    if (lastCallTime === null) {
      return immediate;
    }

    // If maxWait is set and we've exceeded it, we should invoke
    if (maxWait && time - lastInvokeTime >= maxWait) {
      return true;
    }

    // Regular debounce behavior - invoke after wait time has passed
    return time - lastCallTime >= wait;
  };

  // Function to execute after the wait time
  const timerExpired = () => {
    const time = Date.now();

    // Check if we should invoke the function
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }

    // Otherwise, restart the timer for the remaining time
    const nextInvokeTime = calculateNextInvokeTime();
    const remainingWait = nextInvokeTime - time;

    startTimer(timerExpired, remainingWait);
    return undefined;
  };

  // Function to handle the trailing edge of the timeout
  const trailingEdge = (time: number) => {
    timerId = null;

    // Only execute if we have args and trailing is true
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }

    // Reset the last args and this
    lastArgs = null;
    lastThis = null;

    return result;
  };

  // Function to handle the leading edge of the timeout
  const leadingEdge = (time: number) => {
    // Set last invoke time
    lastInvokeTime = time;

    // Start a timer for the trailing edge
    startTimer(timerExpired, wait);

    // Invoke the function immediately if immediate is true
    if (immediate) {
      return invokeFunc(time);
    }

    return result;
  };

  // The debounced function
  function debounced(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    // Save the args and this context
    lastArgs = args;
    lastThis = this;
    lastCallTime = time;

    // If we should invoke, do it
    if (isInvoking) {
      if (timerId === null) {
        return leadingEdge(time);
      }

      if (maxWait) {
        // Handle maxWait case
        startTimer(timerExpired, wait);
        return invokeFunc(time);
      }
    }

    // Start the timer if it's not already running
    if (timerId === null) {
      startTimer(timerExpired, wait);
    }

    return result;
  }

  // Add cancel method
  debounced.cancel = function() {
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }

    lastInvokeTime = 0;
    lastArgs = null;
    lastThis = null;
    lastCallTime = null;
  };

  // Add flush method
  debounced.flush = function() {
    if (timerId !== null) {
      const time = Date.now();
      return trailingEdge(time);
    }
    return result;
  };

  // Add pending method
  debounced.pending = function() {
    return timerId !== null;
  };

  return debounced;
}

/**
 * Creates a debounced search function with a minimum query length threshold
 *
 * This is a specialized utility for search inputs that combines debouncing with
 * a minimum query length check. The search function will only be called if the
 * query meets the minimum length requirement.
 *
 * @param searchFn - The search function to debounce
 * @param options - Configuration options
 * @returns A debounced search function with cancel and flush methods
 *
 * @example
 * // Basic usage
 * const debouncedSearch = createDebouncedSearch(
 *   async (query) => {
 *     const results = await api.search(query);
 *     setSearchResults(results);
 *   }
 * );
 *
 * // With custom options
 * const debouncedSearch = createDebouncedSearch(
 *   async (query) => {
 *     const results = await api.search(query);
 *     setSearchResults(results);
 *   },
 *   {
 *     wait: 500,
 *     minLength: 3,
 *     onEmpty: () => setSearchResults([]),
 *     onTooShort: (query) => setMessage(`Type at least 3 characters (${query.length}/3)`)
 *   }
 * );
 */
export interface DebouncedSearchOptions<T extends (...args: any[]) => any> {
  /** Debounce wait time in milliseconds */
  wait?: number;
  /** Minimum query length required to trigger the search */
  minLength?: number;
  /** Function to call when the query is empty */
  onEmpty?: () => void;
  /** Function to call when the query is too short */
  onTooShort?: (query: string) => void;
  /** Debounce options */
  debounceOptions?: DebounceOptions;
  /** Additional arguments to pass to the search function */
  additionalArgs?: Parameters<T> extends [string, ...infer Rest] ? Rest : never;
}

/**
 * Basic logger utility.
 * Logs messages to the console with a timestamp and log level.
 *
 * @param level - The log level (e.g., 'INFO', 'WARN', 'ERROR').
 * @param message - The message to log.
 * @param data - Optional additional data to log.
 */
export function logger(level: 'INFO' | 'WARN' | 'ERROR', message: string, data?: any) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}`;

  if (data) {
    console.log(logEntry, data);
  } else {
    console.log(logEntry);
  }

  // In a real application, you might want to send errors to a logging service
  if (level === 'ERROR') {
    // Example: Sentry.captureMessage(message, { extra: data });
  }
}

export function createDebouncedSearch<T extends (query: string, ...args: any[]) => any>(
  searchFn: T,
  options: DebouncedSearchOptions<T> = {}
): DebouncedFunction<(query: string) => ReturnType<T> | undefined> {
  const {
    wait = 300,
    minLength = 2,
    onEmpty,
    onTooShort,
    debounceOptions = {},
    additionalArgs = [] as any,
  } = options;

  // Create the debounced function
  const debouncedFn = debounce(
    (query: string) => {
      // Check if the query is empty
      if (!query || query.trim() === '') {
        if (onEmpty) {
          onEmpty();
        }
        return;
      }

      // Check if the query meets the minimum length requirement
      if (query.length < minLength) {
        if (onTooShort) {
          onTooShort(query);
        }
        return;
      }

      // Call the search function with the query and additional args
      return searchFn(query, ...additionalArgs);
    },
    wait,
    debounceOptions
  );

  return debouncedFn;
}