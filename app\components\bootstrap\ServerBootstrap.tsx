import { bootstrapApplication } from '@/app/config/bootstrap';

/**
 * Server-side component that bootstraps the application
 * This component is responsible for initializing server-side services
 * like database connections. It runs on the server during SSR.
 */
export default async function ServerBootstrap() {
  try {
    // This runs on the server and initializes server-side services
    await bootstrapApplication();
  } catch (error) {
    console.error('[ServerBootstrap] Error in server bootstrap:', 
      error instanceof Error ? error.message : String(error));
  }
  
  // Return null as this component doesn't render anything visible
  return null;
} 