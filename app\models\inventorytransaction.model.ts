import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for movement location (from/to) - V4 Schema
export interface IMovementLocation {
  locationId: Types.ObjectId; // Reference to locations._id (V4 Schema - REQUIRED)
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected' | 'scrap';
  // REMOVED: warehouseId field - use locationId instead (V4 Schema)
}

// Define interface for event-sourced InventoryTransaction document
export interface IInventoryTransaction extends Document {
  _id: Types.ObjectId;
  transactionId: string; // Human-readable transaction ID (e.g., "MOV-20250722-001")
  partId: Types.ObjectId; // Reference to the part being moved
  itemType?: 'Part' | 'Assembly' | 'Product'; // Made optional for backward compatibility

  // EVENT-SOURCED MOVEMENT STRUCTURE
  from: IMovementLocation | null; // null for external receipts (purchases)
  to: IMovementLocation | null;   // null for external shipments (sales, scrap)
  quantity: number; // Always positive - represents amount moved

  // TRANSACTION METADATA
  transactionType: 'purchase_receipt' | 'internal_transfer' | 'sales_shipment' | 'scrap_disposal' | 'process_move' | 'adjustment';
  transactionDate: Date;
  referenceNumber?: string | null;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | 'ProcessOrder' | null;
  userId: Types.ObjectId;
  notes?: string | null;

  // REMOVED: Legacy fields - no longer used in V4 event-sourced model
  // DEPRECATED: warehouseId?: Types.ObjectId; // Use from/to.locationId instead
  // DEPRECATED: previousStock?: number; // Event-sourced model doesn't track running totals
  // DEPRECATED: newStock?: number; // Event-sourced model doesn't track running totals

  createdAt: Date;
  updatedAt: Date;
}

// Schema for movement location (from/to) - V4 Schema
const MovementLocationSchema = new Schema<IMovementLocation>({
  locationId: {
    type: Schema.Types.ObjectId,
    ref: 'Location',
    required: true // V4 Schema - locationId is required
  },
  stockType: {
    type: String,
    required: true,
    enum: {
      values: ['raw', 'hardening', 'grinding', 'finished', 'rejected', 'scrap'],
      message: 'Stock type must be one of: raw, hardening, grinding, finished, rejected, scrap'
    }
  }
  // REMOVED: warehouseId field - use locationId instead (V4 Schema)
}, { _id: false });

// Define schema for event-sourced InventoryTransaction model
const InventoryTransactionSchema: Schema = new Schema(
  {
    transactionId: {
      type: String,
      required: [true, 'Transaction ID is required'],
      unique: true,
      index: true,
      trim: true
    },
    partId: {
      type: Schema.Types.ObjectId,
      ref: 'Part',
      required: [true, 'Part ID is required'],
      index: true
    },
    itemType: {
      type: String,
      required: false,
      enum: {
        values: ['Part', 'Assembly', 'Product'],
        message: 'Item type must be one of: Part, Assembly, Product'
      },
      index: true,
      default: 'Part'
    },

    // EVENT-SOURCED MOVEMENT STRUCTURE
    from: {
      type: MovementLocationSchema,
      required: false, // null for external receipts
      default: null
    },
    to: {
      type: MovementLocationSchema,
      required: false, // null for external shipments
      default: null
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [0, 'Quantity must be positive'],
      validate: {
        validator: Number.isInteger,
        message: 'Quantity must be a whole number'
      }
    },

    transactionType: {
      type: String,
      required: [true, 'Transaction type is required'],
      enum: {
        values: ['purchase_receipt', 'internal_transfer', 'sales_shipment', 'scrap_disposal', 'process_move', 'adjustment'],
        message: 'Transaction type must be one of the predefined values'
      },
      index: true
    },

    transactionDate: {
      type: Date,
      required: [true, 'Transaction date is required'],
      default: Date.now,
      index: true
    },
    referenceNumber: {
      type: String,
      default: null,
      index: true
    },
    referenceType: {
      type: String,
      enum: {
        values: ['PurchaseOrder', 'WorkOrder', 'SalesOrder', 'StockAdjustment', 'ProcessOrder', null],
        message: 'Reference type must be a valid model name or null'
      },
      default: null
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    notes: {
      type: String,
      default: null,
      trim: true
    },

    // REMOVED: Legacy fields - no longer used in V4 event-sourced model
    // DEPRECATED: warehouseId, previousStock, newStock fields removed
  },
  { timestamps: true, collection: 'transactions' }
);

// Add indexes for efficient queries on event-sourced structure (V4 Schema)
InventoryTransactionSchema.index({ partId: 1, transactionDate: -1 });
InventoryTransactionSchema.index({ 'from.locationId': 1, transactionDate: -1 });
InventoryTransactionSchema.index({ 'to.locationId': 1, transactionDate: -1 });
InventoryTransactionSchema.index({ transactionType: 1, transactionDate: -1 });
InventoryTransactionSchema.index({ 'from.stockType': 1, 'to.stockType': 1 });

// Add validation for event-sourced structure
InventoryTransactionSchema.pre('validate', function(this: IInventoryTransaction & Document, next) {
  // Ensure at least one of from or to is specified (not both null)
  if (!this.from && !this.to) {
    this.invalidate('from', 'Either from or to location must be specified');
    this.invalidate('to', 'Either from or to location must be specified');
  }

  // Validate transaction type matches from/to structure
  if (this.transactionType === 'purchase_receipt' && this.from !== null) {
    this.invalidate('from', 'Purchase receipts should have from=null (external source)');
  }
  if (this.transactionType === 'sales_shipment' && this.to !== null) {
    this.invalidate('to', 'Sales shipments should have to=null (external destination)');
  }
  if (this.transactionType === 'internal_transfer' && (!this.from || !this.to)) {
    this.invalidate('from', 'Internal transfers require both from and to locations');
    this.invalidate('to', 'Internal transfers require both from and to locations');
  }

  next();
});

// Create and export InventoryTransaction model with proper TypeScript typing
const InventoryTransaction = mongoose.models?.InventoryTransaction as mongoose.Model<IInventoryTransaction> || mongoose.model<IInventoryTransaction>('InventoryTransaction', InventoryTransactionSchema);

// Export as both named export and default export for compatibility
export { InventoryTransaction };
export default InventoryTransaction;