"use client";

import { Textarea } from '@/app/components/forms';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { <PERSON><PERSON>, DialogContent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from '@/app/components/navigation/dialog';
import { PartSearch, PartSearchResult } from '@/app/components/search/PartSearch';
import { AlertTriangle, ArrowDownLeft, ArrowUpRight, Factory, Package, RefreshCw, Truck } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

interface Part {
  _id: string;
  partNumber: string;
  name: string;
  description?: string;
  inventory?: {
    currentStock?: number;
    warehouseId?: string;
  };
}

interface Warehouse {
  _id: string;
  warehouseCode: string;
  name: string;
}

interface Inventory {
  partId: string;
  warehouseId: string;
  quantity: number;
}

interface TransactionFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

/**
 * Form data interface aligned with the canonical schema
 */
interface FormData {
  partId: string;
  warehouseId: string;
  transactionType: string; // Canonical field name
  quantity: number; // Canonical field name
  referenceType: string; // Canonical field name
  referenceNumber: string; // Canonical field name
  notes: string;
  userId: string; // Will be auto-filled with current user or default value for demo

  // Legacy field names for backward compatibility
  type?: string; // Maps to transactionType
  quantityChanged?: number; // Maps to quantity
  referenceModel?: string; // Maps to referenceType
  referenceId?: string; // Maps to referenceNumber
}

export function TransactionForm({ isOpen, onClose, onSuccess }: TransactionFormProps) {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [inventoryItems, setInventoryItems] = useState<Inventory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPart, setSelectedPart] = useState<PartSearchResult | null>(null);
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);
  const [currentStock, setCurrentStock] = useState<number | null>(null);

  const { register, handleSubmit, reset, setValue, watch, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      transactionType: 'adjustment_cycle_count', // Canonical field name
      type: 'stock_adjustment', // Legacy field name
      quantity: 1, // Canonical field name
      quantityChanged: 1, // Legacy field name
      referenceType: '', // Canonical field name
      referenceModel: '', // Legacy field name
      referenceNumber: '', // Canonical field name
      referenceId: '', // Legacy field name
      notes: '',
      userId: '000000000000000000000000' // Default user ID (for demo purposes)
    }
  });

  // Watch both canonical and legacy field names for compatibility
  const transactionType = watch('transactionType') || watch('type');
  const selectedPartId = watch('partId');
  const selectedWarehouseId = watch('warehouseId');

  useEffect(() => {
    if (isOpen) {
      fetchWarehouses();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedPartId && selectedWarehouseId) {
      fetchInventory(selectedPartId, selectedWarehouseId);
    }
  }, [selectedPartId, selectedWarehouseId]);



  const fetchWarehouses = async () => {
    try {
      const { getApiUrl } = await import('@/app/utils/env');
      const response = await fetch(getApiUrl('/api/warehouses'));
      const result = await response.json();

      if (result.data) {
        setWarehouses(result.data);
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error);
      toast.error('Failed to load warehouses');
    }
  };

  const fetchInventory = async (partId: string, warehouseId: string) => {
    try {
      const { getApiUrl } = await import('@/app/utils/env');
      const response = await fetch(getApiUrl(`/api/inventory?partId=${partId}&warehouseId=${warehouseId}`));
      const result = await response.json();

      if (result.data && result.data.length > 0) {
        setCurrentStock(result.data[0].quantity);
      } else {
        setCurrentStock(0);
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setCurrentStock(0);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);

      // Ensure quantity is correct format based on transaction type
      let quantity = Math.abs(Number(data.quantity));
      if (['stock_out_production', 'sales_shipment', 'transfer_out'].includes(data.transactionType)) {
        quantity = -quantity;
      }

      const response = await fetch('/api/inventory-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Use canonical field names
          partId: data.partId,
          warehouseId: data.warehouseId,
          transactionType: data.transactionType,
          quantity,
          transactionDate: new Date().toISOString(),
          referenceType: data.referenceType || null,
          referenceNumber: data.referenceNumber || null,
          notes: data.notes || '',
          userId: data.userId,

          // Include legacy field names for backward compatibility
          type: data.type || data.transactionType,
          quantityChanged: quantity,
          referenceModel: data.referenceModel || data.referenceType || null,
          referenceId: data.referenceId || data.referenceNumber || null
        }),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create transaction');
      }

      toast.success('Transaction created successfully');
      reset();
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating transaction:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create transaction');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePartSelect = (part: PartSearchResult | null) => {
    setSelectedPart(part);
    setValue('partId', part?._id || '');

    // V4 Schema: Update current stock using inventory.stockLevels.finished
    if (part?.inventory?.stockLevels?.finished !== undefined) {
      setCurrentStock(part.inventory.stockLevels.finished);
    } else if (part?.inventory?.currentStock !== undefined) {
      // Fallback for legacy data
      setCurrentStock(part.inventory.currentStock);
    } else {
      setCurrentStock(0);
    }

    // Auto-select warehouse based on part's inventory warehouse
    if (part?.inventory?.warehouseId) {
      const warehouse = warehouses.find(w => w._id === part.inventory!.warehouseId);
      if (warehouse) {
        setSelectedWarehouse(warehouse);
        setValue('warehouseId', warehouse._id);
        console.log(`[TransactionForm] Auto-selected warehouse for part ${part.name}:`, {
          warehouseId: warehouse._id,
          warehouseName: warehouse.name,
          warehouseCode: warehouse.warehouseCode
        });
      } else {
        console.warn(`[TransactionForm] Warehouse not found for part ${part.name}:`, {
          partWarehouseId: part.inventory.warehouseId,
          availableWarehouses: warehouses.map(w => ({ id: w._id, name: w.name }))
        });
      }
    } else {
      // Clear warehouse selection if part has no inventory warehouse
      setSelectedWarehouse(null);
      setValue('warehouseId', '');
      console.log(`[TransactionForm] No warehouse found in part inventory for ${part?.name || 'selected part'}`);
    }
  };

  const handleWarehouseChange = (warehouseId: string) => {
    const warehouse = warehouses.find(w => w._id === warehouseId);
    setSelectedWarehouse(warehouse || null);
    setValue('warehouseId', warehouseId);
  };

  const getTransactionTypeIcon = () => {
    switch (transactionType) {
      // Canonical transaction types
      case 'stock_in_purchase':
        return <ArrowDownLeft size={18} className="text-green-600" />;
      case 'stock_out_production':
        return <ArrowUpRight size={18} className="text-red-600" />;
      case 'adjustment_cycle_count':
        return <RefreshCw size={18} className="text-blue-600" />;
      case 'stock_in_production':
        return <Factory size={18} className="text-purple-600" />;
      case 'transfer_out':
        return <Truck size={18} className="text-amber-600" />;
      case 'transfer_in':
        return <Truck size={18} className="text-amber-600" />;
      case 'sales_shipment':
        return <ArrowUpRight size={18} className="text-red-600" />;

      // Legacy transaction types
      case 'purchase_receipt':
      case 'stock_in':
        return <ArrowDownLeft size={18} className="text-green-600" />;
      case 'stock_out':
      case 'production_consumption':
        return <ArrowUpRight size={18} className="text-red-600" />;
      case 'stock_adjustment':
        return <RefreshCw size={18} className="text-blue-600" />;
      case 'production_output':
        return <Factory size={18} className="text-purple-600" />;
      case 'internal_transfer_out':
      case 'internal_transfer_in':
        return <Truck size={18} className="text-amber-600" />;
      default:
        return <Package size={18} />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {getTransactionTypeIcon()}
            <span className="ml-2">New Inventory Transaction</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="type">Transaction Type</Label>
            <Select
              defaultValue="adjustment_cycle_count"
              onValueChange={(value) => {
                // Set both canonical and legacy field names for compatibility
                setValue('transactionType', value);

                // Map canonical values to legacy values for backward compatibility
                const legacyValueMap: Record<string, string> = {
                  'stock_in_purchase': 'purchase_receipt',
                  'stock_out_production': 'production_consumption',
                  'adjustment_cycle_count': 'stock_adjustment',
                  'stock_in_production': 'production_output',
                  'transfer_out': 'internal_transfer_out',
                  'transfer_in': 'internal_transfer_in',
                  'sales_shipment': 'sales_shipment'
                };
                setValue('type', legacyValueMap[value] || value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select transaction type" />
              </SelectTrigger>
              <SelectContent>
                {/* Use canonical transaction types */}
                <SelectItem value="stock_in_purchase">Purchase Receipt</SelectItem>
                <SelectItem value="stock_in_production">Production Output</SelectItem>
                <SelectItem value="stock_out_production">Production Consumption</SelectItem>
                <SelectItem value="sales_shipment">Sales Shipment</SelectItem>
                <SelectItem value="adjustment_cycle_count">Stock Adjustment</SelectItem>
                <SelectItem value="transfer_out">Internal Transfer Out</SelectItem>
                <SelectItem value="transfer_in">Internal Transfer In</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <PartSearch
            selectedPartId={selectedPartId}
            selectedPart={selectedPart}
            warehouseId={selectedWarehouseId}
            onPartSelect={handlePartSelect}
            placeholder="Search parts by name or number..."
            label="Part"
            error={errors.partId ? "Part is required" : ""}
            disabled={isLoading}
            showStockLevels={true}
            minQueryLength={3}
            debounceTime={300}
            maxResults={10}
          />

          <div className="space-y-2">
            <Label htmlFor="warehouseId">Warehouse</Label>
            <Select value={selectedWarehouse?._id || ''} onValueChange={handleWarehouseChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a warehouse" />
              </SelectTrigger>
              <SelectContent>
                {warehouses.map((warehouse) => (
                  <SelectItem key={warehouse._id} value={warehouse._id}>
                    {warehouse.name} ({warehouse.warehouseCode})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.warehouseId && <p className="text-sm text-red-500">Warehouse is required</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">
              Quantity {transactionType && ['stock_out_production', 'sales_shipment', 'transfer_out'].includes(transactionType) ? '(will be converted to negative)' : ''}
            </Label>
            <Input
              type="number"
              min="1"
              step="1"
              {...register('quantity', { required: true, min: 1 })}
              onChange={(e) => {
                // Set both canonical and legacy field names for compatibility
                const value = parseInt(e.target.value);
                setValue('quantity', value);
                setValue('quantityChanged', value);
              }}
            />
            {(errors.quantity || errors.quantityChanged) && <p className="text-sm text-red-500">Valid quantity is required</p>}
          </div>

          {currentStock !== null && selectedPart && selectedWarehouse && (
            <div className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded">
              Current stock of {selectedPart.name} in {selectedWarehouse.name}: <span className={currentStock > 0 ? "text-green-600 font-medium" : "text-red-600 font-medium"}>{currentStock}</span>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="referenceType">Reference Type (Optional)</Label>
            <Select defaultValue="none" onValueChange={(value) => {
              // Set both canonical and legacy field names for compatibility
              const referenceValue = value === "none" ? "" : value;
              setValue('referenceType', referenceValue);
              setValue('referenceModel', referenceValue);
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Select reference type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="PurchaseOrder">Purchase Order</SelectItem>
                <SelectItem value="WorkOrder">Work Order</SelectItem>
                <SelectItem value="SalesOrder">Sales Order</SelectItem>
                <SelectItem value="StockAdjustment">Stock Adjustment</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="referenceNumber">Reference ID (Optional)</Label>
            <Input {...register('referenceNumber')} onChange={(e) => {
              // Set both canonical and legacy field names for compatibility
              setValue('referenceNumber', e.target.value);
              setValue('referenceId', e.target.value);
            }} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea {...register('notes')} />
          </div>

          {currentStock !== null && selectedPart &&
           transactionType && ['stock_out_production', 'sales_shipment', 'transfer_out'].includes(transactionType) &&
           currentStock - Math.abs(Number(watch('quantity') || watch('quantityChanged'))) < 0 && (
            <div className="text-red-500 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded border border-red-200 dark:border-red-800">
              <AlertTriangle className="inline-block mr-2" size={16} />
              Warning: This transaction will result in negative stock ({currentStock} available, {Math.abs(Number(watch('quantity') || watch('quantityChanged')))} requested).
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || !selectedPart || !selectedWarehouse}>
              {isLoading ? 'Processing...' : 'Create Transaction'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}