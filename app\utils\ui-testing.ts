/**
 * UI Testing Utilities
 *
 * This file provides utilities for testing UI components using Puppeteer
 * to inspect enhanced components and check for console errors.
 */

// Mock puppeteer types for when the package is not installed
const puppeteer: any = null;
type Browser = any;
type Page = any;
import { UIEnhancementConfig } from '../models/ui-enhancement.model';

/**
 * Interface for console log entry
 */
interface ConsoleLogEntry {
  type: 'log' | 'error' | 'warning' | 'info';
  message: string;
  timestamp: Date;
  location?: string;
}

/**
 * Interface for component inspection result
 */
interface ComponentInspectionResult {
  componentId: string;
  exists: boolean;
  visible: boolean;
  cssProperties?: Record<string, string>;
  attributes?: Record<string, string>;
  animationActive?: boolean;
  screenshot?: Buffer;
}

/**
 * Class for testing UI components with Puppeteer
 */
export class UITester {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private consoleLogs: ConsoleLogEntry[] = [];
  private config: UIEnhancementConfig | null = null;
  
  /**
   * Initialize the UI tester
   * @param config UI enhancement configuration
   */
  constructor(config?: UIEnhancementConfig) {
    this.config = config || null;
  }
  
  /**
   * Launch the browser and navigate to the specified URL
   * @param url URL to navigate to
   */
  async launch(url: string): Promise<void> {
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1280, height: 800 }
    });
    
    this.page = await this.browser.newPage();
    
    // Capture console logs
    this.page.on('console', (message: any) => {
      this.consoleLogs.push({
        type: message.type() as 'log' | 'error' | 'warning' | 'info',
        message: message.text(),
        timestamp: new Date(),
        location: message.location()?.url
      });
    });
    
    await this.page.goto(url, { waitUntil: 'networkidle2' });
  }
  
  /**
   * Close the browser
   */
  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
    }
  }
  
  /**
   * Get all console logs
   */
  getConsoleLogs(): ConsoleLogEntry[] {
    return [...this.consoleLogs];
  }

  /**
   * Get console errors
   */
  getConsoleErrors(): ConsoleLogEntry[] {
    return this.consoleLogs.filter((log: ConsoleLogEntry) => log.type === 'error');
  }
  
  /**
   * Check if a component exists
   * @param selector CSS selector for the component
   */
  async componentExists(selector: string): Promise<boolean> {
    if (!this.page) return false;
    
    return await this.page.evaluate((sel: string) => {
      return document.querySelector(sel) !== null;
    }, selector);
  }
  
  /**
   * Inspect a component
   * @param componentId Component ID from the UI enhancement config
   * @param selector CSS selector for the component
   */
  async inspectComponent(componentId: string, selector: string): Promise<ComponentInspectionResult | null> {
    if (!this.page) return null;
    
    const exists = await this.componentExists(selector);
    
    if (!exists) {
      return {
        componentId,
        exists: false,
        visible: false
      };
    }
    
    const result = await this.page.evaluate((sel: string, id: string) => {
      const element = document.querySelector(sel);
      if (!element) return null;
      
      const rect = element.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(element);
      
      // Get CSS properties
      const cssProperties: Record<string, string> = {
        backgroundColor: computedStyle.backgroundColor,
        color: computedStyle.color,
        borderRadius: computedStyle.borderRadius,
        boxShadow: computedStyle.boxShadow,
        transform: computedStyle.transform,
        transition: computedStyle.transition,
        animation: computedStyle.animation
      };
      
      // Get attributes
      const attributes: Record<string, string> = {};
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        if (attr) {
          attributes[attr.name] = attr.value;
        }
      }
      
      // Check if animation is active
      const hasAnimation = computedStyle.animation !== 'none' || 
                          computedStyle.transition !== 'none' || 
                          computedStyle.transform !== 'none';
      
      return {
        componentId: id,
        exists: true,
        visible: rect.width > 0 && rect.height > 0 && computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden',
        cssProperties,
        attributes,
        animationActive: hasAnimation
      };
    }, selector, componentId);
    
    if (result && this.page) {
      // Take a screenshot of the component
      const elementHandle = await this.page.$(selector);
      if (elementHandle) {
        result.screenshot = await elementHandle.screenshot();
      }
    }
    
    return result;
  }
  
  /**
   * Test all components from the UI enhancement config
   */
  async testAllComponents(): Promise<Record<string, ComponentInspectionResult>> {
    if (!this.config || !this.page) {
      throw new Error('Config not provided or browser not launched');
    }
    
    const results: Record<string, ComponentInspectionResult> = {};
    
    // Test each component from the config
    for (const mapping of this.config.entityMappings) {
      // Test card component
      if (mapping.cardComponent) {
        const selector = `[data-component-id="${mapping.cardComponent.id}"]`;
        const result = await this.inspectComponent(mapping.cardComponent.id, selector);
        if (result) {
          results[mapping.cardComponent.id] = result;
        }
      }
      
      // Test row component
      if (mapping.rowComponent) {
        const selector = `[data-component-id="${mapping.rowComponent.id}"]`;
        const result = await this.inspectComponent(mapping.rowComponent.id, selector);
        if (result) {
          results[mapping.rowComponent.id] = result;
        }
      }
      
      // Test background component
      if (mapping.backgroundComponent) {
        const selector = `[data-component-id="${mapping.backgroundComponent.id}"]`;
        const result = await this.inspectComponent(mapping.backgroundComponent.id, selector);
        if (result) {
          results[mapping.backgroundComponent.id] = result;
        }
      }
      
      // Test button components
      if (mapping.buttonComponents) {
        for (const button of mapping.buttonComponents) {
          const selector = `[data-component-id="${button.id}"]`;
          const result = await this.inspectComponent(button.id, selector);
          if (result) {
            results[button.id] = result;
          }
        }
      }
      
      // Test text components
      if (mapping.textComponents) {
        for (const text of mapping.textComponents) {
          const selector = `[data-component-id="${text.id}"]`;
          const result = await this.inspectComponent(text.id, selector);
          if (result) {
            results[text.id] = result;
          }
        }
      }
    }
    
    return results;
  }
  
  /**
   * Generate a report of the UI testing results
   * @param results Component inspection results
   */
  generateReport(results: Record<string, ComponentInspectionResult>): string {
    const errors = this.getConsoleErrors();
    
    let report = '# UI Testing Report\n\n';
    
    // Add timestamp
    report += `Generated: ${new Date().toLocaleString()}\n\n`;
    
    // Add console errors section
    report += '## Console Errors\n\n';
    if (errors.length === 0) {
      report += 'No console errors detected.\n\n';
    } else {
      report += `Found ${errors.length} console errors:\n\n`;
      errors.forEach((error, index) => {
        report += `${index + 1}. **${error.message}**\n`;
        report += `   - Type: ${error.type}\n`;
        report += `   - Location: ${error.location || 'Unknown'}\n`;
        report += `   - Time: ${error.timestamp.toLocaleTimeString()}\n\n`;
      });
    }
    
    // Add component inspection results
    report += '## Component Inspection Results\n\n';
    
    const componentIds = Object.keys(results);
    if (componentIds.length === 0) {
      report += 'No components were inspected.\n\n';
    } else {
      report += `Inspected ${componentIds.length} components:\n\n`;
      
      componentIds.forEach((id: string) => {
        const result = results[id];
        if (!result) return;

        report += `### ${id}\n\n`;
        report += `- Exists: ${result.exists ? '✅' : '❌'}\n`;
        report += `- Visible: ${result.visible ? '✅' : '❌'}\n`;

        if (result.animationActive !== undefined) {
          report += `- Animation Active: ${result.animationActive ? '✅' : '❌'}\n`;
        }

        if (result.cssProperties) {
          report += '\n**CSS Properties:**\n\n';
          for (const [prop, value] of Object.entries(result.cssProperties) as [string, string][]) {
            report += `- ${prop}: ${value}\n`;
          }
        }
        
        report += '\n';
      });
    }
    
    return report;
  }
}

/**
 * Run a UI test on the specified URL
 * @param url URL to test
 * @param config UI enhancement configuration
 */
export async function runUITest(url: string, config: UIEnhancementConfig): Promise<string> {
  const tester = new UITester(config);
  
  try {
    await tester.launch(url);
    
    // Wait for components to load and animations to complete
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const results = await tester.testAllComponents();
    const report = tester.generateReport(results);
    
    await tester.close();
    
    return report;
  } catch (error) {
    await tester.close();
    return `Error running UI test: ${error instanceof Error ? error.message : String(error)}`;
  }
}