'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Package, X, Check } from 'lucide-react';

import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Badge } from '@/app/components/data-display/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/app/components/navigation/dialog';
import { cn } from '@/app/lib/utils';

interface Assembly {
  _id: string;
  assemblyCode: string;
  name: string;
  description?: string;
  status: string;
  isTopLevel: boolean;
  productId?: string | null;
}

interface AssemblySelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (assemblyId: string | null) => void;
  currentAssemblyId?: string | null;
  title?: string;
  description?: string;
}

export function AssemblySelector({
  isOpen,
  onClose,
  onSelect,
  currentAssemblyId,
  title = "Select Assembly",
  description = "Choose an assembly to associate"
}: AssemblySelectorProps) {
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [filteredAssemblies, setFilteredAssemblies] = useState<Assembly[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAssemblyId, setSelectedAssemblyId] = useState<string | null>(currentAssemblyId || null);

  // Fetch assemblies
  useEffect(() => {
    if (isOpen) {
      fetchAssemblies();
    }
  }, [isOpen]);

  // Filter assemblies based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAssemblies(assemblies);
    } else {
      const filtered = assemblies.filter(assembly =>
        assembly.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        assembly.assemblyCode.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (assembly.description && assembly.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredAssemblies(filtered);
    }
  }, [searchQuery, assemblies]);

  const fetchAssemblies = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/assemblies');
      if (!response.ok) {
        throw new Error('Failed to fetch assemblies');
      }
      const data = await response.json();
      const assemblyList = data.data || [];
      
      // Filter to only show top-level assemblies that don't already have a product association
      const availableAssemblies = assemblyList.filter((assembly: Assembly) => 
        assembly.isTopLevel && 
        (assembly.status === 'active' || assembly.status === 'pending_review') &&
        (!assembly.productId || assembly._id === currentAssemblyId)
      );
      
      setAssemblies(availableAssemblies);
    } catch (error) {
      console.error('Error fetching assemblies:', error);
      setAssemblies([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelect = () => {
    onSelect(selectedAssemblyId);
    onClose();
  };

  const handleRemoveAssociation = () => {
    onSelect(null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && (
            <DialogDescription>{description}</DialogDescription>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search assemblies by name or code..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Current Association */}
          {currentAssemblyId && (
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Currently Associated</p>
                    <p className="text-xs text-muted-foreground">
                      {assemblies.find(a => a._id === currentAssemblyId)?.name || 'Loading...'}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRemoveAssociation}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    Remove Association
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Assembly List */}
          <div className="flex-1 overflow-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredAssemblies.length === 0 ? (
              <div className="text-center py-8">
                <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                <p className="text-muted-foreground">
                  {searchQuery ? 'No assemblies match your search' : 'No available assemblies found'}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <AnimatePresence>
                  {filteredAssemblies.map((assembly) => (
                    <motion.div
                      key={assembly._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:shadow-md",
                          selectedAssemblyId === assembly._id
                            ? "border-primary bg-primary/5"
                            : "hover:border-primary/50"
                        )}
                        onClick={() => setSelectedAssemblyId(assembly._id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium">{assembly.name}</h4>
                                {selectedAssemblyId === assembly._id && (
                                  <Check className="h-4 w-4 text-primary" />
                                )}
                              </div>
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="outline" className="font-mono text-xs">
                                  {assembly.assemblyCode}
                                </Badge>
                                <Badge 
                                  variant={assembly.status === 'active' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {assembly.status}
                                </Badge>
                              </div>
                              {assembly.description && (
                                <p className="text-sm text-muted-foreground line-clamp-2">
                                  {assembly.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSelect}
            disabled={!selectedAssemblyId || selectedAssemblyId === currentAssemblyId}
          >
            {selectedAssemblyId === currentAssemblyId ? 'No Change' : 'Associate Assembly'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
