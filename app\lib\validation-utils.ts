/**
 * Enhanced validation utilities for type safety and security
 * Addresses CodeRabbit feedback on ObjectId validation, input limits, and type safety
 */

import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';

// Security constants for reasonable limits
export const VALIDATION_LIMITS = {
  MAX_OBJECTID_BATCH_SIZE: 1000, // Prevent DoS attacks with massive ObjectId arrays
  MAX_STRING_LENGTH: 10000, // Reasonable maximum for text fields
  MAX_ARRAY_LENGTH: 1000, // Prevent memory exhaustion
  MAX_NESTED_DEPTH: 10, // Prevent deeply nested object attacks
  MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB max request size
} as const;

/**
 * Enhanced ObjectId validation with security limits
 */
export function validateObjectId(id: string, fieldName = 'id'): { isValid: boolean; error?: string } {
  if (!id || typeof id !== 'string') {
    return { isValid: false, error: `${fieldName} must be a non-empty string` };
  }

  if (id.length > 24) {
    return { isValid: false, error: `${fieldName} exceeds maximum length for ObjectId` };
  }

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return { isValid: false, error: `${fieldName} must be a valid ObjectId format` };
  }

  return { isValid: true };
}

/**
 * Validate array of ObjectIds with reasonable limits
 */
export function validateObjectIdArray(
  ids: unknown, 
  fieldName = 'ids',
  maxLength = VALIDATION_LIMITS.MAX_OBJECTID_BATCH_SIZE
): { isValid: boolean; error?: string; validIds?: string[] } {
  if (!Array.isArray(ids)) {
    return { isValid: false, error: `${fieldName} must be an array` };
  }

  if (ids.length === 0) {
    return { isValid: false, error: `${fieldName} array cannot be empty` };
  }

  if (ids.length > maxLength) {
    return { 
      isValid: false, 
      error: `${fieldName} array exceeds maximum length of ${maxLength} items` 
    };
  }

  const validIds: string[] = [];
  for (let i = 0; i < ids.length; i++) {
    const validation = validateObjectId(ids[i], `${fieldName}[${i}]`);
    if (!validation.isValid) {
      return { isValid: false, error: validation.error! };
    }
    validIds.push(ids[i] as string);
  }

  return { isValid: true, validIds };
}

/**
 * Enhanced JSON parsing with proper error handling and Content-Type validation
 */
export async function parseJsonWithValidation(
  request: NextRequest,
  options: {
    requireContentType?: boolean;
    maxSize?: number;
    allowedContentTypes?: string[];
  } = {}
): Promise<{ success: boolean; data?: any; error?: string; statusCode?: number }> {
  const {
    requireContentType = true,
    maxSize = VALIDATION_LIMITS.MAX_REQUEST_SIZE,
    allowedContentTypes = ['application/json']
  } = options;

  try {
    // Validate Content-Type header
    const contentType = request.headers.get('content-type');
    
    if (requireContentType) {
      if (!contentType) {
        return {
          success: false,
          error: 'Content-Type header is required',
          statusCode: 400
        };
      }

      const isValidContentType = allowedContentTypes.some(type => 
        contentType.toLowerCase().includes(type.toLowerCase())
      );

      if (!isValidContentType) {
        return {
          success: false,
          error: `Unsupported Content-Type. Expected one of: ${allowedContentTypes.join(', ')}`,
          statusCode: 415
        };
      }
    }

    // Check content length if available
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > maxSize) {
      return {
        success: false,
        error: `Request body too large. Maximum size: ${maxSize} bytes`,
        statusCode: 413
      };
    }

    // Parse JSON with error handling
    const data = await request.json();
    
    // Basic structure validation
    if (data === null || data === undefined) {
      return {
        success: false,
        error: 'Request body cannot be null or undefined',
        statusCode: 400
      };
    }

    return { success: true, data };

  } catch (error) {
    // Handle specific JSON parsing errors
    if (error instanceof SyntaxError) {
      return {
        success: false,
        error: 'Invalid JSON format in request body',
        statusCode: 400
      };
    }

    // Handle other parsing errors
    return {
      success: false,
      error: 'Failed to parse request body',
      statusCode: 400
    };
  }
}

/**
 * Validate string with length limits and sanitization
 */
export function validateString(
  value: unknown,
  fieldName: string,
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    allowEmpty?: boolean;
    trim?: boolean;
  } = {}
): { isValid: boolean; error?: string; value?: string } {
  const {
    required = false,
    minLength = 0,
    maxLength = VALIDATION_LIMITS.MAX_STRING_LENGTH,
    allowEmpty = true,
    trim = true
  } = options;

  if (value === null || value === undefined) {
    if (required) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    return { isValid: true };
  }

  if (typeof value !== 'string') {
    return { isValid: false, error: `${fieldName} must be a string` };
  }

  let processedValue = trim ? value.trim() : value;

  if (!allowEmpty && processedValue.length === 0) {
    return { isValid: false, error: `${fieldName} cannot be empty` };
  }

  if (processedValue.length < minLength) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${minLength} characters long` 
    };
  }

  if (processedValue.length > maxLength) {
    return { 
      isValid: false, 
      error: `${fieldName} exceeds maximum length of ${maxLength} characters` 
    };
  }

  return { isValid: true, value: processedValue };
}

/**
 * Validate numeric values with reasonable ranges
 */
export function validateNumber(
  value: unknown,
  fieldName: string,
  options: {
    required?: boolean;
    min?: number;
    max?: number;
    integer?: boolean;
  } = {}
): { isValid: boolean; error?: string; value?: number } {
  const { required = false, min, max, integer = false } = options;

  if (value === null || value === undefined) {
    if (required) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    return { isValid: true };
  }

  const numValue = Number(value);

  if (isNaN(numValue) || !isFinite(numValue)) {
    return { isValid: false, error: `${fieldName} must be a valid number` };
  }

  if (integer && !Number.isInteger(numValue)) {
    return { isValid: false, error: `${fieldName} must be an integer` };
  }

  if (min !== undefined && numValue < min) {
    return { isValid: false, error: `${fieldName} must be at least ${min}` };
  }

  if (max !== undefined && numValue > max) {
    return { isValid: false, error: `${fieldName} must not exceed ${max}` };
  }

  return { isValid: true, value: numValue };
}

/**
 * Create standardized error response
 */
export function createValidationErrorResponse(
  error: string,
  statusCode: number = 400,
  details?: any
): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error,
      code: 'VALIDATION_ERROR',
      details,
      timestamp: new Date().toISOString()
    },
    { status: statusCode }
  );
}

/**
 * Validate inventory flags for safety
 */
export function validateInventoryFlags(flags: unknown): { isValid: boolean; error?: string } {
  if (!flags || typeof flags !== 'object') {
    return { isValid: true }; // Flags are optional
  }

  const allowedFlags = [
    'isActive',
    'isManufactured',
    'isObsolete',
    'requiresInspection',
    'isHazardous',
    'isConsignment'
  ];

  for (const [key, value] of Object.entries(flags)) {
    if (!allowedFlags.includes(key)) {
      return { 
        isValid: false, 
        error: `Unknown inventory flag: ${key}. Allowed flags: ${allowedFlags.join(', ')}` 
      };
    }

    if (typeof value !== 'boolean') {
      return { 
        isValid: false, 
        error: `Inventory flag ${key} must be a boolean value` 
      };
    }
  }

  return { isValid: true };
}
