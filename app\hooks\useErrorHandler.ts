/**
 * Error Handling Hooks
 * Standardized hooks for consistent error management across the application
 */

"use client";

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { 
  StandardError, 
  ErrorCategory, 
  UseErrorHandlerOptions,
  UseErrorRecoveryOptions 
} from '@/app/types/error.types';
import { 
  normalizeError, 
  logError, 
  extractErrorMessage,
  isRetryableError,
  createErrorContext 
} from '@/app/utils/error.utils';

/**
 * Core error handler hook for consistent error management
 */
export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    onError,
    showToast = true,
    logError: shouldLog = true,
    category = ErrorCategory.SYSTEM,
  } = options;

  const handleError = useCallback((
    error: unknown,
    context?: Record<string, any>
  ) => {
    const standardError = normalizeError(error);
    
    // Add category if not already set
    if (standardError.category === ErrorCategory.SYSTEM && category !== ErrorCategory.SYSTEM) {
      standardError.category = category;
    }

    // Log error if enabled
    if (shouldLog) {
      const errorContext = createErrorContext(
        context?.component,
        context?.action,
        context
      );
      logError(standardError, errorContext);
    }

    // Show toast notification if enabled
    if (showToast) {
      const message = extractErrorMessage(standardError);
      toast.error(message, {
        description: standardError.details && process.env.NODE_ENV === 'development' 
          ? standardError.details 
          : undefined,
        action: isRetryableError(standardError) && context?.onRetry ? {
          label: 'Retry',
          onClick: context.onRetry,
        } : undefined,
      });
    }

    // Call custom error handler if provided
    if (onError) {
      onError(standardError);
    }

    return standardError;
  }, [onError, showToast, shouldLog, category]);

  return { handleError };
}

/**
 * Error recovery hook with retry logic and exponential backoff
 */
export function useErrorRecovery(
  operation: () => Promise<any>,
  options: UseErrorRecoveryOptions = {}
) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onRetrySuccess,
    onRetryFailure,
  } = options;

  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<StandardError | null>(null);

  const { handleError } = useErrorHandler({
    showToast: false, // We'll handle toast notifications manually
    logError: true,
  });

  const retry = useCallback(async () => {
    if (retryCount >= maxRetries) {
      const error = normalizeError('Maximum retry attempts exceeded');
      if (onRetryFailure) {
        onRetryFailure(error);
      }
      return;
    }

    setIsRetrying(true);
    
    try {
      // Exponential backoff delay
      const delay = retryDelay * Math.pow(2, retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      const result = await operation();
      
      // Success - reset retry state
      setRetryCount(0);
      setLastError(null);
      setIsRetrying(false);
      
      if (onRetrySuccess) {
        onRetrySuccess();
      }

      toast.success('Operation completed successfully');
      return result;
      
    } catch (error) {
      const standardError = handleError(error, {
        component: 'useErrorRecovery',
        action: 'retry',
        retryCount: retryCount + 1,
      });

      setLastError(standardError);
      setRetryCount(prev => prev + 1);
      setIsRetrying(false);

      // Show retry-specific toast
      toast.error(`Retry ${retryCount + 1}/${maxRetries} failed`, {
        description: extractErrorMessage(standardError),
        action: retryCount + 1 < maxRetries ? {
          label: 'Retry Again',
          onClick: retry,
        } : undefined,
      });

      if (retryCount + 1 >= maxRetries && onRetryFailure) {
        onRetryFailure(standardError);
      }

      throw standardError;
    }
  }, [operation, retryCount, maxRetries, retryDelay, onRetrySuccess, onRetryFailure, handleError]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setLastError(null);
    setIsRetrying(false);
  }, []);

  return {
    retry,
    reset,
    isRetrying,
    retryCount,
    maxRetries,
    canRetry: retryCount < maxRetries,
    lastError,
  };
}

/**
 * Toast error notifications hook
 */
export function useErrorToast() {
  const showError = useCallback((
    error: unknown,
    options: {
      duration?: number;
      action?: { label: string; action: () => void };
      description?: string;
    } = {}
  ) => {
    const standardError = normalizeError(error);
    const message = extractErrorMessage(standardError);

    toast.error(message, {
      ...(options.duration !== undefined ? { duration: options.duration } : {}),
      ...(options.description || (standardError.details && process.env.NODE_ENV === 'development' ? standardError.details : undefined) ? {
        description: options.description || (standardError.details && process.env.NODE_ENV === 'development' ? standardError.details : undefined)
      } : {}),
      ...(options.action ? {
        action: {
          label: options.action.label,
          onClick: options.action.action,
        }
      } : {}),
    });
  }, []);

  const showWarning = useCallback((
    message: string,
    options: {
      duration?: number;
      action?: { label: string; action: () => void };
      description?: string;
    } = {}
  ) => {
    toast.warning(message, {
      ...(options.duration !== undefined ? { duration: options.duration } : {}),
      ...(options.description !== undefined ? { description: options.description } : {}),
      ...(options.action ? {
        action: {
          label: options.action.label,
          onClick: options.action.action,
        }
      } : {}),
    });
  }, []);

  const showInfo = useCallback((
    message: string,
    options: {
      duration?: number;
      action?: { label: string; action: () => void };
      description?: string;
    } = {}
  ) => {
    toast.info(message, {
      ...(options.duration !== undefined ? { duration: options.duration } : {}),
      ...(options.description !== undefined ? { description: options.description } : {}),
      ...(options.action ? {
        action: {
          label: options.action.label,
          onClick: options.action.action,
        }
      } : {}),
    });
  }, []);

  return {
    showError,
    showWarning,
    showInfo,
  };
}

/**
 * Form error management hook
 */
export function useFormErrors() {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formError, setFormError] = useState<string | null>(null);

  const setFieldError = useCallback((field: string, message: string) => {
    setErrors(prev => ({
      ...prev,
      [field]: message,
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
    setFormError(null);
  }, []);

  const setFormErrorMessage = useCallback((error: unknown) => {
    const message = extractErrorMessage(error);
    setFormError(message);
  }, []);

  const clearFormError = useCallback(() => {
    setFormError(null);
  }, []);

  const hasErrors = Object.keys(errors).length > 0 || formError !== null;
  const hasFieldErrors = Object.keys(errors).length > 0;

  return {
    errors,
    formError,
    hasErrors,
    hasFieldErrors,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    setFormError: setFormErrorMessage,
    clearFormError,
  };
}

/**
 * Async operation error handling hook
 */
export function useAsyncError() {
  const [error, setError] = useState<StandardError | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { handleError } = useErrorHandler();

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options: {
      onSuccess?: (result: T) => void;
      onError?: (error: StandardError) => void;
      showToast?: boolean;
    } = {}
  ): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await operation();
      
      if (options.onSuccess) {
        options.onSuccess(result);
      }
      
      return result;
    } catch (err) {
      const standardError = handleError(err, {
        component: 'useAsyncError',
        action: 'execute',
      });
      
      setError(standardError);
      
      if (options.onError) {
        options.onError(standardError);
      }
      
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    isLoading,
    execute,
    clearError,
  };
}
