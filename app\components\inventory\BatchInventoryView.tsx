'use client';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/app/components/data-display/table';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { useTheme } from '@/app/contexts/ThemeContext';
import { format } from 'date-fns';
import {
    AlertCircle,
    ArrowDownLeft,
    ArrowUpRight,
    Boxes,
    Calendar,
    Loader2,
    Package,
    RefreshCw,
    TrendingDown,
    TrendingUp
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Skeleton } from '../data-display/skeleton';

interface Transaction {
  _id: string;
  partId: string | {
    _id: string;
    name: string;
    description?: string;
  };
  transactionType: string;
  quantity: number;
  previousStock: number;
  newStock: number;
  transactionDate: string | Date;
  referenceNumber: string;
  notes?: string;
  batchId: string;
  createdAt: string | Date;
}

interface InventoryMetrics {
  totalStockIn: number;
  totalStockOut: number;
  netChange: number;
  lastTransactionDate: string | Date | null;
  currentStock: number;
  transactionCount: number;
}

interface BatchInventoryViewProps {
  batchId: string;
  className?: string;
  showTransactions?: boolean;
  limit?: number;
}

export default function BatchInventoryView({
  batchId,
  className,
  showTransactions = true,
  limit = 5
}: BatchInventoryViewProps) {
  const { theme } = useTheme();
  const [inventoryData, setInventoryData] = useState<{
    batch: any;
    part: any;
    inventoryMetrics: InventoryMetrics;
    transactions: Transaction[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch batch inventory data
  const fetchBatchInventory = async () => {
    if (!batchId) return;
    
    try {
      setIsRefreshing(true);
      const response = await fetch(`/api/batches/${batchId}/inventory`);

      if (!response.ok) {
        throw new Error(`Error fetching batch inventory: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setInventoryData(data.data || null);
      setError(null);
    } catch (err) {
      console.error('Error fetching batch inventory:', err);
      setError(err instanceof Error ? err.message : 'Failed to load batch inventory data');
      toast.error('Failed to load batch inventory data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Fetch inventory data on mount and when batchId changes
  useEffect(() => {
    if (batchId) {
      fetchBatchInventory();
    }
  }, [batchId]);

  // Get icon for transaction type
  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'stock_in':
        return <ArrowUpRight className="h-4 w-4 text-green-500" />;
      case 'stock_out':
        return <ArrowDownLeft className="h-4 w-4 text-red-500" />;
      case 'adjustment':
        return <RefreshCw className="h-4 w-4 text-amber-500" />;
      default:
        return <Package className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format transaction type for display
  const formatTransactionType = (type: string) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Package className="mr-2 h-5 w-5 text-muted-foreground" />
            Batch Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton key={index} className="h-24 w-full" />
            ))}
          </div>
          {showTransactions && (
            <Skeleton className="h-64 w-full mt-4" />
          )}
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Package className="mr-2 h-5 w-5 text-muted-foreground" />
            Batch Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchBatchInventory}
              className="flex items-center"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no inventory data is available
  if (!inventoryData || !inventoryData.batch) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Package className="mr-2 h-5 w-5 text-muted-foreground" />
            Batch Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Package className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">No inventory data available for this batch</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { batch, part, inventoryMetrics, transactions } = inventoryData;

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg flex items-center">
          <Package className="mr-2 h-5 w-5 text-muted-foreground" />
          Batch Inventory
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={fetchBatchInventory}
          disabled={isRefreshing}
          className="h-8 w-8 p-0"
        >
          {isRefreshing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          <span className="sr-only">Refresh</span>
        </Button>
      </CardHeader>
      <CardContent>
        {/* Inventory Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Stock In */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Total Stock In</p>
                  <h3 className="text-2xl font-bold mt-1">{inventoryMetrics.totalStockIn}</h3>
                </div>
                <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-full">
                  <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock Out */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Total Stock Out</p>
                  <h3 className="text-2xl font-bold mt-1">{inventoryMetrics.totalStockOut}</h3>
                </div>
                <div className="bg-red-100 dark:bg-red-900/30 p-2 rounded-full">
                  <TrendingDown className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Stock */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Current Stock</p>
                  <h3 className="text-2xl font-bold mt-1">{part?.inventory?.currentStock || 0}</h3>
                </div>
                <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                  <Boxes className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Part Information */}
        {part && (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Part Information</h3>
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-muted-foreground">Part Name</p>
                    <p className="font-medium">{part.businessName || part.name}</p>
                    {part.businessName && (
                      <p className="text-xs text-muted-foreground italic">{part.name}</p>
                    )}
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Part ID</p>
                    <p className="font-mono text-xs">{part._id}</p>
                  </div>
                  {part.description && (
                    <div className="col-span-2">
                      <p className="text-xs text-muted-foreground">Description</p>
                      <p className="text-sm">{part.description}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Transactions Table */}
        {showTransactions && transactions && transactions.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">Recent Transactions</h3>
              <Button
                variant="link"
                size="sm"
                className="h-8 px-2 text-xs"
                onClick={() => window.open(`/inventory-transactions?batchId=${batchId}`, '_blank')}
              >
                View All
              </Button>
            </div>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Stock Level</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction, index) => (
                    <TableRow key={transaction._id}>
                      <TableCell>
                        <div className="flex items-center">
                          {getTransactionTypeIcon(transaction.transactionType)}
                          <span className="ml-2 text-xs">
                            {formatTransactionType(transaction.transactionType)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className={transaction.quantity > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                        {transaction.quantity > 0 ? '+' : ''}{transaction.quantity}
                      </TableCell>
                      <TableCell>
                        <span className="text-xs text-muted-foreground">{transaction.previousStock}</span>
                        {' → '}
                        <span className="font-medium">{transaction.newStock}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3 mr-1" />
                          {format(new Date(transaction.transactionDate), 'MMM d, yyyy')}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
