/**
 * InlineError Component
 * Small inline error messages for inputs and field validation
 * Lightweight component for form field errors and inline warnings
 */

"use client";

import React from 'react';
import { AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/app/lib/utils';
import { InlineErrorProps } from '@/app/types/error.types';

/**
 * InlineError component for small inline error messages
 * Perfect for form field validation errors
 */
export function InlineError({
  message,
  icon = true,
  className,
}: InlineErrorProps) {
  if (!message) {
    return null;
  }

  return (
    <div className={cn('flex items-center gap-1 mt-1', className)}>
      {icon && (
        <AlertCircle className="h-3 w-3 text-theme-error flex-shrink-0" />
      )}
      <span className="text-xs text-theme-error leading-tight">
        {message}
      </span>
    </div>
  );
}

/**
 * InlineWarning component for warning messages
 */
export function InlineWarning({
  message,
  icon = true,
  className,
}: InlineErrorProps) {
  if (!message) {
    return null;
  }

  return (
    <div className={cn('flex items-center gap-1 mt-1', className)}>
      {icon && (
        <AlertTriangle className="h-3 w-3 text-theme-warning flex-shrink-0" />
      )}
      <span className="text-xs text-theme-warning leading-tight">
        {message}
      </span>
    </div>
  );
}

/**
 * InlineInfo component for informational messages
 */
export function InlineInfo({
  message,
  icon = true,
  className,
}: InlineErrorProps) {
  if (!message) {
    return null;
  }

  return (
    <div className={cn('flex items-center gap-1 mt-1', className)}>
      {icon && (
        <Info className="h-3 w-3 text-theme-info flex-shrink-0" />
      )}
      <span className="text-xs text-theme-info leading-tight">
        {message}
      </span>
    </div>
  );
}

/**
 * InlineSuccess component for success messages
 */
export function InlineSuccess({
  message,
  icon = true,
  className,
}: InlineErrorProps) {
  if (!message) {
    return null;
  }

  return (
    <div className={cn('flex items-center gap-1 mt-1', className)}>
      {icon && (
        <div className="h-3 w-3 rounded-full bg-theme-success flex-shrink-0 flex items-center justify-center">
          <div className="h-1 w-1 bg-white rounded-full" />
        </div>
      )}
      <span className="text-xs text-theme-success leading-tight">
        {message}
      </span>
    </div>
  );
}

/**
 * Adaptive inline message component that chooses the right variant
 */
export function InlineMessage({
  message,
  variant = 'error',
  icon = true,
  className,
}: {
  message: string;
  variant?: 'error' | 'warning' | 'info' | 'success';
  icon?: boolean;
  className?: string;
}) {
  switch (variant) {
    case 'warning':
      return <InlineWarning message={message} icon={icon} className={className || ''} />;
    case 'info':
      return <InlineInfo message={message} icon={icon} className={className || ''} />;
    case 'success':
      return <InlineSuccess message={message} icon={icon} className={className || ''} />;
    case 'error':
    default:
      return <InlineError message={message} icon={icon} className={className || ''} />;
  }
}

export default InlineError;
