import { Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { captureException, setTag } from '../lib/logging-utils';
import connectToMongoose from '../lib/mongodb';
import { IWarehouse, Warehouse } from '../models/warehouse.model';

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[WarehouseService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[WarehouseService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'warehouse');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// Interface for the service
export interface IWarehouseService extends IWarehouse {}

// Define DTOs for warehouse operations
export interface CreateWarehouseDto {
  location_id?: string; // Optional, generated if not provided
  name: string;
  location: string;
  capacity: number;
  manager: string;
  contact: string;
  isBinTracked?: boolean;
}

export interface UpdateWarehouseDto extends Partial<CreateWarehouseDto> {}

/**
 * Fetches warehouses with pagination, sorting, and filtering.
 */
export async function fetchWarehouses(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'service', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;

    // Fetch warehouses with pagination
    const warehouses = await (Warehouse.find as any)(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await Warehouse.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', 'service', {
      count: warehouses.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      warehouses,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch warehouses');
  }
}

/**
 * Gets a single warehouse by its location_id.
 */
export async function getWarehouseByLocationId(locationId: string): Promise<IWarehouseService | null> {
  logOperation('GET_BY_LOCATION_ID', 'service', { locationId });
  try {
    await connectToMongoose();

    const warehouse = await (Warehouse.findOne as any)({ location_id: locationId }).lean() as IWarehouseService | null;
    
    if (!warehouse) {
      logOperation('GET_BY_LOCATION_ID_NOT_FOUND', 'service', { locationId });
      return null;
    }
    
    logOperation('GET_BY_LOCATION_ID_SUCCESS', 'service', { locationId });
    return warehouse;
  } catch (error: any) {
    logOperation('GET_BY_LOCATION_ID_ERROR', 'service', { locationId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get warehouse by location ID ${locationId}`);
  }
}

/**
 * Gets a single warehouse by its MongoDB ObjectId.
 */
export async function getWarehouseById(id: string): Promise<IWarehouseService | null> {
  logOperation('GET_BY_ID', 'service', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();

    const warehouse = await (Warehouse.findById as any)(id).lean() as IWarehouseService | null;
    
    if (!warehouse) {
      logOperation('GET_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', 'service', { id });
    return warehouse;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get warehouse by ID ${id}`);
  }
}

/**
 * Creates a new warehouse.
 */
export async function createWarehouse(warehouseData: CreateWarehouseDto): Promise<IWarehouseService> {
  logOperation('CREATE', 'service', { name: warehouseData.name });
  try {
    await connectToMongoose();

    // Generate location_id if not provided
    const locationId = warehouseData.location_id || `WH-${uuidv4().substring(0, 8).toUpperCase()}`;
    
    // Check if location_id already exists
    const existingWarehouse = await (Warehouse.findOne as any)({ location_id: locationId }).exec();
    if (existingWarehouse) {
      throw new Error(`Warehouse with location ID ${locationId} already exists`);
    }

    const warehouseToSave = new Warehouse({
      ...warehouseData,
      location_id: locationId
    });

    const savedWarehouse = await warehouseToSave.save();
    logOperation('CREATE_SUCCESS', 'service', { _id: savedWarehouse._id, location_id: savedWarehouse.location_id });
    return savedWarehouse.toObject() as IWarehouseService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create warehouse');
  }
}

/**
 * Updates an existing warehouse by its location_id.
 */
export async function updateWarehouseByLocationId(locationId: string, updateData: UpdateWarehouseDto): Promise<IWarehouseService | null> {
  logOperation('UPDATE_BY_LOCATION_ID', 'service', { locationId, data: updateData });
  try {
    await connectToMongoose();
    
    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    delete updatePayload.location_id; // Don't update the unique identifier
    
    // Check if warehouse exists
    const existingWarehouse = await (Warehouse.findOne as any)({ location_id: locationId }).exec();
    if (!existingWarehouse) {
      logOperation('UPDATE_BY_LOCATION_ID_NOT_FOUND', 'service', { locationId });
      return null;
    }

    const updatedWarehouse = await (Warehouse.findOneAndUpdate as any)(
      { location_id: locationId },
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IWarehouseService | null;

    if (!updatedWarehouse) {
      logOperation('UPDATE_BY_LOCATION_ID_FAILED', 'service', { locationId });
      return null;
    }
    
    logOperation('UPDATE_BY_LOCATION_ID_SUCCESS', 'service', { locationId });
    return updatedWarehouse;
  } catch (error: any) {
    logOperation('UPDATE_BY_LOCATION_ID_ERROR', 'service', { locationId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update warehouse ${locationId}`);
  }
}

/**
 * Updates an existing warehouse by its MongoDB ObjectId.
 */
export async function updateWarehouseById(id: string, updateData: UpdateWarehouseDto): Promise<IWarehouseService | null> {
  logOperation('UPDATE_BY_ID', 'service', { id, data: updateData });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...updateData };
    delete updatePayload.location_id; // Don't update the unique identifier
    
    const updatedWarehouse = await (Warehouse.findByIdAndUpdate as any)(
      new Types.ObjectId(id),
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IWarehouseService | null;

    if (!updatedWarehouse) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    logOperation('UPDATE_BY_ID_SUCCESS', 'service', { id });
    return updatedWarehouse;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update warehouse with ID ${id}`);
  }
}

/**
 * Deletes a warehouse by its location_id.
 */
export async function deleteWarehouseByLocationId(locationId: string): Promise<IWarehouseService | null> {
  logOperation('DELETE_BY_LOCATION_ID', 'service', { locationId });
  try {
    await connectToMongoose();

    const deletedWarehouse = await (Warehouse.findOneAndDelete as any)({ location_id: locationId }).lean() as IWarehouseService | null;
    
    if (!deletedWarehouse) {
      logOperation('DELETE_BY_LOCATION_ID_NOT_FOUND', 'service', { locationId });
      return null;
    }
    
    logOperation('DELETE_BY_LOCATION_ID_SUCCESS', 'service', { locationId });
    return deletedWarehouse;
  } catch (error: any) {
    logOperation('DELETE_BY_LOCATION_ID_ERROR', 'service', { locationId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete warehouse ${locationId}`);
  }
}

/**
 * Deletes a warehouse by its MongoDB ObjectId.
 */
export async function deleteWarehouseById(id: string): Promise<IWarehouseService | null> {
  logOperation('DELETE_BY_ID', 'service', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToMongoose();
    
    const deletedWarehouse = await (Warehouse.findByIdAndDelete as any)(new Types.ObjectId(id)).lean() as IWarehouseService | null;
    
    if (!deletedWarehouse) {
      logOperation('DELETE_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    logOperation('DELETE_BY_ID_SUCCESS', 'service', { id });
    return deletedWarehouse;
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete warehouse with ID ${id}`);
  }
}

/**
 * Searches warehouses based on a text query string.
 */
export async function searchWarehouses(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', 'service', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToMongoose();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { location: new RegExp(query, 'i') },
        { manager: new RegExp(query, 'i') },
        { location_id: new RegExp(query, 'i') }
      ];
    }

    const warehousesQuery = (Warehouse.find as any)(searchFilter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const [warehouses, totalCount] = await Promise.all([
      warehousesQuery.exec() as any as Promise<IWarehouseService[]>,
      Warehouse.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', 'service', { query, count: warehouses.length, pagination });
    return { warehouses, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', 'service', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search warehouses');
  }
} 