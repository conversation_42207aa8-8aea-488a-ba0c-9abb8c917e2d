import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn, logger } from "@/app/lib/utils"

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-theme-primary",
  {
    variants: {
      variant: {
        default: "bg-bg-primary text-text-primary border-border-primary",
        destructive:
          "border-theme-error/20 text-theme-error bg-theme-error-light [&>svg]:text-theme-error",
        warning:
          "border-theme-warning/20 text-theme-warning bg-theme-warning-light [&>svg]:text-theme-warning",
        success:
          "border-theme-success/20 text-theme-success bg-theme-success-light [&>svg]:text-theme-success",
        info:
          "border-theme-info/20 text-theme-info bg-theme-info-light [&>svg]:text-theme-info",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

/**
 * Displays a callout for user attention.
 * It supports different variants like 'default', 'destructive', 'warning', 'success', and 'info'.
 */
const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => {
  logger('INFO', `Alert component rendered with variant: ${variant || 'default'}`, { className, variant, props });
  return (
  <div
    ref={ref}
    role="alert"
    className={cn(alertVariants({ variant }), className)}
    {...props}
  />
  );
});

Alert.displayName = "Alert"

/**
 * Component for rendering the title of the Alert.
 * It should be used as a child of the `Alert` component.
 */
const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={cn("mb-1 font-medium leading-none tracking-tight text-text-primary", className)}
    {...props}
  />
))
AlertTitle.displayName = "AlertTitle"

/**
 * Component for rendering the description or main content of the Alert.
 * It should be used as a child of the `Alert` component, typically following an `AlertTitle`.
 */
const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm [&_p]:leading-relaxed text-text-secondary", className)}
    {...props}
  />
))
AlertDescription.displayName = "AlertDescription"

export { Alert, AlertTitle, AlertDescription }