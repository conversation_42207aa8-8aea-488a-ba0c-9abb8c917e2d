import { NextRequest, NextResponse } from 'next/server';
import { generateInventoryValueByCategory } from '@/app/services/analytics';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import { standardizeAnalyticsResponse } from '@/app/lib/analytics-helpers';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

const ROUTE_PATH = '/api/analytics/inventory-value';

/**
 * GET handler for generating inventory value by category data
 * @param request - The incoming request
 * @param context - The route context (required by Next.js 15)
 * @returns JSON response with inventory value by category data
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, null, true);
  
  // Generate the report
  const reportData = await generateInventoryValueByCategory({});
  
  // Standardize the response format to ensure consistent structure
  const standardizedData = standardizeAnalyticsResponse(reportData);
  
  const duration = Date.now() - startTime;
  
  return successResponse(
    standardizedData,
    'Inventory value by category data generated successfully',
    { duration }
  );
}

// Apply the withErrorHandling middleware to our handler
// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
