"use client";

import React from "react";
import { Loader2, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { cn } from "@/app/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { format } from "date-fns";
import { AutosaveStatus } from "@/app/hooks/useAutosave";

export interface AutosaveIndicatorProps {
  status: AutosaveStatus;
  lastSaved: Date | null;
  error: Error | null;
  className?: string;
  showTime?: boolean;
  showIcon?: boolean;
  iconOnly?: boolean;
}

export const AutosaveIndicator: React.FC<AutosaveIndicatorProps> = ({
  status,
  lastSaved,
  error,
  className,
  showTime = true,
  showIcon = true,
  iconOnly = false,
}) => {
  // Format the last saved time
  const formattedTime = lastSaved 
    ? format(lastSaved, "h:mm:ss a") 
    : "";

  // Get the appropriate status text
  const getStatusText = () => {
    switch (status) {
      case "idle":
        return "Changes will be saved automatically";
      case "saving":
        return "Saving changes...";
      case "saved":
        return showTime 
          ? `Saved at ${formattedTime}` 
          : "Changes saved";
      case "error":
        return error?.message || "Failed to save changes";
      default:
        return "";
    }
  };

  // Get the appropriate icon
  const getIcon = () => {
    switch (status) {
      case "idle":
        return <Clock className="h-4 w-4" />;
      case "saving":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "saved":
        return <CheckCircle className="h-4 w-4" />;
      case "error":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Get the appropriate color class
  const getColorClass = () => {
    switch (status) {
      case "idle":
        return "text-muted-foreground";
      case "saving":
        return "text-blue-500 dark:text-blue-400";
      case "saved":
        return "text-green-500 dark:text-green-400";
      case "error":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={status}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -5 }}
        className={cn(
          "flex items-center text-xs font-medium",
          getColorClass(),
          className
        )}
      >
        {showIcon && (
          <span className="mr-1.5">{getIcon()}</span>
        )}
        {!iconOnly && (
          <span>{getStatusText()}</span>
        )}
      </motion.div>
    </AnimatePresence>
  );
};
