{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,

    // Enhanced error reporting
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,

    // Better debugging and development experience
    "sourceMap": true,
    "declaration": false,
    "removeComments": false,
    "preserveConstEnums": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/app/*": ["./app/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": [
    "node_modules",
    "__tests__",
    "__mocks__",
    ".next",
    "mcp-postman",
    "sentry-mcp",
    "mcp-taskmanager",
    "playwright-report",
    "tests",
    "scripts",
    "postman-collections",
    "instrumentation-client.ts",
    "sentry.server.config.ts",
    "debug-relationships.js",
    "js_backup",
    "sql_backup",
    "split_sql",
    "backup",
    "backup/**/*",
    "src_old",
    "src_old/**/*",
    "src_older_backup",
    "src_older_backup/**/*"
  ]
}
