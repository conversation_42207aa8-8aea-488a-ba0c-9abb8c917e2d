'use client';

import { UnifiedItemGrid } from '@/app/components/grids/UnifiedItemGrid';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';

interface AssembliesGridProps {
  assemblies: Assembly[];
}

/**
 * Grid view for assemblies using the unified grid component
 */
export function AssembliesGrid({ assemblies }: AssembliesGridProps) {
  return (
    <UnifiedItemGrid
      items={assemblies}
      itemType="assembly"
      emptyStateConfig={{
        title: 'No assemblies found',
        description: 'Get started by creating your first assembly.',
        actionLabel: 'Create Assembly',
        actionHref: '/assemblies/new'
      }}
    />
  );
}
