import { NextRequest, NextResponse } from 'next/server';
import { generateProcurementReport } from '@/app/services/reports';
import { successResponse, errorResponse } from '@/app/lib/api-response';

/**
 * GET handler for generating procurement report
 * @param request - The incoming request with query parameters
 * @returns JSON response with procurement report data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/reports/procurement - Generating procurement report');
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const statusFilter = searchParams.get('status') || null;
    
    // Parse date range if provided
    let dateRange = null;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    if (startDate && endDate) {
      dateRange = { startDate, endDate };
    }
    
    // Generate the report with the specified options
    const reportData = await generateProcurementReport({
      statusFilter,
      dateRange
    });
    
    const duration = Date.now() - startTime;
    console.log(`[API] Procurement report generated in ${duration}ms`);
    
    return successResponse(
      reportData,
      'Procurement report generated successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/reports/procurement (${duration}ms):`, error);
    
    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
