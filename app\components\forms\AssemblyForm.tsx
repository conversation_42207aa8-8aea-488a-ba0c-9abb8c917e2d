'use client';

import { zod<PERSON><PERSON><PERSON><PERSON> } from '@hookform/resolvers/zod';
import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { showValidationErrorToast, showErrorToast } from "@/app/components/feedback/ErrorToast";

import { Textarea } from '@/app/components/forms';
import { Button } from '@/app/components/forms/Button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/app/components/forms/Form';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Switch } from '@/app/components/forms/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/app/components/navigation/tabs';

import { useAssemblyForm } from './AssemblyFormWrapper';
import { PartRequirementsList } from './PartRequirementsList';
import { PartSelector } from './PartSelector';
import {
    assemblyFormSchema,
    assemblyStatusOptions,
    partialAssemblyFormSchema,
    PartialAssemblyFormValues
} from './schema';
import { StockSummary } from './StockSummary';

interface AssemblyFormProps {
  className?: string;
}

export function AssemblyForm({ className }: AssemblyFormProps) {
  const router = useRouter();
  const { 
    formData, 
    isLoading, 
    isSaving, 
    isEditing, 
    isDirty,
    updateFormField,
    resetForm,
    saveAssembly
  } = useAssemblyForm();
  
  // Setup react-hook-form with zod validation
  const form = useForm<PartialAssemblyFormValues>({
    resolver: zodResolver(partialAssemblyFormSchema) as any,
    defaultValues: formData,
    mode: 'onChange'
  });
  
  // Update form when formData changes
  useEffect(() => {
    if (!isLoading) {
      form.reset(formData);
    }
  }, [form, formData, isLoading]);
  
  // Update context when form fields change
  const handleFieldChange = <K extends keyof PartialAssemblyFormValues>(
    field: K, 
    value: PartialAssemblyFormValues[K]
  ) => {
    (form.setValue as any)(field, value);
    updateFormField(field, value);
  };
  
  // Handle form submission
  const onSubmit = async () => {
    try {
      // Validate the form with the stricter schema when submitting
      const result = assemblyFormSchema.safeParse(formData);
      
      if (!result.success) {
        // Show validation errors using standardized toast
        const fieldErrors = result.error.flatten().fieldErrors;
        const errorMessages: string[] = [];

        for (const [field, errors] of Object.entries(fieldErrors)) {
          if (errors && errors.length > 0) {
            errorMessages.push(`${field}: ${errors[0]}`);
          }
        }

        showValidationErrorToast(errorMessages, { title: 'Assembly Validation Error' });
        return;
      }
      
      // Save the assembly
      const success = await saveAssembly();
      
      if (success) {
        // Navigate back to assemblies list
        router.push('/assemblies');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      showErrorToast({ error: error instanceof Error ? error.message : 'An error occurred', duration: 5000 });
    }
  };
  
  // Handle cancel button click
  const handleCancel = () => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        resetForm();
        router.push('/assemblies');
      }
    } else {
      router.push('/assemblies');
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading assembly data...</span>
      </div>
    );
  }
  
  return (
    <div className={className}>
      {/* Header with actions */}
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={handleCancel} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Assemblies
        </Button>
        
        <Button 
          onClick={onSubmit}
          disabled={isSaving || (!isDirty && isEditing)}
          className="flex items-center gap-2"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Assembly
            </>
          )}
        </Button>
      </div>
      
      <FormProvider {...form}>
        <Form {...form}>
          <form className="space-y-6">
            <Tabs defaultValue="details" className="w-full">
              <TabsList className="w-full justify-start mb-4">
                <TabsTrigger value="details" className="flex-1 max-w-[200px]">Assembly Details</TabsTrigger>
                <TabsTrigger value="parts" className="flex-1 max-w-[200px]">Parts Required</TabsTrigger>
                <TabsTrigger value="stock" className="flex-1 max-w-[200px]">Stock Status</TabsTrigger>
              </TabsList>
              
              {/* Assembly Details Tab */}
              <TabsContent value="details" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {/* Assembly Code */}
                      <FormField
                        control={form.control as any}
                        name="assemblyCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assembly Code</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter assembly code" 
                                {...field} 
                                value={field.value || ''}
                                onChange={(e) => handleFieldChange('assemblyCode', e.target.value)}
                                disabled={isEditing} // Cannot change code in edit mode
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {/* Assembly Name */}
                      <FormField
                        control={form.control as any}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assembly Name</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter assembly name" 
                                {...field} 
                                value={field.value || ''}
                                onChange={(e) => handleFieldChange('name', e.target.value)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    {/* Description */}
                    <FormField
                      control={form.control as any}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter assembly description" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleFieldChange('description', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="grid grid-cols-2 gap-4">
                      {/* Status */}
                      <FormField
                        control={form.control as any}
                        name="status"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Status</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={(value) => handleFieldChange('status', value as any)}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {assemblyStatusOptions.map((status) => (
                                  <SelectItem key={status} value={status}>
                                    {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {/* Version */}
                      <FormField
                        control={form.control as any}
                        name="version"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Version</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={1}
                                {...field}
                                value={field.value || 1}
                                onChange={(e) => handleFieldChange('version', parseInt(e.target.value) || 1)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    {/* Is Top Level Assembly */}
                    <FormField
                      control={form.control as any}
                      name="isTopLevel"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Top Level Assembly</FormLabel>
                            <FormDescription>
                              This is a standalone assembly, not part of another assembly
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => handleFieldChange('isTopLevel', checked)}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Manufacturing Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Manufacturing Instructions */}
                    <FormField
                      control={form.control as any}
                      name="manufacturingInstructions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Manufacturing Instructions</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter manufacturing instructions (optional)" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleFieldChange('manufacturingInstructions', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {/* Estimated Build Time */}
                    <FormField
                      control={form.control as any}
                      name="estimatedBuildTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Build Time</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="e.g. 2 hours (optional)" 
                              {...field} 
                              value={field.value || ''}
                              onChange={(e) => handleFieldChange('estimatedBuildTime', e.target.value)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Parts Required Tab */}
              <TabsContent value="parts" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Add Parts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <PartSelector />
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Parts Required</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <PartRequirementsList />
                  </CardContent>
                </Card>
              </TabsContent>
              
              {/* Stock Status Tab */}
              <TabsContent value="stock" className="space-y-6">
                <StockSummary />
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </FormProvider>
    </div>
  );
} 