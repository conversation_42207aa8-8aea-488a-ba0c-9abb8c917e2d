import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { addSetting, fetchSettings, handleMongoDBError } from '@/app/services/mongodb';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching settings
 * @param request - The incoming request with query parameters
 * @returns JSON response with settings and pagination info
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/settings - Fetching settings');
    const searchParams = request.nextUrl.searchParams;

    // --- Parse Query Parameters ---
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), MAX_LIMIT);

    // Sorting
    const sortField = searchParams.get('sortField') || 'key';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;
    const sort = { [sortField]: sortOrder };

    // Filtering
    const filter: any = {};

    // Group filter
    const group = searchParams.get('group');
    if (group) {
      filter.group = group;
    }

    // Data type filter
    const dataType = searchParams.get('dataType');
    if (dataType) {
      filter.dataType = dataType;
    }

    // Search term (for key or description)
    const search = searchParams.get('search');
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      filter.$or = [
        { key: searchRegex },
        { description: searchRegex }
      ];
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort,
      filter,
    };

    console.log(`[API] Calling fetchSettings service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchSettings(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchSettings completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.settings,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/settings (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new setting
 * @param request - The incoming request with setting data
 * @returns JSON response with the newly created setting
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/settings - Creating new setting');
    const settingData = await request.json() as any;

    // Basic validation
    if (!settingData || typeof settingData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid setting data provided' }, { status: 400 });
    }

    // Validate required fields based on the setting schema
    if (!settingData.key) {
      return NextResponse.json({ data: null, error: 'Missing required field: key' }, { status: 400 });
    }

    if (!settingData.value) {
      return NextResponse.json({ data: null, error: 'Missing required field: value' }, { status: 400 });
    }

    if (!settingData.dataType) {
      return NextResponse.json({ data: null, error: 'Missing required field: dataType' }, { status: 400 });
    }

    if (!settingData.lastModifiedBy) {
      return NextResponse.json({ data: null, error: 'Missing required field: lastModifiedBy' }, { status: 400 });
    }

    console.log(`[API] Calling addSetting service with data: ${JSON.stringify(settingData)}`);

    // Call the addSetting service function
    const savedSetting = await addSetting(settingData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service addSetting completed in ${duration}ms`);

    return NextResponse.json({ data: savedSetting, error: null, meta: { duration } }, { status: 201 }); // 201 Created
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/settings (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
