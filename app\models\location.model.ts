import mongoose, { Schema, Document, Types, Model } from 'mongoose';

// Interface for Location document
export interface ILocation extends Document {
  _id: Types.ObjectId;
  warehouseId: Types.ObjectId; // Reference to warehouses._id
  name: string; // Unique, human-readable code (e.g., "A5-R2-S3", "BIN-1024", "HARDENING-AREA-1")
  description?: string | null; // Optional longer description
  locationType: 'Bin' | 'Shelf' | 'Floor Area' | 'Staging' | 'Production Area' | 'Quality Control' | 'Shipping' | 'Receiving';
  capacity?: {
    maxWeightKg?: number;
    volumeM3?: number;
  } | null;
  isActive: boolean; // Whether this location is currently active/usable
  createdAt: Date;
  updatedAt: Date;
}

// Schema for capacity sub-document
const CapacitySchema = new Schema({
  maxWeightKg: { 
    type: Number, 
    min: [0, 'Maximum weight cannot be negative'],
    required: false 
  },
  volumeM3: { 
    type: Number, 
    min: [0, 'Volume cannot be negative'],
    required: false 
  }
}, { _id: false });

// Main Location Schema
const LocationSchema: Schema<ILocation> = new Schema({
  warehouseId: {
    type: Schema.Types.ObjectId,
    ref: 'Warehouse',
    required: [true, 'Warehouse ID is required'],
    index: true
  },
  name: {
    type: String,
    required: [true, 'Location name is required'],
    trim: true,
    maxlength: [100, 'Location name cannot exceed 100 characters'],
    index: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: null,
    required: false
  },
  locationType: {
    type: String,
    required: [true, 'Location type is required'],
    enum: {
      values: ['Bin', 'Shelf', 'Floor Area', 'Staging', 'Production Area', 'Quality Control', 'Shipping', 'Receiving'],
      message: 'Location type must be one of the predefined values'
    },
    index: true
  },
  capacity: {
    type: CapacitySchema,
    default: null,
    required: false
  },
  isActive: {
    type: Boolean,
    default: true,
    required: true,
    index: true
  }
}, { 
  timestamps: true,
  collection: 'locations'
});

// CRITICAL INDEXES for performance
// Compound index to ensure unique location names within a warehouse
LocationSchema.index(
  { warehouseId: 1, name: 1 }, 
  { 
    unique: true,
    name: 'locations_warehouse_name_unique',
    background: true,
    comment: 'Ensures unique location names within each warehouse'
  }
);

// Index for finding active locations in a warehouse
LocationSchema.index(
  { warehouseId: 1, isActive: 1 }, 
  { 
    name: 'locations_warehouse_active',
    background: true,
    comment: 'Optimizes queries for active locations in a warehouse'
  }
);

// Index for location type queries
LocationSchema.index(
  { warehouseId: 1, locationType: 1, isActive: 1 }, 
  { 
    name: 'locations_warehouse_type_active',
    background: true,
    comment: 'Optimizes queries for specific location types within warehouses'
  }
);

// Text index for location search functionality
LocationSchema.index({
  'name': 'text',
  'description': 'text'
}, {
  name: 'locations_text_search',
  background: true,
  comment: 'Text index for location search functionality'
});

// Pre-save validation
LocationSchema.pre('save', function(this: ILocation & Document, next) {
  // Ensure location name is uppercase for consistency
  if (this.name) {
    this.name = this.name.toUpperCase();
  }
  next();
});

// Virtual for full location identifier (warehouse + location)
LocationSchema.virtual('fullIdentifier').get(function(this: ILocation) {
  return `${this.warehouseId}-${this.name}`;
});

// Ensure virtuals are included in JSON output
LocationSchema.set('toJSON', { virtuals: true });
LocationSchema.set('toObject', { virtuals: true });

// Create the model with proper TypeScript typing
const Location: Model<ILocation> = mongoose.models?.Location as mongoose.Model<ILocation> || mongoose.model<ILocation>('Location', LocationSchema);

export { LocationSchema, Location };
export default Location;
