"use client";

import Header from '@/app/components/layout/Header';
import { UnifiedCard } from '@/app/components/layout';
import { useAppContext } from '@/app/contexts/AppContext';
import { useTheme } from '@/app/contexts/ThemeContext';
import { asSuppliersResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { Supplier } from '@/app/types/user';
import { getApiUrl } from '@/app/utils/env';
import { motion } from 'framer-motion';
import { AlertCircle, Building2, Edit, ExternalLink, Loader2, Mail, Phone, Plus, Star, Tag, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { showNetworkErrorToast } from '@/app/components/feedback';

/**
 * Suppliers page component
 * Displays a list of suppliers with their contact information and associated products
 * Allows users to add, edit, and delete suppliers
 */
export default function SuppliersPage() {
  const { products } = useAppContext();
  const { theme } = useTheme();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch suppliers from the API
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(getApiUrl('/api/suppliers'));

        if (!response.ok) {
          throw new Error(`Error fetching suppliers: ${response.status}`);
        }

        const data = asSuppliersResponse(await response.json());

        if (hasApiError(data)) {
          throw new Error(extractApiError(data) || 'Unknown error occurred');
        }

        setSuppliers(data.data || []);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch suppliers';
        setError(errorMessage);
        showNetworkErrorToast(fetchSuppliers, { customMessage: errorMessage });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuppliers();
  }, []);

  const handleAddSupplier = () => {
    console.log("Add supplier clicked");
    // Add supplier logic here
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Suppliers" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium text-foreground">Suppliers & Manufacturers</h2>

          <UnifiedCard
            variant="action"
            icon={<Plus size={16} />}
            label="Add Supplier"
            color="yellow"
            onClick={handleAddSupplier}
            className="pl-3 pr-4 py-2 rounded-full"
          />
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
            <span className="ml-3 text-muted-foreground">Loading suppliers...</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-64">
            <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">Error Loading Suppliers</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
        ) : suppliers.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64">
            <Building2 className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No Suppliers Found</h3>
            <p className="text-muted-foreground">Get started by adding your first supplier.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {suppliers.map((supplier) => (
              <UnifiedCard
                key={supplier._id}
                variant="base"
                icon={<Building2 size={20} className="text-gray-700 dark:text-gray-300" />}
                color="yellow"
                title={supplier.name}
                subtitle={`ID: ${supplier.supplier_id}`}
                onViewDetails={() => console.log(`View products for ${supplier.name}`)}
                viewDetailsText="View Products"
              >
                <div className="flex justify-end mb-4">
                  <div className="flex space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-gray-400 dark:text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                    >
                      <Edit size={16} />
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400"
                    >
                      <Trash2 size={16} />
                    </motion.button>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Phone size={14} className="mr-2" />
                    <span>{supplier.phone}</span>
                  </div>

                  <div className="flex items-center text-sm text-muted-foreground">
                    <Mail size={14} className="mr-2" />
                    <span>{supplier.email}</span>
                  </div>

                  <div className="flex items-start text-sm text-muted-foreground">
                    <Building2 size={14} className="mr-2 mt-1" />
                    <span>{supplier.address}</span>
                  </div>

                  <div className="flex items-center text-sm text-muted-foreground">
                    <Star size={14} className="mr-2" />
                    <span>Rating: {supplier.rating !== null ? supplier.rating : 'N/A'}</span>
                  </div>
                </div>

                {supplier.specialty && supplier.specialty.length > 0 && (
                  <div className="mt-4">
                    <div className="text-sm text-muted-foreground mb-2 flex items-center">
                      <Tag size={14} className="mr-2" />
                      <span>Specialties:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {supplier.specialty.map((spec, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-1 bg-muted rounded-md text-xs"
                        >
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-6 flex space-x-2">
                  <button className="flex-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-300 py-2 rounded-lg text-sm flex items-center justify-center">
                    <Phone size={14} className="mr-2" />
                    <span>Contact</span>
                  </button>

                  <button className="flex-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-300 py-2 rounded-lg text-sm flex items-center justify-center">
                    <ExternalLink size={14} className="mr-2" />
                    <span>View Products</span>
                  </button>
                </div>
              </UnifiedCard>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}