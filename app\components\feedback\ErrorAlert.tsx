/**
 * ErrorAlert Component
 * Standardized inline error alerts for forms and components
 * Built on top of the existing Alert component with consistent styling
 */

"use client";

import React from 'react';
import { AlertCircle, AlertTriangle, Info, X } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/app/components/data-display/alert';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { ErrorAlertProps } from '@/app/types/error.types';
import { extractErrorMessage, isTestError, normalizeError } from '@/app/utils/error.utils';

/**
 * ErrorAlert component for inline error display
 * Uses the existing Alert component for consistency
 */
export function ErrorAlert({
  error,
  variant = 'destructive',
  dismissible = false,
  onDismiss,
  className,
}: ErrorAlertProps) {
  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);
  
  // Use warning variant for test errors
  const alertVariant = isTest ? 'warning' : variant;
  
  // Get appropriate icon based on variant
  const getIcon = () => {
    switch (alertVariant) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'destructive':
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  // Get appropriate title based on variant
  const getTitle = () => {
    if (isTest) {
      return 'Test Error';
    }
    
    switch (alertVariant) {
      case 'warning':
        return 'Warning';
      case 'info':
        return 'Information';
      case 'destructive':
      default:
        return 'Error';
    }
  };

  // Format error message for display
  const formatMessage = (message: string) => {
    // Handle multi-line error messages
    const lines = message.split('\n').filter(line => line.trim());
    
    if (lines.length <= 1) {
      return <span>{message}</span>;
    }

    return (
      <div>
        {lines.map((line, index) => (
          <div key={index} className="mb-1 last:mb-0">
            {line}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Alert variant={alertVariant} className={cn('relative', className)}>
      {getIcon()}
      
      <div className="flex-1">
        <AlertTitle>{getTitle()}</AlertTitle>
        <AlertDescription>
          {formatMessage(errorMessage)}
          
          {isTest && (
            <div className="mt-2 text-xs opacity-75">
              This is an intentional test error to verify error handling.
            </div>
          )}
        </AlertDescription>
      </div>

      {dismissible && onDismiss && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-transparent"
          onClick={onDismiss}
          aria-label="Dismiss error"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </Alert>
  );
}

/**
 * Compact variant of ErrorAlert for smaller spaces
 */
export function ErrorAlertCompact({
  error,
  variant = 'destructive',
  dismissible = false,
  onDismiss,
  className,
}: ErrorAlertProps) {
  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);
  const alertVariant = isTest ? 'warning' : variant;

  return (
    <Alert 
      variant={alertVariant} 
      className={cn('py-2 px-3', className)}
    >
      <AlertCircle className="h-3 w-3" />
      
      <div className="flex-1 min-w-0">
        <AlertDescription className="text-xs leading-tight">
          {errorMessage}
        </AlertDescription>
      </div>

      {dismissible && onDismiss && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 ml-2 hover:bg-transparent"
          onClick={onDismiss}
          aria-label="Dismiss error"
        >
          <X className="h-2 w-2" />
        </Button>
      )}
    </Alert>
  );
}

export default ErrorAlert;
