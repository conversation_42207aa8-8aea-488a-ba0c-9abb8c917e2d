"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, TrendingUp, AlertCircle, ArrowRight } from 'lucide-react';
import { useTheme } from '@/app/contexts/ThemeContext';

interface CapacityData {
  department: string;
  currentCapacity: number;
  maxCapacity: number;
  bottleneck: boolean;
}

interface ProductionCapacityProps {
  capacityData: CapacityData[];
  forecastAccuracy: number;
  productionTrend: 'increasing' | 'decreasing' | 'stable';
  bottleneckImpact: number;
}

const ProductionCapacity: React.FC<ProductionCapacityProps> = ({
  capacityData,
  forecastAccuracy,
  productionTrend,
  bottleneckImpact
}) => {
  const { theme } = useTheme();

  // Guard against undefined or empty data
  if (!capacityData || capacityData.length === 0) {
    // Render a placeholder or loading state, or return null
    // For now, just return null to avoid errors
    return null;
  }

  // Calculate overall capacity utilization
  const overallUtilization = capacityData.reduce((sum, item) => sum + (item.currentCapacity / item.maxCapacity), 0) / capacityData.length * 100;

  // Identify bottlenecks
  const bottlenecks = capacityData.filter(item => item.bottleneck);

  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-lg font-medium text-foreground">Production Capacity</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-6 h-6 bg-muted rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={14} className="text-muted-foreground" />
        </motion.div>
      </div>

      <div className="flex-1 overflow-hidden flex flex-col space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 flex-1 min-h-0">
          {/* Overall Capacity Utilization */}
          <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
            <h4 className="font-medium text-foreground mb-2 flex items-center text-sm">
              <BarChart size={16} className="mr-2 text-purple-600 dark:text-purple-400" />
              Capacity Utilization
            </h4>
            <div className="flex items-center justify-center py-1">
              <div className="relative w-20 h-20">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    className="text-muted"
                    strokeWidth="10"
                    stroke="currentColor"
                    fill="transparent"
                    r="40"
                    cx="50"
                    cy="50"
                  />
                  <circle
                    className={`${
                      overallUtilization > 90 ? 'text-red-500 dark:text-red-400' :
                      overallUtilization > 75 ? 'text-orange-500 dark:text-orange-400' :
                      'text-green-500 dark:text-green-400'
                    }`}
                    strokeWidth="10"
                    strokeDasharray={`${overallUtilization * 2.51} 251`}
                    strokeLinecap="round"
                    stroke="currentColor"
                    fill="transparent"
                    r="40"
                    cx="50"
                    cy="50"
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-foreground">{Math.round(overallUtilization)}%</span>
                </div>
              </div>
            </div>
            <div className="mt-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Forecast Accuracy</span>
                <span className="font-medium text-foreground">{forecastAccuracy}%</span>
              </div>
              <div className="flex items-center justify-between text-xs mt-1">
                <span className="text-muted-foreground">Production Trend</span>
                <span className={`font-medium flex items-center ${
                  productionTrend === 'increasing' ? 'text-green-600 dark:text-green-400' :
                  productionTrend === 'decreasing' ? 'text-red-600 dark:text-red-400' :
                  'text-blue-600 dark:text-blue-400'
                }`}>
                  {productionTrend.charAt(0).toUpperCase() + productionTrend.slice(1)}
                  {productionTrend === 'increasing' && <TrendingUp size={14} className="ml-1" />}
                </span>
              </div>
            </div>
          </div>

          {/* Department Capacity */}
          <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
            <h4 className="font-medium text-foreground mb-2 text-sm">Department Capacity</h4>
            <div className="space-y-2 overflow-y-auto max-h-32">
              {capacityData.map((item) => (
                <div key={item.department} className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className={`${item.bottleneck ? 'font-medium text-red-600 dark:text-red-400 flex items-center' : 'text-muted-foreground'}`}>
                      {item.bottleneck && <AlertCircle size={12} className="mr-1" />}
                      {item.department}
                    </span>
                    <span className="font-medium text-foreground">
                      {Math.round(item.currentCapacity / item.maxCapacity * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full ${
                        item.bottleneck ? 'bg-red-500 dark:bg-red-600' :
                        (item.currentCapacity / item.maxCapacity) > 0.9 ? 'bg-orange-500 dark:bg-orange-600' :
                        'bg-blue-500 dark:bg-blue-600'
                      }`}
                      style={{ width: `${(item.currentCapacity / item.maxCapacity) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottleneck Analysis */}
        <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
          <h4 className="font-medium text-foreground mb-2 flex items-center text-sm">
            <AlertCircle size={16} className="mr-2 text-red-500 dark:text-red-400" />
            Bottleneck Analysis
          </h4>
          {bottlenecks.length > 0 ? (
            <div className="overflow-y-auto max-h-28">
              <div className="mb-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Estimated Impact</span>
                  <span className="text-xs font-medium text-red-600 dark:text-red-400">-{bottleneckImpact}% Production</span>
                </div>
              </div>
              <div className="space-y-2">
                {bottlenecks.map((item) => (
                  <div key={item.department} className="bg-background p-2 rounded-lg border border-red-200 dark:border-red-900/30">
                    <div className="flex justify-between items-center">
                      <h5 className="font-medium text-foreground text-xs">{item.department}</h5>
                      <span className="px-1.5 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 rounded-full text-xs">Bottleneck</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Operating at {Math.round(item.currentCapacity / item.maxCapacity * 100)}% capacity
                    </p>
                    <div className="mt-2 flex space-x-1">
                      <button className="flex-1 text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-1 px-2 rounded hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                        Optimize
                      </button>
                      <button className="flex-1 text-xs bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 py-1 px-2 rounded hover:bg-green-100 dark:hover:bg-green-900/50 transition-colors">
                        Add Capacity
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <p className="text-xs text-muted-foreground">No bottlenecks detected in the production line</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductionCapacity;