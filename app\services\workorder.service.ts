import { cache, invalidatePattern } from '@/app/lib/cache';
import { connectToDatabase } from '@/app/lib/mongodb';
import { monitorDatabaseOperation } from '@/app/lib/n1-detection';
import { WorkOrder } from '@/app/models/workOrder.model';

/**
 * Optimized Work Orders Service
 * Uses aggregation pipelines instead of populate() to eliminate N+1 queries
 */

interface WorkOrderOptions {
  page?: number;
  limit?: number;
  sort?: any;
  filter?: any;
}

/**
 * Fetch work orders with optimized aggregation pipeline
 * Replaces the old fetchWorkOrders function from mongodb.ts
 */
export async function getAllWorkOrders(options: WorkOrderOptions = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 },
    filter = {}
  } = options;

  const cacheKey = `workorders:${JSON.stringify({ page, limit, sort, filter })}`;
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached) {
    return cached;
  }

  await connectToDatabase();
  
  const skip = (page - 1) * limit;

  const pipeline = [
    // Match stage - apply filters early
    { $match: filter },
    
    // Sort early to optimize index usage
    { $sort: sort },
    
    // Facet for pagination and data
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: limit },
          
          // Optimized lookups with minimal projections
          {
            $lookup: {
              from: 'users',
              localField: 'assignedTo',
              foreignField: '_id',
              as: 'assignedUser',
              pipeline: [
                { $project: { username: 1, first_name: 1, last_name: 1, email: 1 } }
              ]
            }
          },
          {
            $lookup: {
              from: 'assemblies',
              localField: 'assemblyId',
              foreignField: '_id',
              as: 'assembly',
              pipeline: [
                { $project: { assemblyCode: 1, name: 1 } }
              ]
            }
          },
          {
            $lookup: {
              from: 'products',
              localField: 'productId',
              foreignField: '_id',
              as: 'product',
              pipeline: [
                { $project: { productCode: 1, name: 1 } }
              ]
            }
          },
          {
            $lookup: {
              from: 'parts',
              localField: 'partIdToManufacture',
              foreignField: '_id',
              as: 'partToManufacture',
              pipeline: [
                { $project: { partNumber: 1, name: 1, description: 1 } }
              ]
            }
          },
          
          // Transform the results
          {
            $addFields: {
              assignedTo: { $arrayElemAt: ['$assignedUser', 0] },
              assembly: { $arrayElemAt: ['$assembly', 0] },
              product: { $arrayElemAt: ['$product', 0] },
              partToManufacture: { $arrayElemAt: ['$partToManufacture', 0] }
            }
          },
          
          // Remove temporary arrays
          {
            $unset: ['assignedUser', 'assembly', 'product', 'partToManufacture']
          },
          
          // Project final fields
          {
            $project: {
              woNumber: 1,
              assemblyId: 1,
              partIdToManufacture: 1,
              productId: 1,
              quantity: 1,
              status: 1,
              priority: 1,
              dueDate: 1,
              startDate: 1,
              assignedTo: 1,
              assembly: 1,
              product: 1,
              partToManufacture: 1,
              notes: 1,
              completedAt: 1,
              createdAt: 1,
              updatedAt: 1
            }
          }
        ],
        
        totalCount: [
          { $count: 'count' }
        ]
      }
    }
  ];

  return monitorDatabaseOperation('aggregate', 'workorders', pipeline, async () => {
    const result = await WorkOrder.aggregate(pipeline).exec();
    
    const workOrders = result[0]?.data || [];
    const totalCount = result[0]?.totalCount[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    const response = {
      data: workOrders,
      pagination: {
        totalCount,
        totalPages,
        currentPage: page,
        limit
      }
    };

    // Cache the result for 5 minutes
    cache.set(cacheKey, response, 300);
    
    return response;
  });
}

/**
 * Get a single work order by woNumber with optimized lookup
 */
export async function getWorkOrderByNumber(woNumber: string) {
  const cacheKey = `workorder:${woNumber}`;
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached) {
    return cached;
  }

  await connectToDatabase();

  const pipeline = [
    { $match: { woNumber } },
    
    // Optimized lookups
    {
      $lookup: {
        from: 'users',
        localField: 'assignedTo',
        foreignField: '_id',
        as: 'assignedUser',
        pipeline: [
          { $project: { username: 1, first_name: 1, last_name: 1, email: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'assemblies',
        localField: 'assemblyId',
        foreignField: '_id',
        as: 'assembly',
        pipeline: [
          { $project: { assemblyCode: 1, name: 1, description: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'products',
        localField: 'productId',
        foreignField: '_id',
        as: 'product',
        pipeline: [
          { $project: { productCode: 1, name: 1, description: 1 } }
        ]
      }
    },
    {
      $lookup: {
        from: 'parts',
        localField: 'partIdToManufacture',
        foreignField: '_id',
        as: 'partToManufacture',
        pipeline: [
          { $project: { partNumber: 1, name: 1, description: 1, unitOfMeasure: 1 } }
        ]
      }
    },
    
    // Transform results
    {
      $addFields: {
        assignedTo: { $arrayElemAt: ['$assignedUser', 0] },
        assembly: { $arrayElemAt: ['$assembly', 0] },
        product: { $arrayElemAt: ['$product', 0] },
        partToManufacture: { $arrayElemAt: ['$partToManufacture', 0] }
      }
    },
    
    // Remove temporary arrays
    {
      $unset: ['assignedUser']
    }
  ];

  return monitorDatabaseOperation('aggregate', 'workorder', pipeline, async () => {
    const result = await WorkOrder.aggregate(pipeline).exec();
    const workOrder = result[0] || null;

    if (workOrder) {
      // Cache for 10 minutes
      cache.set(cacheKey, workOrder, 600);
    }

    return workOrder;
  });
}

/**
 * Create a new work order
 */
export async function createWorkOrder(workOrderData: any) {
  await connectToDatabase();

  return monitorDatabaseOperation('create', 'workorder', workOrderData, async () => {
    const workOrder = new WorkOrder(workOrderData);
    const savedWorkOrder = await workOrder.save();

    // Invalidate related cache entries
    invalidatePattern('workorders:*');

    return savedWorkOrder;
  });
}

/**
 * Update a work order
 */
export async function updateWorkOrder(woNumber: string, updateData: any) {
  await connectToDatabase();

  return monitorDatabaseOperation('update', 'workorder', { woNumber, updateData }, async () => {
    const updatedWorkOrder = await (WorkOrder.findOneAndUpdate as any)(
      { woNumber },
      updateData,
      { new: true, runValidators: true }
    );

    if (updatedWorkOrder) {
      // Invalidate cache
      cache.delete(`workorder:${woNumber}`);
      invalidatePattern('workorders:*');
    }

    return updatedWorkOrder;
  });
}

/**
 * Search work orders with optimized aggregation
 */
export async function searchWorkOrders(searchTerm: string, options: WorkOrderOptions = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 }
  } = options;

  await connectToDatabase();
  
  const skip = (page - 1) * limit;
  const searchRegex = { $regex: searchTerm, $options: 'i' };

  const pipeline = [
    {
      $match: {
        $or: [
          { woNumber: searchRegex },
          { notes: searchRegex },
          { status: searchRegex },
          { priority: searchRegex }
        ]
      }
    },
    { $sort: sort },
    
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: limit },
          
          // Same optimized lookups as getAllWorkOrders
          {
            $lookup: {
              from: 'users',
              localField: 'assignedTo',
              foreignField: '_id',
              as: 'assignedUser',
              pipeline: [{ $project: { username: 1, first_name: 1, last_name: 1, email: 1 } }]
            }
          },
          {
            $lookup: {
              from: 'assemblies',
              localField: 'assemblyId',
              foreignField: '_id',
              as: 'assembly',
              pipeline: [{ $project: { assemblyCode: 1, name: 1 } }]
            }
          },
          
          {
            $addFields: {
              assignedTo: { $arrayElemAt: ['$assignedUser', 0] },
              assembly: { $arrayElemAt: ['$assembly', 0] }
            }
          },
          
          { $unset: ['assignedUser'] }
        ],
        
        totalCount: [{ $count: 'count' }]
      }
    }
  ];

  return monitorDatabaseOperation('aggregate', 'workorders_search', pipeline, async () => {
    const result = await WorkOrder.aggregate(pipeline).exec();
    
    const workOrders = result[0]?.data || [];
    const totalCount = result[0]?.totalCount[0]?.count || 0;

    return {
      data: workOrders,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  });
}

// Error handling function
export function handleMongoDBError(error: any) {
  console.error('WorkOrder Service Error:', error);
  
  if (error.code === 11000) {
    return { message: 'Work order number already exists', status: 409 };
  }
  
  if (error.name === 'ValidationError') {
    const messages = Object.values(error.errors).map((err: any) => err.message);
    return { message: messages.join(', '), status: 400 };
  }
  
  return { message: 'Internal server error', status: 500 };
}
