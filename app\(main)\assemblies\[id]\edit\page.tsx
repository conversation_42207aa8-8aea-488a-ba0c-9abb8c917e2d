import React from 'react';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import AssemblyFormContent from './AssemblyFormContent';

/**
 * Interface for route parameters including the assembly ID
 * In Next.js 15, params is a Promise
 */
interface EditAssemblyPageProps {
  params: Promise<{
    id: string;
  }>;
}

/**
 * Edit Assembly page component - allows editing an existing assembly
 */
export default async function EditAssemblyPage({ params }: EditAssemblyPageProps) {
  // Await params to comply with Next.js 15 requirements
  const resolvedParams = await params;
  console.log("[EditAssemblyPage] Received params:", JSON.stringify(resolvedParams));

  const assemblyId = resolvedParams?.id;
  console.log("[EditAssemblyPage] Extracted assemblyId type: " + typeof assemblyId + ", value: " + String(assemblyId));

  if (!assemblyId) {
    console.error("[EditAssemblyPage] assemblyId is missing or invalid.");
    // Optionally, render an error message or redirect
    return <div>Error: Assembly ID is missing.</div>;
  }

  return (
    <AssemblyFormProvider assemblyId={assemblyId}>
      <AssemblyFormContent />
    </AssemblyFormProvider>
  );
}