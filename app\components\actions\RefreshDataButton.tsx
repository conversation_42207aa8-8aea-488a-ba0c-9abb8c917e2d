'use client';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface RefreshDataButtonProps {
  onRefresh: () => Promise<void>;
  label?: string;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showTooltip?: boolean;
  tooltipText?: string;
}

/**
 * A button component for refreshing data with loading state
 */
export function RefreshDataButton({
  onRefresh,
  label,
  className,
  variant = 'outline',
  size = 'default',
  showTooltip = true,
  tooltipText = 'Refresh Data',
}: RefreshDataButtonProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const button = (
    <Button
      variant={variant}
      size={size}
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={cn(className)}
    >
      <RefreshCw
        className={cn(
          'h-4 w-4',
          isRefreshing && 'animate-spin',
          label && 'mr-2'
        )}
      />
      {label}
    </Button>
  );

  if (!showTooltip) {
    return button;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {button}
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
