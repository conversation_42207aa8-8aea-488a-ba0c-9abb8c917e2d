import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { addBatch, fetchBatches, handleMongoDBError } from '@/app/services/mongodb';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching batches
 * @param request - The incoming request with query parameters
 * @returns JSON response with batches and pagination info
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/batches - Fetching batches');
    const searchParams = request.nextUrl.searchParams;

    // --- Parse Query Parameters ---
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), MAX_LIMIT);

    // Sorting
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;
    const sort = { [sortField]: sortOrder } as Record<string, 1 | -1>;

    // Filtering
    const filter: any = {};

    // Status filter
    const status = searchParams.get('status');
    if (status) {
      filter.status = status;
    }

    // Date range filters
    const startDateFrom = searchParams.get('startDateFrom');
    const startDateTo = searchParams.get('startDateTo');
    if (startDateFrom || startDateTo) {
      filter.startDate = {};
      if (startDateFrom) {
        filter.startDate.$gte = new Date(startDateFrom);
      }
      if (startDateTo) {
        filter.startDate.$lte = new Date(startDateTo);
      }
    }

    const endDateFrom = searchParams.get('endDateFrom');
    const endDateTo = searchParams.get('endDateTo');
    if (endDateFrom || endDateTo) {
      filter.endDate = {};
      if (endDateFrom) {
        filter.endDate.$gte = new Date(endDateFrom);
      }
      if (endDateTo) {
        filter.endDate.$lte = new Date(endDateTo);
      }
    }

    // Work order filter
    const workOrderId = searchParams.get('workOrderId');
    if (workOrderId) {
      filter.workOrderId = workOrderId;
    }

    // Item type filter
    const itemType = searchParams.get('itemType');
    if (itemType) {
      if (itemType === 'part') {
        filter.partId = { $exists: true };
        filter.assemblyId = { $exists: false };

        // Specific part filter
        const partId = searchParams.get('partId');
        if (partId) {
          filter.partId = partId;
        }
      } else if (itemType === 'assembly') {
        filter.assemblyId = { $exists: true };
        filter.partId = { $exists: false };

        // Specific assembly filter
        const assemblyId = searchParams.get('assemblyId');
        if (assemblyId) {
          filter.assemblyId = assemblyId;
        }
      }
    } else {
      // If no item type filter, check for specific part or assembly
      const partId = searchParams.get('partId');
      if (partId) {
        filter.partId = partId;
      }

      const assemblyId = searchParams.get('assemblyId');
      if (assemblyId) {
        filter.assemblyId = assemblyId;
      }
    }

    // Search term (for batchCode or notes)
    const search = searchParams.get('search');
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      filter.$or = [
        { batchCode: searchRegex },
        { notes: searchRegex }
      ];
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort,
      filter,
    };

    console.log(`[API] Calling fetchBatches service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchBatches(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchBatches completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.batches,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new batch
 * @param request - The incoming request with batch data
 * @returns JSON response with the newly created batch
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/batches - Creating new batch');
    const batchData = await request.json() as {
      userId?: string;
      quantityPlanned?: number;
      workOrderId?: string;
      assemblyId?: string;
      status?: string;
      [key: string]: any;
    };

    // Basic validation
    if (!batchData || typeof batchData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid batch data provided' }, { status: 400 });
    }

    // Extract userId from the request if available
    const userId = batchData.userId || null;
    delete batchData.userId; // Remove userId from batch data as it's not part of the batch model

    // Validate required fields based on the batch schema
    if (!batchData.quantityPlanned) {
      return NextResponse.json({ data: null, error: 'Missing required field: quantityPlanned' }, { status: 400 });
    }

    if (!batchData.workOrderId) {
      return NextResponse.json({ data: null, error: 'Missing required field: workOrderId' }, { status: 400 });
    }

    if (!batchData.status) {
      return NextResponse.json({ data: null, error: 'Missing required field: status' }, { status: 400 });
    } else {
      // Validate status if provided
      const validStatuses = ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold', 'quality_check'];
      if (!validStatuses.includes(batchData.status)) {
        return NextResponse.json({
          data: null,
          error: `Invalid status: ${batchData.status}. Valid statuses are: ${validStatuses.join(', ')}`
        }, { status: 400 });
      }
    }

    // Either partId or assemblyId must be provided
    if (!batchData.partId && !batchData.assemblyId) {
      return NextResponse.json({ data: null, error: 'Either partId or assemblyId must be provided' }, { status: 400 });
    }

    // Both partId and assemblyId should not be provided
    if (batchData.partId && batchData.assemblyId) {
      return NextResponse.json({ data: null, error: 'Only one of partId or assemblyId should be provided' }, { status: 400 });
    }

    console.log(`[API] Calling addBatch service with data: ${JSON.stringify(batchData)}`);

    // Call the addBatch service function with userId for logging
    const savedBatch = await addBatch(batchData, userId || undefined);

    const duration = Date.now() - startTime;
    console.log(`[API] Service addBatch completed in ${duration}ms`);

    return NextResponse.json({ data: savedBatch, error: null, meta: { duration } }, { status: 201 }); // 201 Created
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/batches (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
