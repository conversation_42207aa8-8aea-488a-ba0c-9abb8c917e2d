'use client';

import { useState, useEffect, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Loader2, Package, Plus } from 'lucide-react';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/app/components/navigation/dialog';
import { Button } from '@/app/components/forms/Button';
import { ProductFormFields } from './ProductFormFields';
import { ProductFormSchema, ProductFormData, defaultProductFormValues } from './ProductFormSchema';
import { showErrorToast, showSuccessToast } from '@/app/components/feedback';
import { getApiUrl } from '@/app/utils/apiUtils';
import { safeFetch } from '@/app/utils/safeFetch';
import { useTheme } from '@/app/contexts/ThemeContext';

/**
 * Props for ProductFormModal component
 */
interface ProductFormModalProps {
  /**
   * Whether the modal is open
   */
  isOpen: boolean;
  
  /**
   * Function to close the modal
   */
  onClose: () => void;
  
  /**
   * Function called when product is successfully created/updated
   */
  onSuccess?: () => void;
  
  /**
   * Product ID for editing (optional)
   */
  productId?: string;
  
  /**
   * Mode of the form
   */
  mode?: 'create' | 'edit';
}

/**
 * Category type for dropdown
 */
interface Category {
  _id: string;
  name: string;
  description?: string;
}

/**
 * Assembly type for dropdown
 */
interface Assembly {
  _id: string;
  name: string;
  assemblyCode: string;
}

/**
 * Part type for dropdown
 */
interface Part {
  _id: string;
  name: string;
  partNumber: string;
}

/**
 * ProductFormModal Component
 * Floating modal for creating and editing products
 * Follows the same pattern as AssemblyFormModal
 */
export function ProductFormModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  productId, 
  mode = 'create' 
}: ProductFormModalProps) {
  const { theme } = useTheme();
  
  // Form state
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [parts, setParts] = useState<Part[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<ProductFormData>({
    resolver: zodResolver(ProductFormSchema) as any, // Temporary fix for type mismatch
    defaultValues: defaultProductFormValues,
    mode: 'onChange',
    criteriaMode: 'all',
  });

  const { handleSubmit, reset, formState: { errors, isDirty } } = form;

  // Fetch dropdown data
  const fetchDropdownData = useCallback(async () => {
    setIsLoadingData(true);
    try {
      // Fetch categories
      const categoriesResult = await safeFetch(getApiUrl('/api/categories'));
      if (categoriesResult.success) {
        const categoriesData = categoriesResult.data as { data?: Category[] };
        setCategories(categoriesData.data || []);
      }

      // Fetch assemblies
      const assembliesResult = await safeFetch(getApiUrl('/api/assemblies'));
      if (assembliesResult.success) {
        const assembliesData = assembliesResult.data as { data?: Assembly[] };
        setAssemblies(assembliesData.data || []);
      }

      // Fetch parts
      const partsResult = await safeFetch(getApiUrl('/api/parts'));
      if (partsResult.success) {
        const partsData = partsResult.data as { data?: Part[] };
        setParts(partsData.data || []);
      }
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
      showErrorToast({ error: 'Failed to load form data' });
    } finally {
      setIsLoadingData(false);
    }
  }, []);

  // Fetch product data for editing
  const fetchProductData = useCallback(async () => {
    if (mode !== 'edit' || !productId) return;

    setIsLoading(true);
    try {
      const result = await safeFetch(getApiUrl(`/api/products/${productId}`));
      if (result.success) {
        const productData = result.data as { data?: any };
        const product = productData.data;
        
        if (product) {
          reset({
            productCode: product.productCode || '',
            name: product.name || '',
            description: product.description || '',
            categoryId: product.categoryId || '',
            status: product.status || 'active',
            sellingPrice: product.sellingPrice || 0,
            assemblyId: product.assemblyId || null,
            partId: product.partId || null,
          });
        }
      } else {
        showErrorToast({ error: 'Failed to load product data' });
      }
    } catch (error) {
      console.error('Error fetching product data:', error);
      showErrorToast({ error: 'Failed to load product data' });
    } finally {
      setIsLoading(false);
    }
  }, [mode, productId, reset]);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDropdownData();
      if (mode === 'edit') {
        fetchProductData();
      } else {
        reset(defaultProductFormValues);
      }
    }
  }, [isOpen, mode, fetchDropdownData, fetchProductData, reset]);

  // Handle form submission
  const onSubmit = async (data: ProductFormData) => {
    setIsSaving(true);
    try {
      const url = mode === 'edit' 
        ? getApiUrl(`/api/products/${productId}`)
        : getApiUrl('/api/products');
      
      const method = mode === 'edit' ? 'PUT' : 'POST';
      
      const result = await safeFetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (result.success) {
        showSuccessToast(
          mode === 'edit' 
            ? 'Product updated successfully' 
            : 'Product created successfully'
        );
        
        if (onSuccess) {
          onSuccess();
        }
        
        handleClose();
      } else {
        showErrorToast({ error: result.error || 'Failed to save product' });
      }
    } catch (error) {
      console.error('Error saving product:', error);
      showErrorToast({ error: 'Failed to save product' });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSaving) {
      reset(defaultProductFormValues);
      onClose();
    }
  };

  // Handle form errors
  const onError = (errors: any) => {
    console.error('Form validation errors:', errors);
    showErrorToast({ error: 'Please fix the form errors before submitting' });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {mode === 'edit' ? 'Edit Product' : 'Create New Product'}
          </DialogTitle>
        </DialogHeader>

        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit, onError)} className="space-y-6">
            {/* Loading state */}
            {(isLoading || isLoadingData) && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading...</span>
              </div>
            )}

            {/* Form fields */}
            {!isLoading && !isLoadingData && (
              <ProductFormFields
                showAdvancedFields={true}
                categories={categories}
                assemblies={assemblies}
                parts={parts}
              />
            )}

            {/* Form actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSaving}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSaving || isLoading || isLoadingData}
                className="min-w-[120px]"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    {mode === 'edit' ? 'Update Product' : 'Create Product'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

/**
 * ProductFormButton Component
 * Button to open the product form modal
 */
export function ProductFormButton({ onSuccess }: { onSuccess?: () => void }) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
    closeModal();
  };

  return (
    <>
      <Button onClick={openModal}>
        <Plus className="h-4 w-4 mr-2" />
        New Product
      </Button>

      <ProductFormModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSuccess={handleSuccess}
        mode="create"
      />
    </>
  );
}
