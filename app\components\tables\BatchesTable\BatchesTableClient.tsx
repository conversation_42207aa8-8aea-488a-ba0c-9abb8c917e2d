"use client";

import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { createBatchesColumns, BatchColumnData, BatchesTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { Button } from "@/app/components/forms/Button";
import { useTheme } from "@/app/contexts/ThemeContext";
import {
    Layers
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { Batch, BatchesTableProps } from "./types";



/**
 * BatchesTableClient component
 * Client component for displaying batches in a table using DataTable
 */
export default function BatchesTableClient({
  simple = false,
  className,
  initialData,
  fetchData = true,
  woNumber,
  onBatchClick,
  onBatchEdit,
  onBatchDelete,
  onBatchCreate
}: BatchesTableProps) {
  const { theme } = useTheme();
  const [batches, setBatches] = useState<Batch[]>(initialData || []);
  const [isLoading, setIsLoading] = useState<boolean>(fetchData && !initialData);
  const [error, setError] = useState<string | null>(null);

  // Transform Batch data to BatchColumnData for DataTable
  const tableData: BatchColumnData[] = useMemo(() => {
    return batches.map(batch => ({
      _id: batch._id,
      batchCode: batch.batchCode,
      partId: batch.partId,
      assemblyId: batch.assemblyId,
      quantityPlanned: batch.quantityPlanned,
      quantityProduced: batch.quantityProduced,
      startDate: batch.startDate,
      endDate: batch.endDate,
      status: batch.status,
      notes: batch.notes,
      workOrderId: batch.workOrderId,
      createdAt: batch.createdAt,
      updatedAt: batch.updatedAt,
    }));
  }, [batches]);

  // Define actions for the table
  const actions: BatchesTableActions = useMemo(() => ({
    onView: onBatchClick ? (batch: BatchColumnData) => {
      // Convert back to Batch type for callback
      const originalBatch = batches.find(b => b._id === batch._id);
      if (originalBatch) onBatchClick(originalBatch);
    } : undefined,
    onEdit: onBatchEdit ? (batch: BatchColumnData) => {
      // Convert back to Batch type for callback
      const originalBatch = batches.find(b => b._id === batch._id);
      if (originalBatch) onBatchEdit(originalBatch);
    } : undefined,
    onDelete: onBatchDelete ? (batch: BatchColumnData) => {
      // Convert back to Batch type for callback
      const originalBatch = batches.find(b => b._id === batch._id);
      if (originalBatch) onBatchDelete(originalBatch);
    } : undefined,
    onCreate: onBatchCreate,
  }), [batches, onBatchClick, onBatchEdit, onBatchDelete, onBatchCreate]);

  // Create columns with actions
  const columns = useMemo(() => {
    return createBatchesColumns(actions);
  }, [actions]);

  // Fetch batches from API
  const fetchBatchesData = async () => {
    if (!fetchData) return;

    setIsLoading(true);
    setError(null);

    try {
      // Import getApiUrl for absolute URLs in production
      const { getApiUrl } = await import("@/app/utils/env");

      // Construct the API URL
      let endpoint = woNumber
        ? `/api/work-orders/${woNumber}/batches`
        : '/api/batches';

      // Use absolute URL for production compatibility
      const url = getApiUrl(endpoint);
      const response = await fetch(url);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch batches');
      }

      setBatches(result.data || []);
    } catch (err: any) {
      console.error('Error fetching batches:', err);
      setError(err.message || 'Failed to fetch batches');
      setBatches([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch batches when component mounts or woNumber changes
  useEffect(() => {
    if (fetchData) {
      fetchBatchesData();
    }
  }, [fetchData, woNumber]);

  // Update batches when initialData changes
  useEffect(() => {
    if (initialData) {
      setBatches(initialData);
    }
  }, [initialData]);

  // Handle error display
  if (error) {
    return (
      <div className={className}>
        <div className="p-8 text-center">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <Layers className="h-12 w-12 mx-auto mb-2" />
            <h3 className="text-lg font-medium mb-2">Error Loading Batches</h3>
            <p className="text-sm">{error}</p>
          </div>
          <Button onClick={fetchBatchesData} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Handle empty state
  if (!isLoading && batches.length === 0) {
    return (
      <div className={className}>
        <div className="p-8 text-center">
          <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">No Batches Found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {woNumber
              ? `There are no batches associated with work order ${woNumber}.`
              : 'There are no batches in the system yet.'}
          </p>
          {onBatchCreate && (
            <Button onClick={onBatchCreate}>
              Create Batch
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <StandardizedTable
        data={tableData}
        columns={columns}
        searchPlaceholder="Search batches..."
        enableSearch={true}
        enableViewToggle={false}
        {...(onBatchCreate && {
          renderActions: () => (
            <Button
              onClick={onBatchCreate}
              size="sm"
              className="bg-primary hover:bg-primary/90"
            >
              Create Batch
            </Button>
          )
        })}
        // FIXED: Updated from legacy tableProps pattern to direct props pattern
        enableSorting={true}
        enableFiltering={!simple}
        enableGlobalSearch={false}
        enablePagination={true}
        enableColumnVisibility={false}
        mobileDisplayMode="cards"
        density={simple ? "compact" : "normal"}
        initialPagination={{ pageIndex: 0, pageSize: 10 }}
        pageSizeOptions={[10, 20, 50, 100]}
        caption={`${simple ? 'Simple' : 'Detailed'} batches table with ${batches.length} items`}
        isLoading={isLoading}
        onRowClick={(batch: unknown) => {
          // Convert back to Batch type for callback
          const typedBatch = batch as BatchColumnData;
          const originalBatch = batches.find(b => b._id === typedBatch._id);
          if (originalBatch && onBatchClick) {
            onBatchClick(originalBatch);
          }
        }}
      />
    </div>
  );
}
