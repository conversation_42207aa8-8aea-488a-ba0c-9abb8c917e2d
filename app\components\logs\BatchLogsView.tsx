'use client';

import { Skeleton } from '@/app/components/data-display/skeleton';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { useTheme } from '@/app/contexts/ThemeContext';
import { format, formatDistanceToNow } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import {
    AlertCircle,
    Calendar,
    CheckCircle,
    Clock,
    FileText,
    Info,
    Loader2,
    RefreshCw,
    User,
    XCircle
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface BatchLog {
  _id: string;
  batchId: string;
  timestamp: string | Date;
  event: string;
  userId: string | {
    _id: string;
    username: string;
    fullName?: string;
  };
  details?: string;
  createdAt: string | Date;
  updatedAt: string | Date;
}

interface BatchLogsViewProps {
  batchId: string;
  className?: string;
  limit?: number;
  showRefresh?: boolean;
}

export default function BatchLogsView({
  batchId,
  className,
  limit = 10,
  showRefresh = true
}: BatchLogsViewProps) {
  const { theme } = useTheme();
  const [logs, setLogs] = useState<BatchLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch batch logs
  const fetchBatchLogs = async () => {
    if (!batchId) return;
    
    try {
      setIsRefreshing(true);
      const response = await fetch(`/api/batches/${batchId}/logs?limit=${limit}`);

      if (!response.ok) {
        throw new Error(`Error fetching batch logs: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setLogs(data.data || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching batch logs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load batch logs');
      toast.error('Failed to load batch logs');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Fetch logs on mount and when batchId changes
  useEffect(() => {
    if (batchId) {
      fetchBatchLogs();
    }
  }, [batchId]);

  // Get icon for log event
  const getEventIcon = (event: string) => {
    const eventLower = event.toLowerCase();
    
    if (eventLower.includes('created') || eventLower.includes('added')) {
      return <FileText className="h-4 w-4 text-blue-500" />;
    } else if (eventLower.includes('updated') || eventLower.includes('changed')) {
      return <Info className="h-4 w-4 text-amber-500" />;
    } else if (eventLower.includes('completed') || eventLower.includes('approved')) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (eventLower.includes('cancelled') || eventLower.includes('rejected') || eventLower.includes('failed')) {
      return <XCircle className="h-4 w-4 text-red-500" />;
    } else if (eventLower.includes('status')) {
      return <AlertCircle className="h-4 w-4 text-purple-500" />;
    } else {
      return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Get username from userId
  const getUserName = (userId: string | { _id: string; username: string; fullName?: string }) => {
    if (typeof userId === 'string') {
      return 'Unknown User';
    }
    return userId.fullName || userId.username || 'Unknown User';
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Clock className="mr-2 h-5 w-5 text-muted-foreground" />
            Batch Activity Log
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-start space-x-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Clock className="mr-2 h-5 w-5 text-muted-foreground" />
            Batch Activity Log
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-2" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchBatchLogs}
              className="flex items-center"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg flex items-center">
          <Clock className="mr-2 h-5 w-5 text-muted-foreground" />
          Batch Activity Log
        </CardTitle>
        {showRefresh && (
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchBatchLogs}
            disabled={isRefreshing}
            className="h-8 w-8 p-0"
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span className="sr-only">Refresh</span>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {logs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <FileText className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">No activity logs found for this batch</p>
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border" />
            
            {/* Timeline events */}
            <div className="space-y-6 relative">
              <AnimatePresence>
                {logs.map((log, index) => (
                  <motion.div
                    key={log._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="relative pl-10"
                  >
                    {/* Timeline dot */}
                    <div className="absolute left-0 top-1 h-8 w-8 rounded-full bg-background border-2 border-border flex items-center justify-center">
                      {getEventIcon(log.event)}
                    </div>
                    
                    {/* Event content */}
                    <div className="bg-muted/40 rounded-lg p-3">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-1">
                        <h4 className="font-medium text-sm">{log.event}</h4>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3 mr-1" />
                          <time dateTime={new Date(log.timestamp).toISOString()}>
                            {format(new Date(log.timestamp), 'MMM d, yyyy h:mm a')}
                          </time>
                          <span className="mx-1">•</span>
                          <span title={format(new Date(log.timestamp), 'PPpp')}>
                            {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                      
                      {log.details && (
                        <p className="text-sm text-muted-foreground mt-1">{log.details}</p>
                      )}
                      
                      <div className="flex items-center mt-2">
                        <User className="h-3 w-3 text-muted-foreground mr-1" />
                        <span className="text-xs text-muted-foreground">
                          {getUserName(log.userId)}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
