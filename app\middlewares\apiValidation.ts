/**
 * Enhanced API validation middleware
 * Addresses CodeRabbit feedback on input validation, error handling, and type safety
 */

import { NextRequest, NextResponse } from 'next/server';
import { parseJsonWithValidation, createValidationErrorResponse } from '@/app/lib/validation-utils';

export interface ApiValidationOptions {
  requireAuth?: boolean;
  requireContentType?: boolean;
  maxRequestSize?: number;
  allowedMethods?: string[];
  rateLimit?: {
    windowMs: number;
    maxRequests: number;
  };
}

/**
 * Enhanced API validation middleware with comprehensive error handling
 */
export function withApiValidation(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: ApiValidationOptions = {}
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const startTime = Date.now();
    
    try {
      // Method validation
      if (options.allowedMethods && !options.allowedMethods.includes(request.method)) {
        return NextResponse.json(
          {
            success: false,
            error: `Method ${request.method} not allowed`,
            allowedMethods: options.allowedMethods
          },
          { 
            status: 405,
            headers: {
              'Allow': options.allowedMethods.join(', ')
            }
          }
        );
      }

      // Content-Type validation for POST/PUT/PATCH requests
      if (['POST', 'PUT', 'PATCH'].includes(request.method) && options.requireContentType !== false) {
        const contentType = request.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          return createValidationErrorResponse(
            'Content-Type must be application/json',
            415
          );
        }
      }

      // Request size validation
      if (options.maxRequestSize) {
        const contentLength = request.headers.get('content-length');
        if (contentLength && parseInt(contentLength) > options.maxRequestSize) {
          return createValidationErrorResponse(
            `Request body too large. Maximum size: ${options.maxRequestSize} bytes`,
            413
          );
        }
      }

      // Execute the handler
      const response = await handler(request, ...args);
      
      // Add performance headers
      const duration = Date.now() - startTime;
      response.headers.set('X-Response-Time', `${duration}ms`);
      response.headers.set('X-API-Version', 'v3');
      
      return response;

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('[API Validation Middleware] Unhandled error:', error);
      
      // Return standardized error response
      return NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          code: 'INTERNAL_ERROR',
          timestamp: new Date().toISOString(),
          meta: { duration }
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware specifically for JSON body validation
 */
export function withJsonValidation(
  handler: (request: NextRequest, body: any, ...args: any[]) => Promise<NextResponse>,
  options: {
    required?: boolean;
    maxSize?: number;
    schema?: (data: any) => { isValid: boolean; error?: string };
  } = {}
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    try {
      // Skip JSON parsing for GET requests
      if (request.method === 'GET') {
        return handler(request, null, ...args);
      }

      const parseResult = await parseJsonWithValidation(request, {
        requireContentType: true,
        ...(options.maxSize && { maxSize: options.maxSize })
      });

      if (!parseResult.success) {
        return createValidationErrorResponse(
          parseResult.error!,
          parseResult.statusCode!
        );
      }

      // Schema validation if provided
      if (options.schema) {
        const validation = options.schema(parseResult.data);
        if (!validation.isValid) {
          return createValidationErrorResponse(validation.error!);
        }
      }

      return handler(request, parseResult.data, ...args);

    } catch (error) {
      console.error('[JSON Validation Middleware] Error:', error);
      return createValidationErrorResponse('Failed to process request');
    }
  };
}

/**
 * Rate limiting middleware (simple in-memory implementation)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: {
    windowMs: number;
    maxRequests: number;
    keyGenerator?: (request: NextRequest) => string;
  }
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const key = options.keyGenerator ? 
      options.keyGenerator(request) : 
      request.headers.get('x-forwarded-for') || 'unknown';
    
    const now = Date.now();
    const windowStart = now - options.windowMs;
    
    // Clean up old entries
    const entries = Array.from(rateLimitStore.entries());
    for (const [k, v] of entries) {
      if (v.resetTime < windowStart) {
        rateLimitStore.delete(k);
      }
    }
    
    const current = rateLimitStore.get(key) || { count: 0, resetTime: now + options.windowMs };
    
    if (current.count >= options.maxRequests && current.resetTime > now) {
      return NextResponse.json(
        {
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((current.resetTime - now) / 1000)
        },
        { 
          status: 429,
          headers: {
            'Retry-After': Math.ceil((current.resetTime - now) / 1000).toString(),
            'X-RateLimit-Limit': options.maxRequests.toString(),
            'X-RateLimit-Remaining': Math.max(0, options.maxRequests - current.count).toString()
          }
        }
      );
    }
    
    current.count++;
    rateLimitStore.set(key, current);
    
    return handler(request, ...args);
  };
}

/**
 * Security headers middleware
 */
export function withSecurityHeaders(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const response = await handler(request, ...args);
    
    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    return response;
  };
}

/**
 * Comprehensive API middleware combining all validations
 */
export function withComprehensiveValidation(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: ApiValidationOptions & {
    jsonValidation?: {
      required?: boolean;
      maxSize?: number;
      schema?: (data: any) => { isValid: boolean; error?: string };
    };
  } = {}
) {
  let wrappedHandler = handler;
  
  // Apply middlewares in reverse order (they wrap each other)
  wrappedHandler = withSecurityHeaders(wrappedHandler);
  
  if (options.rateLimit) {
    wrappedHandler = withRateLimit(wrappedHandler, options.rateLimit);
  }
  
  if (options.jsonValidation) {
    wrappedHandler = withJsonValidation(
      (req, body, ...args) => wrappedHandler(req, ...args),
      options.jsonValidation
    );
  }
  
  wrappedHandler = withApiValidation(wrappedHandler, options);
  
  return wrappedHandler;
}
