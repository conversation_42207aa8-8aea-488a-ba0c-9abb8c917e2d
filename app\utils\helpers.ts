/**
 * Common utility functions for the application
 */

/**
 * Format a date to a readable string
 * @param date The date to format
 * @returns Formatted date string
 */
/**
 * Formats a given date into a readable string (e.g., "Jan 1, 2023").
 *
 * @param {Date | string} date - The date to format, can be a Date object or a date string.
 * @returns {string} The formatted date string, or an empty string if the input is invalid.
 */
export const formatDate = (date: Date | string): string => {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format a currency value
 * @param value The value to format
 * @param currency The currency code (default: USD)
 * @returns Formatted currency string
 */
/**
 * Formats a number as a currency string (e.g., "$1,234.56").
 *
 * @param {number} value - The numeric value to format.
 * @param {string} [currency='USD'] - The currency code (e.g., 'USD', 'EUR'). Defaults to 'USD'.
 * @returns {string} The formatted currency string.
 */
export const formatCurrency = (value: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(value);
};

/**
 * Truncate a string to a specified length
 * @param str The string to truncate
 * @param length The maximum length
 * @returns Truncated string
 */
/**
 * Truncates a string to a specified maximum length, appending '...' if truncated.
 *
 * @param {string} str - The string to truncate.
 * @param {number} [length=50] - The maximum length of the string before truncation. Defaults to 50.
 * @returns {string} The truncated string, or the original string if it's shorter than the specified length. Returns an empty string if the input is invalid.
 */
export const truncateString = (str: string, length = 50): string => {
  if (!str) return '';
  return str.length > length ? `${str.substring(0, length)}...` : str;
};

/**
 * Generate a random ID
 * @returns Random ID string
 */
/**
 * Generates a simple pseudo-random ID string.
 * Note: This is not a cryptographically secure or universally unique ID. For robust unique IDs, consider using libraries like `uuid`.
 *
 * @returns {string} A pseudo-random string ID.
 */
export const generateId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

/**
 * Deep clone an object
 * @param obj The object to clone
 * @returns Cloned object
 */
/**
 * Creates a deep clone of an object using JSON stringification and parsing.
 * Note: This method has limitations (e.g., loses functions, Date objects become strings, undefined becomes null).
 * For more robust deep cloning, consider libraries like lodash's `cloneDeep`.
 *
 * @template T - The type of the object to clone.
 * @param {T} obj - The object to clone.
 * @returns {T} A deep clone of the input object.
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// Debounce function moved to app/lib/utils.ts for better organization
// Import it from there: import { debounce } from '@/app/lib/utils';
