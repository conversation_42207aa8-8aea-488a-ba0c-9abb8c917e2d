#!/usr/bin/env node

/**
 * PRODUCTION V3 SCHEMA MIGRATION
 * 
 * This script executes the complete V3 migration for the production database,
 * migrating all 128 parts from embedded inventory.stockLevels to the 
 * dedicated inventories collection.
 */

const mongoose = require('mongoose');

const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS',
  BATCH_SIZE: 20,
  DRY_RUN: process.env.DRY_RUN === 'true'
};

class ProductionV3Migration {
  constructor() {
    this.db = null;
    this.migratedParts = 0;
    this.createdRecords = 0;
    this.errorCount = 0;
    this.startTime = Date.now();
  }

  async connect() {
    try {
      await mongoose.connect(CONFIG.MONGODB_URI);
      this.db = mongoose.connection.db;
      console.log('✅ Connected to production database');
    } catch (error) {
      console.error('❌ Failed to connect:', error.message);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      console.log('✅ Disconnected from database');
    } catch (error) {
      console.error('❌ Error disconnecting:', error.message);
    }
  }

  async executeProductionMigration() {
    console.log('🚀 Starting Production V3 Schema Migration...\n');
    
    try {
      // Step 1: Validate pre-migration state
      await this.validatePreMigrationState();
      
      // Step 2: Clear any existing test data
      await this.clearExistingInventoryData();
      
      // Step 3: Execute migration in batches
      await this.migrateAllProductionParts();
      
      // Step 4: Validate post-migration state
      await this.validatePostMigrationState();
      
      // Step 5: Generate final report
      this.generateMigrationReport();
      
    } catch (error) {
      console.error('❌ Production migration failed:', error.message);
      throw error;
    }
  }

  async validatePreMigrationState() {
    console.log('🔍 Validating production database state...');
    
    const totalParts = await this.db.collection('parts').countDocuments();
    const partsWithStockLevels = await this.db.collection('parts').countDocuments({
      'inventory.stockLevels': { $exists: true }
    });
    const existingInventoryRecords = await this.db.collection('inventories').countDocuments();
    
    console.log(`📊 Pre-migration state:`);
    console.log(`   - Total parts: ${totalParts}`);
    console.log(`   - Parts with stockLevels: ${partsWithStockLevels}`);
    console.log(`   - Existing inventory records: ${existingInventoryRecords}`);
    
    if (partsWithStockLevels === 0) {
      throw new Error('No parts with stockLevels found - initial migration may not be complete');
    }
    
    if (partsWithStockLevels !== 128) {
      console.warn(`⚠️  Expected 128 parts, found ${partsWithStockLevels}`);
    }
    
    // Validate stock totals before migration
    const stockTotals = await this.db.collection('parts').aggregate([
      { $match: { 'inventory.stockLevels': { $exists: true } } },
      {
        $group: {
          _id: null,
          totalFinished: { $sum: '$inventory.stockLevels.finished' },
          totalRaw: { $sum: '$inventory.stockLevels.raw' },
          totalHardening: { $sum: '$inventory.stockLevels.hardening' },
          totalGrinding: { $sum: '$inventory.stockLevels.grinding' },
          totalRejected: { $sum: '$inventory.stockLevels.rejected' },
          totalAll: {
            $sum: {
              $add: [
                '$inventory.stockLevels.raw',
                '$inventory.stockLevels.hardening',
                '$inventory.stockLevels.grinding',
                '$inventory.stockLevels.finished',
                '$inventory.stockLevels.rejected'
              ]
            }
          }
        }
      }
    ]).toArray();
    
    this.originalStockTotals = stockTotals[0];
    console.log(`   - Original total stock: ${this.originalStockTotals.totalAll} units`);
    console.log(`   - Original finished stock: ${this.originalStockTotals.totalFinished} units`);
  }

  async clearExistingInventoryData() {
    console.log('🧹 Clearing existing inventory data...');
    
    if (!CONFIG.DRY_RUN) {
      const result = await this.db.collection('inventories').deleteMany({});
      console.log(`   - Cleared ${result.deletedCount} existing records`);
    } else {
      console.log('   - DRY RUN: Would clear existing inventory data');
    }
  }

  async migrateAllProductionParts() {
    console.log('📦 Migrating all production parts...');
    
    // Get all parts with stockLevels
    const parts = await this.db.collection('parts').find({
      'inventory.stockLevels': { $exists: true }
    }).toArray();
    
    console.log(`   - Found ${parts.length} parts to migrate`);
    console.log(`   - Expected inventory records: ${parts.length * 5}`);
    
    // Process in batches
    for (let i = 0; i < parts.length; i += CONFIG.BATCH_SIZE) {
      const batch = parts.slice(i, i + CONFIG.BATCH_SIZE);
      await this.migrateBatch(batch, i + 1);
    }
    
    console.log(`   ✅ Migration complete: ${this.migratedParts} parts, ${this.createdRecords} records`);
  }

  async migrateBatch(parts, startIndex) {
    const batchNumber = Math.ceil(startIndex / CONFIG.BATCH_SIZE);
    console.log(`   📦 Processing batch ${batchNumber} (${parts.length} parts)...`);
    
    const inventoryRecords = [];
    
    for (const part of parts) {
      try {
        const records = this.createInventoryRecords(part);
        inventoryRecords.push(...records);
        this.migratedParts++;
      } catch (error) {
        console.error(`     ❌ Error processing part ${part.partNumber}:`, error.message);
        this.errorCount++;
      }
    }
    
    // Insert batch of inventory records
    if (inventoryRecords.length > 0 && !CONFIG.DRY_RUN) {
      try {
        await this.db.collection('inventories').insertMany(inventoryRecords);
        this.createdRecords += inventoryRecords.length;
        console.log(`     ✅ Inserted ${inventoryRecords.length} inventory records`);
      } catch (error) {
        console.error(`     ❌ Error inserting batch:`, error.message);
        this.errorCount += inventoryRecords.length / 5;
      }
    } else if (CONFIG.DRY_RUN) {
      console.log(`     🔍 DRY RUN: Would insert ${inventoryRecords.length} inventory records`);
      this.createdRecords += inventoryRecords.length;
    }
  }

  createInventoryRecords(part) {
    const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
    const records = [];
    const now = new Date();
    
    for (const stockType of stockTypes) {
      const quantity = part.inventory.stockLevels[stockType] || 0;
      
      const record = {
        partId: part._id,
        warehouseId: part.inventory.warehouseId,
        stockType: stockType,
        quantity: quantity,
        lastUpdated: part.inventory.lastStockUpdate || now,
        safetyStockLevel: part.inventory.safetyStockLevel || 10,
        maximumStockLevel: part.inventory.maximumStockLevel || 1000,
        averageDailyUsage: part.inventory.averageDailyUsage || 5,
        abcClassification: part.inventory.abcClassification || 'C',
        createdAt: now,
        updatedAt: now
      };
      
      records.push(record);
    }
    
    return records;
  }

  async validatePostMigrationState() {
    console.log('🔍 Validating post-migration state...');
    
    const totalParts = await this.db.collection('parts').countDocuments();
    const partsWithStockLevels = await this.db.collection('parts').countDocuments({
      'inventory.stockLevels': { $exists: true }
    });
    const inventoryRecords = await this.db.collection('inventories').countDocuments();
    
    console.log(`📊 Post-migration state:`);
    console.log(`   - Total parts: ${totalParts}`);
    console.log(`   - Parts with embedded inventory: ${partsWithStockLevels}`);
    console.log(`   - Inventory records created: ${inventoryRecords}`);
    
    // Validate expected counts
    const expectedRecords = partsWithStockLevels * 5;
    if (inventoryRecords === expectedRecords) {
      console.log(`   ✅ Perfect! Expected ${expectedRecords} records, got ${inventoryRecords}`);
    } else {
      console.log(`   ⚠️  Expected ${expectedRecords} records, got ${inventoryRecords}`);
    }
    
    // Validate stock totals after migration
    const newStockTotals = await this.db.collection('inventories').aggregate([
      {
        $group: {
          _id: '$stockType',
          totalQuantity: { $sum: '$quantity' }
        }
      }
    ]).toArray();
    
    const stockTotalsByType = {};
    newStockTotals.forEach(item => {
      stockTotalsByType[item._id] = item.totalQuantity;
    });
    
    console.log(`   📊 Stock totals validation:`);
    console.log(`      - Finished: ${this.originalStockTotals.totalFinished} → ${stockTotalsByType.finished || 0}`);
    console.log(`      - Raw: ${this.originalStockTotals.totalRaw} → ${stockTotalsByType.raw || 0}`);
    console.log(`      - Hardening: ${this.originalStockTotals.totalHardening} → ${stockTotalsByType.hardening || 0}`);
    console.log(`      - Grinding: ${this.originalStockTotals.totalGrinding} → ${stockTotalsByType.grinding || 0}`);
    console.log(`      - Rejected: ${this.originalStockTotals.totalRejected} → ${stockTotalsByType.rejected || 0}`);
    
    // Test aggregation pipeline
    await this.testAggregationPipeline();
  }

  async testAggregationPipeline() {
    console.log('🔧 Testing aggregation pipeline...');
    
    try {
      const result = await this.db.collection('parts').aggregate([
        { $match: { 'inventory.stockLevels': { $exists: true } } },
        { $limit: 2 },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'partId',
            as: 'inventoryRecords'
          }
        },
        {
          $addFields: {
            reconstructedInventory: {
              currentStock: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'finished'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              totalStock: { $sum: '$inventoryRecords.quantity' }
            }
          }
        },
        {
          $project: {
            partNumber: 1,
            originalFinished: '$inventory.stockLevels.finished',
            reconstructedFinished: '$reconstructedInventory.currentStock',
            totalStock: '$reconstructedInventory.totalStock',
            recordCount: { $size: '$inventoryRecords' }
          }
        }
      ]).toArray();
      
      if (result.length > 0) {
        console.log(`   ✅ Aggregation pipeline working correctly`);
        result.forEach(part => {
          console.log(`      - ${part.partNumber}: Original(${part.originalFinished}) = Reconstructed(${part.reconstructedFinished})`);
        });
      }
      
    } catch (error) {
      console.error('   ❌ Aggregation pipeline test failed:', error.message);
    }
  }

  generateMigrationReport() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n📋 PRODUCTION MIGRATION REPORT');
    console.log('===============================');
    console.log(`✅ Parts migrated: ${this.migratedParts}`);
    console.log(`✅ Inventory records created: ${this.createdRecords}`);
    console.log(`❌ Errors: ${this.errorCount}`);
    console.log(`⏱️  Duration: ${Math.round(duration / 1000)}s`);
    console.log(`🔄 Mode: ${CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE'}`);
    
    if (this.errorCount === 0) {
      console.log('\n🎉 PRODUCTION MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('The inventory page should now display data correctly.');
    } else {
      console.log('\n⚠️  MIGRATION COMPLETED WITH ERRORS');
      console.log('Please review the errors above and re-run if necessary.');
    }
  }
}

// Main execution
async function main() {
  const migration = new ProductionV3Migration();
  
  try {
    await migration.connect();
    await migration.executeProductionMigration();
    process.exit(0);
  } catch (error) {
    console.error('💥 Production migration failed:', error.message);
    process.exit(1);
  } finally {
    await migration.disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  console.log('🚀 PRODUCTION V3 SCHEMA MIGRATION');
  console.log('==================================');
  console.log(`Database: ${CONFIG.MONGODB_URI}`);
  console.log(`Batch Size: ${CONFIG.BATCH_SIZE}`);
  console.log(`Dry Run: ${CONFIG.DRY_RUN}`);
  console.log('');
  
  main().catch(console.error);
}

module.exports = { ProductionV3Migration };
