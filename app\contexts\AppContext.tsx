"use client";

import React, {
    createContext,
    ReactNode,
    useContext,
    useEffect,
    useState,
    useCallback,
} from 'react';
// Import API utilities instead of direct MongoDB access
import {
    AssemblyItem,
    LogisticsInfo,
    OrderStatusCount,
    StockStatus,
    Product,
    HierarchicalComponent
} from '@/app/types';
import { Part } from '@/app/types/inventory';
import { getApiUrl } from '@/app/utils/env';

/**
 * Extended Part type for frontend use that includes additional fields
 * This combines the database Part structure with frontend-specific fields
 */
interface InventoryPart extends Omit<Part, 'createdAt' | 'updatedAt'> {
  // Frontend compatibility fields
  id: string; // Mapped from _id
  partNumber: string; // Already in Part but ensuring it's required
  businessName?: string | null; // Business name for the part

  // Additional frontend fields
  supplierManufacturer?: string;
  supplierId?: any; // Can be string or populated object
  category?: string;
  categoryId?: string;
  cost?: number;
  standardCost: number; // Changed from costPrice to match target schema
  abcClassification?: string | null; // NEW FIELD: ABC classification
  onHandValue?: number;
  demand?: 'High' | 'Medium' | 'Low';
  location?: string; // Top-level location for backward compatibility
  unitOfMeasure: string;

  // Date fields as strings for frontend
  createdAt: string;
  updatedAt: string;

  // Assembly-related fields
  isAssembly?: boolean;

  // Nested inventory object (canonical structure with new stockLevels support)
  inventory: {
    currentStock: number; // Primary stock count (finished goods ready for use/sale)
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
  };
}

/**
 * Type definition for the application context
 * Contains all the data and functions needed throughout the application
 */
interface AppContextType {
  /** List of all products in the inventory */
  products: InventoryPart[];
  /** Current stock status information */
  stockStatus: StockStatus;
  /** Order status counts by category */
  orderStatus: OrderStatusCount;
  /** Logistics information */
  logisticsInfo: LogisticsInfo;
  /** List of assembly items */
  assemblies: AssemblyItem[];
  /** Distribution of products by category */
  categoryDistribution: Record<string, number>;
  /** Distribution of products by supplier */
  supplierDistribution: Record<string, number>;
  /** Inventory value by category */
  inventoryValueByCategory: Record<string, number>;
  /** High demand items */
  highDemandItems: InventoryPart[];
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Sidebar expanded state */
  sidebarExpanded: boolean;
  /** Function to set sidebar expanded state */
  setSidebarExpanded: (expanded: boolean) => void;
  /** Current location */
  currentLocation: string;
  /** Function to set current location */
  setCurrentLocation: (location: string) => void;
  /** Time frame for data */
  timeFrame: string;
  /** Function to set time frame */
  setTimeFrame: (timeFrame: string) => void;
  /** Function to refresh data */
  refreshData: () => Promise<void>;
  /** Whether using mock data */
  isUsingMockData: boolean;

  // CRUD operations for products
  /** Add a new product */
  addProduct: (product: InventoryPart) => Promise<InventoryPart>;
  /** Update an existing product */
  updateProduct: (id: string, updates: Partial<InventoryPart>) => Promise<InventoryPart>;
  /** Delete a product */
  deleteProduct: (id: string) => Promise<boolean>;
  /** Get products with pagination */
  getProducts: (options?: { page?: number; limit?: number; search?: string }) => Promise<{
    products: InventoryPart[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>;

  // Pagination state for products
  productsPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  setProductsPagination: (pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }) => void;

  // New product-specific methods
  createProduct: (productData: any) => Promise<Product>;
  updateProductById: (id: string, productData: any) => Promise<Product>;
  getProductById: (id: string, includeAssembly?: boolean) => Promise<Product>;
}

/**
 * Create the application context with default values
 */
const AppContext = createContext<AppContextType | undefined>(undefined);

/**
 * Custom hook to use the application context
 * Throws an error if used outside of AppProvider
 */
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

/**
 * Provider component for the application context
 * Manages the global state of the application
 * Handles data fetching, state updates, and provides methods for CRUD operations
 */
export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State for data
  const [products, setProducts] = useState<InventoryPart[]>([]);
  const [stockStatus, setStockStatus] = useState<StockStatus>({
    inStock: 0,
    lowStock: 0,
    outOfStock: 0,
    totalItems: 0,
    total: 0,
    overstock: 0
  });
  const [orderStatus, setOrderStatus] = useState<OrderStatusCount>({
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0,
    total: 0,
    completed: 0,
    cancelled: 0
  });
  const [logisticsInfo, setLogisticsInfo] = useState<LogisticsInfo>({
    inTransit: 0,
    delivered: 0,
    delayed: 0,
    total: 0,
    pending: 0,
    totalShipments: 0
  });
  const [assemblies, setAssemblies] = useState<AssemblyItem[]>([]);

  // State for UI
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [currentLocation, setCurrentLocation] = useState('All Locations');
  const [timeFrame, setTimeFrame] = useState('This Month');
  const [isUsingMockData, setIsUsingMockData] = useState(false);

  // Pagination state for products
  const [productsPagination, setProductsPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Computed values
  const categoryDistribution = products.reduce((acc, product) => {
    const category = product.category || 'Uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const supplierDistribution = products.reduce((acc, product) => {
    const supplier = product.supplierManufacturer || 'Unknown';
    acc[supplier] = (acc[supplier] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const inventoryValueByCategory = products.reduce((acc, product) => {
    const category = product.category || 'Uncategorized';
    const value = (product.inventory?.currentStock || 0) * (product.cost || 0);
    acc[category] = (acc[category] || 0) + value;
    return acc;
  }, {} as Record<string, number>);

  const highDemandItems = products
    .filter((p) => p.demand === 'High')
    .sort((a, b) => (b.inventory?.currentStock || 0) - (a.inventory?.currentStock || 0))
    .slice(0, 5);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // V4 Schema: Fetch parts with aggregated inventory data
      let partsData = [];
      let useRealData = false;
      let connectionError = null;

      // Store the total count from pagination for later use
      let totalPartsCount = 0;

      try {
        // Fetch a reasonable amount of data for dashboard metrics
        const response = await fetch(getApiUrl('/api/parts?page=1&limit=50'));
        if (response.status !== 200) {
          console.warn('[AppContext] Parts API returned status:', response.status);
        }

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          result = JSON.parse(responseText);
        } catch (parseError: any) {
          throw new Error(`Failed to parse API response: ${parseError.message || 'Unknown parse error'}`);
        }

        if (!response.ok) {
          console.error('[FRONTEND DEBUG] API response not OK:', response.status, result.error);
          throw new Error(result.error || 'Failed to fetch parts');
        }

        partsData = result.data || [];
        // Store the total count from pagination
        totalPartsCount = result.pagination?.totalCount || partsData.length;

        if (partsData.length > 0) {
          // V4 Schema: Parts API now includes aggregated inventory data
          useRealData = true;
          setIsUsingMockData(false);
        } else {
          connectionError = 'No parts found in database. Using sample data instead.';
        }
      } catch (error) {
        console.error('[AppContext] Error fetching parts:', error);
        connectionError = `Failed to fetch parts: ${error instanceof Error ? error.message : String(error)}`;
      }

      if (!useRealData) {
        console.log('[FRONTEND DEBUG] Attempting to use real data despite initial issues');
        // Instead of immediately falling back to mock data, try to fetch assemblies directly
        try {
          console.log('[FRONTEND DEBUG] Attempting direct fetch of assemblies');
          const assembliesResponse = await fetch(getApiUrl('/api/assemblies'));

          // Check content type before parsing
          const contentType = assembliesResponse.headers.get('content-type') || '';
          if (!contentType.includes('application/json')) {
            const body = await assembliesResponse.text();
            console.error('[FRONTEND DEBUG] Expected JSON, got:', body.substring(0, 200));
            throw new Error('Server returned HTML instead of JSON');
          }

          if (assembliesResponse.ok) {
            const assembliesResult = await assembliesResponse.json();
            if (assembliesResult.data && assembliesResult.data.length > 0) {
              console.log(`[FRONTEND DEBUG] Successfully fetched ${assembliesResult.data.length} assemblies directly`);
              // We have assemblies data, so we can use real data
              useRealData = true;
              setIsUsingMockData(false);
            } else {
              throw new Error('No assemblies found in direct fetch');
            }
          } else {
            const errorText = await assembliesResponse.text();
            console.error('[FRONTEND DEBUG] Assemblies API error:', errorText);
            throw new Error(`Assemblies fetch failed with status ${assembliesResponse.status}`);
          }
        } catch (directFetchError) {
          console.error('[AppContext] Direct fetch of assemblies failed:', directFetchError);
          setIsUsingMockData(true);
          setError(connectionError || 'Unable to fetch data from MongoDB. Using sample data instead.');

          // Initialize with empty data (will use mock data from UI components)
          setProducts([]);
          setStockStatus({ total: 0, lowStock: 0, outOfStock: 0, overstock: 0, inStock: 0, totalItems: 0 });
          setOrderStatus({ pending: 0, processing: 0, shipped: 0, delivered: 0, total: 0, completed: 0, cancelled: 0 });
          setLogisticsInfo({ inTransit: 0, delivered: 0, delayed: 0, total: 0, pending: 0, totalShipments: 0 });
          setAssemblies([]);

          console.warn('[AppContext] Using mock data due to database connectivity issues');

          // Stop here, don't try to fetch other data
          setIsLoading(false);
          return;
        }
      }
      console.log('[FRONTEND DEBUG] Using real data because useRealData is true');

      // Continue with the rest of the data fetching logic for real data
      // Fetch suppliers data
      let suppliersData;
      try {
        const response = await fetch(getApiUrl('/api/suppliers'));
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch suppliers');
        }

        suppliersData = result.data;
        console.log(`Fetched ${suppliersData?.length || 0} suppliers`);
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        throw new Error(`Failed to fetch suppliers: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Fetch assemblies data
      let assembliesData = [];
      try {
        console.log('[FRONTEND DEBUG] Fetching assemblies from API');
        const response = await fetch(getApiUrl('/api/assemblies'));
        console.log('[FRONTEND DEBUG] Assemblies API response status:', response.status);

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw assemblies API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            result = JSON.parse(responseText);
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] Assemblies JSON parse error:', parseError);
            throw new Error(`Failed to parse assemblies API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting assemblies response text:', textError);
          throw new Error(`Failed to get assemblies response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          console.error('[FRONTEND DEBUG] Assemblies API error:', result.error);
          throw new Error(result.error || 'Failed to fetch assemblies');
        }

        assembliesData = result.data || [];

        // Minimal logging for performance
        if (assembliesData.length === 0) {
          console.warn('[AppContext] No assemblies found in API response');
        }
      } catch (error) {
        console.error('[AppContext] Error fetching assemblies:', error);
        // Continue with empty assemblies data instead of throwing
        assembliesData = [];
      }

      // Fetch purchase orders data for order status card
      let purchaseOrdersData = [];
      try {
        const response = await fetch(getApiUrl('/api/purchase-orders'));

        // Add detailed error handling for JSON parsing
        let result;
        try {
          const responseText = await response.text();
          console.log('[FRONTEND DEBUG] Raw purchase orders API response text:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

          try {
            result = JSON.parse(responseText);
          } catch (parseError: any) {
            console.error('[FRONTEND DEBUG] Purchase orders JSON parse error:', parseError);
            throw new Error(`Failed to parse purchase orders API response: ${parseError.message || 'Unknown parse error'}. Raw response: ${responseText.substring(0, 100)}...`);
          }
        } catch (textError: any) {
          console.error('[FRONTEND DEBUG] Error getting purchase orders response text:', textError);
          throw new Error(`Failed to get purchase orders response text: ${textError.message || 'Unknown text error'}`);
        }

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch purchase orders');
        }

        purchaseOrdersData = result.data || [];
        console.log(`Fetched ${purchaseOrdersData.length} purchase orders`);
      } catch (error) {
        console.error('Error fetching purchase orders:', error);
        // Don't use mock data, just throw the error to be handled in the main catch block
        throw new Error(`Failed to fetch purchase orders: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Map parts and inventory data
      console.log('Mapping fetched data to application state');

      // Add defensive check before mapping partsData
      console.log('[FRONTEND DEBUG] ===== STARTING APPCONTEXT MAPPING =====');
      console.log('[FRONTEND DEBUG] partsData array length:', partsData.length);

      const mappedProducts = Array.isArray(partsData) ? partsData.map((part: any, index: number) => {
        console.log(`[FRONTEND DEBUG] ===== MAPPING PART ${index + 1}/${partsData.length} =====`);
        console.log(`[FRONTEND DEBUG] Part ID: ${part._id}`);
        try { // Add try...catch around individual part mapping
          // ENHANCED DEBUG LOGGING: Log complete raw part structure
          console.log(`[FRONTEND DEBUG] Part ${part._id} Complete Raw Data:`, JSON.stringify(part, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} has inventory object?`, !!part.inventory);
          console.log(`[FRONTEND DEBUG] Part ${part._id} inventory structure:`, JSON.stringify(part.inventory, null, 2));

          // V4 Schema: Use aggregated inventory data from parts API response
          console.log(`[FRONTEND DEBUG] Part ${part._id} Aggregated inventory data:`, JSON.stringify({
            currentStock: part.currentStock,
            stockLevels: part.stockLevels
          }));

          // Find the corresponding supplier
          const supplier = part.supplierId || {};
          console.log(`[FRONTEND DEBUG] Part ${part._id} Supplier Raw:`, JSON.stringify(supplier));

          // Calculate cost and value (using default values if not available)
          const cost = part.costPrice || part.cost || 0;
          const supplierName = typeof part.supplierId === 'object' ? part.supplierId?.name ?? '' : '';

          // V4 Schema: Use aggregated currentStock from parts API response
          // This represents total stock across all locations and stock types
          const currentStock = part.currentStock ?? 0;

          // Calculate onHandValue using the aggregated currentStock
          const onHandValue = currentStock * cost;
          console.log(`[FRONTEND DEBUG] Part ${part._id} Calculated: cost=${cost}, currentStock=${currentStock} (from aggregated API response), reorderLevel=${part.reorderLevel}, onHandValue=${onHandValue}, supplierName='${supplierName}'`);

          const mappedProduct: InventoryPart = {
            // Core Part fields
            _id: part._id.toString(),
            id: part._id.toString(),
            partNumber: part.partNumber || '',
            name: part.name || '',
            businessName: part.businessName || null, // NEW FIELD: Human-readable business name
            description: part.description || '',
            technicalSpecs: part.technicalSpecs || '',
            isManufactured: part.isManufactured || false,
            reorderLevel: part.reorderLevel ?? null,
            status: part.status || 'active',

            // V4 Schema: Inventory object from aggregated API response
            inventory: {
              // Use aggregated currentStock from API response
              currentStock: currentStock,
              // Include aggregated stockLevels from API response
              stockLevels: part.stockLevels || { raw: 0, hardening: 0, grinding: 0, finished: currentStock, rejected: 0 },
              warehouseId: '', // No single warehouse in V4 schema - parts can be in multiple locations
              // V4 Schema: Safety stock and planning data are in part master data
              safetyStockLevel: part.safetyStockLevel ?? 0,
              maximumStockLevel: part.maximumStockLevel ?? 0,
              averageDailyUsage: part.averageDailyUsage ?? 0,
              abcClassification: 'C', // Default value
              lastStockUpdate: null // Will be calculated from inventory records
            },

            // Frontend-specific fields
            supplierManufacturer: supplierName,
            supplierId: part.supplierId,
            category: part.category || 'Uncategorized',
            categoryId: part.categoryId,
            cost,
            standardCost: cost, // Changed from costPrice to match target schema
            abcClassification: part.abcClassification || null, // NEW FIELD: ABC classification
            onHandValue,
            demand: part.demand || 'Low',
            location: '', // V4 Schema: No single location - parts can be in multiple locations
            unitOfMeasure: part.unitOfMeasure || 'pcs',

            // Timestamps
            createdAt: part.createdAt || new Date().toISOString(),
            updatedAt: part.updatedAt || new Date().toISOString(),

            // Assembly fields
            isAssembly: part.isAssembly || false
          };

          // ENHANCED DEBUG LOGGING: Log mapped product structure
          console.log(`[FRONTEND DEBUG] ===== MAPPED INVENTORY PART STRUCTURE =====`);
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped InventoryPart:`, JSON.stringify(mappedProduct, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped has inventory?`, !!mappedProduct.inventory);
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped inventory structure:`, JSON.stringify(mappedProduct.inventory, null, 2));
          console.log(`[FRONTEND DEBUG] Part ${part._id} Mapped inventory.currentStock:`, mappedProduct.inventory?.currentStock);
          console.log(`[FRONTEND DEBUG] Part ${part._id} NO MORE TOP-LEVEL currentStock - using only nested inventory.currentStock`);
          console.log(`[FRONTEND DEBUG] ===== END MAPPED INVENTORY PART =====`);

          return mappedProduct;
        } catch (mapError) {
          console.error(`[FRONTEND DEBUG] Error mapping part index ${index}, ID: ${part._id}`, mapError);
          console.error(`[FRONTEND DEBUG] Failing Part Raw Data:`, JSON.stringify(part));
          // Return null or a default object to avoid breaking the map, filter out later
          return null;
        }
      }).filter(p => p !== null) as InventoryPart[] : []; // Filter out nulls from failed mappings

      setProducts(mappedProducts);

      // ENHANCED DEBUG LOGGING: Log final products state
      console.log('[FRONTEND DEBUG] ===== FINAL APPCONTEXT PRODUCTS STATE =====');
      console.log(`[FRONTEND DEBUG] Set ${mappedProducts.length} products in state`);
      if (mappedProducts.length > 0) {
        const firstProduct = mappedProducts[0];
        if (firstProduct) {
          console.log('[FRONTEND DEBUG] First product in state:', JSON.stringify(firstProduct, null, 2));
          console.log('[FRONTEND DEBUG] First product has inventory?', !!firstProduct.inventory);
          console.log('[FRONTEND DEBUG] First product inventory structure:', JSON.stringify(firstProduct.inventory, null, 2));
          console.log('[FRONTEND DEBUG] First product currentStock (top-level):', firstProduct.inventory?.currentStock);
          console.log('[FRONTEND DEBUG] First product inventory.currentStock:', firstProduct.inventory?.currentStock);
        }
      }
      console.log('[FRONTEND DEBUG] ===== END APPCONTEXT PRODUCTS STATE =====');

      // Calculate stock status from real data using v4 schema structure
      if (mappedProducts.length > 0) {
        // V4 Schema: Use aggregated currentStock from API response
        const getFinishedStock = (p: any) => p.inventory?.currentStock ?? 0;

        const outOfStock = mappedProducts.filter(p => getFinishedStock(p) === 0).length;
        const lowStock = mappedProducts.filter(p => getFinishedStock(p) > 0 && getFinishedStock(p) <= (p.reorderLevel ?? 0)).length;
        const overstock = mappedProducts.filter(p => getFinishedStock(p) > ((p.reorderLevel ?? 0) * 2)).length;

        // Use the total count from pagination that we stored earlier
        console.log('[FRONTEND DEBUG] Using total count from pagination:', totalPartsCount);

        setStockStatus({
          inStock: totalPartsCount - outOfStock - lowStock, // Assuming inStock is total - outOfStock - lowStock
          lowStock,
          outOfStock,
          totalItems: totalPartsCount,
          total: totalPartsCount, // Keep for backward compatibility if needed
          overstock
        });
      }

      // Map assemblies data if available
      if (Array.isArray(assembliesData) && assembliesData.length > 0) {
        try {
          console.log('[FRONTEND DEBUG] Mapping assemblies data, count:', assembliesData.length);

          const mappedAssemblies = assembliesData.map((assembly: any) => {
            try {
              console.log('[FRONTEND DEBUG] Processing assembly:', assembly.name, 'with', assembly.parts?.length || 0, 'parts');

            // Ensure assembly.parts is an array before mapping
            const assemblyParts = Array.isArray(assembly.parts) ? assembly.parts.map((part: any) => {
              try {
                // Log the part structure for debugging
                console.log('[FRONTEND DEBUG] Assembly part raw data:', part);

                // Safety check for part ID (supporting both new and legacy field names)
                // The part object can have different structures depending on the API response
                // 1. { partId: ObjectId } - New schema from MongoDB
                // 2. { partId: { _id, name, ... } } - Populated object from MongoDB
                // 3. { part_id: ObjectId } - Old schema from MongoDB
                // 4. { part_id: { _id, name, ... } } - Populated object from old schema

              console.log('[FRONTEND DEBUG] Part object structure:', {
                hasPartId: !!part.partId,
                hasPartIdObject: part.partId && typeof part.partId === 'object',
                hasPartIdId: part.partId && part.partId._id,
                hasPartIdName: part.partId && part.partId.name,
                hasPart_id: !!part.part_id,
                hasPart_idObject: part.part_id && typeof part.part_id === 'object',
                hasPart_idId: part.part_id && part.part_id._id,
                hasPart_idName: part.part_id && part.part_id.name
              });

              // Try to get the part ID from all possible locations
              let partIdObj = null;
              let partName = 'Unknown Part';

              // Check for partId (new schema)
              if (part.partId) {
                partIdObj = part.partId;
                // If partId is an object with name, use it
                if (typeof partIdObj === 'object' && partIdObj.name) {
                  partName = partIdObj.name;
                }
              }
              // If not found, check for part_id (old schema)
              else if (part.part_id) {
                partIdObj = part.part_id;
                // If part_id is an object with name, use it
                if (typeof partIdObj === 'object' && partIdObj.name) {
                  partName = partIdObj.name;
                }
              }

              if (!partIdObj) {
                console.warn('[FRONTEND DEBUG] Missing partId/part_id in assembly part');
                return {
                  id: 'unknown',
                  name: 'Unknown Part',
                  quantity: 0,
                  unit: 'pcs'
                };
              }

              return {
                id: typeof partIdObj === 'object' ? partIdObj._id || partIdObj.id : partIdObj,
                name: partName,
                quantity: part.quantity || 0,
                unit: part.unit || 'pcs'
              };
            } catch (partError) {
              console.error('[FRONTEND DEBUG] Error processing assembly part:', partError);
              return {
                id: 'error',
                name: 'Error Loading Part',
                quantity: 0,
                unit: 'pcs'
              };
            }
          }) : [];

          return {
            id: assembly._id,
            name: assembly.name || 'Unnamed Assembly',
            description: assembly.description || '',
            parts: assemblyParts,
            totalParts: assemblyParts.length,
            status: (assembly.status || 'active') as 'completed' | 'in_progress' | 'pending' | 'delayed',
            stage: assembly.isTopLevel ? 'Product' : 'Sub-Assembly',
            progress: assembly.status === 'active' ? 100 :
                     assembly.status === 'pending_review' ? 50 :
                     assembly.status === 'design_phase' ? 25 : 0,
            createdAt: assembly.createdAt || new Date().toISOString(),
            updatedAt: assembly.updatedAt || new Date().toISOString()
          };
        } catch (assemblyError) {
          console.error('[FRONTEND DEBUG] Error processing assembly:', assemblyError);
          return {
            id: assembly._id || 'error',
            name: 'Error Loading Assembly',
            description: '',
            parts: [],
            totalParts: 0,
            status: 'pending' as const,
            stage: 'Error',
            progress: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
        }
      });

      setAssemblies(mappedAssemblies);
      console.log('[FRONTEND DEBUG] Set assemblies in state:', mappedAssemblies.length);
    } catch (assembliesError) {
      console.error('[FRONTEND DEBUG] Error mapping assemblies:', assembliesError);
      setAssemblies([]);
    }
  } else {
    console.log('[FRONTEND DEBUG] No assemblies data to map');
    setAssemblies([]);
  }

  // Calculate order status from purchase orders
  if (Array.isArray(purchaseOrdersData) && purchaseOrdersData.length > 0) {
    const pending = purchaseOrdersData.filter(order => order.status === 'pending').length;
    const processing = purchaseOrdersData.filter(order => order.status === 'processing').length;
    const shipped = purchaseOrdersData.filter(order => order.status === 'shipped').length;
    const delivered = purchaseOrdersData.filter(order => order.status === 'delivered').length;
    const completed = purchaseOrdersData.filter(order => order.status === 'completed').length;
    const cancelled = purchaseOrdersData.filter(order => order.status === 'cancelled').length;

    setOrderStatus({
      pending,
      processing,
      shipped,
      delivered,
      completed,
      cancelled,
      total: purchaseOrdersData.length
    });
  }

  // Set logistics info (placeholder for now)
  setLogisticsInfo({
    inTransit: 0,
    delivered: 0,
    delayed: 0,
    total: 0,
    pending: 0,
    totalShipments: 0
  });

  console.log('[AppContext] Data fetch completed successfully');
} catch (error) {
  console.error('[AppContext] Error in fetchData:', error);
  setError(`Failed to fetch data: ${error instanceof Error ? error.message : String(error)}`);
  setIsUsingMockData(true);
} finally {
  setIsLoading(false);
}
};

  // Initial data fetch on mount
  useEffect(() => {
    fetchData();
  }, []);

  // Refresh data function
  const refreshData = useCallback(async () => {
    await fetchData();
  }, []);

  // CRUD operations for products
  const addProduct = async (product: InventoryPart): Promise<InventoryPart> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Adding product:', product);

      const response = await fetch(getApiUrl('/api/parts'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to add product');
      }

      console.log('[AppContext] Product added successfully:', result.data);

      // Refresh data to include the new product
      await fetchData();

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error adding product:', error);
      setError(`Failed to add product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProduct = async (id: string, updates: Partial<InventoryPart>): Promise<InventoryPart> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Updating product:', id, updates);

      const response = await fetch(getApiUrl(`/api/parts/${encodeURIComponent(id)}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update product');
      }

      console.log('[AppContext] Product updated successfully:', result.data);

      // Optimistic update: immediately update the local products state
      // This provides instant UI feedback instead of waiting for full data refresh
      setProducts(prevProducts =>
        prevProducts.map(product =>
          product._id === id ? { ...product, ...result.data } : product
        )
      );

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error updating product:', error);
      setError(`Failed to update product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Deleting product:', id);

      const response = await fetch(getApiUrl(`/api/parts/${encodeURIComponent(id)}`), {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete product');
      }

      console.log('[AppContext] Product deleted successfully');

      // Refresh data to reflect the deletion
      await fetchData();

      return true;
    } catch (error) {
      console.error('[AppContext] Error deleting product:', error);
      setError(`Failed to delete product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getProducts = async (options: { page?: number; limit?: number; search?: string } = {}) => {
    try {
      setIsLoading(true);
      const { page = 1, limit = 20, search = '' } = options;

      console.log('[AppContext] Getting products with options:', { page, limit, search });

      const url = new URL(getApiUrl('/api/parts'));
      url.searchParams.set('page', page.toString());
      url.searchParams.set('limit', limit.toString());
      if (search) {
        url.searchParams.set('search', search);
      }

      const response = await fetch(url.toString());
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch products');
      }

      console.log('[AppContext] Products fetched successfully:', result.data?.length || 0);

      // Update pagination state
      if (result.pagination) {
        setProductsPagination({
          page: result.pagination.page || page,
          limit: result.pagination.limit || limit,
          total: result.pagination.totalCount || 0,
          totalPages: result.pagination.totalPages || 0
        });
      }

      // FIXED: Update the main products state to ensure table synchronization
      if (result.data && Array.isArray(result.data)) {
        console.log('[AppContext] Updating main products state with fetched data');
        setProducts(result.data);
      }

      return {
        products: result.data || [],
        pagination: result.pagination || {
          page,
          limit,
          total: 0,
          totalPages: 0
        }
      };
    } catch (error) {
      console.error('[AppContext] Error getting products:', error);
      setError(`Failed to get products: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // New product-specific methods
  const createProduct = async (productData: any): Promise<Product> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Creating product:', productData);

      const response = await fetch(getApiUrl('/api/products'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create product');
      }

      console.log('[AppContext] Product created successfully:', result.data);

      // Refresh data to include the new product
      await fetchData();

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error creating product:', error);
      setError(`Failed to create product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProductById = async (id: string, productData: any): Promise<Product> => {
    try {
      setIsLoading(true);
      console.log('[AppContext] Updating product by ID:', id, productData);

      const response = await fetch(getApiUrl(`/api/products/${encodeURIComponent(id)}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update product');
      }

      console.log('[AppContext] Product updated successfully:', result.data);

      // Refresh data to reflect the changes
      await fetchData();

      return result.data;
    } catch (error) {
      console.error('[AppContext] Error updating product:', error);
      setError(`Failed to update product: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppContext.Provider
      value={{
        products,
        stockStatus,
        orderStatus,
        logisticsInfo,
        assemblies,
        categoryDistribution,
        supplierDistribution,
        inventoryValueByCategory,
        highDemandItems,
        isLoading,
        error,
        sidebarExpanded,
        setSidebarExpanded,
        currentLocation,
        setCurrentLocation,
        timeFrame,
        setTimeFrame,
        refreshData,
        isUsingMockData,
        addProduct,
        updateProduct,
        deleteProduct,
        getProducts,
        // New product-specific methods
        createProduct,
        updateProductById,
        getProductById: async (id: string, includeAssembly: boolean = true): Promise<Product> => {
          try {
            setIsLoading(true);
            console.log('[AppContext] Fetching product by ID:', id, 'includeAssembly:', includeAssembly);

            const url = new URL(getApiUrl(`/api/products/${encodeURIComponent(id)}`));
            if (includeAssembly) {
              url.searchParams.set('includeAssembly', 'true');
            }

            const response = await fetch(url.toString());
            const result = await response.json();

            if (!response.ok) {
              throw new Error(result.error || 'Failed to fetch product');
            }

            console.log('[AppContext] Product fetched successfully:', result.data);
            return result.data;
          } catch (error) {
            console.error('[AppContext] Error fetching product:', error);
            setError(`Failed to fetch product: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
          } finally {
            setIsLoading(false);
          }
        },

        // Centralized pagination state
        productsPagination,
        setProductsPagination,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// Duplicate useAppContext export removed - using the one defined earlier in the file
