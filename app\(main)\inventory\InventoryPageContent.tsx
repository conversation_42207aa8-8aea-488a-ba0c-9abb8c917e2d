'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useAppContext } from '@/app/contexts/AppContext';
import { Button } from '@/app/components/forms/Button';
import type { PartFormData } from '@/app/components/forms/PartForm';
import { PartForm } from '@/app/components/forms/PartForm';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { createInventoryColumns, InventoryColumnData, InventoryTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { Plus, RefreshCw, SlidersHorizontal } from 'lucide-react';
import { showErrorToast, showSuccessToast } from '@/app/components/feedback';
import { Badge } from '@/app/components/data-display/badge';

import {
    DEFAULT_FILTER_STATE,
    FilterState,
    InventoryFilter,
    applyFilters,
    loadFiltersFromStorage,
    saveFiltersToStorage
} from '@/app/components/inventory/filters';
import { createOrUpdatePartInventory } from '@/app/api-client/part.api';
import { debounce } from '@/app/lib/utils';
import { Product } from '@/app/types/inventory';
import { usePartInventoryDetails } from '@/app/hooks/usePartInventoryDetails';

// Define InventoryItem interface matching what InventoryTable expects
interface InventoryItem {
  _id: string;
  id: string;
  partNumber: string;
  name: string;
  businessName?: string | null;
  description: string;
  currentStock?: number; // Add currentStock at top level

  // NEW: Part Master Data Planning Parameters
  planningMethod?: string | null;        // Planning method (MRP, EOQ, etc.)
  safetyStockLevel?: number | null;      // Safety stock level for this part
  maximumStockLevel?: number | null;     // Maximum stock level for this part
  leadTimeDays?: number | null;          // Lead time in days
  averageDailyUsage?: number | null;     // Average daily usage

  inventory: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    currentStock?: number;
    warehouseId?: { _id: string; name: string; location?: string } | string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
  supplierId?: { _id: string; name: string } | string;
  unitOfMeasure?: string;
  standardCost?: number; // Changed from costPrice to match target schema
  costPrice?: number; // Keep for backward compatibility during transition
  cost?: number;
  categoryId?: { _id: string; name: string } | string;
  reorderLevel?: number;
  status?: string;
  isManufactured?: boolean;
  technicalSpecs?: string;
  isAssembly?: boolean;
  schemaVersion?: number;
  subParts?: Array<{ partId: string; quantity: number }>;
  createdAt?: string;
  updatedAt?: string;
}

// Function to map API product data to InventoryItem format
const mapToAppProduct = (apiProduct: any): InventoryItem | null => {
  if (!apiProduct || !apiProduct._id) {
    console.warn('[mapToAppProduct] Invalid product data:', apiProduct);
    return null;
  }

  return {
    _id: apiProduct._id,
    id: apiProduct._id,
    name: apiProduct.name || 'Unnamed Part',
    businessName: apiProduct.businessName || null,
    description: apiProduct.description || '',
    technicalSpecs: apiProduct.technicalSpecs || '',
    isManufactured: apiProduct.isManufactured || false,
    status: apiProduct.status || 'active',
    supplierId: apiProduct.supplierId || apiProduct.supplier || null,
    unitOfMeasure: apiProduct.unitOfMeasure || 'pcs',
    costPrice: apiProduct.costPrice || apiProduct.cost || 0,
    cost: apiProduct.cost || apiProduct.costPrice || 0,
    categoryId: apiProduct.categoryId || null,
    reorderLevel: apiProduct.reorderLevel || 0,
    isAssembly: apiProduct.isAssembly || false,
    schemaVersion: apiProduct.schemaVersion || 1,
    subParts: apiProduct.subParts || [],
    createdAt: apiProduct.createdAt || '',
    updatedAt: apiProduct.updatedAt || '',
    inventory: {
      // V4 Schema: Use aggregated stock data from inventories collection
      stockLevels: apiProduct.stockLevels || {
        raw: 0,
        hardening: 0,
        grinding: 0,
        finished: 0,
        rejected: 0
      },
      currentStock: apiProduct.currentStock || 0,
      // V4 Schema: These fields are now in part master data, not inventory
      warehouseId: '', // No longer applicable - parts can be in multiple locations
      safetyStockLevel: apiProduct.safetyStockLevel || 0,
      maximumStockLevel: apiProduct.maximumStockLevel || 0,
      averageDailyUsage: apiProduct.averageDailyUsage || 0,
      abcClassification: 'C', // Default value
      lastStockUpdate: null // Will be calculated from inventory records
    },
    // V4 Schema: Use aggregated currentStock from inventories collection
    currentStock: Number(apiProduct.currentStock ?? 0),
    // Ensure partNumber is present, falling back to productCode or an empty string
    partNumber: apiProduct.partNumber || apiProduct.productCode || '',
  };
};

export function InventoryPageContent() {
  const {
    products: srcProducts,
    isLoading,
    addProduct,
    updateProduct,
    deleteProduct,
    getProducts,
    productsPagination,
    setProductsPagination,
  } = useAppContext();

  // Performance: Use refs for values that don't need to trigger re-renders
  const abortControllerRef = useRef<AbortController | null>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // State for showing/hiding the filter panel and modal
  const [showFilters, setShowFilters] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedPart, setSelectedPart] = useState<InventoryItem | null>(null);

  // Fetch fresh inventory data for the selected part when editing
  const { inventoryDetails, isLoading: isLoadingInventory } = usePartInventoryDetails(
    selectedPart?.id || null
  );

  // Advanced filtering state
  const [filterState, setFilterState] = useState<FilterState>(DEFAULT_FILTER_STATE);
  const [searchQuery, setSearchQuery] = useState('');
  // Track total results for server-side search to drive correct pagination totals
  const [searchTotal, setSearchTotal] = useState<number | null>(null);

  // State for filter dropdown data
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>([]);
  const [locations, setLocations] = useState<string[]>([]);

  // Server-side pagination states - now using centralized state from AppContext
  // const [currentPage, setCurrentPage] = useState(1); // REMOVED - using productsPagination.page
  // const [productsPerPage, setProductsPerPage] = useState(50); // REMOVED - using productsPagination.limit
  // const [totalItems, setTotalItems] = useState(0); // REMOVED - using productsPagination.total
  const [serverParts, setServerParts] = useState<any[]>([]); // Keep for search results

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      // Safely abort the controller only if it exists and is not already aborted
      if (abortControllerRef.current && !abortControllerRef.current.signal.aborted) {
        // Provide an explicit abort reason for better debugging and to avoid "aborted without reason" warnings
        abortControllerRef.current.abort('component-unmounted');
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Load filters from localStorage on mount
  useEffect(() => {
    const savedFilters = loadFiltersFromStorage();
    if (savedFilters) {
      setFilterState(savedFilters);
    }
  }, []);

  // Save filters to localStorage when they change
  useEffect(() => {
    saveFiltersToStorage(filterState);
  }, [filterState]);

  // Initialize data on mount
  useEffect(() => {
    console.log('[INVENTORY DEBUG] Initializing inventory data on mount');
    const initializeData = async () => {
      try {
        await getProducts({ page: 1, limit: productsPagination.limit });
        setServerParts([]); // Clear search results
      } catch (error) {
        console.error('[INVENTORY DEBUG] Error initializing data:', error);
      }
    };
    initializeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productsPagination.limit]); // Use centralized pagination limit

  // Fetch filter dropdown data on mount
  useEffect(() => {
    const fetchFilterData = async () => {
      try {
        // Fetch suppliers, categories, and locations in parallel
        const [suppliersResponse, categoriesResponse, locationsResponse] = await Promise.all([
          fetch('/api/suppliers?limit=1000').then(res => res.json()),
          fetch('/api/categories?limit=1000').then(res => res.json()),
          fetch('/api/locations?limit=1000').then(res => res.json())
        ]);

        // Process suppliers data
        if (suppliersResponse.data && Array.isArray(suppliersResponse.data)) {
          const supplierNames = suppliersResponse.data.map((supplier: any) => supplier.name).filter(Boolean);
          setSuppliers(supplierNames);
          console.log(`[FRONTEND DEBUG] Loaded ${supplierNames.length} suppliers for filters`);
        }

        // Process categories data
        if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
          const categoryData = categoriesResponse.data.map((category: any) => ({
            id: category._id || category.id,
            name: category.name
          })).filter((cat: { id: string; name: string }) => cat.id && cat.name);
          setCategories(categoryData);
          console.log(`[FRONTEND DEBUG] Loaded ${categoryData.length} categories for filters`);
        }

        // Process locations data
        if (locationsResponse.data && Array.isArray(locationsResponse.data)) {
          const locationNames = locationsResponse.data.map((location: any) => location.name).filter(Boolean);
          setLocations(locationNames);
          console.log(`[FRONTEND DEBUG] Loaded ${locationNames.length} locations for filters`);
        }

      } catch (error) {
        console.error('[FRONTEND DEBUG] Error fetching filter data:', error);
      }
    };

    fetchFilterData();
  }, []);

  // Apply filters to products (client-side filtering)
  const filteredProducts = useMemo(() => {
    // If a search query is active, the data comes from serverParts
    if (searchQuery && serverParts && serverParts.length > 0) {
      return serverParts.map(mapToAppProduct).filter(p => p !== null) as InventoryItem[];
    }
    if (searchQuery) {
        return [];
    }

    // Use AppContext products (already processed with correct currentStock)
    if (srcProducts && srcProducts.length > 0) {
      return applyFilters(srcProducts as unknown as Product[], filterState) as unknown as InventoryItem[];
    }

    return [];
  }, [srcProducts, filterState, searchQuery, serverParts]);

  // Performance: Memoize the display data to avoid unnecessary recalculations
  const displayProducts = useMemo(() => filteredProducts, [filteredProducts]);
  const displayTotalItems = useMemo(() => {
    console.log('[INVENTORY DEBUG] displayTotalItems calculation:', {
      productsPaginationTotal: productsPagination.total,
      serverPartsLength: serverParts.length,
      searchQuery: searchQuery,
      isSearching: !!searchQuery.trim(),
      searchTotal
    });
    return (searchQuery.trim() && searchTotal !== null) ? searchTotal : productsPagination.total;
  }, [productsPagination.total, serverParts.length, searchQuery]);

  // Debug pagination state
  const pageIndex = productsPagination.page - 1;
  console.log(`[PAGINATION DEBUG] Rendering table with pageIndex: ${pageIndex}`);
  console.log(`[PAGINATION DEBUG] Current productsPagination:`, productsPagination);

  // Performance: Optimized debounced server search function with abort controller
  const debouncedServerSearch = useCallback(
    debounce(async (query: string, page: number = 1, pageSize: number = productsPagination.limit) => {
      if (!query.trim()) {
        setServerParts([]);
        return;
      }

      // Cancel previous request if still pending
      if (abortControllerRef.current && !abortControllerRef.current.signal.aborted) {
        // Provide an explicit abort reason; helps avoid confusing errors in some environments
        abortControllerRef.current.abort('new-search-triggered');
      }

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      try {
        console.log(`[FRONTEND DEBUG] Searching for: "${query}" (page ${page}, pageSize ${pageSize})`);

        // FIX: Use correct search param name expected by API: 'search' not 'q'
        const response = await fetch(`/api/parts/search?search=${encodeURIComponent(query)}&page=${page}&limit=${pageSize}`, {
          signal: abortControllerRef.current.signal
        });

        if (!response.ok) {
          console.error(`[FRONTEND DEBUG] Search failed: ${response.status} ${response.statusText}`);
          setServerParts([]);
          // Let AppContext getProducts handle pagination state updates
          return;
        }

        const result = await response.json();

        // Check if component is still mounted before updating state
        if (!isMountedRef.current) return;

        console.log('[FRONTEND DEBUG] Search result:', result);

        // Normalize result
        const parts = result?.data ?? [];
        const totalFromPagination = result?.pagination?.totalCount ?? result?.meta?.totalCount ?? null;

        setServerParts(parts);
        setSearchTotal(typeof totalFromPagination === 'number' ? totalFromPagination : parts.length);
      } catch (error) {
        // Gracefully handle explicit aborts triggered by new searches or unmounts
        if (error instanceof DOMException && error.name === 'AbortError') {
          console.log('[FRONTEND DEBUG] Search request aborted intentionally:', (error as any).message || 'AbortError');
        } else if ((error as any)?.name === 'AbortError') {
          console.log('[FRONTEND DEBUG] Search request aborted intentionally');
        } else {
          console.error('[FRONTEND DEBUG] Search error:', error);
        }
        setServerParts([]);
      }
    }, 300),
    [productsPagination.limit, setProductsPagination]
  );

  // Removed conflicting useEffect - pagination is handled by handlePageChange function

  // Removed fetchInventoryData function - using direct getProducts calls for consistency

  // Fetch server parts function (for search)
  const fetchServerParts = useCallback(async (page: number = 1, query: string = searchQuery, pageSize: number = productsPagination.limit) => {
    if (!query.trim()) {
      setServerParts([]);
      return;
    }

    try {
      console.log(`[FRONTEND DEBUG] Fetching server parts: "${query}" (page ${page}, pageSize ${pageSize})`);

      // FIX: Use correct search param name expected by API: 'search' not 'q'
      const response = await fetch(`/api/parts/search?search=${encodeURIComponent(query)}&page=${page}&limit=${pageSize}`);

      if (!response.ok) {
        console.error(`[FRONTEND DEBUG] Fetch failed: ${response.status} ${response.statusText}`);
        setServerParts([]);
        // Let AppContext getProducts handle pagination state updates
        return;
      }

      const result = await response.json();
      console.log('[FRONTEND DEBUG] Fetch result:', result);

      // Handle both success/data format and direct data format
      if (result.success && result.data) {
        setServerParts(result.data.parts || result.data || []);
        // Let AppContext getProducts handle pagination state updates
      } else if (result.data && Array.isArray(result.data)) {
        // Handle direct data array format
        setServerParts(result.data);
        // Let AppContext getProducts handle pagination state updates
      } else {
        console.error('[FRONTEND DEBUG] Fetch API returned unsuccessful result:', result);
        setServerParts([]);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Fetch error:', error);
      setServerParts([]);
    }
  }, [searchQuery, productsPagination.limit, setProductsPagination]);

  // Handle search change - FIXED VERSION
  const handleSearchChange = (term: string) => {
    console.log(`[INVENTORY DEBUG] Search changed: "${term}"`);
    setSearchQuery(term);

    if (term.trim()) {
      // Use search API - reset to page 1 for new search
      debouncedServerSearch(term, 1, productsPagination.limit);
    } else {
      // Clear search and return to regular pagination
      setServerParts([]);
      setSearchTotal(null);
      getProducts({ page: 1, limit: productsPagination.limit })
        .then(() => {
          // Pagination state is automatically updated by getProducts via setProductsPagination
          console.log('[INVENTORY DEBUG] Search cleared, returned to normal pagination');
        })
        .catch(error => {
          console.error('[INVENTORY DEBUG] Error fetching normal data after search clear:', error);
        });
    }
  };

  // Handle page change - FIXED VERSION
  const handlePageChange = (pageNumber: number) => {
    console.log(`[INVENTORY DEBUG] Page change to: ${pageNumber}`);

    if (searchQuery) {
      fetchServerParts(pageNumber, searchQuery);
    } else {
      getProducts({ page: pageNumber, limit: productsPagination.limit })
        .then(() => {
          // Pagination state is automatically updated by getProducts via setProductsPagination
          setServerParts([]);
          console.log('[INVENTORY DEBUG] Page change completed');
        })
        .catch(error => {
          console.error('[INVENTORY DEBUG] Error in page change:', error);
        });
    }
  };

  // Handle pagination change from StandardizedTable - FIXED: Optimistic updates
  const handlePaginationChange = (pagination: { pageIndex: number; pageSize: number }) => {
    console.log(`[PAGINATION DEBUG] handlePaginationChange called with:`, pagination);
    console.log(`[PAGINATION DEBUG] Current productsPagination before change:`, productsPagination);
    const newPage = pagination.pageIndex + 1; // Convert 0-based to 1-based
    const newPageSize = pagination.pageSize;

    // Handle page size changes
    if (newPageSize !== productsPagination.limit) {
      console.log(`[INVENTORY DEBUG] Page size changed: ${productsPagination.limit} -> ${newPageSize}`);

      // FIXED: Optimistically update pagination state immediately for instant UI feedback
      setProductsPagination({
        ...productsPagination,
        page: 1, // Reset to page 1 when changing page size
        limit: newPageSize,
        totalPages: Math.ceil(productsPagination.total / newPageSize)
      });

      // Fetch data with new page size in the background
      if (searchQuery) {
        fetchServerParts(1, searchQuery, newPageSize);
      } else {
        getProducts({ page: 1, limit: newPageSize })
          .then(() => {
            setServerParts([]); // Clear search results
          })
          .catch(error => {
            console.error('[INVENTORY DEBUG] Error in page size change:', error);
            // Revert pagination state on error
            setProductsPagination(productsPagination);
          });
      }
    }
    // Handle page changes
    else if (newPage !== productsPagination.page) {
      console.log(`[INVENTORY DEBUG] Page changed: ${productsPagination.page} -> ${newPage}`);
      console.log(`[PAGINATION DEBUG] Calling getProducts with page: ${newPage}, limit: ${newPageSize}`);

      // FIXED: Optimistically update pagination state immediately for instant UI feedback
      setProductsPagination({
        ...productsPagination,
        page: newPage
      });

      // Fetch data for new page in the background
      if (searchQuery) {
        fetchServerParts(newPage, searchQuery, newPageSize);
      } else {
        getProducts({ page: newPage, limit: newPageSize })
          .then(() => {
            console.log(`[PAGINATION DEBUG] getProducts completed for page: ${newPage}`);
            setServerParts([]); // Clear search results
          })
          .catch(error => {
            console.error('[INVENTORY DEBUG] Error in page change:', error);
            // Revert pagination state on error
            setProductsPagination(productsPagination);
          });
      }
    }
  };

  // Performance: Memoize the data transformation function
  // UPDATED: V4 Schema - Handle missing embedded inventory structure
  const adaptProductsForTable = useCallback((products: InventoryItem[]): InventoryColumnData[] => {
    return products.map((product) => {
      // V4 Schema: Use aggregated stock data from API response
      const finishedStock = product.inventory?.stockLevels?.finished ?? product.currentStock ?? 0;

      return {
        _id: product._id,
        id: product.id || product._id,
        partNumber: product.partNumber,
        name: product.name,
        businessName: product.businessName ?? null,
        description: product.description,
        unitOfMeasure: product.unitOfMeasure || 'pcs',
        currentStock: finishedStock,
        reorderLevel: product.reorderLevel || 0,

        // NEW: Part Master Data Planning Parameters
        planningMethod: product.planningMethod ?? null,
        safetyStockLevel: product.safetyStockLevel ?? null,
        maximumStockLevel: product.maximumStockLevel ?? null,
        leadTimeDays: product.leadTimeDays ?? null,
        averageDailyUsage: product.averageDailyUsage ?? null,

        supplier: typeof product.supplierId === 'object' && product.supplierId !== null
          ? { _id: product.supplierId._id, name: product.supplierId.name }
          : { _id: '', name: '' },
        inventory: {
          // V4 Schema: Use aggregated stock data
          stockLevels: product.inventory?.stockLevels || {
            raw: 0,
            hardening: 0,
            grinding: 0,
            finished: finishedStock,
            rejected: 0
          },
          currentStock: finishedStock,
          // V4 Schema: No single warehouse - parts can be in multiple locations
          warehouseId: '',
          // V4 Schema: These fields are now part of the part master data
          safetyStockLevel: product.safetyStockLevel || 0,
          maximumStockLevel: product.maximumStockLevel || 0,
          averageDailyUsage: product.averageDailyUsage || 0,
          abcClassification: 'C', // Default value
          lastStockUpdate: null // Will be calculated from inventory records
        },
        stockLevels: product.inventory?.stockLevels || {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: finishedStock,
          rejected: 0
        },
        createdAt: product.createdAt || '',
        updatedAt: product.updatedAt || ''
      };
    });
  }, []); // Empty dependency array since this function doesn't depend on any props or state

  // Performance: Memoize the transformed table data
  const tableData = useMemo(() => {
    return adaptProductsForTable(displayProducts);
  }, [displayProducts, adaptProductsForTable]);

  // Performance: Memoize pagination object to prevent unnecessary re-renders
  const memoizedPagination = useMemo(() => ({
    pageIndex: productsPagination.page - 1,
    pageSize: productsPagination.limit
  }), [productsPagination.page, productsPagination.limit]);

  // Refresh function for actions
  const handleRefreshData = async () => {
    try {
      console.log(`[FRONTEND DEBUG] Refreshing data after part action`);

      if (searchQuery) {
        await fetchServerParts(productsPagination.page, searchQuery);
      } else {
        await getProducts({ page: productsPagination.page, limit: productsPagination.limit });
        // No need to set local state - getProducts updates centralized state
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error refreshing data:', error);
    }
  };

  // Handle add part
  const handleAddPart = async (formData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Adding part:', formData);

      // Extract inventory data before removing it from part data
      const inventoryData = formData.inventory;

      // Prepare part data (without inventory for V4 schema)
      const dataWithPartNumber: any = {
        ...formData,
        partNumber: formData.partNumber || `P${Date.now().toString().slice(-6)}`,
        businessName: formData.businessName ?? null
      };

      // Remove inventory data from part data (V4 schema requirement)
      delete dataWithPartNumber.inventory;

      // Only include categoryId and supplierId if they have values
      if (formData.categoryId) {
        dataWithPartNumber.categoryId = formData.categoryId;
      }
      if (formData.supplierId) {
        dataWithPartNumber.supplierId = formData.supplierId;
      }

      // Step 1: Create the part
      console.log('[FRONTEND DEBUG] Creating part without inventory data');
      const partResult = await addProduct(dataWithPartNumber);

      // Step 2: Create inventory records if inventory data is provided
      if (inventoryData && inventoryData.locationId && partResult?._id) {
        console.log('[FRONTEND DEBUG] Creating inventory records for part:', partResult._id);
        try {
          // Ensure required fields are present
          const validInventoryData = {
            ...inventoryData,
            warehouseId: inventoryData.warehouseId || '',
            locationId: inventoryData.locationId || ''
          };
          await createOrUpdatePartInventory(partResult._id, validInventoryData);
          console.log('[FRONTEND DEBUG] Inventory records created successfully');
        } catch (inventoryError) {
          console.error('[FRONTEND DEBUG] Error creating inventory records:', inventoryError);
          // Note: Part was created successfully, but inventory failed
          showErrorToast({ error: 'Part created but inventory setup failed. Please edit the part to add inventory.' });
          setShowAddForm(false);
          return;
        }
      }

      setShowAddForm(false);
      showSuccessToast('Part added successfully');

      // FIXED: If user is currently searching, refresh the search results to include the new part
      if (searchQuery.trim()) {
        console.log('[FRONTEND DEBUG] Refreshing search results after adding part');
        await fetchServerParts(productsPagination.page, searchQuery);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error adding part:', error);
      showErrorToast({ error: 'Failed to add part' });
    }
  };

  // Handle edit part
  const handleEditPart = async (formData: PartFormData) => {
    try {
      console.log('[FRONTEND DEBUG] Editing part:', formData);
      if (!formData._id) {
        console.error('[FRONTEND DEBUG] Part ID is required for editing');
        return;
      }

      // Extract inventory data before removing it from part data
      const inventoryData = formData.inventory;

      // Prepare part data (without inventory for V4 schema)
      const dataWithPartNumber: any = {
        ...formData,
        partNumber: formData.partNumber || `P${Date.now().toString().slice(-6)}`,
        businessName: formData.businessName ?? null
      };

      // Remove inventory data from part data (V4 schema requirement)
      delete dataWithPartNumber.inventory;

      // Only include categoryId and supplierId if they have values
      if (formData.categoryId) {
        dataWithPartNumber.categoryId = formData.categoryId;
      }
      if (formData.supplierId) {
        dataWithPartNumber.supplierId = formData.supplierId;
      }

      // Step 1: Update the part
      console.log('[FRONTEND DEBUG] Updating part without inventory data');
      await updateProduct(formData._id, dataWithPartNumber);

      // Step 2: Update inventory records if inventory data is provided
      if (inventoryData && inventoryData.locationId) {
        console.log('[FRONTEND DEBUG] Updating inventory records for part:', formData._id);
        try {
          // Ensure required fields are present
          const validInventoryData = {
            ...inventoryData,
            warehouseId: inventoryData.warehouseId || '',
            locationId: inventoryData.locationId || ''
          };
          await createOrUpdatePartInventory(formData._id, validInventoryData);
          console.log('[FRONTEND DEBUG] Inventory records updated successfully');
        } catch (inventoryError) {
          console.error('[FRONTEND DEBUG] Error updating inventory records:', inventoryError);
          // Note: Part was updated successfully, but inventory failed
          showErrorToast({ error: 'Part updated but inventory update failed. Please try editing again.' });
          setShowEditForm(false);
          setSelectedPart(null);
          return;
        }
      }

      setShowEditForm(false);
      setSelectedPart(null);
      showSuccessToast('Part updated successfully');

      // FIXED: If user is currently searching, refresh the search results to show updated part
      if (searchQuery.trim()) {
        console.log('[FRONTEND DEBUG] Refreshing search results after updating part');
        await fetchServerParts(productsPagination.page, searchQuery);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error editing part:', error);
      showErrorToast({ error: 'Failed to update part' });
    }
  };

  // Handle delete part
  const handleDeletePart = async (partId: string) => {
    try {
      console.log('[FRONTEND DEBUG] Deleting part:', partId);
      await deleteProduct(partId);
      showSuccessToast('Part deleted successfully');

      // FIXED: If user is currently searching, refresh the search results to remove deleted part
      if (searchQuery.trim()) {
        console.log('[FRONTEND DEBUG] Refreshing search results after deleting part');
        await fetchServerParts(productsPagination.page, searchQuery);
      }
    } catch (error) {
      console.error('[FRONTEND DEBUG] Error deleting part:', error);
      showErrorToast({ error: 'Failed to delete part' });
    }
  };

  // Create table actions
  const tableActions: InventoryTableActions = useMemo(() => ({
    onView: (item) => {
      console.log('View item:', item);
      // Could navigate to detail page or open modal
    },
    onEdit: (item) => {
      setSelectedPart(item as any);
      setShowEditForm(true);
    },
    onDelete: (item) => handleDeletePart(item._id),
    onRefresh: handleRefreshData
  }), []);

  // Create table columns
  const columns = useMemo(() => createInventoryColumns(tableActions), [tableActions]);

  // Check if filters are active - FIXED: Added null/undefined checks to prevent TypeError
  const hasActiveFilters = useMemo(() => {
    if (!filterState) return false;

    return (filterState.stockQuantity?.enabled || false) ||
           (filterState.reorderLevel?.enabled || false) ||
           (filterState.supplier?.enabled || false) ||
           (filterState.category?.enabled || false) ||
           (filterState.location?.enabled || false) ||
           // NEW: Planning Parameter Filters with null checks
           (filterState.planningMethod?.enabled || false) ||
           (filterState.safetyStockLevel?.enabled || false) ||
           (filterState.maximumStockLevel?.enabled || false) ||
           (filterState.leadTimeDays?.enabled || false) ||
           (filterState.averageDailyUsage?.enabled || false);
  }, [filterState]);

  // Render filters function for StandardizedTable
  const renderFilters = () => (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center gap-2"
      >
        <SlidersHorizontal size={16} />
        Filters
        {hasActiveFilters && <Badge variant="secondary" className="ml-1">Active</Badge>}
      </Button>
      <Button
        variant="outline"
        onClick={() => {
          if (searchQuery) {
            fetchServerParts(productsPagination.page, searchQuery);
          } else {
            getProducts({ page: productsPagination.page, limit: productsPagination.limit })
              .then(result => {
                console.log('[DEBUG] Refresh completed, pagination:', result.pagination);
                // Pagination state is automatically updated by getProducts via setProductsPagination
              })
              .catch(error => {
                console.error('[DEBUG] Error in refresh:', error);
              });
          }
        }}
        className="p-2"
        title="Refresh data"
      >
        <RefreshCw size={16} />
      </Button>
    </div>
  );

  // Render actions function for StandardizedTable
  const renderActions = () => (
    <Button
      onClick={() => setShowAddForm(true)}
      className="flex items-center gap-2"
    >
      <Plus size={16} />
      Add Part
    </Button>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Filter Panel */}
      {showFilters && (
        <div className="mb-6">
          <InventoryFilter
            filters={filterState}
            onFiltersChange={setFilterState}
            onReset={() => setFilterState(DEFAULT_FILTER_STATE)}
            suppliers={suppliers}
            categories={categories}
            locations={locations}
          />
        </div>
      )}

      {/* StandardizedTable - FIXED: Using memoized pagination to prevent unnecessary re-renders */}
      <StandardizedTable
        data={tableData}
        columns={columns}
        searchPlaceholder="Search parts by name, part number, or description..."
        enableViewToggle={false}
        enableSearch={true}
        onSearchChange={handleSearchChange}
        renderFilters={renderFilters}
        renderActions={renderActions}
        manualPagination={true}
        totalRows={displayTotalItems}
        onPaginationChange={handlePaginationChange}
        initialPagination={memoizedPagination}
        pageSizeOptions={[10, 20, 50, 100, 150]}
        isLoading={isLoading}
      />

      {/* Add Part Modal */}
      {showAddForm && (
        <PartForm
          onSubmit={handleAddPart}
          onClose={() => setShowAddForm(false)}
        />
      )}

      {/* Edit Part Modal */}
      {showEditForm && selectedPart && (
        <PartForm
          initialData={{
            _id: selectedPart.id,
            name: selectedPart.name,
            businessName: selectedPart.businessName || null,
            description: selectedPart.description || '',
            technicalSpecs: selectedPart.technicalSpecs || '',
            isManufactured: selectedPart.isManufactured || false,
            partNumber: selectedPart.partNumber || '',
            reorderLevel: selectedPart.reorderLevel || 0,
            status: (selectedPart.status as 'active' | 'inactive' | 'obsolete') || 'active',
            // V4 Schema: Include fresh inventory data from inventories collection
            inventory: inventoryDetails ? {
              warehouseId: inventoryDetails.warehouses?.[0]?.warehouseId || '',
              locationId: inventoryDetails.warehouses?.[0]?.locations?.[0]?.locationId || '',
              stockLevels: {
                raw: inventoryDetails.stockLevelsSummary?.raw || inventoryDetails.totals?.raw || 0,
                hardening: inventoryDetails.stockLevelsSummary?.hardening || inventoryDetails.totals?.hardening || 0,
                grinding: inventoryDetails.stockLevelsSummary?.grinding || inventoryDetails.totals?.grinding || 0,
                finished: inventoryDetails.stockLevelsSummary?.finished || inventoryDetails.totals?.finished || 0,
                rejected: inventoryDetails.stockLevelsSummary?.rejected || inventoryDetails.totals?.rejected || 0
              },
              adjustmentReason: undefined, // Will be filled by user for manual adjustments
              adjustmentNotes: ''   // Will be filled by user for manual adjustments
            } : {
              warehouseId: '',
              locationId: '',
              stockLevels: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
              adjustmentReason: undefined,
              adjustmentNotes: ''
            },
            // Planning parameters are now part of part master data
            planningMethod: selectedPart.planningMethod || null,
            safetyStockLevel: selectedPart.safetyStockLevel || null,
            maximumStockLevel: selectedPart.maximumStockLevel || null,
            leadTimeDays: selectedPart.leadTimeDays || null,
            averageDailyUsage: selectedPart.averageDailyUsage || null,
            supplierId: (typeof selectedPart.supplierId === 'object' && selectedPart.supplierId !== null ? selectedPart.supplierId._id : selectedPart.supplierId) || '',
            unitOfMeasure: selectedPart.unitOfMeasure || 'pcs',
            standardCost: selectedPart.standardCost || selectedPart.costPrice || selectedPart.cost || 0, // Changed from costPrice to standardCost
            categoryId: typeof selectedPart.categoryId === 'object' && selectedPart.categoryId !== null
              ? selectedPart.categoryId._id
              : (selectedPart.categoryId || ''),
            isAssembly: selectedPart.isAssembly || false,
            schemaVersion: selectedPart.schemaVersion || 1,
            subParts: selectedPart.subParts
              ? selectedPart.subParts.map(part => ({
                  partId: part.partId || '',
                  quantity: part.quantity
                }))
              : []
          }}
          onSubmit={handleEditPart}
          onClose={() => {
            setShowEditForm(false);
            setSelectedPart(null);
          }}
          isEdit={true}
          title="Edit Part"
        />
      )}
    </div>
  );
}
