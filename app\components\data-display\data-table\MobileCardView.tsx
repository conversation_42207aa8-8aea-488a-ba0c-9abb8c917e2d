"use client";

import { cn } from '@/app/lib/utils';
import { useTheme } from '@/app/contexts/ThemeContext';
import { MobileCardProps, DataTableColumn } from './types';

/**
 * Individual mobile card component
 */
function MobileCard<T>({
  row,
  columns,
  index,
  onClick,
  renderRowActions,
}: MobileCardProps<T>) {
  const { theme } = useTheme();

  return (
    <div
      className={cn(
        'rounded-lg border border-border bg-card p-4 shadow-sm transition-all',
        onClick && 'cursor-pointer hover:shadow-md hover:bg-accent/5',
        'space-y-3'
      )}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={(e) => {
        if (onClick && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          onClick();
        }
      }}
    >
      {/* Primary information - first 2 columns */}
      <div className="space-y-2">
        {columns.slice(0, 2).map((column, columnIndex) => {
          const value = (column as any).accessorFn ? (column as any).accessorFn(row) : null;

          return (
            <div key={`primary-${column.id || columnIndex}`} className="flex flex-col">
              <dt className="text-sm font-medium text-muted-foreground">
                {typeof column.header === 'string' ? column.header : column.id}
              </dt>
              <dd className="text-base font-semibold">
                {column.mobileRender
                  ? column.mobileRender(value, row)
                  : column.cell
                  ? (column as any).cell({ row: { original: row }, getValue: () => value })
                  : String(value || '')}
              </dd>
            </div>
          );
        })}
      </div>

      {/* Secondary information - remaining columns */}
      {columns.length > 2 && (
        <div className="grid grid-cols-2 gap-3 pt-2 border-t border-border">
          {columns.slice(2).map((column, columnIndex) => {
            const value = (column as any).accessorFn ? (column as any).accessorFn(row) : null;

            return (
              <div key={`secondary-${column.id || columnIndex}`} className="flex flex-col">
                <dt className="text-xs font-medium text-muted-foreground truncate">
                  {typeof column.header === 'string' ? column.header : column.id}
                </dt>
                <dd className="text-sm">
                  {column.mobileRender
                    ? column.mobileRender(value, row)
                    : column.cell
                    ? (column as any).cell({ row: { original: row }, getValue: () => value })
                    : String(value || '')}
                </dd>
              </div>
            );
          })}
        </div>
      )}

      {/* Row actions */}
      {renderRowActions && (
        <div className="flex justify-end pt-2 border-t border-border">
          {renderRowActions(row)}
        </div>
      )}
    </div>
  );
}

/**
 * Mobile card view container
 */
interface MobileCardViewProps<T> {
  data: T[];
  columns: DataTableColumn<T>[];
  onRowClick?: (row: T) => void;
  renderRowActions?: ((row: T) => React.ReactNode) | undefined;
  renderMobileCard?: ((row: T, index: number) => React.ReactNode) | undefined;
}

export function MobileCardView<T>({
  data,
  columns,
  onRowClick,
  renderRowActions,
  renderMobileCard,
}: MobileCardViewProps<T>) {
  return (
    <div className="space-y-3">
      {data.map((row, index) => {
        const uniqueKey = `mobile-card-${index}-${(row as any).id || (row as any)._id || index}`;
        
        if (renderMobileCard) {
          return (
            <div key={uniqueKey} className="mb-4">
              {renderMobileCard(row, index)}
            </div>
          );
        }

        return (
          <MobileCard
            key={uniqueKey}
            row={row}
            columns={columns}
            index={index}
            onClick={onRowClick ? () => onRowClick(row) : undefined}
            renderRowActions={renderRowActions}
          />
        );
      })}
    </div>
  );
}
