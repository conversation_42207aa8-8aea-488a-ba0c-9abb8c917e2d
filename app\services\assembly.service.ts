import mongoose, { Document, Types } from 'mongoose';
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import type { IAssembly } from '../models/assembly.model';
import Assembly from '../models/assembly.model';
import Part from '../models/part.model'; // Ensure Part model is imported for existence checks

// Import the interface for assembly part requirements
interface IAssemblyPartRequired {
  partId: Types.ObjectId;
  quantityRequired: number;
  unitOfMeasure?: string | null;
  children?: IAssemblyPartRequired[];
}

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[AssemblyService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Error handler for MongoDB errors
 */
export function handleMongoDBError(error: any) {
  console.error('MongoDB Error:', error);

  try {
    setTag('error.type', 'mongodb');
    if (error.name === 'CastError') {
      setTag('mongodb.error.name', 'CastError');
      if (error.path) setTag('mongodb.error.path', error.path);
      if (error.value) setTag('mongodb.error.value', String(error.value).substring(0, 100));
      if (error.kind) setTag('mongodb.error.kind', error.kind);
    }
    captureException(error);
  } catch (sentryError) {
    console.error('Failed to report error to Sentry:', sentryError);
  }

  if (error.name === 'MongoServerError' && error.code === 11000) {
    const fieldName = Object.keys(error.keyPattern)[0];
    const fieldValue = fieldName ? error.keyValue[fieldName] : 'unknown';
    return {
      type: 'duplicate',
      message: `The ${fieldName} '${fieldValue}' already exists. Please use a unique value.`,
      field: fieldName,
      value: fieldValue,
      code: 409
    };
  }

  if (error.name === 'ValidationError') {
    const validationErrors = Object.keys(error.errors).map(field => ({
      field,
      message: error.errors[field].message
    }));
    return {
      type: 'validation',
      message: 'Validation failed',
      errors: validationErrors,
      code: 400
    };
  }

  if (error.name === 'CastError') {
    return {
      type: 'cast_error',
      message: `Invalid data format: ${error.message}. Field: ${error.path}, Value: '${String(error.value)}', Expected Type: ${error.kind}.`,
      field: error.path,
      value: error.value,
      kind: error.kind,
      code: 400
    };
  }

  return {
    type: 'database',
    message: error.message || 'An unknown database error occurred',
    code: 500
  };
}

/**
 * Common aggregation pipeline to join parts with inventories and reconstruct inventory object
 * This pipeline maintains backward compatibility by reconstructing the embedded inventory structure
 * from the new dedicated inventories collection.
 *
 * SHARED WITH PART SERVICE: This ensures consistent inventory data across all features
 */
const getPartInventoryAggregationPipeline = () => [
  // Stage 1: Lookup inventories for each part
  {
    $lookup: {
      from: 'inventories',
      localField: '_id',
      foreignField: 'partId',
      as: 'inventoryRecords'
    }
  },

  // Stage 2: Lookup warehouse information for each inventory record
  {
    $lookup: {
      from: 'warehouses',
      localField: 'inventoryRecords.warehouseId',
      foreignField: '_id',
      as: 'warehouseInfo'
    }
  },

  // Stage 3: Reconstruct the inventory object to maintain backward compatibility
  {
    $addFields: {
      inventory: {
        $let: {
          vars: {
            // Group inventory records by warehouse (taking the first warehouse for now)
            warehouseInventory: { $arrayElemAt: ['$inventoryRecords', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseInfo', 0] }
          },
          in: {
            stockLevels: {
              $reduce: {
                input: '$inventoryRecords',
                initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
                in: {
                  raw: { $add: ['$$value.raw', { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, '$$this.quantity', 0] }] },
                  hardening: { $add: ['$$value.hardening', { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, '$$this.quantity', 0] }] },
                  grinding: { $add: ['$$value.grinding', { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, '$$this.quantity', 0] }] },
                  finished: { $add: ['$$value.finished', { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, '$$this.quantity', 0] }] },
                  rejected: { $add: ['$$value.rejected', { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, '$$this.quantity', 0] }] }
                }
              }
            },
            warehouseId: '$$warehouseInventory.warehouseId',
            safetyStockLevel: { $ifNull: ['$$warehouseInventory.safetyStockLevel', 0] },
            maximumStockLevel: { $ifNull: ['$$warehouseInventory.maximumStockLevel', 0] },
            averageDailyUsage: { $ifNull: ['$$warehouseInventory.averageDailyUsage', 0] },
            abcClassification: { $ifNull: ['$$warehouseInventory.abcClassification', 'C'] },
            lastStockUpdate: '$$warehouseInventory.lastUpdated',
            // Backward compatibility: currentStock as finished quantity
            currentStock: {
              $reduce: {
                input: '$inventoryRecords',
                initialValue: 0,
                in: { $add: ['$$value', { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, '$$this.quantity', 0] }] }
              }
            }
          }
        }
      }
    }
  },

  // Stage 4: Clean up temporary fields
  {
    $unset: ['inventoryRecords', 'warehouseInfo']
  }
];

/**
 * Aggregation pipeline to populate parts with inventory data for assemblies
 * This replaces the old .populate() approach with a more efficient aggregation pipeline
 */
const getAssemblyPartsAggregationPipeline = () => [
  // Stage 1: Unwind partsRequired array to process each part individually
  {
    $unwind: {
      path: '$partsRequired',
      preserveNullAndEmptyArrays: true
    }
  },

  // Stage 2: Lookup part details with inventory data
  {
    $lookup: {
      from: 'parts',
      let: { partId: '$partsRequired.partId' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$partId'] } } },
        ...getPartInventoryAggregationPipeline(),
        {
          $project: {
            _id: 1,
            partNumber: 1,
            name: 1,
            businessName: 1,
            description: 1,
            technicalSpecs: 1,
            isManufactured: 1,
            status: 1,
            inventory: 1,
            unitOfMeasure: 1,
            costPrice: 1,
            categoryId: 1
          }
        }
      ],
      as: 'partsRequired.partDetails'
    }
  },

  // Stage 3: Flatten the part details
  {
    $addFields: {
      'partsRequired.partId': { $arrayElemAt: ['$partsRequired.partDetails', 0] }
    }
  },

  // Stage 4: Clean up temporary fields
  {
    $unset: ['partsRequired.partDetails']
  },

  // Stage 5: Group back to reconstruct the partsRequired array
  {
    $group: {
      _id: '$_id',
      assemblyCode: { $first: '$assemblyCode' },
      name: { $first: '$name' },
      productId: { $first: '$productId' },
      parentId: { $first: '$parentId' },
      isTopLevel: { $first: '$isTopLevel' },
      status: { $first: '$status' },
      version: { $first: '$version' },
      manufacturingInstructions: { $first: '$manufacturingInstructions' },
      estimatedBuildTime: { $first: '$estimatedBuildTime' },
      createdAt: { $first: '$createdAt' },
      updatedAt: { $first: '$updatedAt' },
      partsRequired: {
        $push: {
          $cond: [
            { $ne: ['$partsRequired', null] },
            '$partsRequired',
            '$$REMOVE'
          ]
        }
      }
    }
  }
];

// Service interface for return types, extending IAssembly with Mongoose Document properties
export type IAssemblyService = IAssembly & Document;

// DTO-specific interfaces for part and sub-assembly requirements for API input
export interface AssemblyPartRequiredDto {
  partId: string; // Input as string from API, will be converted to ObjectId in service
  quantityRequired: number;
  unitOfMeasure?: string; // Optional, as per IAssemblyPartRequired
  children?: AssemblyPartRequiredDto[]; // Support for hierarchical structure
}

// Note: Sub-assemblies are handled through parentId relationships, not as an array

// Canonical DTOs (Aligned with database_schema_updated.md)
export interface CanonicalAssemblyPartRequiredDto {
  partId: string; // Input as string, converted to ObjectId in service
  quantityRequired: number; // Canonical name to match database schema
  unitOfMeasure?: string;
  children?: CanonicalAssemblyPartRequiredDto[]; // Support for hierarchical structure
}

export interface CanonicalSubAssemblyRequiredDto {
  subAssemblyId: string; // Input as string, converted to ObjectId in service
  quantityRequired: number; // Canonical name to match database schema
}

export interface CanonicalCostDataDto {
  estimatedMaterialCost?: number | null;
  estimatedLaborCost?: number | null;
  estimatedOverheadCost?: number | null;
  totalEstimatedCost?: number | null;
}

export interface CanonicalCreateAssemblyDto {
  assemblyCode: string;
  name: string;
  description?: string | null;
  version: number; // Canonical: number, aligned with Mongoose schema
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete'; // Values from Mongoose schema
  parentId?: string | null; // string from API, or null
  partsRequired?: CanonicalAssemblyPartRequiredDto[];
  subAssemblies?: CanonicalSubAssemblyRequiredDto[];
  manufacturingLeadTime?: string | null; // Canonical name
  costData?: CanonicalCostDataDto | null; // Canonical structure
  notes?: string | null;
  // ⚠️ TEMPORARY AUTHENTICATION BYPASS ⚠️
  createdBy?: string; // TODO: Make required when authentication is implemented
  // When authentication is ready: change to `createdBy: string;` (required field)
}

export interface CanonicalUpdateAssemblyDto {
  name?: string;
  description?: string | null;
  version?: number; // Canonical: number, aligned with Mongoose schema
  status?: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete'; // Values from Mongoose schema
  parentId?: string | null;
  partsRequired?: CanonicalAssemblyPartRequiredDto[] | null;
  subAssemblies?: CanonicalSubAssemblyRequiredDto[] | null;
  manufacturingLeadTime?: string | null;
  costData?: CanonicalCostDataDto | null;
  notes?: string | null;
  // ⚠️ TEMPORARY AUTHENTICATION BYPASS ⚠️
  updatedBy?: string; // TODO: Make required when authentication is implemented
  // When authentication is ready: change to `updatedBy: string;` (required field)
}

// DTO for creating an assembly
export interface CreateAssemblyDto {
  assemblyCode: string;
  name: string;
  description?: string | null;
  version: number; // Consider defaulting in service if not provided, or make optional
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete'; // Consider defaulting
  isTopLevel: boolean; // Consider defaulting based on parentId
  parentId?: string | null; // string from API, or null
  partsRequired?: AssemblyPartRequiredDto[];
  // Note: Sub-assemblies are handled through parentId relationships, not as an array
  estimatedManufacturingTime?: string | null;
  manufacturingInstructions?: string | null;
  assemblyCost?: number | null;
  notes?: string | null;
  createdBy: string; // string from API (user ID)
  updatedBy?: string; // Optional: User ID string from API, defaults to createdBy if not provided
}

// DTO for updating an assembly
export interface UpdateAssemblyDto {
  name?: string;
  description?: string | null;
  version?: number;
  status?: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete';
  isTopLevel?: boolean;
  parentId?: string | null; // string from API, null to clear parent, undefined to leave untouched
  partsRequired?: AssemblyPartRequiredDto[] | null; // Array to update, null to clear, undefined to leave untouched
  // Note: Sub-assemblies are handled through parentId relationships, not as an array
  estimatedManufacturingTime?: string | null;
  manufacturingInstructions?: string | null;
  assemblyCost?: number | null;
  notes?: string | null;
  updatedBy: string; // Mandatory, string from API (user ID)
}

// Helper interfaces for canonical model data structures
interface ICanonicalModelAssemblyPartRequired {
  partId: Types.ObjectId;
  quantityRequired: number; // Canonical name to match database schema
  unitOfMeasure?: string;
  children?: ICanonicalModelAssemblyPartRequired[]; // Support for hierarchical structure
}

// Note: Sub-assemblies are handled through parentId relationships, not as an array



/**
 * Fetches assemblies with pagination, sorting, and filtering.
 */
export async function getAllAssemblies(options: { page?: number; limit?: number; sort?: any; filter?: any; populateParts?: boolean } = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    populateParts = false,
  } = options;

  logOperation('FETCH_ALL', 'service', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Create the aggregation pipeline
    const pipeline: any[] = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    let assemblies;

    if (populateParts) {
      // REFACTORED: Use aggregation pipeline with inventory integration instead of populate
      console.log('[AssemblyService] Using aggregation pipeline with inventory integration...');

      // Enhanced pipeline with parts population and inventory data
      const enhancedPipeline = [
        { $match: filter },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        ...getAssemblyPartsAggregationPipeline()
      ];

      assemblies = await Assembly.aggregate(enhancedPipeline);

      console.log('[AssemblyService] Parts populated successfully via aggregation pipeline with inventory integration');
    } else {
      assemblies = await Assembly.aggregate(pipeline);
    }
    const totalCount = await Assembly.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', 'service', {
      count: assemblies.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      assemblies,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    // Create a new error object that includes the code from errDetails
    const serviceError: any = new Error(errDetails.message || 'Failed to fetch assembly');
    serviceError.statusCode = errDetails.code || 500; // Attach the status code
    throw serviceError; // Throw this error
  }
}

/**
 * Gets a single assembly by its MongoDB ObjectId.
 */
export async function getAssemblyById(id: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ID', 'service', { id, includeParts });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToDatabase();

    let assemblyQuery;
    
    let assembly: IAssemblyService | null = null;

    if (includeParts) {
      // REFACTORED: Use aggregation pipeline with inventory integration
      const pipeline = [
        { $match: { _id: new Types.ObjectId(id) } },
        ...getAssemblyPartsAggregationPipeline()
      ];

      const results = await Assembly.aggregate(pipeline);
      assembly = results.length > 0 ? results[0] : null;
    } else {
      // Simple find without parts population
      assembly = await Assembly.findById(id).lean() as IAssemblyService | null;
    }
    
    if (!assembly) {
      logOperation('GET_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    logOperation('GET_BY_ID_SUCCESS', 'service', { id, includeParts });
    return assembly;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    const serviceError: any = new Error(errDetails.message || `Failed to get assembly by ID ${id}`);
    serviceError.statusCode = errDetails.code || 500;
    throw serviceError;
  }
}

/**
 * Gets a single assembly by its assemblyCode.
 */
export async function getAssemblyByAssemblyCode(assemblyCode: string, includeParts: boolean = false): Promise<IAssemblyService | null> {
  logOperation('GET_BY_ASSEMBLY_CODE', 'service', { assemblyCode, includeParts });
  console.log(`[AssemblyService] Looking up assembly by assemblyCode: "${assemblyCode}", includeParts: ${includeParts}`);
  
  try {
    await connectToDatabase();
    // console.log(`[AssemblyService] DB connected. Querying with { assemblyCode: "${assemblyCode}" }`);

    let assembly: IAssemblyService | null = null;
    let queryPathDescription = '';

    // --- Attempt 1: Find by assemblyCode ---
    queryPathDescription = `assemblyCode: "${assemblyCode}"`;

    if (includeParts) {
      console.log(`[AssemblyService] [${queryPathDescription}] Using aggregation pipeline with inventory integration`);
      const pipeline = [
        { $match: { assemblyCode: assemblyCode } },
        ...getAssemblyPartsAggregationPipeline()
      ];

      const results = await Assembly.aggregate(pipeline);
      assembly = results.length > 0 ? results[0] : null;
    } else {
      assembly = await Assembly.findOne({ assemblyCode: assemblyCode }).lean() as IAssemblyService | null;
    }
    // console.log(`[AssemblyService] [${queryPathDescription}] Found assembly (pre-population or no parts):`, assembly ? assembly._id : 'null');

    // --- Attempt 2: Find by assembly_id (fallback for backward compatibility) ---
    if (!assembly) {
      queryPathDescription = `assembly_id: "${assemblyCode}"`;
      console.log(`[AssemblyService] Not found with assemblyCode, trying ${queryPathDescription}`);

      if (includeParts) {
        console.log(`[AssemblyService] [${queryPathDescription}] Using aggregation pipeline with inventory integration`);
        const fallbackPipeline = [
          { $match: { assembly_id: assemblyCode } },
          ...getAssemblyPartsAggregationPipeline()
        ];

        const results = await Assembly.aggregate(fallbackPipeline);
        assembly = results.length > 0 ? results[0] : null;
      } else {
        assembly = await Assembly.findOne({ assembly_id: assemblyCode }).lean() as IAssemblyService | null;
      }
    }
    
    if (!assembly) {
      logOperation('GET_BY_ASSEMBLY_CODE_NOT_FOUND', 'service', { assemblyCode });
      console.log(`[AssemblyService] Assembly with code/ID "${assemblyCode}" not found after all attempts.`);
      return null;
    }
    
    // console.log(`[AssemblyService] Final assembly object for "${assemblyCode}":`, JSON.stringify(assembly, null, 2));
    logOperation('GET_BY_ASSEMBLY_CODE_SUCCESS', 'service', { assemblyCode, id: assembly._id, includeParts });
    return assembly;

  } catch (error: any) {
    logOperation('GET_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, error: error.message, stack: error.stack });
    console.error(`[AssemblyService] Error fetching assembly by code "${assemblyCode}":`, error);
    // Let the centralized Sentry error handler in the API route or middleware catch this
    // Re-throwing the original error or a new error with context
    const errDetails = handleMongoDBError(error); // This function already reports to Sentry
    const serviceError: any = new Error(errDetails.message || `Failed to get assembly by code ${assemblyCode}. Original error: ${error.message}`);
    serviceError.statusCode = errDetails.code || 500;
    throw serviceError;
  }
}

/**
 * Recursively processes hierarchical parts data to preserve children structure
 */
function processPartsRequiredRecursively(parts: AssemblyPartRequiredDto[]): IAssemblyPartRequired[] {
  logOperation('PROCESS_PARTS_RECURSIVE_START', 'service', { partsCount: parts.length });

  return parts.map((part: AssemblyPartRequiredDto, index: number) => {
    logOperation('PROCESS_PART', 'service', {
      index,
      partId: part.partId,
      hasChildren: !!(part.children && part.children.length > 0),
      childrenCount: part.children?.length || 0
    });

    const processedPart: IAssemblyPartRequired = {
      partId: new Types.ObjectId(String(part.partId)),
      quantityRequired: part.quantityRequired,
      unitOfMeasure: part.unitOfMeasure || 'ea',
    };

    // Recursively process children if they exist
    if (part.children && Array.isArray(part.children) && part.children.length > 0) {
      logOperation('PROCESS_CHILDREN', 'service', {
        parentPartId: part.partId,
        childrenCount: part.children.length
      });
      processedPart.children = processPartsRequiredRecursively(part.children);
      logOperation('CHILDREN_PROCESSED', 'service', {
        parentPartId: part.partId,
        processedChildrenCount: processedPart.children.length
      });
    }

    return processedPart;
  });
}

// For backward compatibility, alias the function

/**
 * Creates a new assembly.
 */
export async function createAssembly(assemblyData: CanonicalCreateAssemblyDto): Promise<IAssemblyService> {
  const { assemblyCode, name, createdBy } = assemblyData;
  logOperation('CREATE_ASSEMBLY_START', 'service', { assemblyCode, name, createdBy });

  // TODO: Re-enable createdBy requirement when authentication is implemented
  if (!assemblyCode || !name) {
    logOperation('CREATE_ASSEMBLY_ERROR', 'service', { assemblyCode, name, error: 'Missing required fields: assemblyCode, name' });
    const err = new Error('Assembly code and name are required.');
    (err as any).statusCode = 400;
    throw err;
  }
  // Only validate createdBy format if provided
  if (createdBy && !mongoose.Types.ObjectId.isValid(createdBy)) {
    logOperation('CREATE_ASSEMBLY_ERROR', 'service', { assemblyCode, name, createdBy, error: 'Invalid createdBy ID format' });
    const err = new Error('Invalid createdBy user ID format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    await connectToDatabase();

    const existingAssembly = await (Assembly.findOne as any)({ assemblyCode }).lean();
    if (existingAssembly) {
      logOperation('CREATE_ASSEMBLY_ERROR', 'service', { assemblyCode, error: 'Duplicate assembly code' });
      const err = new Error(`Assembly with code ${assemblyCode} already exists.`);
      (err as any).statusCode = 409; // Conflict
      throw err;
    }

    // Process partsRequired with hierarchical support (using 'quantityRequired')
    let processedPartsRequired: IAssemblyPartRequired[] = [];
    if (assemblyData.partsRequired && assemblyData.partsRequired.length > 0) {
      // Validate all parts exist (including nested children) before processing
      const validatePartsExist = async (parts: AssemblyPartRequiredDto[]): Promise<void> => {
        for (const partReq of parts) {
          if (!mongoose.Types.ObjectId.isValid(partReq.partId)) {
            throw new Error(`Invalid partId format: ${partReq.partId}`);
          }
          if (typeof partReq.quantityRequired !== 'number' || partReq.quantityRequired <= 0) {
            throw new Error(`Quantity for part ${partReq.partId} must be a positive number.`);
          }
          const partObjectId = new Types.ObjectId(partReq.partId);
          const partExists = await Part.findById(partObjectId).select('_id').lean();
          if (!partExists) {
            throw new Error(`Part with ID ${partReq.partId} not found.`);
          }
          // Recursively validate children
          if (partReq.children && Array.isArray(partReq.children)) {
            await validatePartsExist(partReq.children);
          }
        }
      };

      await validatePartsExist(assemblyData.partsRequired);

      logOperation('PARTS_PROCESSING_START', 'service', {
        originalPartsCount: assemblyData.partsRequired.length,
        originalParts: assemblyData.partsRequired.map(p => ({
          partId: p.partId,
          hasChildren: !!(p.children && p.children.length > 0),
          childrenCount: p.children?.length || 0
        }))
      });

      processedPartsRequired = processPartsRequiredRecursively(assemblyData.partsRequired);

      logOperation('PARTS_PROCESSING_COMPLETE', 'service', {
        processedPartsCount: processedPartsRequired.length,
        processedParts: processedPartsRequired.map(p => ({
          partId: p.partId.toString(),
          hasChildren: !!(p.children && p.children.length > 0),
          childrenCount: p.children?.length || 0
        }))
      });
    }

    // Note: Sub-assemblies are handled through parentId relationships, not as an array
    // If this assembly should be a sub-assembly of another, set the parentId
    // If other assemblies should be sub-assemblies of this one, they should reference this assembly's _id as their parentId

    let parentObjectId: Types.ObjectId | null = null;
    if (assemblyData.parentId) {
      if (!mongoose.Types.ObjectId.isValid(assemblyData.parentId)) {
        throw new Error(`Invalid parentId format: ${assemblyData.parentId}`);
      }
      parentObjectId = new Types.ObjectId(assemblyData.parentId);
      const parentExists = await (Assembly.findById as any)(parentObjectId).select('_id').lean();
      if (!parentExists) {
        throw new Error(`Parent assembly with ID ${assemblyData.parentId} not found.`);
      }
      // Note: Sub-assembly validation is handled through parentId relationships
    }

    // Construct the payload for the new Assembly document, aligning with canonical schema names
    // Note: createdBy/updatedBy fields are not included as they don't exist in the current model
    // TODO: Add createdBy/updatedBy when authentication is implemented and model is updated
    const newAssemblyPayload = {
      assemblyCode: assemblyData.assemblyCode,
      name: assemblyData.name,
      description: assemblyData.description === undefined ? null : assemblyData.description,
      version: assemblyData.version, // string, as per canonical
      status: assemblyData.status, // string, as per canonical
      parentId: parentObjectId,
      isTopLevel: !parentObjectId,
      partsRequired: processedPartsRequired, // Uses 'quantityRequired'
      manufacturingLeadTime: assemblyData.manufacturingLeadTime === undefined ? null : assemblyData.manufacturingLeadTime,
      costData: assemblyData.costData === undefined ? null : assemblyData.costData,
      notes: assemblyData.notes === undefined ? null : assemblyData.notes,
      // isTopLevel will be derived by Mongoose model virtual or pre-save hook if needed, or not stored if purely derivative
    };

    const newAssembly = new Assembly(newAssemblyPayload);

    logOperation('PRE_SAVE_ASSEMBLY_DATA', 'service', {
      assemblyCode: newAssembly.assemblyCode,
      partsRequiredCount: newAssembly.partsRequired.length,
      partsRequiredData: newAssembly.partsRequired.map((p: any) => ({
        partId: p.partId.toString(),
        quantityRequired: p.quantityRequired,
        unitOfMeasure: p.unitOfMeasure,
        hasChildren: !!(p.children && p.children.length > 0),
        childrenCount: p.children?.length || 0,
        children: p.children?.map((c: any) => ({
          partId: c.partId.toString(),
          quantityRequired: c.quantityRequired,
          unitOfMeasure: c.unitOfMeasure
        })) || []
      }))
    });

    await newAssembly.save();

    logOperation('POST_SAVE_ASSEMBLY_DATA', 'service', {
      assemblyId: newAssembly._id.toString(),
      assemblyCode: newAssembly.assemblyCode,
      partsRequiredCount: newAssembly.partsRequired.length,
      partsRequiredData: newAssembly.partsRequired.map((p: any) => ({
        partId: p.partId.toString(),
        quantityRequired: p.quantityRequired,
        unitOfMeasure: p.unitOfMeasure,
        hasChildren: !!(p.children && p.children.length > 0),
        childrenCount: p.children?.length || 0,
        children: p.children?.map((c: any) => ({
          partId: c.partId.toString(),
          quantityRequired: c.quantityRequired,
          unitOfMeasure: c.unitOfMeasure
        })) || []
      }))
    });

    logOperation('CREATE_ASSEMBLY_SUCCESS', 'service', { assemblyId: newAssembly._id.toString(), assemblyCode });
    return newAssembly as IAssemblyService;

  } catch (error: any) {
    logOperation('CREATE_ASSEMBLY_MONGO_ERROR', 'service', { assemblyCode, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode
    
    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to create assembly ${assemblyCode}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  } // End of catch (error: any)
} // End of export async function createAssembly

/**
 * Updates an existing assembly by its assemblyCode.
 */
export async function updateAssemblyByAssemblyCode(assemblyCode: string, updateData: CanonicalUpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE_BY_ASSEMBLY_CODE_START', 'service', { assemblyCode, updatedBy: updateData.updatedBy });

  if (!assemblyCode || typeof assemblyCode !== 'string' || assemblyCode.trim() === '') {
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, error: 'Assembly code must be a non-empty string.' });
    const err = new Error('Assembly code must be a non-empty string.');
    (err as any).statusCode = 400;
    throw err;
  }

  // TODO: Re-enable when authentication is implemented
  // Temporarily making updatedBy optional since authentication is not yet implemented
  if (updateData.updatedBy && !mongoose.Types.ObjectId.isValid(String(updateData.updatedBy))){
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, updatedBy: updateData.updatedBy, error: 'Invalid updatedBy field format.' });
    const err = new Error('Invalid updatedBy field format.');
    (err as any).statusCode = 400;
    throw err;
  }
  
  // Prevent updates to immutable fields (assemblyCode and createdBy are not in CanonicalUpdateAssemblyDto)
  // No explicit check needed here if DTO definition enforces this.

  try {
    await connectToDatabase();
    const assembly = await (Assembly.findOne as any)({ assemblyCode });

    if (!assembly) {
      logOperation('UPDATE_BY_ASSEMBLY_CODE_NOT_FOUND', 'service', { assemblyCode });
      return null; // Or throw 404 error
    }

    // Process partsRequired if present in updateData (with hierarchical support)
    if (updateData.partsRequired === null) {
        assembly.partsRequired = [];
    } else if (Array.isArray(updateData.partsRequired)) {
      // Validate all parts exist (including nested children) before processing
      const validatePartsExist = async (parts: AssemblyPartRequiredDto[]): Promise<void> => {
        for (const partReq of parts) {
          if (!partReq.partId || !mongoose.Types.ObjectId.isValid(String(partReq.partId))) {
            throw new Error(`Invalid partId format in partsRequired: ${partReq.partId}`);
          }
          if (typeof partReq.quantityRequired !== 'number' || partReq.quantityRequired <= 0) {
            throw new Error(`Quantity for part ${partReq.partId} must be a positive number.`);
          }
          const partObjectId = new Types.ObjectId(String(partReq.partId));
          const partExists = await Part.findById(partObjectId).select('_id').lean();
          if (!partExists) {
            throw new Error(`Part with ID ${partObjectId.toString()} not found during update.`);
          }
          // Recursively validate children
          if (partReq.children && Array.isArray(partReq.children)) {
            await validatePartsExist(partReq.children);
          }
        }
      };

      await validatePartsExist(updateData.partsRequired);
      assembly.partsRequired = processPartsRequiredRecursively(updateData.partsRequired);
    } // If undefined, partsRequired is not modified

    // Note: Sub-assemblies are handled through parentId relationships, not as an array
    // To create sub-assemblies, create separate assembly documents with this assembly's _id as their parentId

    // Handle parentId. isTopLevel is not in CanonicalUpdateAssemblyDto and should be derived or handled by model.
    if (updateData.hasOwnProperty('parentId')) {
      if (updateData.parentId === null) {
        assembly.parentId = null;
      } else if (updateData.parentId && mongoose.Types.ObjectId.isValid(String(updateData.parentId))) {
        const parentObjectId = new Types.ObjectId(String(updateData.parentId));
        if (parentObjectId.equals(assembly._id)) {
          throw new Error('An assembly cannot be its own parent.');
        }
        const parentExists = await (Assembly.findById as any)(parentObjectId).select('_id').lean();
        if (!parentExists) {
          throw new Error(`Parent assembly with ID ${updateData.parentId} not found.`);
        }
        assembly.parentId = parentObjectId;
      } else if (updateData.parentId !== undefined) { // parentId is present but not null and not valid ObjectId string
        throw new Error(`Invalid parentId format: ${updateData.parentId}`);
      }
      // Implicitly, isTopLevel might change based on parentId. Model should handle this if it's a virtual or pre-save logic.
    }

    // Apply other updatable fields from CanonicalUpdateAssemblyDto
    // Explicitly list fields to update to ensure type safety and adherence to the DTO.
    if (updateData.hasOwnProperty('name')) assembly.name = updateData.name!;
    if (updateData.hasOwnProperty('description')) assembly.description = updateData.description;
    if (updateData.hasOwnProperty('version')) assembly.version = updateData.version!;
    if (updateData.hasOwnProperty('status')) {
        if (!['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'].includes(updateData.status!)){
            logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, status: updateData.status, error: 'Invalid status value' });
            throw new Error(`Invalid status value: ${updateData.status}. Must be one of 'active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'.`);
        }
        assembly.status = updateData.status!;
    }
    if (updateData.hasOwnProperty('manufacturingLeadTime')) assembly.manufacturingLeadTime = updateData.manufacturingLeadTime;
    if (updateData.hasOwnProperty('costData')) assembly.costData = updateData.costData;
    if (updateData.hasOwnProperty('notes')) assembly.notes = updateData.notes;
    // 'updatedBy' is handled below. 'partsRequired', 'subAssemblies', 'parentId' handled above.

    // TODO: Re-enable when authentication is implemented and model has updatedBy field
    // Temporarily not setting updatedBy since the model doesn't have this field
    // if (updateData.updatedBy) {
    //   assembly.updatedBy = new Types.ObjectId(String(updateData.updatedBy));
    // }
    // assembly.updatedAt = new Date(); // This is handled automatically by timestamps: true

    await assembly.save();
    logOperation('UPDATE_BY_ASSEMBLY_CODE_SUCCESS', 'service', { assemblyCode: assembly.assemblyCode, assemblyId: assembly._id.toString() });
    return assembly as IAssemblyService;
  } catch (error: any) {
    logOperation('UPDATE_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode

    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to update assembly ${assemblyCode}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  }
}

// For backward compatibility
export const updateAssemblyByAssemblyId = updateAssemblyByAssemblyCode;

/**
 * Deletes an assembly by its MongoDB ObjectId.
 */
/**
 * Updates an existing assembly by its MongoDB ObjectId.
 */
export async function updateAssembly(id: string, updateData: CanonicalUpdateAssemblyDto): Promise<IAssemblyService | null> {
  logOperation('UPDATE_ASSEMBLY_BY_ID_START', 'service', { assemblyId: id, updatedBy: updateData.updatedBy });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', 'service', { assemblyId: id, error: 'Invalid assembly ID format' });
    const err = new Error('Invalid assembly ID format');
    (err as any).statusCode = 400;
    throw err;
  }
  
  // TODO: Re-enable when authentication is implemented
  // Temporarily making updatedBy optional since authentication is not yet implemented
  if (updateData.updatedBy && !mongoose.Types.ObjectId.isValid(String(updateData.updatedBy))) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', 'service', { assemblyId: id, updatedBy: updateData.updatedBy, error: 'Invalid updatedBy field format.' });
    const err = new Error('Invalid updatedBy field format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    await connectToDatabase();

    // TODO: Re-enable when authentication is implemented and model has updatedBy field
    // Temporarily not setting updatedBy since the model doesn't have this field
    const updatePayload: Record<string, any> = { // Using Record<string, any> to accommodate canonical fields
      // updatedBy: updateData.updatedBy ? new Types.ObjectId(String(updateData.updatedBy)) : undefined,
      // updatedAt: new Date(), // This is handled automatically by timestamps: true
    };

    // Assign fields from CanonicalUpdateAssemblyDto if they exist
    if (updateData.hasOwnProperty('name')) updatePayload.name = updateData.name;
    if (updateData.hasOwnProperty('description')) updatePayload.description = updateData.description;
    if (updateData.hasOwnProperty('version')) updatePayload.version = updateData.version; // version is string
    
    if (updateData.hasOwnProperty('status')) {
      const validStatuses = ['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'];
      if (!validStatuses.includes(updateData.status!)) {
        logOperation('UPDATE_ASSEMBLY_BY_ID_ERROR', 'service', { assemblyId: id, status: updateData.status, error: 'Invalid status value' });
        const err = new Error(`Invalid status value: ${updateData.status}. Must be one of '${validStatuses.join("', '")}'.`);
        (err as any).statusCode = 400;
        throw err;
      }
      updatePayload.status = updateData.status;
    }
    
    if (updateData.hasOwnProperty('manufacturingLeadTime')) updatePayload.manufacturingLeadTime = updateData.manufacturingLeadTime;
    if (updateData.hasOwnProperty('costData')) updatePayload.costData = updateData.costData; // costData is an object
    if (updateData.hasOwnProperty('notes')) updatePayload.notes = updateData.notes;

    // Process partsRequired (with hierarchical support)
    if (updateData.hasOwnProperty('partsRequired')) {
      if (updateData.partsRequired === null) {
        updatePayload.partsRequired = [];
      } else if (Array.isArray(updateData.partsRequired)) {
        // Validate all parts exist (including nested children) before processing
        const validatePartsExist = async (parts: AssemblyPartRequiredDto[]): Promise<void> => {
          for (const partReq of parts) {
            if (!partReq.partId || !mongoose.Types.ObjectId.isValid(String(partReq.partId))) {
              const err = new Error(`Invalid partId format in partsRequired: ${partReq.partId}`);
              (err as any).statusCode = 400;
              throw err;
            }
            if (typeof partReq.quantityRequired !== 'number' || partReq.quantityRequired <= 0) {
              const err = new Error(`Quantity for part ${partReq.partId} must be a positive number.`);
              (err as any).statusCode = 400;
              throw err;
            }
            const partObjectId = new Types.ObjectId(String(partReq.partId));
            const partExists = await Part.findById(partObjectId).select('_id').lean();
            if (!partExists) {
              const err = new Error(`Part with ID ${partObjectId.toString()} not found.`);
              (err as any).statusCode = 404;
              throw err;
            }
            // Recursively validate children
            if (partReq.children && Array.isArray(partReq.children)) {
              await validatePartsExist(partReq.children);
            }
          }
        };

        await validatePartsExist(updateData.partsRequired);
        updatePayload.partsRequired = processPartsRequiredRecursively(updateData.partsRequired);
      }
    }

    // Note: Sub-assemblies are handled through parentId relationships, not as an array
    // To create sub-assemblies, create separate assembly documents with this assembly's _id as their parentId

    // Process parentId
    if (updateData.hasOwnProperty('parentId')) {
      if (updateData.parentId === null) {
        updatePayload.parentId = null;
      } else if (updateData.parentId && mongoose.Types.ObjectId.isValid(String(updateData.parentId))) {
        const parentObjectId = new Types.ObjectId(String(updateData.parentId));
        if (parentObjectId.equals(id)) {
          const err = new Error('An assembly cannot be its own parent.');
          (err as any).statusCode = 400;
          throw err;
        }
        const parentExists = await (Assembly.findById as any)(parentObjectId).select('_id').lean();
        if (!parentExists) {
          const err = new Error(`Parent assembly with ID ${updateData.parentId} not found.`);
          (err as any).statusCode = 404;
          throw err;
        }
        updatePayload.parentId = parentObjectId;
      } else if (updateData.parentId !== undefined) { // parentId is present but not null and not valid ObjectId string
        const err = new Error(`Invalid parentId format: ${updateData.parentId}`);
        (err as any).statusCode = 400;
        throw err;
      }
      // isTopLevel is not in CanonicalUpdateAssemblyDto, should be derived by model if needed
    }

    const updatedAssemblyDoc = await (Assembly.findByIdAndUpdate as any)(
      id,
      { $set: updatePayload }, 
      { new: true, runValidators: true, context: 'query' }
      // Removed .lean() to get a Mongoose document
    );

    if (!updatedAssemblyDoc) {
      logOperation('UPDATE_ASSEMBLY_BY_ID_NOT_FOUND', 'service', { assemblyId: id });
      const err = new Error(`Assembly with ID ${id} not found.`);
      (err as any).statusCode = 404;
      throw err; // Throw error instead of returning null for consistency
    }

    logOperation('UPDATE_ASSEMBLY_BY_ID_SUCCESS', 'service', { assemblyId: updatedAssemblyDoc._id.toString(), assemblyCode: updatedAssemblyDoc.assemblyCode });
    return updatedAssemblyDoc as IAssemblyService; // Cast to IAssemblyService, should be compatible now

  } catch (error: any) {
    logOperation('UPDATE_ASSEMBLY_BY_ID_MONGO_ERROR', 'service', { assemblyId: id, errorName: error.name, errorMessage: error.message });
    if (error.statusCode) throw error; // Re-throw if already has statusCode

    const errDetails = handleMongoDBError(error);
    const serviceError = new Error(errDetails.message || `Failed to update assembly ${id}`);
    (serviceError as any).statusCode = errDetails.code || 500;
    if (errDetails.type === 'validation' && (errDetails as any).errors) {
        (serviceError as any).errors = (errDetails as any).errors;
    }
    throw serviceError;
  }
}

/**
 * Deletes an assembly by its MongoDB ObjectId.
 */
export async function deleteAssembly(id: string): Promise<boolean> {
  logOperation('DELETE', 'service', { id });

  if (!mongoose.Types.ObjectId.isValid(id)) {
    logOperation('DELETE_INVALID_ID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format for deletion.');
  }

  try {
    await connectToDatabase();

    // Check if assembly is referenced by another assembly as a parent
    const referencingAssemblyAsParent = await (Assembly.findOne as any)({ parentId: new Types.ObjectId(id) }).lean();
    if (referencingAssemblyAsParent) {
      logOperation('DELETE_REFERENCED_AS_PARENT', 'service', { id, referencedBy: referencingAssemblyAsParent._id });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${referencingAssemblyAsParent.name} (${referencingAssemblyAsParent.assemblyCode}).`);
    }

    // Check if assembly is referenced as a subAssembly in other assemblies
    const referencingAssemblyAsSubAssembly = await (Assembly.findOne as any)({ 'subAssemblies.subAssemblyId': new Types.ObjectId(id) }).lean();
    if (referencingAssemblyAsSubAssembly) {
      logOperation('DELETE_REFERENCED_AS_SUBASSEMBLY', 'service', { id, referencedBy: referencingAssemblyAsSubAssembly._id });
      throw new Error(`Cannot delete assembly: it is used as a sub-assembly in assembly ${referencingAssemblyAsSubAssembly.name} (${referencingAssemblyAsSubAssembly.assemblyCode}).`);
    }

    const result = await (Assembly.deleteOne as any)({ _id: new Types.ObjectId(id) });

    if (result.deletedCount === 0) {
      logOperation('DELETE_NOT_FOUND', 'service', { id });
      // Consider if this should throw an error or return false. Returning false for not found is common.
      return false; 
    }
    
    logOperation('DELETE_SUCCESS', 'service', { id, deletedCount: result.deletedCount });
    return true;
  } catch (error: any) {
    logOperation('DELETE_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    // Ensure a clear message is thrown, especially if it's a custom one from our checks
    if (error.message.startsWith('Cannot delete assembly:')) {
        throw error; 
    }
    throw new Error(errDetails.message || `Failed to delete assembly with id ${id}.`);
  }
}

/**
 * Deletes an assembly by its assemblyCode.
 */
export async function deleteAssemblyByAssemblyCode(assemblyCode: string): Promise<boolean> {
  logOperation('DELETE_BY_ASSEMBLY_CODE', 'service', { assemblyCode });
  
  try {
    await connectToDatabase();

    // Find the assembly first to check references
    const assembly = await (Assembly.findOne as any)({
      $or: [
        { assemblyCode: assemblyCode },
        { assembly_id: assemblyCode }
      ]
    });
    
    if (!assembly) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_NOT_FOUND', 'service', { assemblyCode });
      return false;
    }
    
    // Check if assembly is referenced by another assembly
    const isReferenced = await (Assembly.findOne as any)({ parentId: assembly._id });
    if (isReferenced) {
      logOperation('DELETE_BY_ASSEMBLY_CODE_REFERENCED', 'service', { 
        assemblyCode, 
        referencedBy: isReferenced.assemblyCode || isReferenced.assembly_id 
      });
      throw new Error(`Cannot delete assembly: it is used as a parent by assembly ${isReferenced.name} (${isReferenced.assemblyCode || isReferenced.assembly_id})`);
    }

    const result = await (Assembly.deleteOne as any)({ _id: assembly._id });
    
    logOperation('DELETE_BY_ASSEMBLY_CODE_SUCCESS', 'service', { assemblyCode, deletedCount: result.deletedCount });
    return result.deletedCount > 0;
  } catch (error: any) {
    logOperation('DELETE_BY_ASSEMBLY_CODE_ERROR', 'service', { assemblyCode, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete assembly with assemblyCode ${assemblyCode}`);
  }
}

// For backward compatibility
export const deleteAssemblyByAssemblyId = deleteAssemblyByAssemblyCode;

/**
 * Searches assemblies based on a text query string.
 */
export async function searchAssemblies(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  logOperation('SEARCH', 'service', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    
    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };
    
    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { assemblyCode: new RegExp(query, 'i') },
        { assembly_id: new RegExp(query, 'i') }, // For backward compatibility
        { description: new RegExp(query, 'i') }
      ];
      
      // If query matches notes field
      if ('notes' in searchFilter) {
        searchFilter.$or.push({ notes: new RegExp(query, 'i') });
      }
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('SEARCH_SUCCESS', 'service', { query, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', 'service', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search assemblies');
  }
}

/**
 * Gets assemblies by status with pagination.
 */
export async function getAssembliesByStatus(status: string, options: any = {}) {
  logOperation('GET_BY_STATUS', 'service', { status });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    
    // Combine status filter with additional filters
    const searchFilter = { 
      ...filter,
      status
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_STATUS_SUCCESS', 'service', { status, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('GET_BY_STATUS_ERROR', 'service', { status, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get assemblies with status ${status}`);
  }
}

/**
 * Gets assemblies by type with pagination.
 */
export async function getAssembliesByType(type: 'standard' | 'custom' | 'kit', options: any = {}) {
  logOperation('GET_BY_TYPE', 'service', { type });

  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Combine type filter with additional filters
    const searchFilter: any = {
      ...filter,
    };

    if (type === 'standard') {
      searchFilter.isTopLevel = true;
    } else if (type === 'custom') {
      searchFilter.isTopLevel = false;
    }
    // 'kit' type is not handled for now

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];

    const [assemblies, totalCount] = await Promise.all([
      Assembly.aggregate(pipeline),
      Assembly.countDocuments(searchFilter)
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };

    logOperation('GET_BY_TYPE_SUCCESS', 'service', { type, count: assemblies.length, pagination });
    return { assemblies, pagination };
  } catch (error: any) {
    logOperation('GET_BY_TYPE_ERROR', 'service', { type, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get assemblies with type ${type}`);
  }
}
